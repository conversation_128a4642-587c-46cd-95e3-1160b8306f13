import React from 'react';
import { Briefcase, Gem, Calendar } from 'lucide-react';

export default function WhyChooseUs() {
  return (
    <div className="bg-[var(--secondary-background)] py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Main Heading */}
        <h2 className="text-2xl sm:text-4xl lg:text-5xl font-bold text-gray-900 text-center mb-8 sm:mb-12 lg:mb-16">
          Why Choose Us?
        </h2>
        
        {/* Three Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {/* Card 1 - Unmatched Experience */}
          <div className="bg-[var(--primary-background)] p-4 sm:p-6 lg:p-8 shadow-sm">
            <div className="flex justify-center mb-4 sm:mb-6">
              <Briefcase size={36} className="text-gray-900 sm:w-12 lg:w-16" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 text-center mb-3 sm:mb-4 lg:mb-6">
              Unmatched Experience
            </h3>
            <p className="text-gray-600 mb-6">
              We&apos;re committed to sustainable tourism practices that benefit both travelers and local communities.
            </p>
          </div>

          {/* Card 2 - Unique Safaris */}
          <div className="bg-[var(--primary-background)] p-4 sm:p-6 lg:p-8 shadow-sm">
            <div className="flex justify-center mb-4 sm:mb-6">
              <Gem size={36} className="text-gray-900 sm:w-12 lg:w-16" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 text-center mb-3 sm:mb-4 lg:mb-6">
              Unique Safaris
            </h3>
            <p className="text-gray-600 mb-6">
              Our team&apos;s deep knowledge of African destinations ensures you get the most authentic experience.
            </p>
          </div>

          {/* Card 3 - Hassle Free Booking */}
          <div className="bg-[var(--primary-background)] p-4 sm:p-6 lg:p-8 shadow-sm">
            <div className="flex justify-center mb-4 sm:mb-6">
              <Calendar size={36} className="text-gray-900 sm:w-12 lg:w-16" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 text-center mb-3 sm:mb-4 lg:mb-6">
              Hassle Free Booking
            </h3>
            <p className="text-sm sm:text-base text-gray-700 text-center leading-relaxed">
              Planning your adventure has never been easier! Our seamless booking process is designed for comfort, with personalized assistance, flexible options, and secured payments, ensuring a smooth experience from start to finish.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}