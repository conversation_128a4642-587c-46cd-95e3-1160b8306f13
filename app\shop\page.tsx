import type { Metadata } from 'next';
import React from 'react';
import MegaMenuHeader from '@/components/header';
import ShopClient from '@/components/shop/ShopClient';
import Footer from '@/components/footer';
import { generateMetadata } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - African Safari Gear & Travel Essentials Shop',
  description: 'Shop premium safari gear, travel essentials, and African-inspired products. Everything you need for your African adventure, from clothing to equipment.',
  url: '/shop',
  image: '/images/shop/community-shop.webp',
  keywords: [
    'safari gear shop',
    'African travel essentials',
    'safari clothing',
    'wildlife photography equipment',
    'safari accessories',
    'travel gear Africa',
    'safari equipment store',
    'African souvenirs',
    'outdoor gear Africa',
    'safari supplies',
    'travel accessories',
    'safari apparel',
    'adventure gear shop',
    'African travel products',
    'safari merchandise'
  ],
  type: 'website'
});

export default function Shop() {
  return (
    <div>
      <MegaMenuHeader />
      <ShopClient />
      <Footer />
    </div>
  );
}