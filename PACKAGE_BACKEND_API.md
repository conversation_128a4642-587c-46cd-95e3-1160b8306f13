# Package Management Backend API Documentation

## Overview

This document describes the complete backend API for the Swift Africa Safaris package management system. The backend provides CRUD operations for packages, content management, booking system, file uploads, and email integration.

## Database Schema

### Tables Created

All tables use the `sas_` prefix as required:

- **sas_packages** - Main package information
- **sas_package_content_blocks** - Block-based content editor data
- **sas_package_itinerary** - Day-by-day itinerary information
- **sas_bookings** - Customer booking requests
- **sas_package_images** - Additional package gallery images

### Storage Buckets

- **sas-package-images** - Main package images
- **sas-package-content** - Content block images

## API Endpoints

### Public Package Endpoints

#### GET /api/packages
Fetch public packages (active only)

**Query Parameters:**
- `search` - Search in title, location, description
- `category` - Filter by category
- `location` - Filter by location
- `difficulty` - Filter by difficulty

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 12)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "Package Title",
      "slug": "package-slug",
      "description": "Package description",
      "location": "Uganda",
      "price": "$1,348",
      "pricing": {
        "solo": 1355,
        "honeymoon": 1353,
        "family": 1351,
        "group": 1348
      },
      "difficulty": "Moderate",
      "category": "Wildlife",
      "featured": true,
      "image": "image_url",
      "imageAlt": "Alt text",
      "duration": "4 Days",
      "highlights": ["highlight1", "highlight2"]
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 12,
    "total": 25,
    "totalPages": 3
  }
}
```

#### GET /api/packages/[slug]
Fetch single package details (active only)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "Package Title",
    "slug": "package-slug",
    "description": "Description",
    "overview": "Detailed overview",
    "difficulty": "Moderate",
    "category": "Wildlife",
    "location": "Uganda",
    "duration": "4 Days",
    "pricing": {
      "solo": { "price": 1355, "text": "Per Person" },
      "honeymoon": { "price": 1353, "text": "Per Person" },
      "family": { "price": 1351, "text": "Per Person" },
      "group": { "price": 1348, "text": "Per Person" }
    },
    "lowestPrice": 1348,
    "heroImage": "hero_image_url",
    "heroImageAlt": "Hero alt text",
    "highlights": ["highlight1", "highlight2"],
    "whatToPack": ["item1", "item2"],
    "includes": ["included1", "included2"],
    "excludes": ["excluded1", "excluded2"],
    "seo": {
      "title": "SEO title",
      "description": "SEO description",
      "keywords": ["keyword1", "keyword2"]
    },
    "schema": { /* Schema.org structured data */ },
    "contentBlocks": [
      {
        "id": "uuid",
        "block_type": "paragraph",
        "content": "Block content",
        "sort_order": 0
      }
    ],
    "itinerary": [
      {
        "id": "uuid",
        "day_number": 1,
        "title": "Day 1 Title",
        "description": "Day description",
        "activities": ["activity1", "activity2"],
        "accommodation": "Lodge name",
        "meals": ["Breakfast", "Lunch"]
      }
    ],
    "gallery": [
      {
        "id": "uuid",
        "image_url": "image_url",
        "image_alt": "Alt text",
        "caption": "Image caption"
      }
    ]
  }
}
```

### Admin Package Management

#### GET /api/admin/packages
Fetch packages for admin (all statuses)

**Query Parameters:**
- `search` - Search in title, location
- `status` - Filter by status (all, active, inactive, draft)
- `category` - Filter by category
- `page` - Page number
- `limit` - Items per page

#### POST /api/admin/packages
Create new package

**Request Body:**
```json
{
  "title": "Package Title",
  "location": "Uganda",
  "difficulty": "Moderate",
  "category": "Wildlife",
  "pricing": {
    "solo": 1355,
    "honeymoon": 1353,
    "family": 1351,
    "group": 1348
  },
  "description": "Package description",
  "overview": "Detailed overview",
  "duration": "4 Days",
  "status": "draft",
  "featured": false,
  "imageUrl": "image_url",
  "imageAlt": "Alt text",
  "highlights": ["highlight1", "highlight2"],
  "packingList": ["item1", "item2"],
  "includes": ["included1", "included2"],
  "excludes": ["excluded1", "excluded2"],
  "seoTitle": "SEO title",
  "seoDescription": "SEO description",
  "seoKeywords": ["keyword1", "keyword2"]
}
```

#### GET /api/admin/packages/[slug]
Fetch single package for editing (includes all related data)

#### PUT /api/admin/packages/[slug]
Update single package

#### DELETE /api/admin/packages/[slug]
Delete single package

#### PUT /api/admin/packages
Bulk delete packages

**Request Body:**
```json
{
  "action": "delete",
  "packageIds": ["uuid1", "uuid2"]
}
```

### Content Management

#### GET /api/admin/packages/[slug]/content
Fetch content blocks for a package

#### POST /api/admin/packages/[slug]/content
Create new content block

**Request Body:**
```json
{
  "block_type": "paragraph",
  "content": "Block content",
  "image_url": "image_url",
  "image_alt": "Alt text (required for image blocks)",
  "image_caption": "Image caption",
  "sort_order": 0
}
```

#### PUT /api/admin/packages/[slug]/content
Update content blocks (bulk update for reordering)

#### DELETE /api/admin/packages/[slug]/content?blockId=uuid
Delete content block

### Itinerary Management

#### GET /api/admin/packages/[slug]/itinerary
Fetch itinerary for a package

#### POST /api/admin/packages/[slug]/itinerary
Create new itinerary day

**Request Body:**
```json
{
  "day_number": 1,
  "title": "Day 1 Title",
  "description": "Day description",
  "activities": ["activity1", "activity2"],
  "accommodation": "Lodge name",
  "meals": ["Breakfast", "Lunch"],
  "sort_order": 0
}
```

#### PUT /api/admin/packages/[slug]/itinerary
Update itinerary days (bulk update)

#### DELETE /api/admin/packages/[slug]/itinerary?dayId=uuid
Delete itinerary day

### File Upload

#### POST /api/upload/image
Upload image to Supabase Storage

**Form Data:**
- `file` - Image file (JPEG, PNG, WebP, GIF)
- `altText` - Alt text (required)
- `bucket` - Storage bucket (default: sas-package-images)

**Response:**
```json
{
  "success": true,
  "data": {
    "fileName": "timestamp-random.jpg",
    "originalName": "original.jpg",
    "url": "public_url",
    "altText": "Alt text",
    "size": 1024000,
    "type": "image/jpeg",
    "bucket": "sas-package-images"
  }
}
```

#### GET /api/upload/image
List uploaded images

#### DELETE /api/upload/image?fileName=filename&bucket=bucket
Delete image from storage

### Booking System

#### POST /api/bookings
Create new booking

**Request Body:**
```json
{
  "packageTitle": "Package Title",
  "packageType": "solo",
  "fullName": "Customer Name",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "people": 2,
  "date": "2024-06-15",
  "checkOutDate": "2024-06-19",
  "message": "Special requests",
  "packageId": "uuid"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "bookingReference": "SAS-*************",
    "packageTitle": "Package Title",
    "fullName": "Customer Name",
    "email": "<EMAIL>",
    "checkInDate": "2024-06-15",
    "numberOfPeople": 2,
    "status": "pending"
  }
}
```

#### GET /api/bookings
Fetch bookings (admin only)

**Query Parameters:**
- `page` - Page number
- `limit` - Items per page
- `status` - Filter by status
- `search` - Search in name, email, booking reference

## Email Integration

The system automatically sends emails when bookings are created:

1. **Customer Confirmation Email** - Sent to customer with booking details
2. **Admin Notification Email** - <NAME_EMAIL>

Email templates are defined in `lib/email-service.ts` and can be customized.

## Security Features

- **Row Level Security (RLS)** enabled on all tables
- **Input validation** on all endpoints
- **File type and size validation** for uploads
- **Email format validation**
- **Date validation** for bookings
- **Alt text requirement** for all images
- **SQL injection prevention**

## Error Handling

All endpoints return consistent error responses:

```json
{
  "success": false,
  "error": "Error message"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation errors)
- `404` - Not Found
- `500` - Internal Server Error

## Setup Instructions

1. Run the database setup:
   ```bash
   node scripts/setup-complete-backend.mjs
   ```

2. Verify the setup by checking the console output

3. Test API endpoints using the provided examples

4. Configure email service in `lib/email-service.ts`

## Testing

Test all functionality:
- ✅ CRUD operations for packages
- ✅ File upload functionality
- ✅ Booking form submission
- ✅ Email sending
- ✅ Content block management
- ✅ Itinerary management
- ✅ Security validations
