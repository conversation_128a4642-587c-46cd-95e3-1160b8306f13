/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-require-imports */
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupAdminUser() {
  console.log('🚀 Setting up admin user for Swift Africa Safaris...\n');

  try {
    // Check if users table exists
    console.log('📋 Checking database schema...');
    const { data: tables, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'users');

    if (tableError) {
      console.error('❌ Error checking tables:', tableError);
      return;
    }

    if (tables.length === 0) {
      console.log('❌ Users table not found. Please run the SQL setup script first.');
      console.log('Run this SQL in your Supabase SQL editor:');
      console.log('📄 scripts/setup-admin-database.sql');
      return;
    }

    console.log('✅ Users table found');

    // Check if avatars bucket exists
    console.log('📦 Checking avatars storage bucket...');
    const { data: buckets, error: bucketError } = await supabase.storage.listBuckets();
    
    if (bucketError) {
      console.error('❌ Error checking buckets:', bucketError);
      return;
    }

    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    if (!avatarsBucket) {
      console.log('⚠️  Avatars bucket not found. Creating...');
      const { error: createBucketError } = await supabase.storage.createBucket('avatars', {
        public: true,
        allowedMimeTypes: ['image/*'],
        fileSizeLimit: 5242880 // 5MB
      });

      if (createBucketError) {
        console.error('❌ Error creating avatars bucket:', createBucketError);
        return;
      }
      console.log('✅ Avatars bucket created');
    } else {
      console.log('✅ Avatars bucket found');
    }

    // Get admin email from user input
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const adminEmail = await new Promise((resolve) => {
      readline.question('📧 Enter admin email address: ', (email) => {
        readline.close();
        resolve(email);
      });
    });

    if (!adminEmail) {
      console.log('❌ No email provided');
      return;
    }

    // Check if user already exists
    console.log('🔍 Checking if user exists...');
    const { data: existingUser, error: userError } = await supabase.auth.admin.getUserByEmail(adminEmail);

    if (userError && userError.message !== 'User not found') {
      console.error('❌ Error checking user:', userError);
      return;
    }

    if (existingUser.user) {
      console.log('✅ User already exists');
      
      // Update user role to admin
      const { error: updateError } = await supabase
        .from('users')
        .update({ role: 'admin' })
        .eq('email', adminEmail);

      if (updateError) {
        console.error('❌ Error updating user role:', updateError);
        return;
      }

      console.log('✅ User role updated to admin');
    } else {
      // Create new admin user
      console.log('👤 Creating new admin user...');
      
      const adminPassword = await new Promise((resolve) => {
        const readline = require('readline').createInterface({
          input: process.stdin,
          output: process.stdout
        });
        readline.question('🔐 Enter admin password (min 8 chars): ', (password) => {
          readline.close();
          resolve(password);
        });
      });

      if (!adminPassword || adminPassword.length < 8) {
        console.log('❌ Invalid password (minimum 8 characters)');
        return;
      }

      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: adminEmail,
        password: adminPassword,
        email_confirm: true,
        user_metadata: {
          name: 'Admin User',
          role: 'admin',
          department: 'Management'
        }
      });

      if (createError) {
        console.error('❌ Error creating user:', createError);
        return;
      }

      console.log('✅ Admin user created successfully');
    }

    // Test the system
    console.log('\n🧪 Testing user management system...');
    
    // Test fetching users
    const { data: users, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .limit(5);

    if (fetchError) {
      console.error('❌ Error fetching users:', fetchError);
      return;
    }

    console.log(`✅ Successfully fetched ${users.length} users`);
    
    if (users.length > 0) {
      console.log('📊 Sample user data:');
      users.forEach(user => {
        console.log(`  - ${user.name} (${user.email}) - ${user.role}`);
      });
    }

    console.log('\n🎉 Admin user setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Go to your admin dashboard: /admin/user-management');
    console.log('2. You should now be able to see and manage users');
    console.log('3. Create additional users using the "Add User" button');
    console.log('4. Test user login functionality');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the setup
setupAdminUser(); 