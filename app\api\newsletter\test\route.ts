import { NextRequest, NextResponse } from 'next/server';
import { sendTestNewsletter } from '@/lib/newsletter-service';

// POST - Send test newsletter (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testEmail, subject, content } = body;

    // Validate test email
    if (!testEmail || typeof testEmail !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Test email is required' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Send test newsletter
    const testSubject = subject || 'Test Newsletter - Swift Africa Safaris';
    const testContent = content || `
      <div style="text-align: center; padding: 20px;">
        <h2 style="color: #163201;">Test Newsletter</h2>
        <p>This is a test email to verify your newsletter configuration is working correctly.</p>
        <p>If you received this email, your SMTP settings are properly configured!</p>
        <div style="margin: 20px 0; padding: 15px; background-color: #f0f8e8; border-left: 4px solid #317100; border-radius: 5px;">
          <p style="margin: 0; color: #317100;"><strong>✅ Configuration Test Successful</strong></p>
          <p style="margin: 5px 0 0 0; font-size: 14px; color: #666;">
            Sent at: ${new Date().toLocaleString()}
          </p>
        </div>
        <p style="font-size: 14px; color: #666;">
          This test was sent from the Swift Africa Safaris newsletter system.
        </p>
      </div>
    `;

    const success = await sendTestNewsletter(testEmail, testSubject, testContent);

    if (success) {
      return NextResponse.json({
        success: true,
        message: `Test newsletter sent successfully to ${testEmail}`
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send test newsletter. Please check your SMTP configuration.' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Newsletter test error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
