import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch single blog post by slug (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Fetch blog post
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count,
        seo_title,
        seo_description,
        seo_keywords,
        og_title,
        og_description,
        og_image_url,
        canonical_url,
        robots_index,
        robots_follow,
        schema_data
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();

    if (postError || !post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Fetch content blocks
    const { data: contentBlocks, error: contentError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true });

    if (contentError) {
      console.error('Content blocks error:', contentError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch blog content' },
        { status: 500 }
      );
    }

    // Increment view count (fire and forget)
    supabase
      .from('sas_blog_posts')
      .update({ view_count: (post.view_count || 0) + 1 })
      .eq('id', post.id)
      .then(() => {
        console.log(`View count incremented for blog post: ${slug}`);
      })
      .catch((error) => {
        console.error('Failed to increment view count:', error);
      });

    // Fetch 4 random blog posts (excluding current post)
    const { data: relatedPosts, error: relatedError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        published_at
      `)
      .eq('status', 'published')
      .is('deleted_at', null)
      .neq('id', post.id)
      .order('created_at', { ascending: false })
      .limit(20); // Get more posts to randomize from

    if (relatedError) {
      console.error('Related posts error:', relatedError);
    }

    // Randomize and select 4 posts
    let randomizedPosts = [];
    if (relatedPosts && relatedPosts.length > 0) {
      // Shuffle the array using Fisher-Yates algorithm
      const shuffled = [...relatedPosts];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      // Take first 4 posts
      randomizedPosts = shuffled.slice(0, 4);
    }

    return NextResponse.json({
      success: true,
      data: {
        post: {
          ...post,
          content: contentBlocks || []
        },
        relatedPosts: randomizedPosts
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH - Update view count (public endpoint for analytics)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    if (body.action !== 'increment_view') {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }

    // Get current view count
    const { data: post, error: fetchError } = await supabase
      .from('sas_blog_posts')
      .select('id, view_count')
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();

    if (fetchError || !post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Increment view count
    const { error: updateError } = await supabase
      .from('sas_blog_posts')
      .update({ view_count: (post.view_count || 0) + 1 })
      .eq('id', post.id);

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update view count' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        view_count: (post.view_count || 0) + 1
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
