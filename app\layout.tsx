import type { Metadata } from "next";
import "./globals.css";
import FloatingWhatsAppButton from '@/components/common/whatsappButton';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateOrganizationSchema, generateWebsiteSchema, defaultSEO } from '@/lib/seo';
import { LoadingProvider } from '@/components/providers/LoadingProvider';
import PerformanceOptimizer from '@/components/common/PerformanceOptimizer';


export const metadata: Metadata = generateMetadata({
  title: defaultSEO.defaultTitle,
  description: defaultSEO.defaultDescription,
  keywords: [
    'African safari tours',
    'Rwanda safari',
    'Tanzania safari',
    'Uganda safari',
    'South Africa safari',
    'gorilla trekking',
    'wildlife tours',
    'safari packages',
    'East Africa tours',
    'safari adventures',
    'African wildlife',
    'safari holidays',
    'tour operator',
    'travel agency',
    'safari booking'
  ],
  type: 'website',
  url: '/'
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const organizationSchema = generateOrganizationSchema();
  const websiteSchema = generateWebsiteSchema();

  return (
    <html lang="en">
      <head>
        {/* Google tag (gtag.js) */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-VWBC8SK2T5"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-VWBC8SK2T5');
            `,
          }}
        />

        <link
          rel="icon"
          href="/images/common/swift-africa-safaris-best-tour-operator-icon.png"
          type="image/png"
        />
        <link
          rel="apple-touch-icon"
          href="/images/common/swift-africa-safaris-best-tour-operator-icon.png"
        />
        <meta name="theme-color" content="#163201" />
        <meta name="msapplication-TileColor" content="#163201" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Structured Data */}
        <StructuredData data={[organizationSchema, websiteSchema]} />
      </head>
      <body>
        <LoadingProvider>
          <PerformanceOptimizer />
          {children}
          <FloatingWhatsAppButton />
        </LoadingProvider>
      </body>
    </html>
  );
}