'use client';

import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  Calendar,
  DollarSign,
  Package,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Eye,
  Plus,
  Bell
} from 'lucide-react';
import Link from 'next/link';
import { useCommonNotifications } from '../../../components/partner/NotificationProvider';

interface DashboardStats {
  totalBookings: number;
  monthlyRevenue: number;
  pendingBookings: number;
  totalCommission: number;
  monthlyGrowth: number;
  averageBookingValue: number;
}

interface RecentBooking {
  id: string;
  packageName: string;
  customerName: string;
  travelers: number;
  amount: number;
  commission: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  bookingDate: string;
  travelDate: string;
}

interface QuickAction {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  color: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalBookings: 0,
    monthlyRevenue: 0,
    pendingBookings: 0,
    totalCommission: 0,
    monthlyGrowth: 0,
    averageBookingValue: 0
  });
  const [recentBookings, setRecentBookings] = useState<RecentBooking[]>([]);
  const notifications = useCommonNotifications();

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockStats: DashboardStats = {
      totalBookings: 156,
      monthlyRevenue: 89400,
      pendingBookings: 8,
      totalCommission: 13410,
      monthlyGrowth: 18.5,
      averageBookingValue: 2850
    };

    const mockBookings: RecentBooking[] = [
      {
        id: 'SA-B2B-001',
        packageName: 'Serengeti & Ngorongoro 5-Day Safari',
        customerName: 'Michael Johnson',
        travelers: 4,
        amount: 4800,
        commission: 720,
        status: 'confirmed',
        bookingDate: '2024-12-15',
        travelDate: '2025-01-20'
      },
      {
        id: 'SA-B2B-002',
        packageName: 'Kilimanjaro Climbing Adventure',
        customerName: 'Sarah Williams',
        travelers: 2,
        amount: 3200,
        commission: 384,
        status: 'pending',
        bookingDate: '2024-12-18',
        travelDate: '2025-02-10'
      },
      {
        id: 'SA-B2B-003',
        packageName: 'Tarangire Day Trip',
        customerName: 'David Brown',
        travelers: 6,
        amount: 1200,
        commission: 180,
        status: 'confirmed',
        bookingDate: '2024-12-10',
        travelDate: '2024-12-25'
      }
    ];

    setStats(mockStats);
    setRecentBookings(mockBookings);
  }, []);

  const quickActions: QuickAction[] = [
    {
      title: 'Browse Packages',
      description: 'Explore our safari packages and create new bookings',
      icon: <Package className="h-6 w-6" />,
      href: '/partner/packages',
      color: 'bg-blue-50 text-blue-600 hover:bg-blue-100'
    },
    {
      title: 'Create Booking',
      description: 'Start a new booking for your customers',
      icon: <Plus className="h-6 w-6" />,
      href: '/partner/bookings/create',
      color: 'bg-green-50 text-green-600 hover:bg-green-100'
    },
    {
      title: 'View Reports',
      description: 'Check your commission reports and analytics',
      icon: <BarChart3 className="h-6 w-6" />,
      href: '/partner/reports',
      color: 'bg-purple-50 text-purple-600 hover:bg-purple-100'
    },
    {
      title: 'Messages',
      description: 'Communicate with Swift Africa Safaris team',
      icon: <MessageSquare className="h-6 w-6" />,
      href: '/partner/messages',
      color: 'bg-orange-50 text-orange-600 hover:bg-orange-100'
    }
  ];

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Partner Dashboard</h1>
              <p className="text-gray-600 mt-1">Welcome back! Here&apos;s your business overview.</p>
            </div>
            <div className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleString()}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Bookings */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalBookings}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">{stats.monthlyGrowth}% this month</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Monthly Revenue */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyRevenue)}</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">12.3% increase</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Total Commission */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Commission</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalCommission)}</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-gray-600">15% commission rate</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          {/* Pending Bookings */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Bookings</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pendingBookings}</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-orange-600">Requires attention</span>
                </div>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <div className={`p-6 rounded-lg border border-gray-200 transition-colors cursor-pointer ${action.color}`}>
                  <div className="flex items-center mb-3">
                    {action.icon}
                    <h3 className="ml-3 font-medium">{action.title}</h3>
                  </div>
                  <p className="text-sm opacity-80">{action.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Notification Test (Development Only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Test Notifications</h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Bell className="h-5 w-5 text-yellow-600 mr-2" />
                <span className="text-sm font-medium text-yellow-800">Development Mode - Test Notifications</span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <button
                  onClick={() => notifications.notifyBookingCreated('SA-TEST-001', 'Test Safari')}
                  className="px-3 py-2 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors"
                >
                  Test Booking
                </button>
                <button
                  onClick={() => notifications.notifyPaymentReceived(1500)}
                  className="px-3 py-2 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors"
                >
                  Test Payment
                </button>
                <button
                  onClick={() => notifications.notifyNewMessage('Test Admin')}
                  className="px-3 py-2 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors"
                >
                  Test Message
                </button>
                <button
                  onClick={() => notifications.showInfo('Settings Tip', 'You can customize notifications in your profile settings', {
                    actionUrl: '/partner/profile',
                    actionText: 'Open Settings'
                  })}
                  className="px-3 py-2 bg-gray-600 text-white text-xs rounded hover:bg-gray-700 transition-colors"
                >
                  Settings Tip
                </button>
              </div>
              <p className="text-xs text-yellow-700 mt-2">
                These buttons test the notification system. Check your notification settings in Profile → Notifications.
              </p>
            </div>
          </div>
        )}

        {/* Recent Bookings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Recent Bookings</h2>
              <Link href="/partner/bookings" className="text-blue-600 hover:text-blue-500 text-sm font-medium">
                View all bookings
              </Link>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Booking ID</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Package</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Customer</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Amount</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Commission</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Travel Date</th>
                  <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentBookings.map((booking) => (
                  <tr key={booking.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-4 px-6">
                      <span className="font-medium text-gray-900">{booking.id}</span>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-gray-900 line-clamp-1">{booking.packageName}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {booking.travelers} traveler{booking.travelers !== 1 ? 's' : ''}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-gray-900">{booking.customerName}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-medium text-gray-900">{formatCurrency(booking.amount)}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-medium text-green-600">{formatCurrency(booking.commission)}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                        {getStatusIcon(booking.status)}
                        <span className="ml-1 capitalize">{booking.status}</span>
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-gray-900">{new Date(booking.travelDate).toLocaleDateString()}</span>
                    </td>
                    <td className="py-4 px-6">
                      <Link href={`/partner/bookings/${booking.id}`}>
                        <button className="p-1 text-blue-600 hover:bg-blue-50 rounded transition-colors">
                          <Eye className="h-4 w-4" />
                        </button>
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
