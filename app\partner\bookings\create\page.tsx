'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { 
  ArrowLeft, 
  Calendar, 
  Users, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Save,
  Package,
  AlertCircle
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCommonNotifications } from '../../../../components/partner/NotificationProvider';

interface BookingForm {
  packageId: string;
  packageName: string;
  pricingType: 'solo' | 'honeymoon' | 'family' | 'group';
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerCountry: string;
  travelers: number;
  travelDate: string;
  specialRequests: string;
  emergencyContact: string;
  emergencyPhone: string;
  dietaryRequirements: string;
  medicalConditions: string;
}

interface PackageInfo {
  id: string;
  name: string;
  duration: string;
  destination: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  commission: number;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

function CreateBookingContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const notifications = useCommonNotifications();
  const packageId = searchParams.get('package');
  const pricingType = searchParams.get('pricing') as 'solo' | 'honeymoon' | 'family' | 'group' || 'solo';

  const [packageInfo, setPackageInfo] = useState<PackageInfo | null>(null);
  const [formData, setFormData] = useState<BookingForm>({
    packageId: packageId || '',
    packageName: '',
    pricingType: pricingType,
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerCountry: '',
    travelers: pricingType === 'solo' ? 1 : pricingType === 'honeymoon' ? 2 : pricingType === 'family' ? 4 : 6,
    travelDate: '',
    specialRequests: '',
    emergencyContact: '',
    emergencyPhone: '',
    dietaryRequirements: '',
    medicalConditions: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 
    'Italy', 'Spain', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Kenya', 
    'South Africa', 'Tanzania', 'Uganda', 'Rwanda', 'Botswana', 'Namibia', 'Zambia'
  ];

  // Mock package data - replace with actual API call
  useEffect(() => {
    if (packageId) {
      const mockPackage: PackageInfo = {
        id: packageId,
        name: 'Serengeti & Ngorongoro 5-Day Safari',
        duration: '5 Days',
        destination: 'Serengeti & Ngorongoro',
        pricing: {
          solo: 2400,
          honeymoon: 2200,
          family: 1800,
          group: 1600
        },
        commission: 15
      };
      setPackageInfo(mockPackage);
      setFormData(prev => ({ ...prev, packageName: mockPackage.name }));
    }
  }, [packageId]);

  const handleInputChange = (field: keyof BookingForm, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Customer email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Customer phone is required';
    }

    if (!formData.travelDate) {
      newErrors.travelDate = 'Travel date is required';
    } else {
      const selectedDate = new Date(formData.travelDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.travelDate = 'Travel date cannot be in the past';
      }
    }

    if (formData.travelers < 1) {
      newErrors.travelers = 'Number of travelers must be at least 1';
    }

    if (!formData.emergencyContact.trim()) {
      newErrors.emergencyContact = 'Emergency contact is required';
    }

    if (!formData.emergencyPhone.trim()) {
      newErrors.emergencyPhone = 'Emergency phone is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would make an API call to create the booking
      console.log('Creating booking:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate booking reference
      const bookingRef = `BK-${Date.now().toString().slice(-6)}`;

      // Show success notification
      notifications.showBookingNotification(
        'Booking Created Successfully!',
        `Booking reference: ${bookingRef}`
      );

      // Redirect to bookings list
      router.push('/partner/bookings');
    } catch (error) {
      console.error('Error creating booking:', error);
      notifications.showError(
        'Booking Creation Failed',
        'Please try again or contact support if the problem persists.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateTotal = () => {
    if (!packageInfo) return 0;
    const basePrice = packageInfo.pricing[formData.pricingType];
    return basePrice * formData.travelers;
  };

  const calculateCommission = () => {
    const total = calculateTotal();
    return (total * (packageInfo?.commission || 0)) / 100;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors mr-4"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Create New Booking</h1>
                <p className="text-sm text-gray-600">Fill in the details to create a new booking</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Booking Information</h2>
            <p className="text-sm text-gray-600 mt-1">Enter customer and travel details</p>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Package Information */}
            {packageInfo && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-blue-900">{packageInfo.name}</h3>
                    <p className="text-sm text-blue-700">
                      {packageInfo.duration} • {packageInfo.destination}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-blue-600">Commission Rate</p>
                    <p className="font-medium text-blue-900">{packageInfo.commission}%</p>
                  </div>
                </div>
              </div>
            )}

            {/* Customer Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <User className="w-5 h-5 mr-2 text-gray-600" />
                Customer Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name *
                  </label>
                  <input
                    id="customerName"
                    type="text"
                    value={formData.customerName}
                    onChange={(e) => handleInputChange('customerName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.customerName ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter customer's full name"
                  />
                  {errors.customerName && (
                    <p className="mt-1 text-sm text-red-600">{errors.customerName}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      id="customerEmail"
                      type="email"
                      value={formData.customerEmail}
                      onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                      className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.customerEmail ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {errors.customerEmail && (
                    <p className="mt-1 text-sm text-red-600">{errors.customerEmail}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="customerPhone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number *
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      id="customerPhone"
                      type="tel"
                      value={formData.customerPhone}
                      onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                      className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.customerPhone ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="+1234567890"
                    />
                  </div>
                  {errors.customerPhone && (
                    <p className="mt-1 text-sm text-red-600">{errors.customerPhone}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="customerCountry" className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <select
                      id="customerCountry"
                      value={formData.customerCountry}
                      onChange={(e) => handleInputChange('customerCountry', e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select a country</option>
                      {countries.map((country) => (
                        <option key={country} value={country}>
                          {country}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Travel Details */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-gray-600" />
                Travel Details
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="pricingType" className="block text-sm font-medium text-gray-700 mb-1">
                    Pricing Type
                  </label>
                  <select
                    id="pricingType"
                    value={formData.pricingType}
                    onChange={(e) => handleInputChange('pricingType', e.target.value as 'solo' | 'honeymoon' | 'family' | 'group')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="solo">Solo Traveler</option>
                    <option value="honeymoon">Honeymoon (2 people)</option>
                    <option value="family">Family (4 people)</option>
                    <option value="group">Group (6+ people)</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="travelers" className="block text-sm font-medium text-gray-700 mb-1">
                    Number of Travelers *
                  </label>
                  <div className="relative">
                    <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      id="travelers"
                      type="number"
                      min="1"
                      value={formData.travelers}
                      onChange={(e) => handleInputChange('travelers', parseInt(e.target.value))}
                      className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.travelers ? 'border-red-500' : 'border-gray-300'
                      }`}
                    />
                  </div>
                  {errors.travelers && (
                    <p className="mt-1 text-sm text-red-600">{errors.travelers}</p>
                  )}
                </div>
              </div>

              <div>
                <label htmlFor="travelDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Travel Date *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    id="travelDate"
                    type="date"
                    value={formData.travelDate}
                    onChange={(e) => handleInputChange('travelDate', e.target.value)}
                    className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.travelDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
                {errors.travelDate && (
                  <p className="mt-1 text-sm text-red-600">{errors.travelDate}</p>
                )}
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <AlertCircle className="w-5 h-5 mr-2 text-gray-600" />
                Emergency Contact
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-1">
                    Emergency Contact Name *
                  </label>
                  <input
                    id="emergencyContact"
                    type="text"
                    value={formData.emergencyContact}
                    onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.emergencyContact ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Emergency contact name"
                  />
                  {errors.emergencyContact && (
                    <p className="mt-1 text-sm text-red-600">{errors.emergencyContact}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="emergencyPhone" className="block text-sm font-medium text-gray-700 mb-1">
                    Emergency Phone *
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      id="emergencyPhone"
                      type="tel"
                      value={formData.emergencyPhone}
                      onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                      className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                        errors.emergencyPhone ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="+1234567890"
                    />
                  </div>
                  {errors.emergencyPhone && (
                    <p className="mt-1 text-sm text-red-600">{errors.emergencyPhone}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
              
              <div>
                <label htmlFor="dietaryRequirements" className="block text-sm font-medium text-gray-700 mb-1">
                  Dietary Requirements
                </label>
                <textarea
                  id="dietaryRequirements"
                  value={formData.dietaryRequirements}
                  onChange={(e) => handleInputChange('dietaryRequirements', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Any dietary restrictions or preferences"
                />
              </div>

              <div>
                <label htmlFor="medicalConditions" className="block text-sm font-medium text-gray-700 mb-1">
                  Medical Conditions
                </label>
                <textarea
                  id="medicalConditions"
                  value={formData.medicalConditions}
                  onChange={(e) => handleInputChange('medicalConditions', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Any medical conditions we should be aware of"
                />
              </div>

              <div>
                <label htmlFor="specialRequests" className="block text-sm font-medium text-gray-700 mb-1">
                  Special Requests
                </label>
                <textarea
                  id="specialRequests"
                  value={formData.specialRequests}
                  onChange={(e) => handleInputChange('specialRequests', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Any special requests or additional information"
                />
              </div>
            </div>

            {/* Pricing Summary */}
            {packageInfo && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                  <Package className="w-5 h-5 mr-2 text-gray-600" />
                  Pricing Summary
                </h3>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Base Price ({formData.pricingType}):</span>
                    <span>{formatCurrency(packageInfo.pricing[formData.pricingType])}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Number of Travelers:</span>
                    <span>{formData.travelers}</span>
                  </div>
                  <div className="border-t border-gray-200 pt-2 flex justify-between font-medium">
                    <span>Total Amount:</span>
                    <span>{formatCurrency(calculateTotal())}</span>
                  </div>
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Your Commission ({packageInfo.commission || 0}%):</span>
                    <span>{formatCurrency(calculateCommission())}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200">
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Booking...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Create Booking
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default function CreateBookingPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreateBookingContent />
    </Suspense>
  );
}
