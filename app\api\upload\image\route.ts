import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// Allowed image types and size limits
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// POST - Upload image to Supabase Storage
export async function POST(request: NextRequest) {
  try {
    console.log('Upload API called');
    console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Missing');
    console.log('Supabase Anon Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing');
    console.log('Supabase Service Role Key:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing');

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const altText = formData.get('altText') as string;
    const bucket = formData.get('bucket') as string || 'sas-package-images';

    console.log('Upload details:', {
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      altText,
      bucket
    });

    // Validate file
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate alt text (mandatory)
    if (!altText || altText.trim() === '') {
      return NextResponse.json(
        { success: false, error: 'Alt text is required for all images' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Use original filename with timestamp to avoid conflicts
    let fileName = file.name;

    // Sanitize filename to ensure it's safe for storage
    fileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');

    // Add timestamp to prevent conflicts while preserving original name structure
    const fileExtension = fileName.split('.').pop();
    const baseName = fileName.substring(0, fileName.lastIndexOf('.'));
    const timestamp = Date.now();
    fileName = `${baseName}_${timestamp}.${fileExtension}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);

    // Determine which client to use based on bucket
    // Use service role client for admin buckets to bypass RLS
    const adminBuckets = ['sas-blog-images', 'sas-package-images', 'sas-general-uploads', 'sas-mini-package-images', 'sas-mini-package-content'];
    const useServiceRole = adminBuckets.includes(bucket);

    let storageClient;
    if (useServiceRole) {
      // Create service role client for admin operations (bypasses RLS)
      const supabaseAdmin = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );
      storageClient = supabaseAdmin.storage;
    } else {
      storageClient = supabase.storage;
    }

    // Upload to Supabase Storage
    const { error: uploadError } = await storageClient
      .from(bucket)
      .upload(fileName, buffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      });

    if (uploadError) {
      console.error('Supabase storage error:', uploadError);
      console.error('Upload details:', {
        bucket,
        fileName,
        fileType: file.type,
        fileSize: file.size,
        altText
      });
      return NextResponse.json(
        {
          success: false,
          error: `Failed to upload image to storage: ${uploadError.message}`,
          details: uploadError
        },
        { status: 500 }
      );
    }

    // Get public URL using the same client
    const { data: urlData } = storageClient
      .from(bucket)
      .getPublicUrl(fileName);

    if (!urlData?.publicUrl) {
      return NextResponse.json(
        { success: false, error: 'Failed to get public URL for uploaded image' },
        { status: 500 }
      );
    }

    // Debug: Log the URL being returned
    console.log('Upload API - Returning URL:', urlData.publicUrl);

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        fileName: fileName,
        originalName: file.name,
        url: urlData.publicUrl,
        altText: altText.trim(),
        size: file.size,
        type: file.type,
        bucket: bucket
      },
      message: 'Image uploaded successfully'
    });

  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to upload image' },
      { status: 500 }
    );
  }
}

// GET - List uploaded images (for admin management)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const bucket = searchParams.get('bucket') || 'sas-package-images';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Determine which client to use based on bucket
    const adminBuckets = ['sas-blog-images', 'sas-package-images', 'sas-general-uploads', 'sas-mini-package-images', 'sas-mini-package-content'];
    const useServiceRole = adminBuckets.includes(bucket);

    let storageClient;
    if (useServiceRole) {
      // Create service role client for admin operations (bypasses RLS)
      const supabaseAdmin = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );
      storageClient = supabaseAdmin.storage;
    } else {
      storageClient = supabase.storage;
    }

    // List files in bucket
    const { data: files, error } = await storageClient
      .from(bucket)
      .list('', {
        limit: limit,
        offset: offset,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (error) {
      console.error('Supabase storage error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to list images from storage' },
        { status: 500 }
      );
    }

    // Transform file data and get public URLs
    const transformedFiles = files?.map(file => {
      const { data: urlData } = storageClient
        .from(bucket)
        .getPublicUrl(file.name);

      return {
        name: file.name,
        size: file.metadata?.size || 0,
        type: file.metadata?.mimetype || 'unknown',
        url: urlData?.publicUrl || '',
        createdAt: file.created_at,
        updatedAt: file.updated_at
      };
    }) || [];

    return NextResponse.json({
      success: true,
      data: transformedFiles,
      pagination: {
        limit,
        offset,
        total: files?.length || 0
      }
    });

  } catch (error) {
    console.error('Error listing images:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to list images' },
      { status: 500 }
    );
  }
}

// DELETE - Delete image from storage
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');
    const bucket = searchParams.get('bucket') || 'sas-package-images';

    if (!fileName) {
      return NextResponse.json(
        { success: false, error: 'File name is required' },
        { status: 400 }
      );
    }

    // Delete file from storage
    const { error } = await supabase.storage
      .from(bucket)
      .remove([fileName]);

    if (error) {
      console.error('Supabase storage error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete image from storage' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete image' },
      { status: 500 }
    );
  }
}
