import { NextRequest, NextResponse } from 'next/server'
import { defaultSEO, hreflangConfigs, generateHreflangTags, getPriorityRegions } from '@/lib/seo'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const sitemap = await generateHreflangSitemap()
    
    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',
      },
    })
  } catch (error) {
    console.error('Error generating hreflang sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}

async function generateHreflangSitemap(): Promise<string> {
  const baseUrl = defaultSEO.siteUrl
  const priorityRegions = getPriorityRegions()
  
  // Get all published content
  const [packagesResult, miniPackagesResult, blogPostsResult] = await Promise.allSettled([
    supabase
      .from('packages')
      .select('slug, updated_at, created_at')
      .eq('status', 'published')
      .order('updated_at', { ascending: false }),
    
    supabase
      .from('mini_packages')
      .select('slug, updated_at, created_at')
      .eq('status', 'published')
      .order('updated_at', { ascending: false }),
    
    supabase
      .from('blog_posts')
      .select('slug, updated_at, published_at')
      .eq('status', 'published')
      .order('updated_at', { ascending: false })
  ])

  // Static pages with their priorities
  const staticPages = [
    { url: '/', priority: 1.0, changefreq: 'daily' },
    { url: '/about', priority: 0.8, changefreq: 'monthly' },
    { url: '/package', priority: 0.9, changefreq: 'weekly' },
    { url: '/mini-package', priority: 0.8, changefreq: 'weekly' },
    { url: '/blog', priority: 0.8, changefreq: 'daily' },
    { url: '/contact', priority: 0.7, changefreq: 'monthly' },
    { url: '/faqs', priority: 0.6, changefreq: 'monthly' },
    { url: '/journey', priority: 0.6, changefreq: 'monthly' },
    { url: '/iconic', priority: 0.7, changefreq: 'monthly' },
    { url: '/community', priority: 0.6, changefreq: 'weekly' },
    { url: '/privacy-policy', priority: 0.3, changefreq: 'yearly' },
    { url: '/booking-policy', priority: 0.3, changefreq: 'yearly' }
  ]

  // Dynamic pages
  const dynamicPages: Array<{ url: string; lastmod: string; priority: number; changefreq: string }> = []

  // Add packages
  if (packagesResult.status === 'fulfilled' && packagesResult.value.data) {
    packagesResult.value.data.forEach(pkg => {
      dynamicPages.push({
        url: `/package/${pkg.slug}`,
        lastmod: new Date(pkg.updated_at || pkg.created_at).toISOString(),
        priority: 0.8,
        changefreq: 'weekly'
      })
    })
  }

  // Add mini packages
  if (miniPackagesResult.status === 'fulfilled' && miniPackagesResult.value.data) {
    miniPackagesResult.value.data.forEach(pkg => {
      dynamicPages.push({
        url: `/mini-package/${pkg.slug}`,
        lastmod: new Date(pkg.updated_at || pkg.created_at).toISOString(),
        priority: 0.7,
        changefreq: 'weekly'
      })
    })
  }

  // Add blog posts
  if (blogPostsResult.status === 'fulfilled' && blogPostsResult.value.data) {
    blogPostsResult.value.data.forEach(post => {
      dynamicPages.push({
        url: `/blog/${post.slug}`,
        lastmod: new Date(post.updated_at || post.published_at).toISOString(),
        priority: 0.7,
        changefreq: 'monthly'
      })
    })
  }

  // Combine all pages
  const allPages = [
    ...staticPages.map(page => ({ ...page, lastmod: new Date().toISOString() })),
    ...dynamicPages
  ]

  // Generate XML
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`

  for (const page of allPages) {
    // Generate hreflang tags for this page
    const hreflangTags = generateHreflangTags(page.url, { priorityRegionsOnly: true })
    
    xml += `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
`

    // Add hreflang links
    for (const tag of hreflangTags) {
      xml += `    <xhtml:link rel="alternate" hreflang="${tag.hrefLang}" href="${tag.href}" />
`
    }

    xml += `  </url>
`
  }

  xml += `</urlset>`

  return xml
}

// Also create a separate hreflang index for large sites
export async function generateHreflangIndex(): Promise<string> {
  const baseUrl = defaultSEO.siteUrl
  const priorityRegions = getPriorityRegions()
  
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
`

  // Create separate sitemaps for each priority region
  for (const region of priorityRegions) {
    const config = hreflangConfigs[region]
    if (config) {
      xml += `  <sitemap>
    <loc>${baseUrl}/sitemap-${region}.xml</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
  </sitemap>
`
    }
  }

  xml += `</sitemapindex>`
  
  return xml
}
