// Test newsletter with real SMTP credentials
const testRealNewsletter = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Newsletter with Real SMTP...\n');
  
  // Test 1: Add a test subscriber
  console.log('1. Adding test subscriber...');
  try {
    const subscribeResponse = await fetch(`${baseUrl}/api/newsletter/subscribe`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>' })
    });
    
    const subscribeData = await subscribeResponse.json();
    console.log('Subscribe Response:', subscribeData);
  } catch (error) {
    console.error('Subscribe Error:', error.message);
  }
  
  // Test 2: Send test email
  console.log('\n2. Sending test email...');
  try {
    const testResponse = await fetch(`${baseUrl}/api/newsletter/test`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        testEmail: '<EMAIL>',
        subject: 'Newsletter System Test - Swift Africa Safaris',
        content: `
          <div style="text-align: center; padding: 20px; font-family: Arial, sans-serif;">
            <h1 style="color: #163201;">🦁 Swift Africa Safaris Newsletter Test</h1>
            <p>This is a test email to verify that the newsletter system is working correctly.</p>
            <div style="margin: 20px 0; padding: 15px; background-color: #f0f8e8; border-left: 4px solid #317100; border-radius: 5px;">
              <p style="margin: 0; color: #317100;"><strong>✅ System Status: OPERATIONAL</strong></p>
              <p style="margin: 5px 0 0 0; font-size: 14px; color: #666;">
                Newsletter system successfully configured and ready for use!
              </p>
            </div>
            <p><strong>Features Tested:</strong></p>
            <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
              <li>✅ SMTP Configuration</li>
              <li>✅ Email Template System</li>
              <li>✅ Database Integration</li>
              <li>✅ Subscriber Management</li>
              <li>✅ Security Features</li>
            </ul>
            <p style="margin-top: 20px;">
              <strong>Next Steps:</strong><br>
              Your newsletter system is ready for production use!
            </p>
          </div>
        `
      })
    });
    
    const testData = await testResponse.json();
    console.log('Test Email Response:', testData);
    
    if (testData.success) {
      console.log('✅ Test email sent successfully!');
    } else {
      console.log('❌ Test email failed:', testData.error);
    }
    
  } catch (error) {
    console.error('❌ Test Email Error:', error.message);
  }
  
  // Test 3: Check subscriber count
  console.log('\n3. Checking subscriber count...');
  try {
    const subscribersResponse = await fetch(`${baseUrl}/api/admin/newsletter/subscribers?limit=5`);
    const subscribersData = await subscribersResponse.json();
    
    if (subscribersData.success) {
      console.log(`📊 Total Subscribers: ${subscribersData.pagination.totalSubscribers}`);
      console.log('Recent Subscribers:', subscribersData.data.map(s => s.email));
    }
  } catch (error) {
    console.error('Subscribers Error:', error.message);
  }
  
  console.log('\n🎉 Newsletter system test completed!');
  console.log('\n📧 Check <EMAIL> for the test email.');
};

// Run the test
testRealNewsletter().catch(console.error);
