import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';
import { validateRequestSize, validateBlogContentSize, getContentSizeReductionTips } from '@/lib/request-utils';

// GET - Fetch all blog posts for admin (including drafts and archived)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Build query
    let query = supabaseAdmin
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count
      `)
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (category) {
      query = query.eq('category', category);
    }

    // Get total count for pagination
    let countQuery = supabaseAdmin
      .from('sas_blog_posts')
      .select('*', { count: 'exact', head: true })
      .is('deleted_at', null);

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (search) {
      countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (category) {
      countQuery = countQuery.eq('category', category);
    }

    const { count } = await countQuery;

    // Apply pagination
    const { data: posts, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch blog posts' },
        { status: 500 }
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    const response = NextResponse.json({
      success: true,
      data: {
        posts: posts || [],
        pagination: {
          currentPage: page,
          totalPages,
          totalPosts: count || 0,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

    // Add cache-busting headers
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new blog post (admin only)
export async function POST(request: NextRequest) {
  try {
    // Validate request size
    const sizeValidation = validateRequestSize(request);
    if (!sizeValidation.isValid) {
      const tips = getContentSizeReductionTips();
      return NextResponse.json(
        {
          success: false,
          error: sizeValidation.error,
          tips: tips
        },
        { status: 413 }
      );
    }

    const body = await request.json();

    // Additional validation on the parsed content
    const contentValidation = validateBlogContentSize(body);
    if (!contentValidation.isValid) {
      const tips = getContentSizeReductionTips();
      return NextResponse.json(
        {
          success: false,
          error: contentValidation.error,
          tips: tips
        },
        { status: 413 }
      );
    }

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Validate required fields
    const requiredFields = ['title', 'description', 'hero_image_url', 'hero_image_alt', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Generate slug if not provided
    let slug = body.slug;
    if (!slug) {
      slug = body.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Check if slug already exists
    const { data: existingPost } = await supabaseAdmin
      .from('sas_blog_posts')
      .select('id')
      .eq('slug', slug)
      .is('deleted_at', null)
      .single();

    if (existingPost) {
      // Append timestamp to make slug unique
      slug = `${slug}-${Date.now()}`;
    }

    // Prepare blog post data
    const blogPostData = {
      title: body.title,
      slug,
      description: body.description,
      hero_image_url: body.hero_image_url,
      hero_image_alt: body.hero_image_alt,
      category: body.category,
      tags: body.tags || [],
      status: body.status || 'draft',
      published_at: body.status === 'published' ? new Date().toISOString() : null,
      seo_title: body.seo_title || null,
      seo_description: body.seo_description || null,
      seo_keywords: body.seo_keywords || [],
      og_title: body.og_title || null,
      og_description: body.og_description || null,
      og_image_url: body.og_image_url || null,
      canonical_url: body.canonical_url || null,
      robots_index: body.robots_index || 'index',
      robots_follow: body.robots_follow || 'follow',
      schema_data: body.schema_data || null
    };

    // Insert blog post
    const { data: newPost, error: postError } = await supabaseAdmin
      .from('sas_blog_posts')
      .insert(blogPostData)
      .select()
      .single();

    if (postError) {
      console.error('Post creation error:', postError);
      return NextResponse.json(
        { success: false, error: 'Failed to create blog post' },
        { status: 500 }
      );
    }

    // Insert content blocks if provided
    if (body.content && Array.isArray(body.content) && body.content.length > 0) {
      console.log('Content blocks received:', body.content);

      // Map editor types to database types
      const typeMapping: { [key: string]: string } = {
        'heading2': 'h2',
        'heading3': 'h3',
        'heading4': 'h4',
        'heading5': 'h5',
        'heading6': 'h6',
        'bulleted-list': 'listing',
        'numbered-list': 'listing',
        'paragraph': 'paragraph',
        'image': 'image',
        'video': 'video',
        'quote': 'quote',
        'divider': 'divider'
      };

      const contentBlocks = body.content.map((block: any, index: number) => {
        const blockType = block.type || block.block_type;
        const mappedType = typeMapping[blockType] || blockType;

        return {
          blog_post_id: newPost.id,
          block_type: mappedType,
          content: block.content,
          sort_order: block.sort_order || index
        };
      });
      console.log('Content blocks prepared for insertion:', contentBlocks);

      const { error: contentError } = await supabaseAdmin
        .from('sas_blog_content_blocks')
        .insert(contentBlocks);

      if (contentError) {
        console.error('Content blocks error:', contentError);
        console.error('Content blocks data:', contentBlocks);
        return NextResponse.json(
          { success: false, error: `Failed to insert content blocks: ${contentError.message}` },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      data: newPost,
      message: 'Blog post created successfully'
    });

  } catch (error) {
    console.error('API error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('PayloadTooLargeError') || error.message.includes('request entity too large')) {
        const tips = getContentSizeReductionTips();
        return NextResponse.json(
          {
            success: false,
            error: 'Blog content is too large. Please reduce the content size or split into multiple posts.',
            tips: tips
          },
          { status: 413 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
