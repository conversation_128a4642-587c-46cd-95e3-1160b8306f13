"use client";

import React, { useState } from 'react';
import { FaPlus, FaMinus } from 'react-icons/fa';

const PackageItinerary = ({ itinerary = [] }) => {
  const [expandedHour, setExpandedHour] = useState(1);

  // Handle empty itinerary
  if (!itinerary || itinerary.length === 0) {
    return (
      <section className="mb-12 text-justify">
        <h2 className="text-3xl font-bold mb-6 text-[var(--primary-color)]">Itineraries</h2>
        <p className="text-gray-600">No itinerary available for this package.</p>
      </section>
    );
  }

  return (
    <section className="mb-12 text-justify">
      <h2 className="text-3xl font-bold mb-6 text-[var(--primary-color)]">Itineraries</h2>

      {itinerary.map((item, index) => {
        // Handle both database structure (hour_number) and legacy structure (hour)
        const hourNumber = item.hour_number || item.hour || index + 1;
        const itemId = item.id || `hour-${hourNumber}`;

        return (
          <div key={itemId} className="mb-4 bg-[var(--btn)]">
            <div
              className={`cursor-pointer  ${expandedHour === hourNumber ? 'bg-[var(--accent)]' : 'bg-[var(--btn)]'}
                text-gray-50 font-medium p-4 flex justify-between items-center`}
              onClick={() => setExpandedHour(expandedHour === hourNumber ? null : hourNumber)}
            >
              <h3 className="text-lg ">Hour {hourNumber}: {item.title}</h3>
              <span>{expandedHour === hourNumber ? <FaMinus /> : <FaPlus />}</span>
            </div>

            {expandedHour === hourNumber && (
              <div className="p-4 border border-t-0 border-gray-200 bg-[var(--secondary-background)]">
                <p className="text-gray-700">{item.description}</p>
              </div>
            )}
          </div>
        );
      })}
    </section>
  );
};

export default PackageItinerary;
