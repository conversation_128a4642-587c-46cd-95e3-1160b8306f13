'use client';

import React, { useState, useRef } from 'react';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Loader2, 
  AlertCircle, 
  CheckCircle,
  Eye,
  Trash2
} from 'lucide-react';

interface UploadedImage {
  fileName: string;
  originalName: string;
  url: string;
  altText: string;
  size: number;
  type: string;
  bucket: string;
}

interface BlogImageUploadProps {
  onImageSelect?: (image: UploadedImage) => void;
  currentImage?: string;
  altText?: string;
  onAltTextChange?: (altText: string) => void;
  bucket?: string;
  className?: string;
  showAltTextInput?: boolean;
  showImageLibrary?: boolean;
  maxSizeMB?: number;
}

const BlogImageUpload: React.FC<BlogImageUploadProps> = ({
  onImageSelect,
  currentImage,
  altText = '',
  onAltTextChange,
  bucket = 'sas-blog-images',
  className = '',
  showAltTextInput = true,
  showImageLibrary = true,
  maxSizeMB = 10
}) => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const [showLibrary, setShowLibrary] = useState(false);
  const [libraryImages, setLibraryImages] = useState<UploadedImage[]>([]);
  const [loadingLibrary, setLoadingLibrary] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.';
    }
    
    if (file.size > maxSizeBytes) {
      return `File size too large. Maximum size is ${maxSizeMB}MB.`;
    }
    
    return null;
  };

  const handleFileUpload = async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!altText.trim() && showAltTextInput) {
      setError('Alt text is required for accessibility');
      return;
    }

    setUploading(true);
    setError(null);
    setSuccess(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('altText', altText.trim() || file.name.replace(/\.[^/.]+$/, ''));
      formData.append('bucket', bucket);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('Image uploaded successfully!');
        if (onImageSelect) {
          onImageSelect(result.data);
        }
        
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError(result.error || 'Failed to upload image');
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError('Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const loadImageLibrary = async () => {
    setLoadingLibrary(true);
    try {
      const response = await fetch(`/api/upload/image?bucket=${bucket}&limit=20`);
      const result = await response.json();
      
      if (result.success) {
        setLibraryImages(result.data);
      } else {
        setError('Failed to load image library');
      }
    } catch (err) {
      console.error('Library error:', err);
      setError('Failed to load image library');
    } finally {
      setLoadingLibrary(false);
    }
  };

  const handleLibraryToggle = () => {
    if (!showLibrary) {
      setShowLibrary(true);
      loadImageLibrary();
    } else {
      setShowLibrary(false);
    }
  };

  const handleLibraryImageSelect = (image: any) => {
    const uploadedImage: UploadedImage = {
      fileName: image.name,
      originalName: image.name,
      url: image.url,
      altText: altText || 'Selected image',
      size: image.size,
      type: image.type,
      bucket: bucket
    };
    
    if (onImageSelect) {
      onImageSelect(uploadedImage);
    }
    setShowLibrary(false);
  };

  const removeCurrentImage = () => {
    if (onImageSelect) {
      onImageSelect({
        fileName: '',
        originalName: '',
        url: '',
        altText: '',
        size: 0,
        type: '',
        bucket: bucket
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current Image Display */}
      {currentImage && (
        <div className="relative">
          <img
            src={currentImage}
            alt={altText || 'Uploaded image'}
            className="w-full max-w-md h-48 object-cover rounded-lg border border-gray-200"
          />
          <button
            onClick={removeCurrentImage}
            className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
            title="Remove image"
          >
            <X size={16} />
          </button>
        </div>
      )}

      {/* Alt Text Input */}
      {showAltTextInput && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Alt Text * <span className="text-gray-500">(Required for accessibility)</span>
          </label>
          <input
            type="text"
            value={altText}
            onChange={(e) => onAltTextChange?.(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Describe the image for screen readers..."
            required
          />
        </div>
      )}

      {/* Upload Area */}
      {!currentImage && (
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragOver 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          {uploading ? (
            <div className="flex flex-col items-center">
              <Loader2 size={48} className="animate-spin text-blue-500 mb-4" />
              <p className="text-gray-600">Uploading image...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <Upload size={48} className="mx-auto text-gray-400" />
              <div>
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Upload an image
                </p>
                <p className="text-gray-600 mb-4">
                  Drag and drop an image here, or click to select
                </p>
                <div className="flex justify-center gap-3">
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <Upload size={16} className="mr-2" />
                    Choose File
                  </button>
                  
                  {showImageLibrary && (
                    <button
                      onClick={handleLibraryToggle}
                      className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <ImageIcon size={16} className="mr-2" />
                      Image Library
                    </button>
                  )}
                </div>
                <p className="text-sm text-gray-500 mt-3">
                  Supports: JPEG, PNG, WebP, GIF (max {maxSizeMB}MB)
                </p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Error Message */}
      {error && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
          <AlertCircle size={16} />
          <span className="text-sm">{error}</span>
          <button 
            onClick={() => setError(null)}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            <X size={16} />
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <CheckCircle size={16} />
          <span className="text-sm">{success}</span>
        </div>
      )}

      {/* Image Library Modal */}
      {showLibrary && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-4xl max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Image Library</h3>
              <button
                onClick={() => setShowLibrary(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={24} />
              </button>
            </div>
            
            <div className="p-4 overflow-y-auto max-h-96">
              {loadingLibrary ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 size={24} className="animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading images...</span>
                </div>
              ) : libraryImages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <ImageIcon size={48} className="mx-auto mb-4 text-gray-300" />
                  <p>No images found in library</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {libraryImages.map((image, index) => (
                    <div
                      key={index}
                      className="relative group cursor-pointer border border-gray-200 rounded-lg overflow-hidden hover:border-blue-500 transition-colors"
                      onClick={() => handleLibraryImageSelect(image)}
                    >
                      <img
                        src={image.url}
                        alt={`Library image ${index + 1}`}
                        className="w-full h-32 object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
                        <Eye size={24} className="text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-2 text-xs">
                        <p className="truncate">{image.name}</p>
                        <p className="text-gray-300">
                          {(image.size / 1024 / 1024).toFixed(1)}MB
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="flex justify-end gap-2 p-4 border-t border-gray-200">
              <button
                onClick={() => setShowLibrary(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogImageUpload;
