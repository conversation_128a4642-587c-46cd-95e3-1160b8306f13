-- Setup Package Management Database Schema for Swift Africa Safaris
-- This script creates the necessary tables and policies for package management system
-- All tables use 'sas_' prefix as required

-- =====================================================
-- PACKAGES TABLE - Main package information
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_packages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    difficulty TEXT NOT NULL, -- Accepts custom values (previously: Easy, Moderate, Hard, Challenging)
    category TEXT NOT NULL, -- Accepts custom values (previously: Wildlife, Adventure, Cultural, Beach, Safari, Nature, Luxury)
    location TEXT NOT NULL,
    duration TEXT,
    
    -- Pricing structure for different traveler types
    pricing_solo DECIMAL(10,2) NOT NULL,
    pricing_honeymoon DECIMAL(10,2) NOT NULL,
    pricing_family DECIMAL(10,2) NOT NULL,
    pricing_group DECIMAL(10,2) NOT NULL,
    
    -- Package status and visibility
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'inactive', 'archived')),

    
    -- Main package image
    image_url TEXT,
    image_alt TEXT NOT NULL DEFAULT '',
    hero_image_url TEXT,
    hero_image_alt TEXT DEFAULT '',
    
    -- SEO and metadata fields (user-inputted)
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[], -- Array of keywords
    og_title TEXT,
    og_description TEXT,
    og_image_url TEXT,
    canonical_url TEXT,
    robots_index TEXT DEFAULT 'index' CHECK (robots_index IN ('index', 'noindex')),
    robots_follow TEXT DEFAULT 'follow' CHECK (robots_follow IN ('follow', 'nofollow')),
    
    -- Schema.org structured data (JSON)
    schema_data JSONB,
    
    -- Package highlights
    highlights TEXT[], -- Array of highlight strings
    
    -- What to pack list
    packing_list TEXT[], -- Array of packing items
    
    -- Includes and excludes
    includes TEXT[], -- Array of included items
    excludes TEXT[], -- Array of excluded items
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- PACKAGE CONTENT BLOCKS - Block-based content editor
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_content_blocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    block_type TEXT NOT NULL CHECK (block_type IN (
        'paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6',
        'list', 'quote', 'code', 'image', 'divider', 'bulleted-list', 'numbered-list'
    )),
    content TEXT, -- For text-based blocks
    content_data JSONB, -- For complex blocks (images, lists, etc.)
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    -- Image-specific fields (when block_type = 'image')
    image_url TEXT,
    image_alt TEXT, -- MANDATORY for all images
    image_caption TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PACKAGE ITINERARY - Day-by-day itinerary
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_itinerary (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    day_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    activities TEXT[], -- Array of activities for the day
    accommodation TEXT,
    meals TEXT[], -- Array of meals included
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BOOKINGS TABLE - Customer booking information
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_bookings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    booking_reference TEXT UNIQUE NOT NULL,
    
    -- Package information
    package_id UUID REFERENCES public.sas_packages(id),
    package_title TEXT NOT NULL,
    package_type TEXT NOT NULL CHECK (package_type IN ('solo', 'honeymoon', 'family', 'group')),
    
    -- Customer information
    full_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    number_of_people INTEGER NOT NULL CHECK (number_of_people > 0),
    
    -- Travel dates
    check_in_date DATE NOT NULL,
    check_out_date DATE,
    
    -- Additional information
    message TEXT, -- Special requests or additional information
    
    -- Booking status
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    
    -- Pricing information
    total_amount DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    
    -- Email tracking
    confirmation_email_sent BOOLEAN DEFAULT false,
    admin_notification_sent BOOLEAN DEFAULT false,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PACKAGE IMAGES - Additional package gallery images
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_alt TEXT NOT NULL, -- MANDATORY alt text
    caption TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES for performance optimization
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_sas_packages_slug ON public.sas_packages(slug);
CREATE INDEX IF NOT EXISTS idx_sas_packages_status ON public.sas_packages(status);
CREATE INDEX IF NOT EXISTS idx_sas_packages_category ON public.sas_packages(category);
CREATE INDEX IF NOT EXISTS idx_sas_packages_location ON public.sas_packages(location);

CREATE INDEX IF NOT EXISTS idx_sas_packages_created_at ON public.sas_packages(created_at);

CREATE INDEX IF NOT EXISTS idx_sas_content_blocks_package_id ON public.sas_package_content_blocks(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_content_blocks_sort_order ON public.sas_package_content_blocks(package_id, sort_order);

CREATE INDEX IF NOT EXISTS idx_sas_itinerary_package_id ON public.sas_package_itinerary(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_itinerary_sort_order ON public.sas_package_itinerary(package_id, sort_order);

CREATE INDEX IF NOT EXISTS idx_sas_bookings_package_id ON public.sas_bookings(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_bookings_status ON public.sas_bookings(status);
CREATE INDEX IF NOT EXISTS idx_sas_bookings_created_at ON public.sas_bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_sas_bookings_reference ON public.sas_bookings(booking_reference);

CREATE INDEX IF NOT EXISTS idx_sas_package_images_package_id ON public.sas_package_images(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_package_images_sort_order ON public.sas_package_images(package_id, sort_order);

-- =====================================================
-- STORAGE BUCKETS for file uploads
-- =====================================================
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'sas-package-images',
  'sas-package-images',
  true,
  10485760, -- 10MB file size limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'sas-package-content',
  'sas-package-content',
  true,
  10485760, -- 10MB file size limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================
ALTER TABLE public.sas_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_itinerary ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_images ENABLE ROW LEVEL SECURITY;

-- Public read access for active packages
CREATE POLICY "Anyone can view active packages" ON public.sas_packages
    FOR SELECT USING (status = 'active');

-- Admin full access to packages
CREATE POLICY "Authenticated users can manage packages" ON public.sas_packages
    FOR ALL USING (auth.role() = 'authenticated');

-- Content blocks follow package permissions
CREATE POLICY "Anyone can view content blocks for active packages" ON public.sas_package_content_blocks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id AND status = 'active'
        )
    );

CREATE POLICY "Authenticated users can manage content blocks" ON public.sas_package_content_blocks
    FOR ALL USING (auth.role() = 'authenticated');

-- Itinerary follows package permissions
CREATE POLICY "Anyone can view itinerary for active packages" ON public.sas_package_itinerary
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id AND status = 'active'
        )
    );

CREATE POLICY "Authenticated users can manage itinerary" ON public.sas_package_itinerary
    FOR ALL USING (auth.role() = 'authenticated');

-- Bookings - customers can create, admins can manage
CREATE POLICY "Anyone can create bookings" ON public.sas_bookings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Authenticated users can manage bookings" ON public.sas_bookings
    FOR ALL USING (auth.role() = 'authenticated');

-- Package images follow package permissions
CREATE POLICY "Anyone can view images for active packages" ON public.sas_package_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id AND status = 'active'
        )
    );

CREATE POLICY "Authenticated users can manage package images" ON public.sas_package_images
    FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_sas_packages_updated_at
    BEFORE UPDATE ON public.sas_packages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_content_blocks_updated_at
    BEFORE UPDATE ON public.sas_package_content_blocks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_itinerary_updated_at
    BEFORE UPDATE ON public.sas_package_itinerary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_bookings_updated_at
    BEFORE UPDATE ON public.sas_bookings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to generate booking reference
CREATE OR REPLACE FUNCTION generate_booking_reference()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.booking_reference IS NULL OR NEW.booking_reference = '' THEN
        NEW.booking_reference := 'SAS-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for booking reference generation
CREATE TRIGGER generate_sas_booking_reference
    BEFORE INSERT ON public.sas_bookings
    FOR EACH ROW EXECUTE FUNCTION generate_booking_reference();

-- Function to generate slug from title
CREATE OR REPLACE FUNCTION generate_slug_from_title()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.slug IS NULL OR NEW.slug = '' THEN
        NEW.slug := LOWER(REGEXP_REPLACE(REGEXP_REPLACE(NEW.title, '[^a-zA-Z0-9\s-]', '', 'g'), '\s+', '-', 'g'));
        -- Ensure uniqueness
        WHILE EXISTS (SELECT 1 FROM public.sas_packages WHERE slug = NEW.slug AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::UUID)) LOOP
            NEW.slug := NEW.slug || '-' || FLOOR(RANDOM() * 1000)::TEXT;
        END LOOP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for slug generation
CREATE TRIGGER generate_sas_package_slug
    BEFORE INSERT OR UPDATE ON public.sas_packages
    FOR EACH ROW EXECUTE FUNCTION generate_slug_from_title();

-- Function to set published_at when status changes to active
CREATE OR REPLACE FUNCTION set_published_at()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'active' AND (OLD.status IS NULL OR OLD.status != 'active') THEN
        NEW.published_at := NOW();
    ELSIF NEW.status != 'active' THEN
        NEW.published_at := NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for published_at
CREATE TRIGGER set_sas_package_published_at
    BEFORE INSERT OR UPDATE ON public.sas_packages
    FOR EACH ROW EXECUTE FUNCTION set_published_at();

-- =====================================================
-- SAMPLE DATA INSERTION (Optional - for testing)
-- =====================================================

-- Insert sample package data based on packageData.js
INSERT INTO public.sas_packages (
    title, slug, difficulty, category, location, duration,
    pricing_solo, pricing_honeymoon, pricing_family, pricing_group,
    status, image_url, image_alt, hero_image_url, hero_image_alt,
    seo_title, seo_description, seo_keywords,
    highlights, packing_list, includes, excludes
) VALUES (
    '4 Day Queen Elizabeth National Park & Lake Mutanda Safari',
    '4-day-queen-elizabeth-lake-mutanda-safari',
    'Moderate',
    'Wildlife',
    'Uganda',
    '4 Days',
    1355.00, 1353.00, 1351.00, 1348.00,
    'active',
    'https://images.unsplash.com/photo-1549366021-9f761d040a94',
    '4 Day Queen Elizabeth National Park Safari - Wildlife viewing in Uganda',
    'https://images.unsplash.com/photo-1549366021-9f761d040a94',
    '4 Day Queen Elizabeth National Park Safari - Hero image showing Uganda wildlife',
    '4 Day Queen Elizabeth & Lake Mutanda Safari - Uganda Wildlife Tour | Swift Africa Safaris',
    'Experience Uganda''s wildlife on this 4-day safari to Queen Elizabeth National Park and Lake Mutanda. Game drives, boat cruises, and cultural immersion. Starting from $1,348.',
    ARRAY['Uganda safari package', 'Queen Elizabeth National Park', 'Lake Mutanda safari', 'Uganda wildlife tour', '4 day Uganda safari'],
    ARRAY[
        'Wonderful game drives in Queen Elizabeth National Park''s wildlife rich Kasenyi Plains',
        'Boat cruise on the Kazinga Channel packed with hippos, crocodiles, and vibrant birdlife',
        'Canoe ride across the magical Lake Mutanda with views of the Virunga Volcanoes',
        'Cultural village walk and local immersion in Kisoro region',
        'Comfortable lodge stays in pristine natural settings'
    ],
    ARRAY[
        'Lightweight, breathable safari clothing in neutral colors',
        'Comfortable walking shoes and sandals',
        'Sun protection: hat, sunglasses, and sunscreen',
        'Insect repellent and personal medication',
        'Binoculars and a good camera for wildlife photography',
        'Reusable water bottle',
        'Warm layers for cool evenings and early mornings'
    ],
    ARRAY[
        'All ground transfers',
        'Accommodation',
        'All meals and drinking water during the tour',
        'Professional local guide',
        'Park Permits and activities'
    ],
    ARRAY[
        'International airfare',
        'Visa fees and travel insurance',
        'Alcoholic and non-included beverages',
        'Personal expenses',
        'Optional activities not specified'
    ]
) ON CONFLICT (slug) DO NOTHING;

-- Insert sample itinerary for the package
INSERT INTO public.sas_package_itinerary (package_id, day_number, title, description, activities, accommodation, meals, sort_order)
SELECT
    p.id,
    1,
    'Arrival at Kasese Airport and Check in',
    'Your safari begins with a warm welcome at Kasese Airport, followed by a wonderful drive at your stay near Queen Elizabeth National Park...',
    ARRAY['Airport pickup', 'Transfer to lodge', 'Welcome briefing', 'Evening relaxation'],
    'Lodge near Queen Elizabeth National Park',
    ARRAY['Dinner'],
    1
FROM public.sas_packages p
WHERE p.slug = '4-day-queen-elizabeth-lake-mutanda-safari'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_package_itinerary (package_id, day_number, title, description, activities, accommodation, meals, sort_order)
SELECT
    p.id,
    2,
    'Queen Elizabeth National Park & Kazinga Channel',
    'Rise early for a wonderful morning game drive, tracking elusive predators like leopards and hyenas as the bush awakens...',
    ARRAY['Morning game drive', 'Kazinga Channel boat cruise', 'Wildlife photography', 'Evening game drive'],
    'Lodge near Queen Elizabeth National Park',
    ARRAY['Breakfast', 'Lunch', 'Dinner'],
    2
FROM public.sas_packages p
WHERE p.slug = '4-day-queen-elizabeth-lake-mutanda-safari'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_package_itinerary (package_id, day_number, title, description, activities, accommodation, meals, sort_order)
SELECT
    p.id,
    3,
    'Journey to Lake Mutanda and Nature',
    'After breakfast, journey southwest through rolling hills, lush farmland, and misty highlands toward the tranquil beauty of Lake Mutanda...',
    ARRAY['Scenic drive to Lake Mutanda', 'Canoe ride', 'Volcano viewing', 'Nature walk'],
    'Lodge at Lake Mutanda',
    ARRAY['Breakfast', 'Lunch', 'Dinner'],
    3
FROM public.sas_packages p
WHERE p.slug = '4-day-queen-elizabeth-lake-mutanda-safari'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_package_itinerary (package_id, day_number, title, description, activities, accommodation, meals, sort_order)
SELECT
    p.id,
    4,
    'Cultural Immersion & Departure from Kisoro Airstrip',
    'Your final morning invites a deeper connection with Uganda''s cultural heritage through a guided village walk...',
    ARRAY['Village cultural walk', 'Local community interaction', 'Transfer to Kisoro Airstrip', 'Departure'],
    'N/A - Departure day',
    ARRAY['Breakfast'],
    4
FROM public.sas_packages p
WHERE p.slug = '4-day-queen-elizabeth-lake-mutanda-safari'
ON CONFLICT DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify tables were created
SELECT
    schemaname,
    tablename,
    tableowner
FROM pg_tables
WHERE tablename LIKE 'sas_%'
ORDER BY tablename;

-- Verify indexes were created
SELECT
    indexname,
    tablename,
    indexdef
FROM pg_indexes
WHERE tablename LIKE 'sas_%'
ORDER BY tablename, indexname;

-- Verify storage buckets were created
SELECT
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at
FROM storage.buckets
WHERE name LIKE 'sas-%'
ORDER BY name;

-- Verify sample data was inserted
SELECT
    id,
    title,
    slug,
    status,
    category,
    location,
    pricing_solo,
    created_at
FROM public.sas_packages
ORDER BY created_at DESC
LIMIT 5;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Package Management Database Setup Complete!';
    RAISE NOTICE '📋 Tables created: sas_packages, sas_package_content_blocks, sas_package_itinerary, sas_bookings, sas_package_images';
    RAISE NOTICE '🔒 RLS policies configured for security';
    RAISE NOTICE '📁 Storage buckets created: sas-package-images, sas-package-content';
    RAISE NOTICE '⚡ Indexes created for performance optimization';
    RAISE NOTICE '🔄 Triggers configured for auto-updates';
    RAISE NOTICE '📝 Sample data inserted for testing';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Ready for API development!';
END $$;
