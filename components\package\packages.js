"use client";
import TourCard from '@/components/cards/packageCard';
import TourFilter from '@/components/package/tourFilter';
import { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { SkeletonGrid } from '../skeleton';

const Packages = () => {
  const searchParams = useSearchParams();
  const [packagesToShow, setPackagesToShow] = useState(8);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCountries, setSelectedCountries] = useState([]);
  const [sortBy, setSortBy] = useState('');
  const [packages, setPackages] = useState([]);
  const [fetchingPackages, setFetchingPackages] = useState(true);

  // Handle URL parameters for destination filtering
  useEffect(() => {
    const destination = searchParams.get('destination');
    if (destination) {
      setSelectedCountries([destination]);
    }
  }, [searchParams]);

  // Fetch packages from API
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setFetchingPackages(true);
        const response = await fetch('/api/packages?limit=100'); // Fetch more packages
        const result = await response.json();

        if (result.success) {
          setPackages(result.data);
        } else {
          console.error('Failed to fetch packages:', result.error);
        }
      } catch (error) {
        console.error('Error fetching packages:', error);
      } finally {
        setFetchingPackages(false);
      }
    };

    fetchPackages();
  }, []);

  const filteredPackages = useMemo(() => {
    let filtered = [...packages];

    if (selectedCountries.length > 0) {
      filtered = filtered.filter(tour => selectedCountries.includes(tour.location));
    }

    if (sortBy) {
      filtered = filtered.filter(tour => tour.category === sortBy);
    }

    return filtered;
  }, [packages, selectedCountries, sortBy]);

  const handleLoadMore = () => {
    setIsLoading(true);
    setTimeout(() => {
      setPackagesToShow(prevValue => prevValue + 8);
      setIsLoading(false);
    }, 400);
  };

  if (fetchingPackages) {
    return (
      <div className="container mx-auto px-4 py-8 bg-[var(--primary-background)]">
        <SkeletonGrid
          variant="package"
          count={8}
          cardProps={{ showPrice: true, showButton: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8  bg-[var(--primary-background)]">
      <TourFilter
        selectedCountries={selectedCountries}
        setSelectedCountries={setSelectedCountries}
        sortBy={sortBy}
        setSortBy={setSortBy}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredPackages.slice(0, packagesToShow).map((tour, index) => (
          <TourCard key={tour.id || index} tour={tour} />
        ))}
      </div>

      {packagesToShow < filteredPackages.length && (
        <div className="text-center mt-8">
          {isLoading ? (
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          ) : (
            <button 
              onClick={handleLoadMore}
              className="btn text-white font-normal rounded py-2 px-6"
              data-testid="load-more-button"
            >
              Load More
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default Packages;
