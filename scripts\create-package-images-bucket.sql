-- Create package-images storage bucket
-- Run this in your Supabase SQL Editor

-- 1. Create the storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'package-images',
  'package-images',
  true,
  5242880, -- 5MB file size limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. Create policy for public read access
DROP POLICY IF EXISTS "Public read access for package-images" ON storage.objects;
CREATE POLICY "Public read access for package-images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'package-images');

-- 4. Create policy for authenticated users to upload
DROP POLICY IF EXISTS "Authenticated users can upload to package-images" ON storage.objects;
CREATE POLICY "Authenticated users can upload to package-images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'package-images' 
  AND auth.role() = 'authenticated'
);

-- 5. Create policy for users to update their uploads
DROP POLICY IF EXISTS "Users can update their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can update their uploads in package-images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'package-images'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 6. Create policy for users to delete their uploads
DROP POLICY IF EXISTS "Users can delete their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can delete their uploads in package-images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'package-images'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 7. Verify the bucket was created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'package-images';