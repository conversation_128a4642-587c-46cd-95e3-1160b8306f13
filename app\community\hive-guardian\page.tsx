import type { Metadata } from 'next';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';
import HiveGuardianClient from './HiveGuardianClient';

export const metadata: Metadata = generateMetadata({
  title: 'Eco-Hive Guardians - Rwanda Community Conservation Project',
  description: 'Join our Eco-Hive Guardians initiative empowering Rwandan communities through sustainable beekeeping, Nyungwe National Park restoration, and conservation cooperatives in Nyamasheke District.',
  keywords: [
    'Rwanda conservation project',
    'Can I visit Rwanda and support local communities?',
    'Can I visit Rwanda sustainably and affordably?',
    'Can I do a multi-country eco-safari in East Africa?',
    'Where to book eco-tourism trips that support locals in Rwanda?',
    'sustainable beekeeping Rwanda',
    'community conservation',
    'regenerative agriculture Rwanda',
    'Nyamasheke District',
    'agricultural cooperatives',
    'eco-tourism Rwanda',
    'community empowerment',
    'conservation volunteering',
    'sustainable development Rwanda',
    'environmental protection',
    'local community support',
    'honey production Rwanda',
    'buffer zone conservation',
    '<PERSON>'
  ],
  type: 'website',
  url: '/community/hive-guardian'
});

const HiveGuardian = () => {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Community Impact', url: '/community' },
    { name: 'Eco-Hive Guardians', url: '/community/hive-guardian' }
  ]);

  const hiveGuardianSchema = {
    '@context': 'https://schema.org',
    '@type': 'Project',
    name: 'Eco-Hive Guardians - Rwanda Community Conservation Project',
    description: 'Join our Eco-Hive Guardians initiative empowering Rwandan communities through sustainable beekeeping, Nyungwe National Park restoration, and conservation cooperatives in Nyamasheke District.',
    url: `${defaultSEO.siteUrl}/community/hive-guardian`,
    startDate: '2024-08-01',
    location: {
      '@type': 'Place',
      name: 'Nyamasheke District, Rwanda',
      address: {
        '@type': 'PostalAddress',
        addressLocality: 'Nyamasheke',
        addressCountry: 'RW'
      }
    },
    organizer: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      url: defaultSEO.siteUrl
    },
    mainEntity: {
      '@type': 'Organization',
      name: 'Eco-Hive Guardians Initiative',
      description: 'Community conservation project focusing on sustainable beekeeping and regenerative agriculture',
      founder: {
        '@type': 'Person',
        name: 'Daniel Ntakirutimana',
        jobTitle: 'Conservationist and Founder of Swift Africa Safaris',
        nationality: 'Rwandan',
        sameAs: 'https://www.linkedin.com/in/daniel-ntakirutimana-413808301?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app'
      },
      focus: [
        'Regenerative Agriculture Training',
        'Cooperative Development',
        'Honey Production and Export',
        'Buffer Zone Reinforcement'
      ],
      serviceArea: {
        '@type': 'Place',
        name: 'Nyamasheke District',
        containedInPlace: {
          '@type': 'Country',
          name: 'Rwanda'
        }
      },
      program: [
        {
          '@type': 'EducationalOrganization',
          name: 'Agricultural Training Program',
          description: 'Equipping local farmers with skills to restore degraded soils and improve yields'
        },
        {
          '@type': 'Organization',
          name: 'Cooperative Development Program',
          description: 'Supporting local cooperatives in governance, infrastructure, and market access'
        }
      ]
    }
  };

  return (
    <>
      <StructuredData data={[breadcrumbSchema, hiveGuardianSchema]} />
      <HiveGuardianClient />
    </>
  );
};

export default HiveGuardian;