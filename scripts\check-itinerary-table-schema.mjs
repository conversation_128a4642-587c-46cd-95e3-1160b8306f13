import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTableSchema() {
  console.log('🔍 Checking sas_mini_package_itinerary table schema...\n');
  
  try {
    // Get table schema information
    const { data, error } = await supabase
      .rpc('exec_sql', {
        sql: `
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default
          FROM information_schema.columns 
          WHERE table_name = 'sas_mini_package_itinerary' 
          AND table_schema = 'public'
          ORDER BY ordinal_position;
        `
      });
    
    if (error) {
      console.log('❌ Error getting schema info via RPC, trying direct query...');
      
      // Try a different approach - query the table structure
      const { data: sampleData, error: sampleError } = await supabase
        .from('sas_mini_package_itinerary')
        .select('*')
        .limit(1);
      
      if (sampleError) {
        console.log(`❌ Error querying table: ${sampleError.message}`);
        return false;
      }
      
      if (sampleData && sampleData.length > 0) {
        console.log('📋 Table columns (from sample data):');
        Object.keys(sampleData[0]).forEach(column => {
          console.log(`   • ${column}`);
        });
      } else {
        console.log('📋 Table exists but has no data to analyze columns');
      }
      
      // Try to insert a test record to see what columns are required
      console.log('\n🧪 Testing column requirements...');
      
      const testRecord = {
        mini_package_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        hour_number: 1,
        title: 'Test',
        description: 'Test description',
        sort_order: 0
      };
      
      const { error: insertError } = await supabase
        .from('sas_mini_package_itinerary')
        .insert(testRecord);
      
      if (insertError) {
        console.log(`❌ Insert test failed: ${insertError.message}`);
        
        // Try with day_number as well
        const testRecordWithDay = {
          ...testRecord,
          day_number: 1
        };
        
        const { error: insertError2 } = await supabase
          .from('sas_mini_package_itinerary')
          .insert(testRecordWithDay);
        
        if (insertError2) {
          console.log(`❌ Insert with day_number also failed: ${insertError2.message}`);
        } else {
          console.log('✅ Insert succeeded when day_number was included');
          
          // Clean up test record
          await supabase
            .from('sas_mini_package_itinerary')
            .delete()
            .eq('mini_package_id', '00000000-0000-0000-0000-000000000000');
          
          console.log('🧹 Cleaned up test record');
        }
      } else {
        console.log('✅ Insert succeeded with only hour_number');
        
        // Clean up test record
        await supabase
          .from('sas_mini_package_itinerary')
          .delete()
          .eq('mini_package_id', '00000000-0000-0000-0000-000000000000');
        
        console.log('🧹 Cleaned up test record');
      }
      
      return false;
    }
    
    console.log('📋 Table Schema:');
    data.forEach(column => {
      const nullable = column.is_nullable === 'YES' ? 'NULL' : 'NOT NULL';
      const defaultVal = column.column_default ? ` DEFAULT ${column.column_default}` : '';
      console.log(`   • ${column.column_name}: ${column.data_type} ${nullable}${defaultVal}`);
    });
    
    return true;
    
  } catch (err) {
    console.log(`❌ Unexpected error: ${err.message}`);
    return false;
  }
}

async function suggestFix() {
  console.log('\n🔧 Suggested Fix:\n');
  
  console.log('The issue is that the table still has a day_number column with NOT NULL constraint.');
  console.log('We need to either:');
  console.log('');
  console.log('Option 1: Remove the day_number column completely');
  console.log('   ALTER TABLE sas_mini_package_itinerary DROP COLUMN IF EXISTS day_number;');
  console.log('');
  console.log('Option 2: Make day_number nullable and set a default');
  console.log('   ALTER TABLE sas_mini_package_itinerary ALTER COLUMN day_number DROP NOT NULL;');
  console.log('   ALTER TABLE sas_mini_package_itinerary ALTER COLUMN day_number SET DEFAULT 1;');
  console.log('');
  console.log('Option 3: Update the API to provide both hour_number and day_number');
  console.log('   (Set day_number = 1 for all mini package itineraries since they are single-day)');
  console.log('');
  console.log('Recommended: Option 1 (remove day_number) since mini packages are hour-based only.');
}

async function main() {
  console.log('🚀 Mini Package Itinerary Table Schema Check\n');
  
  const success = await checkTableSchema();
  
  if (!success) {
    await suggestFix();
  }
}

main();
