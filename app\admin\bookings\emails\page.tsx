'use client';

import React, { useState, useEffect } from 'react';
import { Search, Mail, Plus, Edit, Trash2, Send, Check, X } from 'lucide-react';

interface NotificationEmail {
  id: string;
  email_address: string;
  notification_types: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function EmailNotificationsPage() {
  const [emails, setEmails] = useState<NotificationEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingEmail, setEditingEmail] = useState<NotificationEmail | null>(null);
  const [testEmailAddress, setTestEmailAddress] = useState('');
  const [sendingTest, setSendingTest] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    emailAddress: '',
    notificationTypes: ['all'] as string[],
    isActive: true
  });

  const notificationTypeOptions = [
    { value: 'all', label: 'All Notifications' },
    { value: 'tour', label: 'Tour Bookings' },
    { value: 'apartment', label: 'Apartment Bookings' },
    { value: 'car', label: 'Car Bookings' },
    { value: 'volunteering', label: 'Volunteering Applications' },
    { value: 'contact', label: 'Contact Submissions' }
  ];

  useEffect(() => {
    fetchEmails();
  }, []);

  const fetchEmails = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/emails');
      if (!response.ok) {
        throw new Error('Failed to fetch notification emails');
      }

      const result = await response.json();
      if (result.success) {
        setEmails(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch notification emails');
      }
    } catch (error) {
      console.error('Error fetching notification emails:', error);
      setError('Failed to fetch notification emails');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const url = editingEmail ? '/api/admin/emails' : '/api/admin/emails';
      const method = editingEmail ? 'PATCH' : 'POST';
      const body = editingEmail 
        ? { 
            id: editingEmail.id, 
            emailAddress: formData.emailAddress,
            notificationTypes: formData.notificationTypes,
            isActive: formData.isActive
          }
        : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new Error('Failed to save email');
      }

      const result = await response.json();
      if (result.success) {
        await fetchEmails();
        resetForm();
      } else {
        throw new Error(result.error || 'Failed to save email');
      }
    } catch (error) {
      console.error('Error saving email:', error);
      setError('Failed to save email');
    }
  };

  const handleDelete = async (emailId: string) => {
    if (!confirm('Are you sure you want to delete this email?')) return;

    try {
      const response = await fetch(`/api/admin/emails?id=${emailId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete email');
      }

      const result = await response.json();
      if (result.success) {
        await fetchEmails();
      } else {
        throw new Error(result.error || 'Failed to delete email');
      }
    } catch (error) {
      console.error('Error deleting email:', error);
      setError('Failed to delete email');
    }
  };

  const handleToggleActive = async (email: NotificationEmail) => {
    try {
      const response = await fetch('/api/admin/emails', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: email.id,
          isActive: !email.is_active,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update email status');
      }

      const result = await response.json();
      if (result.success) {
        await fetchEmails();
      } else {
        throw new Error(result.error || 'Failed to update email status');
      }
    } catch (error) {
      console.error('Error updating email status:', error);
      setError('Failed to update email status');
    }
  };

  const handleSendTestEmail = async () => {
    if (!testEmailAddress) return;

    try {
      setSendingTest(true);
      setTestResult(null);

      const response = await fetch('/api/admin/emails/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailAddress: testEmailAddress,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send test email');
      }

      const result = await response.json();
      if (result.success) {
        setTestResult('Test email sent successfully!');
        setTestEmailAddress('');
      } else {
        throw new Error(result.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      setTestResult('Failed to send test email');
    } finally {
      setSendingTest(false);
    }
  };

  const resetForm = () => {
    setFormData({
      emailAddress: '',
      notificationTypes: ['all'],
      isActive: true
    });
    setShowAddForm(false);
    setEditingEmail(null);
  };

  const startEdit = (email: NotificationEmail) => {
    setFormData({
      emailAddress: email.email_address,
      notificationTypes: email.notification_types,
      isActive: email.is_active
    });
    setEditingEmail(email);
    setShowAddForm(true);
  };

  const handleNotificationTypeChange = (type: string, checked: boolean) => {
    if (type === 'all') {
      setFormData(prev => ({
        ...prev,
        notificationTypes: checked ? ['all'] : []
      }));
    } else {
      setFormData(prev => {
        let newTypes = [...prev.notificationTypes];
        
        if (checked) {
          // Remove 'all' if adding specific type
          newTypes = newTypes.filter(t => t !== 'all');
          newTypes.push(type);
        } else {
          newTypes = newTypes.filter(t => t !== type);
        }
        
        // If no types selected, default to 'all'
        if (newTypes.length === 0) {
          newTypes = ['all'];
        }
        
        return { ...prev, notificationTypes: newTypes };
      });
    }
  };

  const filteredEmails = emails.filter(email =>
    email.email_address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 ml-16">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold text-gray-900">Email Notifications</h1>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            <Plus className="w-4 h-4" />
            Add Email
          </button>
        </div>

        {/* Header Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Total Emails</div>
            <div className="text-2xl font-bold text-gray-900">{emails.length}</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Active</div>
            <div className="text-2xl font-bold text-green-600">
              {emails.filter(e => e.is_active).length}
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm font-medium text-gray-500">Inactive</div>
            <div className="text-2xl font-bold text-red-600">
              {emails.filter(e => !e.is_active).length}
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
            {error}
          </div>
        )}

        {/* Test Email Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Test Email Configuration</h3>
          <div className="flex gap-2">
            <input
              type="email"
              placeholder="Enter email address to test..."
              value={testEmailAddress}
              onChange={(e) => setTestEmailAddress(e.target.value)}
              className="flex-1 px-3 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleSendTestEmail}
              disabled={!testEmailAddress || sendingTest}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <Send className="w-4 h-4" />
              {sendingTest ? 'Sending...' : 'Send Test'}
            </button>
          </div>
          {testResult && (
            <div className={`mt-2 text-sm ${testResult.includes('success') ? 'text-green-600' : 'text-red-600'}`}>
              {testResult}
            </div>
          )}
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search email addresses..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {editingEmail ? 'Edit Email' : 'Add New Email'}
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <input
                type="email"
                required
                value={formData.emailAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, emailAddress: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notification Types
              </label>
              <div className="space-y-2">
                {notificationTypeOptions.map(option => (
                  <label key={option.value} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.notificationTypes.includes(option.value)}
                      onChange={(e) => handleNotificationTypeChange(option.value, e.target.checked)}
                      className="mr-2"
                    />
                    {option.label}
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="mr-2"
                />
                Active
              </label>
            </div>

            <div className="flex gap-2">
              <button
                type="submit"
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                {editingEmail ? 'Update' : 'Add'} Email
              </button>
              <button
                type="button"
                onClick={resetForm}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Emails List */}
      <div className="bg-white rounded-lg shadow">
        {filteredEmails.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <p>No notification emails found.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notification Types
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEmails.map((email) => (
                  <tr key={email.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {email.email_address}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex flex-wrap gap-1">
                        {email.notification_types.map(type => (
                          <span
                            key={type}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {notificationTypeOptions.find(opt => opt.value === type)?.label || type}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleActive(email)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          email.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {email.is_active ? (
                          <>
                            <Check className="w-3 h-3 mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <X className="w-3 h-3 mr-1" />
                            Inactive
                          </>
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(email.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => startEdit(email)}
                          className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                        >
                          <Edit className="w-4 h-4" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(email.id)}
                          className="text-red-600 hover:text-red-900 flex items-center gap-1"
                        >
                          <Trash2 className="w-4 h-4" />
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>


    </div>
  );
}
