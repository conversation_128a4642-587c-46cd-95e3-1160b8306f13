# Blog CRUD System Database Schema Design

## Overview
This document outlines the comprehensive database schema for the blog CRUD system with comments, designed based on the existing blogData.js structure and admin form requirements.

## Database Tables

### 1. sas_blog_posts
Main table for blog posts following the existing 'sas_' prefix pattern.

```sql
CREATE TABLE sas_blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT NOT NULL,
  hero_image_url TEXT NOT NULL,
  hero_image_alt TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('published', 'draft', 'archived')),
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- SEO Fields (from existing admin form)
  seo_title TEXT,
  seo_description TEXT,
  seo_keywords TEXT[] DEFAULT '{}',
  og_title TEXT,
  og_description TEXT,
  og_image_url TEXT,
  canonical_url TEXT,
  robots_index TEXT DEFAULT 'index' CHECK (robots_index IN ('index', 'noindex')),
  robots_follow TEXT DEFAULT 'follow' CHECK (robots_follow IN ('follow', 'nofollow')),
  
  -- Schema.org structured data (JSON field for flexibility)
  schema_data JSONB,
  
  -- View count for analytics
  view_count INTEGER DEFAULT 0,
  
  -- Soft delete
  deleted_at TIMESTAMPTZ
);

-- Indexes for performance
CREATE INDEX idx_sas_blog_posts_slug ON sas_blog_posts(slug);
CREATE INDEX idx_sas_blog_posts_status ON sas_blog_posts(status);
CREATE INDEX idx_sas_blog_posts_published_at ON sas_blog_posts(published_at DESC);
CREATE INDEX idx_sas_blog_posts_category ON sas_blog_posts(category);
CREATE INDEX idx_sas_blog_posts_tags ON sas_blog_posts USING GIN(tags);
CREATE INDEX idx_sas_blog_posts_deleted_at ON sas_blog_posts(deleted_at);

-- Full-text search index
CREATE INDEX idx_sas_blog_posts_search ON sas_blog_posts USING GIN(
  to_tsvector('english', title || ' ' || description)
);
```

### 2. sas_blog_content_blocks
Content blocks for blog posts (similar to package content blocks system).

```sql
CREATE TABLE sas_blog_content_blocks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID NOT NULL REFERENCES sas_blog_posts(id) ON DELETE CASCADE,
  block_type TEXT NOT NULL CHECK (block_type IN (
    'paragraph', 'h2', 'h3', 'h4', 'h5', 'h6', 
    'image', 'video', 'listing', 'quote', 'divider'
  )),
  content JSONB NOT NULL,
  sort_order INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_sas_blog_content_blocks_blog_post_id ON sas_blog_content_blocks(blog_post_id);
CREATE INDEX idx_sas_blog_content_blocks_sort_order ON sas_blog_content_blocks(blog_post_id, sort_order);
```

### 3. sas_blog_comments
Comments system for blog posts.

```sql
CREATE TABLE sas_blog_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID NOT NULL REFERENCES sas_blog_posts(id) ON DELETE CASCADE,
  parent_comment_id UUID REFERENCES sas_blog_comments(id) ON DELETE CASCADE,
  author_name TEXT NOT NULL,
  author_email TEXT NOT NULL,
  content TEXT NOT NULL,
  is_admin_reply BOOLEAN DEFAULT FALSE,
  status TEXT NOT NULL DEFAULT 'approved' CHECK (status IN ('approved', 'pending', 'rejected')),
  likes_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Soft delete
  deleted_at TIMESTAMPTZ
);

-- Indexes
CREATE INDEX idx_sas_blog_comments_blog_post_id ON sas_blog_comments(blog_post_id);
CREATE INDEX idx_sas_blog_comments_parent_comment_id ON sas_blog_comments(parent_comment_id);
CREATE INDEX idx_sas_blog_comments_status ON sas_blog_comments(status);
CREATE INDEX idx_sas_blog_comments_created_at ON sas_blog_comments(created_at DESC);
CREATE INDEX idx_sas_blog_comments_deleted_at ON sas_blog_comments(deleted_at);
```

## Content Block Structure Examples

Based on the existing blogData.js structure, content blocks will store different types of content:

### Paragraph Block
```json
{
  "content": "Your paragraph text here"
}
```

### Heading Blocks (h2, h3, h4, h5, h6)
```json
{
  "content": "Heading text",
  "id": "optional-anchor-id"
}
```

### Image Block
```json
{
  "src": "image-url",
  "alt": "alt text",
  "caption": "image caption",
  "width": "full|lg|md|sm"
}
```

### Video Block
```json
{
  "src": "video-url",
  "poster": "poster-image-url",
  "caption": "video caption",
  "width": "full|lg|md|sm"
}
```

### Listing Block
```json
{
  "items": ["item 1", "item 2", "item 3"],
  "listType": "ordered|unordered"
}
```

### Quote Block
```json
{
  "content": "quote text",
  "author": "quote author",
  "source": "quote source"
}
```

### Divider Block
```json
{
  "style": "line|dots|custom"
}
```

## Row Level Security (RLS) Policies

### Blog Posts Policies
```sql
-- Enable RLS
ALTER TABLE sas_blog_posts ENABLE ROW LEVEL SECURITY;

-- Public can read published posts
CREATE POLICY "Public can read published blog posts" ON sas_blog_posts
  FOR SELECT USING (status = 'published' AND deleted_at IS NULL);

-- Authenticated users can read all posts (for admin)
CREATE POLICY "Authenticated users can read all blog posts" ON sas_blog_posts
  FOR SELECT TO authenticated USING (true);

-- Only authenticated users can insert/update/delete
CREATE POLICY "Authenticated users can manage blog posts" ON sas_blog_posts
  FOR ALL TO authenticated USING (true);
```

### Content Blocks Policies
```sql
-- Enable RLS
ALTER TABLE sas_blog_content_blocks ENABLE ROW LEVEL SECURITY;

-- Public can read content blocks for published posts
CREATE POLICY "Public can read content blocks for published posts" ON sas_blog_content_blocks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

-- Authenticated users can manage all content blocks
CREATE POLICY "Authenticated users can manage content blocks" ON sas_blog_content_blocks
  FOR ALL TO authenticated USING (true);
```

### Comments Policies
```sql
-- Enable RLS
ALTER TABLE sas_blog_comments ENABLE ROW LEVEL SECURITY;

-- Public can read approved comments for published posts
CREATE POLICY "Public can read approved comments" ON sas_blog_comments
  FOR SELECT USING (
    status = 'approved' 
    AND deleted_at IS NULL
    AND EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

-- Public can insert comments (for new comments)
CREATE POLICY "Public can insert comments" ON sas_blog_comments
  FOR INSERT WITH CHECK (
    NOT is_admin_reply 
    AND EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

-- Authenticated users can manage all comments
CREATE POLICY "Authenticated users can manage comments" ON sas_blog_comments
  FOR ALL TO authenticated USING (true);
```

## Storage Configuration

### Blog Images Bucket
The `sas-blog-images` bucket should be configured with:
- Public access for reading
- Authenticated access for uploading
- File size limit: 10MB
- Allowed MIME types: image/jpeg, image/png, image/webp, image/gif

## Migration Considerations

1. **Data Migration**: Existing blog data from `blogData.js` needs to be migrated to the database
2. **SEO Data**: All existing SEO metadata should be preserved
3. **Content Blocks**: The sections array should be converted to content blocks
4. **Slugs**: Existing slugs must be maintained for SEO purposes
5. **Images**: External images should be downloaded and stored in Supabase storage

## Next Steps

1. Create the database tables using the SQL above
2. Set up storage bucket policies
3. Create migration script for existing data
4. Implement API routes for CRUD operations
5. Update frontend components to use database data
