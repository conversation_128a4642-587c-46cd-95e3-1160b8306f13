#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testLogin() {
  console.log('🔐 Testing Admin Login Functionality...\n');

  try {
    // Test 1: Check if users table exists
    console.log('1. Checking users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, name, email, role, status')
      .limit(1);

    if (usersError) {
      console.error('❌ Users table error:', usersError.message);
      return;
    }

    console.log('✅ Users table accessible');
    console.log(`   Found ${users?.length || 0} users\n`);

    // Test 2: Check for admin users
    console.log('2. Checking for admin users...');
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, name, email, role, status')
      .eq('role', 'admin')
      .eq('status', 'active');

    if (adminError) {
      console.error('❌ Admin users query error:', adminError.message);
      return;
    }

    if (adminUsers && adminUsers.length > 0) {
      console.log('✅ Admin users found:');
      adminUsers.forEach(user => {
        console.log(`   - ${user.name} (${user.email}) - ${user.status}`);
      });
    } else {
      console.log('⚠️  No active admin users found');
      console.log('   You may need to create an admin user first');
    }
    console.log('');

    // Test 3: Test authentication endpoint
    console.log('3. Testing authentication endpoint...');
    const { data: authData, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.log('ℹ️  No current user session (expected for login page)');
    } else if (authData.user) {
      console.log('ℹ️  Current user session found:', authData.user.email);
    }
    console.log('✅ Authentication endpoint working\n');

    // Test 4: Check RLS policies
    console.log('4. Checking Row Level Security policies...');
    const { data: policies, error: policiesError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (policiesError && policiesError.message.includes('permission denied')) {
      console.log('✅ RLS policies are active (permission denied expected)');
    } else if (policiesError) {
      console.log('⚠️  RLS check error:', policiesError.message);
    } else {
      console.log('ℹ️  RLS policies may not be fully configured');
    }
    console.log('');

    console.log('🎉 Login functionality test completed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Ensure you have created an admin user using the setup script');
    console.log('   2. Visit /admin/login to test the login page');
    console.log('   3. Use the admin credentials to log in');
    console.log('   4. Check that you are redirected to /admin/user-management');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your Supabase environment variables');
    console.log('   2. Ensure your Supabase project is active');
    console.log('   3. Verify the database schema is set up correctly');
    console.log('   4. Check network connectivity to Supabase');
  }
}

// Run the test
testLogin(); 