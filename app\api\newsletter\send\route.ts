import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import nodemailer from 'nodemailer';
import { getNewsletterSettings } from '@/lib/newsletter-settings';
import { validateNewsletterSubject, validateNewsletterContent } from '@/lib/newsletter-validation';

// POST - Send newsletter to all subscribers (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { subject, content, contentType = 'html' } = body;

    // Validate required fields
    if (!subject || !content) {
      return NextResponse.json(
        { success: false, error: 'Subject and content are required' },
        { status: 400 }
      );
    }

    // Validate and sanitize subject
    const subjectValidation = validateNewsletterSubject(subject);
    if (!subjectValidation.isValid) {
      return NextResponse.json(
        { success: false, error: `Subject validation failed: ${subjectValidation.errors.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate and sanitize content
    const contentValidation = validateNewsletterContent(content, contentType);
    if (!contentValidation.isValid) {
      return NextResponse.json(
        { success: false, error: `Content validation failed: ${contentValidation.errors.join(', ')}` },
        { status: 400 }
      );
    }

    const sanitizedSubject = subjectValidation.sanitizedSubject;
    const sanitizedContent = contentValidation.sanitizedContent;

    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get newsletter settings
    const settings = await getNewsletterSettings();
    if (!settings) {
      return NextResponse.json(
        { success: false, error: 'Newsletter settings not configured' },
        { status: 500 }
      );
    }

    // Get all subscribed users
    const { data: subscribers, error: subscribersError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .select('email')
      .eq('status', 'subscribed');

    if (subscribersError) {
      console.error('Database fetch error:', subscribersError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch subscribers' },
        { status: 500 }
      );
    }

    if (!subscribers || subscribers.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No active subscribers found' },
        { status: 404 }
      );
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: settings.smtp_host,
      port: settings.smtp_port,
      secure: settings.smtp_secure,
      auth: {
        user: settings.smtp_email,
        pass: settings.smtp_password,
      },
    });

    // Verify transporter
    try {
      await transporter.verify();
    } catch (verifyError) {
      console.error('SMTP verification error:', verifyError);
      return NextResponse.json(
        { success: false, error: 'SMTP configuration is invalid' },
        { status: 500 }
      );
    }

    // Prepare email content with unsubscribe link
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://swiftafricasafaris.com';

    const emailContent = contentType === 'html'
      ? `${sanitizedContent}
         <div style="margin-top: 40px; padding: 20px; background-color: #f5f5f5; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #666;">
           <p>You received this email because you subscribed to Swift Africa Safaris newsletter.</p>
           <p><a href="${baseUrl}/api/newsletter/unsubscribe?email={{EMAIL}}" style="color: #d35400;">Unsubscribe</a> | <a href="${baseUrl}" style="color: #317100;">Visit our website</a></p>
           <p>Swift Africa Safaris - Discover the Wild Beauty of Africa</p>
         </div>`
      : `${sanitizedContent}\n\n---\nYou received this email because you subscribed to Swift Africa Safaris newsletter.\nUnsubscribe: ${baseUrl}/api/newsletter/unsubscribe?email={{EMAIL}}\nVisit our website: ${baseUrl}\nSwift Africa Safaris - Discover the Wild Beauty of Africa`;

    // Send emails in batches to avoid overwhelming the SMTP server
    const batchSize = 10;
    const batches = [];
    for (let i = 0; i < subscribers.length; i += batchSize) {
      batches.push(subscribers.slice(i, i + batchSize));
    }

    let successCount = 0;
    let failureCount = 0;
    const failures: string[] = [];

    for (const batch of batches) {
      const promises = batch.map(async (subscriber) => {
        try {
          const personalizedContent = emailContent.replace(/{{EMAIL}}/g, encodeURIComponent(subscriber.email));
          
          const mailOptions = {
            from: `Swift Africa Safaris <${settings.smtp_email}>`,
            to: subscriber.email,
            subject: sanitizedSubject,
            [contentType]: personalizedContent,
          };

          await transporter.sendMail(mailOptions);
          successCount++;
        } catch (error) {
          console.error(`Failed to send email to ${subscriber.email}:`, error);
          failureCount++;
          failures.push(subscriber.email);
        }
      });

      await Promise.all(promises);
      
      // Add delay between batches to be respectful to SMTP server
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Log the newsletter send activity
    try {
      await supabaseAdmin
        .from('sas_newsletter_logs')
        .insert({
          subject: sanitizedSubject,
          content_preview: sanitizedContent.substring(0, 200),
          total_recipients: subscribers.length,
          successful_sends: successCount,
          failed_sends: failureCount,
          sent_at: new Date().toISOString()
        });
    } catch (logError) {
      console.warn('Failed to log newsletter activity:', logError);
    }

    return NextResponse.json({
      success: true,
      message: `Newsletter sent successfully to ${successCount} subscribers`,
      data: {
        totalSubscribers: subscribers.length,
        successfulSends: successCount,
        failedSends: failureCount,
        failures: failures.length > 0 ? failures : undefined
      }
    });

  } catch (error) {
    console.error('Newsletter send error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
