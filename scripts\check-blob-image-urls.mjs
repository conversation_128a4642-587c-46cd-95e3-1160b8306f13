#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkBlobUrls() {
  console.log('🔍 Checking for packages with blob URLs...\n');

  try {
    // Fetch all packages
    const { data: packages, error } = await supabase
      .from('sas_packages')
      .select('id, title, slug, image_url, hero_image_url');

    if (error) {
      console.error('❌ Error fetching packages:', error);
      return;
    }

    console.log(`📦 Found ${packages.length} packages in database\n`);

    const blobPackages = [];
    const validPackages = [];
    const emptyImagePackages = [];

    packages.forEach(pkg => {
      const hasImageUrl = pkg.image_url && pkg.image_url.trim() !== '';
      const hasHeroImageUrl = pkg.hero_image_url && pkg.hero_image_url.trim() !== '';
      
      const imageUrlIsBlob = hasImageUrl && pkg.image_url.startsWith('blob:');
      const heroImageUrlIsBlob = hasHeroImageUrl && pkg.hero_image_url.startsWith('blob:');

      if (imageUrlIsBlob || heroImageUrlIsBlob) {
        blobPackages.push({
          id: pkg.id,
          title: pkg.title,
          slug: pkg.slug,
          image_url: pkg.image_url,
          hero_image_url: pkg.hero_image_url,
          issues: {
            imageUrlIsBlob,
            heroImageUrlIsBlob
          }
        });
      } else if (hasImageUrl || hasHeroImageUrl) {
        validPackages.push(pkg);
      } else {
        emptyImagePackages.push(pkg);
      }
    });

    // Report results
    console.log('📊 RESULTS:');
    console.log(`✅ Packages with valid image URLs: ${validPackages.length}`);
    console.log(`⚠️  Packages with empty image URLs: ${emptyImagePackages.length}`);
    console.log(`❌ Packages with blob URLs: ${blobPackages.length}\n`);

    if (blobPackages.length > 0) {
      console.log('🚨 PACKAGES WITH BLOB URLs:');
      blobPackages.forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.title} (${pkg.slug})`);
        if (pkg.issues.imageUrlIsBlob) {
          console.log(`   - image_url: ${pkg.image_url}`);
        }
        if (pkg.issues.heroImageUrlIsBlob) {
          console.log(`   - hero_image_url: ${pkg.hero_image_url}`);
        }
        console.log('');
      });

      console.log('💡 RECOMMENDATION:');
      console.log('These packages have blob URLs which are temporary and will not work.');
      console.log('You should:');
      console.log('1. Re-upload the images through the admin interface');
      console.log('2. Or run a cleanup script to set them to null so fallback images are used');
    }

    if (emptyImagePackages.length > 0) {
      console.log('📝 PACKAGES WITH NO IMAGES:');
      emptyImagePackages.forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.title} (${pkg.slug})`);
      });
      console.log('');
    }

    if (validPackages.length > 0) {
      console.log('✅ PACKAGES WITH VALID IMAGES:');
      validPackages.slice(0, 5).forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.title} (${pkg.slug})`);
        if (pkg.image_url) {
          console.log(`   - image_url: ${pkg.image_url.substring(0, 80)}...`);
        }
        if (pkg.hero_image_url) {
          console.log(`   - hero_image_url: ${pkg.hero_image_url.substring(0, 80)}...`);
        }
      });
      if (validPackages.length > 5) {
        console.log(`   ... and ${validPackages.length - 5} more`);
      }
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkBlobUrls();
