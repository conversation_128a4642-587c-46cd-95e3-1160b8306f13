import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch email configuration
export async function GET() {
  try {
    const { data, error } = await supabase
      .from('sas_email_config')
      .select('*')
      .order('setting_key');

    if (error) {
      console.error('Error fetching email config:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch email configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });
  } catch (error) {
    console.error('Error in GET /api/admin/email-config:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update email configuration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { settings } = body;

    if (!settings || !Array.isArray(settings)) {
      return NextResponse.json(
        { success: false, error: 'Invalid settings data' },
        { status: 400 }
      );
    }

    // Update each setting
    const updatePromises = settings.map(async (setting: any) => {
      const { setting_key, setting_value } = setting;
      
      if (!setting_key || setting_value === undefined) {
        throw new Error(`Invalid setting: ${JSON.stringify(setting)}`);
      }

      const { error } = await supabase
        .from('sas_email_config')
        .update({ setting_value })
        .eq('setting_key', setting_key);

      if (error) {
        throw error;
      }
    });

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: 'Email configuration updated successfully'
    });
  } catch (error) {
    console.error('Error updating email config:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update email configuration' },
      { status: 500 }
    );
  }
}

// POST - Test email configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { testEmail } = body;

    if (!testEmail) {
      return NextResponse.json(
        { success: false, error: 'Test email address is required' },
        { status: 400 }
      );
    }

    // Import email service functions
    const { sendTestEmail } = await import('@/lib/email-service');
    
    const success = await sendTestEmail(testEmail);

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Test email sent successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send test email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to send test email' },
      { status: 500 }
    );
  }
}
