import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { sendContactNotificationEmail } from '@/lib/email-service';

// Interface for contact submission data
interface ContactData {
  name: string;
  whatsapp: string;
  email: string;
  message?: string;
}

// GET - Fetch contact submissions (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let query = supabase
      .from('sas_contact_submissions')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%,whatsapp.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching contact submissions:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch contact submissions' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET /api/bookings/contact:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new contact submission
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'whatsapp', 'email'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const contactData: ContactData = {
      name: body.name.trim(),
      whatsapp: body.whatsapp.trim(),
      email: body.email.toLowerCase().trim(),
      message: body.message?.trim() || ''
    };

    // Insert contact submission into database
    const { data: newSubmission, error } = await supabase
      .from('sas_contact_submissions')
      .insert([contactData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create contact submission in database' },
        { status: 500 }
      );
    }

    // Send admin notification email
    try {
      await sendContactNotificationEmail(newSubmission);
      console.log('Contact submission notification email sent successfully');
      
      // Update email sent status
      await supabase
        .from('sas_contact_submissions')
        .update({ 
          email_sent: true, 
          email_sent_at: new Date().toISOString() 
        })
        .eq('id', newSubmission.id);
        
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the submission if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        id: newSubmission.id,
        name: newSubmission.name,
        whatsapp: newSubmission.whatsapp,
        email: newSubmission.email,
        message: newSubmission.message,
        status: newSubmission.status,
        createdAt: newSubmission.created_at
      },
      message: 'Contact submission created successfully'
    });

  } catch (error) {
    console.error('Error creating contact submission:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create contact submission' },
      { status: 500 }
    );
  }
}

// PATCH - Update contact submission status (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, adminNotes } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Submission ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    
    if (status) {
      const validStatuses = ['new', 'responded', 'closed'];
      if (!validStatuses.includes(status)) {
        return NextResponse.json(
          { success: false, error: 'Invalid status' },
          { status: 400 }
        );
      }
      updateData.status = status;
    }

    if (adminNotes !== undefined) {
      updateData.admin_notes = adminNotes;
    }

    const { data, error } = await supabase
      .from('sas_contact_submissions')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating contact submission:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update contact submission' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data,
      message: 'Contact submission updated successfully'
    });

  } catch (error) {
    console.error('Error in PATCH /api/bookings/contact:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
