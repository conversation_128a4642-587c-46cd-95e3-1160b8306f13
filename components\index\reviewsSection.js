/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @next/next/no-img-element */
"use client";
import React, { useState } from 'react';
import Slider from 'react-slick';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import ReviewCard from '../cards/reviewsCard';
import reviewData from '../data/reviewsData';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const ReviewsSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentReview, setCurrentReview] = useState(null);

  const renderStars = (rating) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <div
            key={star}
            className={`w-4 h-4 rounded-full ${star <= rating ? '' : 'bg-gray-300'
              }`}
            style={star <= rating ? { backgroundColor: '#00aa6c' } : {}}
          />
        ))}
      </div>
    );
  };

  const handleOpenModal = (review) => {
    setCurrentReview(review);
    setIsModalOpen(true);
  };

  // Remove non-DOM props from being passed to the button
  const CustomPrevArrow = ({ onClick, className, ...props }) => (
    <button
      onClick={onClick}
      className="absolute left-0 sm:left-[-30px] top-1/2 -translate-y-1/2 w-10 h-10 sm:w-10 sm:h-10 bg-white rounded-full shadow-lg flex items-center justify-center border border-gray-100 hover:bg-gray-50 hover:border-gray-200 transition-all z-10 group"
      style={{ color: 'var(--btn)' }}
    >
      <FaChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform" />
    </button>
  );

  const CustomNextArrow = ({ onClick, className, ...props }) => (
    <button
      onClick={onClick}
      className="absolute right-0 sm:right-[-30px] top-1/2 -translate-y-1/2 w-10 h-10 sm:w-10 sm:h-10 bg-white rounded-full shadow-lg flex items-center justify-center border border-gray-100 hover:bg-gray-50 hover:border-gray-200 transition-all z-10 group"
      style={{ color: 'var(--btn)' }}
    >
      <FaChevronRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:scale-110 transition-transform" />
    </button>
  );

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    pauseOnHover: true,
    prevArrow: <CustomPrevArrow />,
    nextArrow: <CustomNextArrow />,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        }
      }
    ]
  };

  return (
    <div className="relative py-6 sm:py-10 bg-[var(--secondary-background)]"
      style={{ paddingBottom: `60px`, }}
    >
      <div className="w-full max-w-[350px] md:max-w-[750px] xl:max-w-[1100px] mx-auto px-4">
        <div className="mb-12 text-center">
          <h2 className="text-3xl font-bold mb-2">What Our Happy Travelers Say</h2>
          <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
        </div>

        <div className="relative">
          <Slider {...settings}>
            {reviewData.map((review, idx) => (
              <div key={idx} className="px-2 md:px-4">
                <ReviewCard
                  review={review}
                  setIsModalOpen={() => handleOpenModal(review)}
                />
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && currentReview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white  max-w-2xl w-full max-h-96 overflow-y-auto">
            <div className="p-6 text-justify">
              {/* Modal header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 overflow-hidden rounded-full">
                    <img src="/images/logo/swift-africa-safaris-reviews.png" alt="Review" className="w-full h-full object-cover" />
                  </div>
                  <div className="text-left">
                    <p className="text-gray-700 font-medium">{currentReview.author}</p>
                    <h3 className="text-lg font-bold text-gray-900">{currentReview.title}</h3>
                  </div>
                </div>

                <button
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                >
                </button>
              </div>

              {/* Star rating in modal */}
              <div className="mb-4">
                {renderStars(currentReview.rating)}
              </div>

              {/* Full review text */}
              <p className="text-gray-700 leading-relaxed text-justify mb-4">
                {currentReview.fullReview}
              </p>

              {/* TripAdvisor Link */}
              {currentReview.tripAdvisorLink && (
                <a
                  href={currentReview.tripAdvisorLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block text-[var(--btn)] hover:underline font-medium text-justify"
                >
                  Read on TripAdvisor
                </a>
              )}
            </div>
          </div>
        </div>
      )}

      <style jsx global>{`
        .slick-track {
          display: flex !important;
          gap: 1rem;
        }
        .slick-slide {
          height: inherit !important;
        }
        .slick-slide > div {
          height: 100%;
        }
        .slick-dots {
          bottom: -40px;
        }
        .slick-dots li {
          margin: 0 4px;
          width: 12px;
          height: 12px;
        }
        .slick-dots li button {
          width: 12px;
          height: 12px;
          padding: 0;
        }
        .slick-dots li button:before {
          font-size: 10px;
          width: 12px;
          height: 12px;
          color: var(--btn);
          opacity: 0.3;
          transition: all 0.3s ease;
        }
        .slick-dots li.slick-active button:before {
          color: var(--btn);
          opacity: 1;
          transform: scale(1.2);
        }

        @media (max-width: 640px) {
          .slick-prev {
            left: 5px !important;
            z-index: 10;
          }
          .slick-next {
            right: 5px !important;
            z-index: 10;
          }
          .slick-dots {
            bottom: -35px;
          }
          .slick-slide {
            padding: 0 8px;
          }
        }
      `}</style>
    </div>
  );
};

export default ReviewsSection;

