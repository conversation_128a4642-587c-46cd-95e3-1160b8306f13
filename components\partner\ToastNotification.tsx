/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { useState, useEffect } from 'react';
import { 
  X, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  AlertTriangle,
  Calendar,
  DollarSign,
  Package,
  MessageSquare
} from 'lucide-react';

interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'booking' | 'payment' | 'message' | 'package';
  title: string;
  message: string;
  duration?: number;
  actionUrl?: string;
  actionText?: string;
}

interface ToastNotificationProps {
  notifications: ToastNotification[];
  onRemove: (id: string) => void;
  onAction?: (notification: ToastNotification) => void;
}

const ToastNotificationComponent: React.FC<ToastNotificationProps> = ({
  notifications,
  onRemove,
  onAction
}) => {
  const getToastIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error': return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'info': return <Info className="h-5 w-5 text-blue-600" />;
      case 'booking': return <Calendar className="h-5 w-5 text-blue-600" />;
      case 'payment': return <DollarSign className="h-5 w-5 text-green-600" />;
      case 'message': return <MessageSquare className="h-5 w-5 text-purple-600" />;
      case 'package': return <Package className="h-5 w-5 text-orange-600" />;
      default: return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getToastColors = (type: string) => {
    switch (type) {
      case 'success': return 'bg-green-50 border-green-200 text-green-800';
      case 'error': return 'bg-red-50 border-red-200 text-red-800';
      case 'warning': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'info': return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'booking': return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'payment': return 'bg-green-50 border-green-200 text-green-800';
      case 'message': return 'bg-purple-50 border-purple-200 text-purple-800';
      case 'package': return 'bg-orange-50 border-orange-200 text-orange-800';
      default: return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {notifications.map((notification) => (
        <ToastItem
          key={notification.id}
          notification={notification}
          onRemove={onRemove}
          onAction={onAction}
          getToastIcon={getToastIcon}
          getToastColors={getToastColors}
        />
      ))}
    </div>
  );
};

interface ToastItemProps {
  notification: ToastNotification;
  onRemove: (id: string) => void;
  onAction?: (notification: ToastNotification) => void;
  getToastIcon: (type: string) => React.ReactNode;
  getToastColors: (type: string) => string;
}

const ToastItem: React.FC<ToastItemProps> = ({
  notification,
  onRemove,
  onAction,
  getToastIcon,
  getToastColors
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 100);
    
    // Auto remove after duration
    if (notification.duration && notification.duration > 0) {
      const removeTimer = setTimeout(() => {
        handleRemove();
      }, notification.duration);
      
      return () => {
        clearTimeout(timer);
        clearTimeout(removeTimer);
      };
    }
    
    return () => clearTimeout(timer);
  }, [notification.duration]);

  const handleRemove = () => {
    setIsRemoving(true);
    setTimeout(() => {
      onRemove(notification.id);
    }, 300);
  };

  const handleAction = () => {
    if (onAction) {
      onAction(notification);
    }
    handleRemove();
  };

  return (
    <div
      className={`transform transition-all duration-300 ease-in-out ${
        isVisible && !isRemoving
          ? 'translate-x-0 opacity-100'
          : 'translate-x-full opacity-0'
      }`}
    >
      <div className={`rounded-lg border shadow-lg p-4 ${getToastColors(notification.type)}`}>
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getToastIcon(notification.type)}
          </div>
          <div className="ml-3 flex-1">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium">{notification.title}</p>
                <p className="text-sm mt-1 opacity-90">{notification.message}</p>
                {notification.actionUrl && notification.actionText && (
                  <button
                    onClick={handleAction}
                    className="text-sm font-medium underline mt-2 hover:no-underline transition-all"
                  >
                    {notification.actionText}
                  </button>
                )}
              </div>
              <button
                onClick={handleRemove}
                className="ml-4 flex-shrink-0 p-1 hover:bg-black hover:bg-opacity-10 rounded transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for managing toast notifications
export const useToastNotifications = () => {
  const [notifications, setNotifications] = useState<ToastNotification[]>([]);

  const addNotification = (notification: Omit<ToastNotification, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification: ToastNotification = {
      id,
      duration: 20000, // Default 5 seconds
      ...notification
    };
    
    setNotifications(prev => [...prev, newNotification]);
    return id;
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Convenience methods for different types
  const showSuccess = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'success', title, message, ...options });
  };

  const showError = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'error', title, message, duration: 7000, ...options });
  };

  const showWarning = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'warning', title, message, ...options });
  };

  const showInfo = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'info', title, message, ...options });
  };

  const showBookingNotification = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'booking', title, message, ...options });
  };

  const showPaymentNotification = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'payment', title, message, ...options });
  };

  const showMessageNotification = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'message', title, message, ...options });
  };

  const showPackageNotification = (title: string, message: string, options?: Partial<ToastNotification>) => {
    return addNotification({ type: 'package', title, message, ...options });
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showBookingNotification,
    showPaymentNotification,
    showMessageNotification,
    showPackageNotification
  };
};

export default ToastNotificationComponent;
