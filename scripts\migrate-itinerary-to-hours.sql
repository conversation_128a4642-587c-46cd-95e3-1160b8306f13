-- Migration Script: Update Itinerary Tables to Use Hours Instead of Days
-- and Remove Activities, Accommodation, Meals Columns
-- Execute this script in your Supabase SQL Editor

-- =============================================
-- 1. UPDATE PACKAGE ITINERARY TABLE
-- =============================================

-- Add new hour_number column
ALTER TABLE public.sas_package_itinerary 
ADD COLUMN IF NOT EXISTS hour_number INTEGER;

-- Copy day_number values to hour_number (assuming 1 day = 24 hours, so day 1 = hour 1, day 2 = hour 25, etc.)
-- For now, we'll just copy the day_number as hour_number for simplicity
UPDATE public.sas_package_itinerary 
SET hour_number = day_number 
WHERE hour_number IS NULL;

-- Make hour_number NOT NULL
ALTER TABLE public.sas_package_itinerary 
ALTER COLUMN hour_number SET NOT NULL;

-- Remove the old columns
ALTER TABLE public.sas_package_itinerary 
DROP COLUMN IF EXISTS day_number,
DROP COLUMN IF EXISTS activities,
DROP COLUMN IF EXISTS accommodation,
DROP COLUMN IF EXISTS meals;

-- =============================================
-- 2. UPDATE MINI PACKAGE ITINERARY TABLE
-- =============================================

-- Add new hour_number column
ALTER TABLE public.sas_mini_package_itinerary 
ADD COLUMN IF NOT EXISTS hour_number INTEGER;

-- Copy day_number values to hour_number
UPDATE public.sas_mini_package_itinerary 
SET hour_number = day_number 
WHERE hour_number IS NULL;

-- Make hour_number NOT NULL
ALTER TABLE public.sas_mini_package_itinerary 
ALTER COLUMN hour_number SET NOT NULL;

-- Remove the old columns
ALTER TABLE public.sas_mini_package_itinerary 
DROP COLUMN IF EXISTS day_number,
DROP COLUMN IF EXISTS activities,
DROP COLUMN IF EXISTS accommodation,
DROP COLUMN IF EXISTS meals;

-- =============================================
-- 3. UPDATE TRIGGERS (if any reference the old columns)
-- =============================================

-- Drop and recreate triggers for updated_at if they exist
DROP TRIGGER IF EXISTS update_sas_package_itinerary_updated_at ON public.sas_package_itinerary;
DROP TRIGGER IF EXISTS update_sas_mini_package_itinerary_updated_at ON public.sas_mini_package_itinerary;

-- Recreate triggers
CREATE TRIGGER update_sas_package_itinerary_updated_at
    BEFORE UPDATE ON public.sas_package_itinerary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_mini_package_itinerary_updated_at
    BEFORE UPDATE ON public.sas_mini_package_itinerary
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 4. VERIFICATION QUERIES
-- =============================================

-- Check the updated table structures
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'sas_package_itinerary' 
ORDER BY ordinal_position;

SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'sas_mini_package_itinerary' 
ORDER BY ordinal_position;

-- Check sample data
SELECT id, hour_number, title, description, sort_order 
FROM public.sas_package_itinerary 
ORDER BY sort_order 
LIMIT 5;

SELECT id, hour_number, title, description, sort_order 
FROM public.sas_mini_package_itinerary 
ORDER BY sort_order 
LIMIT 5;

-- =============================================
-- COMPLETION MESSAGE
-- =============================================
DO $$
BEGIN
    RAISE NOTICE '✅ Itinerary Migration Complete!';
    RAISE NOTICE '📋 Changes made:';
    RAISE NOTICE '   - day_number → hour_number in both itinerary tables';
    RAISE NOTICE '   - Removed activities, accommodation, meals columns';
    RAISE NOTICE '   - Updated triggers for both tables';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  Next steps:';
    RAISE NOTICE '   - Update API endpoints to use hour_number';
    RAISE NOTICE '   - Update frontend components';
    RAISE NOTICE '   - Test all CRUD operations';
END $$;
