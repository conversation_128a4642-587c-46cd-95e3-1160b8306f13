/* eslint-disable @next/next/no-img-element */
import { useState, useEffect, useRef } from 'react';
import { Search, Clock } from 'lucide-react';
import { searchProducts } from '../../lib/search';
import { products } from '../data/productData';

export default function SearchBar({ onSearch }) {
  const clearButtonRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [previewResults, setPreviewResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setRecentSearches(JSON.parse(localStorage.getItem('recentSearches') || '[]'));
    }
  }, []);

  useEffect(() => {
    if (searchTerm.trim().length > 0) {
      setPreviewResults(searchProducts(products, searchTerm).slice(0, 4));
    } else {
      setPreviewResults([]);
    }
  }, [searchTerm]);

  const addToRecentSearches = (term) => {
    if (typeof window !== 'undefined') {
      const updated = [term, ...recentSearches.filter(t => t !== term)].slice(0, 5);
      setRecentSearches(updated);
      localStorage.setItem('recentSearches', JSON.stringify(updated));
    }
  };

  const handleSearch = async () => {
    if (searchTerm.trim()) {
      setIsLoading(true);
      const results = await searchProducts(products, searchTerm);
      setIsLoading(false);
      
      addToRecentSearches(searchTerm);
      onSearch({ term: searchTerm, results });
    }
  };

  const handleClearRecent = (e) => {
    e.stopPropagation();
    setRecentSearches([]);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('recentSearches');
    }
  };

  return (
    <div className="w-full max-w-md relative">
      <div className={`
        relative flex items-center bg-white rounded-full shadow-lg border-2 transition-all duration-300 ease-in-out
        ${isFocused ? 'border-blue-400 shadow-xl' : 'border-gray-200 hover:border-gray-300'}
      `}>
        {/* Search Icon */}
        <div className={`
          absolute transition-all duration-300 ease-in-out z-10
          ${isFocused ? 'right-3' : 'left-4'}
        `}>
          <Search 
            className={`w-5 h-5 transition-all duration-300 cursor-pointer
              ${isFocused ? 'text-blue-500 hover:text-blue-600' : 'text-gray-400'}
            `}
            onClick={handleSearch}
          />
        </div>

        {/* Input field */}
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={(e) => {
            if (!clearButtonRef.current?.contains(e.relatedTarget)) {
              setTimeout(() => setIsFocused(false), 200);
            }
          }}
          onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          placeholder="Search products..."
          className={`w-full py-3 px-4 bg-transparent rounded-full outline-none transition-all duration-300
            ${isFocused ? 'pl-4 pr-12' : 'pl-12 pr-4'}
            text-gray-700 placeholder-gray-400
          `}
        />
      </div>

      {/* Dropdown Content */}
      {isFocused && (
        <div className="absolute mt-2 w-full bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <>
              {/* Recent Searches */}
              {recentSearches.length > 0 && (
                <div className="px-4 py-2">
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-2" />
                      Recent Searches
                    </div>
                    <button 
                      ref={clearButtonRef}
                      onClick={handleClearRecent}
                      className="text-xs text-blue-500 hover:text-blue-700"
                    >
                      Clear
                    </button>
                  </div>
                  {recentSearches.map((term, index) => (
                    <button
                      key={index}
                      onClick={() => setSearchTerm(term)}
                      className="flex items-center w-full px-2 py-1 hover:bg-gray-50 rounded text-sm"
                    >
                      <Clock className="w-3 h-3 mr-2 text-gray-400" />
                      {term}
                    </button>
                  ))}
                </div>
              )}

              {/* Preview Results */}
              {previewResults.length > 0 && (
                <div className="border-t border-gray-200">
                  {previewResults.map((product) => (
                    <button
                      key={product.slug}
                      onClick={() => {
                        setSearchTerm(product.title);
                        handleSearch();
                      }}
                      className="w-full text-left p-3 hover:bg-gray-50 flex items-center space-x-3 border-b border-gray-100 last:border-0"
                    >
                      <img 
                        src={product.images[0]} 
                        alt={product.title}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{product.title}</h4>
                        <p className="text-sm text-gray-500 line-clamp-2">{product.description}</p>
                        <p className="text-sm font-bold text-green-800 mt-1">${product.price}</p>
                      </div>
                    </button>
                  ))}
                  <button
                    onClick={handleSearch}
                    className="w-full mt-2 text-center py-2 text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    See all results
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
}