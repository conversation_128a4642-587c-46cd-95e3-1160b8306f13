'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Bold, Italic, Underline, Link } from 'lucide-react';

interface SimpleRichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  className?: string;
}

const SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start typing...",
  height = 150,
  disabled = false,
  className = ""
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [showToolbar, setShowToolbar] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!editorRef.current || !editorRef.current.contains(e.target as Node)) return;

      const isCtrl = e.ctrlKey || e.metaKey;

      if (isCtrl) {
        switch (e.key.toLowerCase()) {
          case 'b':
            e.preventDefault();
            document.execCommand('bold');
            break;
          case 'i':
            e.preventDefault();
            document.execCommand('italic');
            break;
          case 'u':
            e.preventDefault();
            document.execCommand('underline');
            break;
          case 'k':
            e.preventDefault();
            handleLinkInsertion();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handle text selection for floating toolbar
  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().length > 0 && editorRef.current?.contains(selection.anchorNode)) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      setToolbarPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + window.scrollY - 10
      });
      setShowToolbar(true);
    } else {
      setShowToolbar(false);
    }
  };

  // Handle content changes
  const handleInput = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  };

  // Handle formatting commands
  const handleFormat = (command: string) => {
    document.execCommand(command, false, undefined);
    editorRef.current?.focus();
  };

  // Handle link insertion
  const handleLinkInsertion = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const selectedText = selection.toString();
    const url = prompt('Enter URL:', 'https://');
    
    if (url && url.trim()) {
      if (selectedText) {
        document.execCommand('createLink', false, url.trim());
      } else {
        const linkText = prompt('Enter link text:', url.trim());
        if (linkText) {
          document.execCommand('insertHTML', false, `<a href="${url.trim()}">${linkText}</a>`);
        }
      }
    }
    setShowToolbar(false);
  };

  // Initialize content
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      editorRef.current.innerHTML = value || '';
    }
  }, [value]);

  // Add event listeners
  useEffect(() => {
    const editor = editorRef.current;
    if (!editor) return;

    const handleMouseUp = () => {
      setTimeout(handleTextSelection, 10);
    };

    const handleKeyUp = () => {
      setTimeout(handleTextSelection, 10);
    };

    const handleBlur = () => {
      setTimeout(() => setShowToolbar(false), 200);
    };

    // Prevent drag events from bubbling up to parent containers
    const handleDragStart = (e: DragEvent) => {
      e.stopPropagation();
    };

    const handleDrag = (e: DragEvent) => {
      e.stopPropagation();
    };

    const handleDragEnd = (e: DragEvent) => {
      e.stopPropagation();
    };

    editor.addEventListener('mouseup', handleMouseUp);
    editor.addEventListener('keyup', handleKeyUp);
    editor.addEventListener('blur', handleBlur);
    editor.addEventListener('dragstart', handleDragStart);
    editor.addEventListener('drag', handleDrag);
    editor.addEventListener('dragend', handleDragEnd);

    return () => {
      editor.removeEventListener('mouseup', handleMouseUp);
      editor.removeEventListener('keyup', handleKeyUp);
      editor.removeEventListener('blur', handleBlur);
      editor.removeEventListener('dragstart', handleDragStart);
      editor.removeEventListener('drag', handleDrag);
      editor.removeEventListener('dragend', handleDragEnd);
    };
  }, []);

  const formatButtons = [
    {
      command: 'bold',
      icon: Bold,
      tooltip: 'Bold (Ctrl+B)',
      isActive: () => document.queryCommandState('bold')
    },
    {
      command: 'italic',
      icon: Italic,
      tooltip: 'Italic (Ctrl+I)',
      isActive: () => document.queryCommandState('italic')
    },
    {
      command: 'underline',
      icon: Underline,
      tooltip: 'Underline (Ctrl+U)',
      isActive: () => document.queryCommandState('underline')
    },
    {
      command: 'link',
      icon: Link,
      tooltip: 'Insert Link (Ctrl+K)',
      isActive: () => false,
      onClick: handleLinkInsertion
    }
  ];

  return (
    <div className={`simple-rich-text-editor ${className}`}>
      {/* Main Editor */}
      <div
        ref={editorRef}
        contentEditable={!disabled}
        onInput={handleInput}
        className="w-full border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none overflow-y-auto"
        style={{ 
          minHeight: `${height}px`,
          maxHeight: `${height * 2}px`
        }}
        data-placeholder={placeholder}
        suppressContentEditableWarning={true}
      />

      {/* Floating Formatting Toolbar */}
      {showToolbar && (
        <div
          className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-1 flex items-center gap-1"
          style={{
            left: `${toolbarPosition.x}px`,
            top: `${toolbarPosition.y}px`,
            transform: 'translateX(-50%)',
          }}
        >
          {formatButtons.map((button) => {
            const IconComponent = button.icon;
            const isActive = button.isActive ? button.isActive() : false;
            
            return (
              <button
                key={button.command}
                onClick={button.onClick || (() => handleFormat(button.command))}
                className={`p-2 rounded hover:bg-gray-100 transition-colors ${
                  isActive 
                    ? 'bg-blue-100 text-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title={button.tooltip}
                type="button"
                onMouseDown={(e) => e.preventDefault()} // Prevent blur
              >
                <IconComponent size={16} />
              </button>
            );
          })}
          
          {/* Toolbar arrow pointing down */}
          <div 
            className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"
            style={{ marginTop: '-1px' }}
          />
        </div>
      )}

      {/* CSS for placeholder and styling */}
      <style jsx>{`
        .simple-rich-text-editor [contenteditable]:empty:before {
          content: attr(data-placeholder);
          color: #9ca3af;
          pointer-events: none;
        }
        
        .simple-rich-text-editor [contenteditable] {
          font-family: 'Jost', sans-serif;
          font-size: 14px;
          line-height: 1.6;
          color: #374151;
        }
        
        .simple-rich-text-editor [contenteditable] p {
          margin: 0 0 8px 0;
        }
        
        .simple-rich-text-editor [contenteditable] a {
          color: #2563eb;
          text-decoration: underline;
        }
        
        .simple-rich-text-editor [contenteditable] a:hover {
          color: #1d4ed8;
        }
        
        .simple-rich-text-editor [contenteditable] ul,
        .simple-rich-text-editor [contenteditable] ol {
          margin: 8px 0;
          padding-left: 20px;
        }
        
        .simple-rich-text-editor [contenteditable] li {
          margin: 2px 0;
        }
      `}</style>
    </div>
  );
};

export default SimpleRichTextEditor;
