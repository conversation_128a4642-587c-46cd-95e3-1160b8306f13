import { NextRequest, NextResponse } from 'next/server';
import { trackMiddlewareCrawl, isSearchEngineBot, isSocialBot } from '@/lib/seo-monitoring';
import { detectRegionFromRequest, generateHreflangHeaders } from '@/lib/hreflang-utils';

// Simple in-memory rate limiting (for production, use Redis or similar)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  const pathname = request.nextUrl.pathname;
  const userAgent = request.headers.get('user-agent') || '';
  const ip = request.headers.get('x-forwarded-for') ||
             request.headers.get('x-real-ip') ||
             'unknown';

  // Rate limiting for SEO protection
  if (shouldRateLimit(pathname, userAgent)) {
    const rateLimitResult = checkRateLimit(ip, userAgent);
    if (!rateLimitResult.allowed) {
      return new NextResponse('Too Many Requests', {
        status: 429,
        headers: {
          'Retry-After': '60',
          'X-RateLimit-Limit': '100',
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      });
    }
  }

  // Check for large request bodies on blog API endpoints
  if (pathname.startsWith('/api/admin/blog') &&
      (request.method === 'POST' || request.method === 'PUT')) {

    const contentLength = request.headers.get('content-length');

    if (contentLength) {
      const sizeInBytes = parseInt(contentLength);
      const maxSizeInBytes = 10 * 1024 * 1024; // 10MB

      if (sizeInBytes > maxSizeInBytes) {
        console.warn(`Request too large: ${sizeInBytes} bytes (max: ${maxSizeInBytes})`);
        return NextResponse.json(
          {
            success: false,
            error: 'Request too large. Blog content exceeds the maximum allowed size of 10MB. Please reduce content size, compress images, or split into multiple posts.'
          },
          { status: 413 }
        );
      }
    }
  }

  // Block suspicious crawlers and scrapers
  if (isSuspiciousCrawler(userAgent, pathname)) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Add comprehensive SEO and performance headers
  addSEOHeaders(response, request);

  // Track SEO-relevant crawls (bots and important pages)
  if (isSearchEngineBot(userAgent) || isSocialBot(userAgent) || isImportantPage(pathname)) {
    const startTime = Date.now();

    // Track the crawl event asynchronously
    response.headers.set('X-SEO-Tracked', 'true');

    // Use a promise that doesn't block the response
    Promise.resolve().then(() => {
      const responseTime = Date.now() - startTime;
      trackMiddlewareCrawl(pathname, userAgent, 200, responseTime);
    }).catch(error => {
      console.error('SEO tracking error:', error);
    });
  }

  return response;
}

function shouldRateLimit(pathname: string, userAgent: string): boolean {
  // Don't rate limit legitimate search engine bots
  const legitimateBots = /googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot|facebookexternalhit|twitterbot|linkedinbot/i;
  if (legitimateBots.test(userAgent)) {
    return false;
  }

  // Rate limit aggressive crawling patterns
  return pathname.startsWith('/api/') ||
         pathname.includes('sitemap') ||
         pathname.includes('robots');
}

function checkRateLimit(ip: string, userAgent: string): { allowed: boolean; resetTime: number } {
  const key = `${ip}-${userAgent.slice(0, 50)}`;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute window
  const maxRequests = 100; // Max requests per window

  const current = rateLimitMap.get(key);

  if (!current || now > current.resetTime) {
    // New window
    rateLimitMap.set(key, { count: 1, resetTime: now + windowMs });
    return { allowed: true, resetTime: now + windowMs };
  }

  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime };
  }

  current.count++;
  return { allowed: true, resetTime: current.resetTime };
}

function isSuspiciousCrawler(userAgent: string, pathname: string): boolean {
  // Block known bad bots and scrapers
  const suspiciousPatterns = [
    /ahrefsbot/i,
    /semrushbot/i,
    /mj12bot/i,
    /dotbot/i,
    /blexbot/i,
    /serpstatbot/i,
    /linkdexbot/i,
    /spbot/i,
    /wget/i,
    /curl/i
  ];

  const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(userAgent));

  // Allow access to robots.txt and sitemap even for suspicious bots
  if (pathname === '/robots.txt' || pathname.includes('sitemap')) {
    return false;
  }

  return isSuspicious;
}

function isImportantPage(pathname: string): boolean {
  // Define pages that are important for SEO tracking
  const importantPages = [
    '/',
    '/about',
    '/contact',
    '/blog',
    '/package',
    '/mini-package',
    '/faqs',
    '/journey',
    '/iconic'
  ];

  // Check exact matches
  if (importantPages.includes(pathname)) {
    return true;
  }

  // Check dynamic routes
  return pathname.startsWith('/package/') ||
         pathname.startsWith('/blog/') ||
         pathname.startsWith('/mini-package/') ||
         pathname.startsWith('/iconic/');
}

function addSEOHeaders(response: NextResponse, request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const userAgent = request.headers.get('user-agent') || '';
  const isBot = /bot|crawler|spider|crawling/i.test(userAgent);

  // Enhanced Security Headers for SEO trust signals
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'SAMEORIGIN'); // Allow embedding for social media previews
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Content Security Policy for security and performance
  const csp = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com https://connect.facebook.net",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' https:",
    "connect-src 'self' https://api.supabase.co https://*.supabase.co https://www.google-analytics.com",
    "frame-src 'self' https://www.youtube.com https://player.vimeo.com",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'"
  ].join('; ');

  response.headers.set('Content-Security-Policy', csp);

  // Performance and Caching Headers with SEO optimization
  setCacheHeaders(response, pathname, isBot);

  // SEO-specific headers
  setSEOHeaders(response, pathname, request, isBot);

  // Performance hints and resource optimization
  setPerformanceHints(response, pathname);

  // CORS headers for API endpoints
  if (pathname.startsWith('/api/')) {
    response.headers.set('Access-Control-Allow-Origin', 'https://swiftafricasafaris.com');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  }
}

function setPerformanceHints(response: NextResponse, pathname: string) {
  // Critical resource preloading based on page type
  const preloadLinks: string[] = [];

  if (pathname === '/') {
    // Homepage critical resources
    preloadLinks.push(
      '</images/hero/great-migration-serengeti-national-park.webp>; rel=preload; as=image; fetchpriority=high',
      '</images/common/swift-africa-safaris-best-tour-operator-icon.png>; rel=preload; as=image',
      '<https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap>; rel=preload; as=style'
    );
  } else if (pathname.startsWith('/package/')) {
    // Package page critical resources
    preloadLinks.push(
      '<https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap>; rel=preload; as=style'
    );
  } else if (pathname.startsWith('/blog/')) {
    // Blog page critical resources
    preloadLinks.push(
      '<https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap>; rel=preload; as=style'
    );
  }

  // DNS prefetch for external domains
  const dnsPrefetchLinks = [
    '<https://images.unsplash.com>; rel=dns-prefetch',
    '<https://mtqdzkhkpjutyvorwzjk.supabase.co>; rel=dns-prefetch',
    '<https://fonts.googleapis.com>; rel=dns-prefetch',
    '<https://fonts.gstatic.com>; rel=dns-prefetch'
  ];

  // Combine all performance hints
  const allLinks = [...preloadLinks, ...dnsPrefetchLinks];

  if (allLinks.length > 0) {
    const existingLink = response.headers.get('Link');
    const newLink = existingLink ? `${existingLink}, ${allLinks.join(', ')}` : allLinks.join(', ');
    response.headers.set('Link', newLink);
  }

  // Add performance timing headers
  response.headers.set('Server-Timing', 'middleware;dur=0');

  // Add compression hints
  response.headers.set('Vary', 'Accept-Encoding, User-Agent');
}

function setCacheHeaders(response: NextResponse, pathname: string, isBot: boolean) {
  if (pathname.startsWith('/images/') || pathname.match(/\.(jpg|jpeg|png|webp|avif|svg|ico|woff2|woff)$/)) {
    // Static assets - cache for 1 year with immutable
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    response.headers.set('Vary', 'Accept-Encoding');
  } else if (pathname.startsWith('/_next/static/')) {
    // Next.js static assets - cache for 1 year
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
  } else if (pathname.startsWith('/api/')) {
    // API routes - differentiate between public and private
    if (pathname.includes('/admin/') || pathname.includes('/auth/')) {
      response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate, private');
    } else {
      response.headers.set('Cache-Control', 'public, max-age=60, stale-while-revalidate=300');
    }
  } else if (pathname === '/sitemap.xml' || pathname === '/robots.txt') {
    // SEO files - cache for 1 hour, longer stale-while-revalidate for bots
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
  } else if (pathname === '/' || pathname.startsWith('/package/') || pathname.startsWith('/blog/')) {
    // High-priority dynamic pages with ISR
    if (isBot) {
      // Longer cache for bots to reduce server load
      response.headers.set('Cache-Control', 'public, max-age=1800, stale-while-revalidate=86400');
    } else {
      response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=86400');
    }
    response.headers.set('Vary', 'Accept-Encoding, User-Agent');
  } else {
    // Other pages - standard caching
    response.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
    response.headers.set('Vary', 'Accept-Encoding');
  }
}

function setSEOHeaders(response: NextResponse, pathname: string, request: NextRequest, isBot: boolean) {
  // Default robots directive
  let robotsDirective = 'index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1';

  // Customize robots for specific paths
  if (pathname.startsWith('/admin/') || pathname.startsWith('/api/') || pathname.startsWith('/login')) {
    robotsDirective = 'noindex, nofollow';
  } else if (pathname.startsWith('/partner/')) {
    robotsDirective = 'noindex, follow';
  } else if (pathname === '/privacy-policy' || pathname === '/booking-policy') {
    robotsDirective = 'index, nofollow';
  }

  response.headers.set('X-Robots-Tag', robotsDirective);

  // Add canonical URL hint for dynamic pages
  if (pathname.startsWith('/package/') || pathname.startsWith('/blog/') || pathname.startsWith('/mini-package/')) {
    const canonicalUrl = `https://swiftafricasafaris.com${pathname}`;
    response.headers.set('Link', `<${canonicalUrl}>; rel="canonical"`);
  }

  // Add comprehensive hreflang hints for international pages
  if (pathname === '/' || pathname.startsWith('/package/') || pathname.startsWith('/blog/') || pathname.startsWith('/mini-package/')) {
    const hreflangHeaders = generateHreflangHeaders(pathname);

    // Merge with existing Link headers
    const existingLink = response.headers.get('Link');
    if (hreflangHeaders.Link) {
      const newLink = existingLink ? `${existingLink}, ${hreflangHeaders.Link}` : hreflangHeaders.Link;
      response.headers.set('Link', newLink);
    }
  }

  // Detect and set user's preferred region
  const regionDetection = detectRegionFromRequest(request);
  response.headers.set('X-Detected-Region', regionDetection.region);
  response.headers.set('X-Region-Confidence', regionDetection.confidence);
  response.headers.set('X-Region-Method', regionDetection.method);

  // Add structured data hints
  if (pathname.startsWith('/package/')) {
    response.headers.set('X-Schema-Type', 'Product, TouristTrip');
  } else if (pathname.startsWith('/blog/')) {
    response.headers.set('X-Schema-Type', 'Article, BlogPosting');
  } else if (pathname === '/') {
    response.headers.set('X-Schema-Type', 'WebSite, Organization');
  }

  // Bot-specific optimizations
  if (isBot) {
    // Prioritize content delivery for search engine bots
    response.headers.set('X-Bot-Optimized', 'true');

    // Add crawl delay hint for aggressive bots
    response.headers.set('X-Crawl-Delay', '1');

    // Indicate content freshness for bots
    response.headers.set('X-Content-Fresh', new Date().toISOString());
  }

  // Add page type hints for better categorization
  if (pathname === '/') {
    response.headers.set('X-Page-Type', 'homepage');
  } else if (pathname.startsWith('/package/')) {
    response.headers.set('X-Page-Type', 'product');
  } else if (pathname.startsWith('/blog/')) {
    response.headers.set('X-Page-Type', 'article');
  } else if (pathname === '/contact') {
    response.headers.set('X-Page-Type', 'contact');
  } else if (pathname === '/about') {
    response.headers.set('X-Page-Type', 'about');
  }
}

export const config = {
  matcher: [
    // Apply to all routes except static files that don't need processing
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|manifest.json).*)',
    // Specific API endpoints that need special handling
    '/api/admin/blog/:path*',
    '/api/analytics/:path*',
    '/api/packages/:path*',
    '/api/blog/:path*'
  ]
};
