import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { sendBookingConfirmationEmail, sendAdminNotificationEmail } from '@/lib/email-service';

// Interface for booking data
interface BookingData {
  package_id?: string;
  package_title: string;
  package_type: 'solo' | 'honeymoon' | 'family' | 'group';
  full_name: string;
  email: string;
  phone: string;
  number_of_people: number;
  check_in_date: string;
  check_out_date?: string;
  special_requests?: string;
  amount: number;
}

// GET - Fetch bookings (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let query = supabase
      .from('sas_bookings')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,booking_reference.ilike.%${search}%,package_title.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching bookings:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch bookings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET /api/bookings:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new booking
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['packageTitle', 'packageType', 'fullName', 'email', 'phone', 'people', 'date'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate date format and ensure it's in the future
    const checkInDate = new Date(body.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isNaN(checkInDate.getTime()) || checkInDate < today) {
      return NextResponse.json(
        { success: false, error: 'Invalid or past check-in date' },
        { status: 400 }
      );
    }

    // Validate number of people
    const numberOfPeople = parseInt(body.people);
    if (isNaN(numberOfPeople) || numberOfPeople < 1) {
      return NextResponse.json(
        { success: false, error: 'Invalid number of people' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const bookingData: BookingData = {
      package_title: body.packageTitle,
      package_type: body.packageType,
      full_name: body.fullName,
      email: body.email.toLowerCase().trim(),
      phone: body.phone.trim(),
      number_of_people: numberOfPeople,
      check_in_date: body.date,
      check_out_date: body.checkOutDate || null,
      special_requests: body.message?.trim() || '',
      amount: body.totalAmount ? parseFloat(body.totalAmount) : 0
    };

    // If package ID is provided, validate it exists
    if (body.packageId) {
      const { data: packageExists, error: packageError } = await supabase
        .from('sas_packages')
        .select('id, title')
        .eq('id', body.packageId)
        .single();

      if (packageError || !packageExists) {
        return NextResponse.json(
          { success: false, error: 'Invalid package ID' },
          { status: 400 }
        );
      }

      bookingData.package_id = body.packageId;
      bookingData.package_title = packageExists.title; // Use actual package title
    }

    // Insert booking into database
    const { data: newBooking, error } = await supabase
      .from('sas_bookings')
      .insert([bookingData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create booking in database' },
        { status: 500 }
      );
    }

    // Send admin notification email only (no customer confirmation email)
    try {
      await sendAdminNotificationEmail(newBooking);
      console.log('Admin notification email sent successfully');
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the booking if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        id: newBooking.id,
        bookingReference: newBooking.booking_reference,
        packageTitle: newBooking.package_title,
        fullName: newBooking.full_name,
        email: newBooking.email,
        checkInDate: newBooking.check_in_date,
        numberOfPeople: newBooking.number_of_people,
        status: newBooking.status,
        createdAt: newBooking.created_at
      },
      message: 'Booking created successfully'
    });

  } catch (error) {
    console.error('Error creating booking:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create booking' },
      { status: 500 }
    );
  }
}


