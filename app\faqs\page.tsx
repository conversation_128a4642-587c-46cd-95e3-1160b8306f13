import type { Metadata } from 'next';
import React from 'react';
import MegaMenuHeader from '@/components/header';
import FaqsClient from '@/components/faqs/FaqsClient';
import TravelFooter from '@/components/footer';
import { generateMetadata } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Africa Safaris - Everything You Need to Know Before You Go',
  description: 'Get answers to all your safari questions, travel tips, packing advice, safety, and what to expect on your African journey.',
  url: '/faqs',
  keywords: [
    'African safari FAQ',
    'Are there real cultural villages to visit in Rwanda?',
    'Is gorilla trekking in Rwanda eco-friendly?',
    'Are there community-based tourism programs in Uganda?',
    'How to experience authentic Maasai culture in Tanzania?',
    'Can I support conservation efforts while visiting Tanzania?',
    'gorilla trekking FAQ',
    'Tanzania safari guide',
    'Rwanda travel requirements',
    'Uganda safari questions',
    'safari booking process',
    'African travel tips',
    'safari preparation guide',
    'wildlife tour FAQ',
    'safari cost questions',
    'travel insurance Africa',
    'visa requirements Africa',
    'safari packing list',
    'best time safari Africa'
  ],
  type: 'website'
});


const Faqs = () => {
  return (
    <div>
      <MegaMenuHeader />
      <main>
        <FaqsClient />
      </main>
      <TravelFooter />
    </div>
  );
};

export default Faqs;