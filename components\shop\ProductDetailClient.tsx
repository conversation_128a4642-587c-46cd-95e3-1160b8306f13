/* eslint-disable @next/next/no-img-element */
'use client';
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { getProductBySlug } from '@/components/data/productData';
import StructuredData from '@/components/common/StructuredData';
import { generateProductSchema, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

interface Product {
    slug: string;
    title: string;
    price: number;
    discount: number;
    description: string;
    stock?: number;
    monthlyPrice?: number;
    images?: string[];
    category: string;
    createdAt?: string;
    coverImage?: string;
}

const ProductDetailClient: React.FC = () => {
    const params = useParams();
    const router = useRouter();
    const slug = params?.slug as string;
    const [product, setProduct] = useState<Product | null>(null);
    const [selectedImage, setSelectedImage] = useState(0);
    const [selectedColor] = useState<string | null>(null);
    const [quantity] = useState(1);

    useEffect(() => {
        const productData = getProductBySlug(slug);
        if (productData) {
            setProduct(productData as Product);
        } else {
            router.push('/shop'); // Redirect if product not found
        }
    }, [slug, router]);

    if (!product) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h2>
                    <p className="text-gray-600 mb-6">The product you're looking for doesn't exist.</p>
                    <button
                        onClick={() => router.push('/shop')}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Back to Shop
                    </button>
                </div>
            </div>
        );
    }

    const finalPrice = product.price - (product.price * product.discount / 100);
    const images = product.images || [];
    const displayImages = product.coverImage ? [product.coverImage, ...images] : images;

    const breadcrumbSchema = generateBreadcrumbSchema([
        { name: 'Home', url: '/' },
        { name: 'Shop', url: '/shop' },
        { name: product.title, url: `/shop/product/${product.slug}` }
    ]);

    const productSchema = generateProductSchema({
        name: product.title,
        description: product.description,
        image: product.coverImage || product.images?.[0] || '',
        price: finalPrice,
        currency: 'USD',
        availability: product.stock && product.stock > 0 ? 'InStock' : 'OutOfStock',
        category: product.category,
        brand: defaultSEO.siteName,
        url: `${defaultSEO.siteUrl}/shop/product/${product.slug}`,
        sku: product.slug
    });

    return (
        <div className="min-h-screen bg-gray-50">
            <StructuredData data={[breadcrumbSchema, productSchema]} />
            
            <div className="max-w-7xl mx-auto px-4 py-8">
                {/* Breadcrumb */}
                <nav className="flex mb-8" aria-label="Breadcrumb">
                    <ol className="inline-flex items-center space-x-1 md:space-x-3">
                        <li className="inline-flex items-center">
                            <a href="/" className="text-gray-700 hover:text-blue-600">Home</a>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <span className="mx-2 text-gray-400">/</span>
                                <a href="/shop" className="text-gray-700 hover:text-blue-600">Shop</a>
                            </div>
                        </li>
                        <li aria-current="page">
                            <div className="flex items-center">
                                <span className="mx-2 text-gray-400">/</span>
                                <span className="text-gray-500">{product.title}</span>
                            </div>
                        </li>
                    </ol>
                </nav>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Product Images */}
                    <div className="space-y-4">
                        {displayImages.length > 0 && (
                            <>
                                <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                                    <img
                                        src={displayImages[selectedImage]}
                                        alt={product.title}
                                        className="w-full h-full object-cover"
                                    />
                                </div>
                                {displayImages.length > 1 && (
                                    <div className="grid grid-cols-4 gap-2">
                                        {displayImages.map((image, index) => (
                                            <button
                                                key={index}
                                                onClick={() => setSelectedImage(index)}
                                                className={`aspect-square bg-gray-200 rounded-lg overflow-hidden border-2 ${
                                                    selectedImage === index ? 'border-blue-600' : 'border-transparent'
                                                }`}
                                            >
                                                <img
                                                    src={image}
                                                    alt={`${product.title} ${index + 1}`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </>
                        )}
                    </div>

                    {/* Product Info */}
                    <div className="space-y-6">
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.title}</h1>
                            <p className="text-gray-600">{product.category}</p>
                        </div>

                        <div className="flex items-center space-x-4">
                            <span className="text-3xl font-bold text-gray-900">${finalPrice.toFixed(2)}</span>
                            {product.discount > 0 && (
                                <>
                                    <span className="text-xl text-gray-500 line-through">${product.price.toFixed(2)}</span>
                                    <span className="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                                        {product.discount}% OFF
                                    </span>
                                </>
                            )}
                        </div>

                        <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                            <p className="text-gray-600">{product.description}</p>
                        </div>

                        {product.stock !== undefined && (
                            <div>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                    product.stock > 0 
                                        ? 'bg-green-100 text-green-800' 
                                        : 'bg-red-100 text-red-800'
                                }`}>
                                    {product.stock > 0 ? `${product.stock} in stock` : 'Out of stock'}
                                </span>
                            </div>
                        )}

                        <div className="space-y-4">
                            <button
                                disabled={product.stock === 0}
                                className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                                    product.stock === 0
                                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        : 'bg-blue-600 text-white hover:bg-blue-700'
                                }`}
                            >
                                {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                            </button>
                            
                            <button
                                onClick={() => router.push('/shop')}
                                className="w-full py-3 px-6 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
                            >
                                Continue Shopping
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProductDetailClient;
