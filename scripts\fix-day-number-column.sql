-- Fix for Mini Package Itinerary day_number Column Issue
-- This script removes the day_number column that is causing NOT NULL constraint violations
-- when creating/updating mini package itineraries with hour_number only

-- Step 1: Check current table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sas_mini_package_itinerary' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 2: Remove the day_number column (this is the main fix)
ALTER TABLE public.sas_mini_package_itinerary DROP COLUMN IF EXISTS day_number;

-- Step 3: Verify the column was removed
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sas_mini_package_itinerary' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 4: Test that we can now insert records with only hour_number
-- (This will fail due to foreign key constraint with dummy UUID, but should not mention day_number)
-- INSERT INTO public.sas_mini_package_itinerary (
--     mini_package_id,
--     hour_number,
--     title,
--     description,
--     sort_order
-- ) VALUES (
--     '00000000-0000-0000-0000-000000000000',
--     1,
--     'Test Hour',
--     'Test Description',
--     0
-- );

-- Step 5: Show success message
DO $$
BEGIN
    RAISE NOTICE '✅ Mini Package Itinerary day_number Column Fix Complete!';
    RAISE NOTICE '🗑️  Removed day_number column from sas_mini_package_itinerary';
    RAISE NOTICE '✅ Table now supports hour_number only (as intended for mini packages)';
    RAISE NOTICE '🚀 Upload API should now work correctly with hour-based itineraries';
    RAISE NOTICE '';
    RAISE NOTICE '🔗 Ready to test: /admin/mini-packages/add';
END $$;
