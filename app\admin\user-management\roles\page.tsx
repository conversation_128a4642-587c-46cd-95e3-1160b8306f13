'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, Shield, Plus, Edit, Trash2, Users, Check, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  color: string;
  isSystem: boolean;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function RolesManagementPage() {
  const router = useRouter();
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockPermissions: Permission[] = [
      // Content Management
      { id: 'blog.create', name: 'Create Blog Posts', description: 'Create new blog posts', category: 'Content' },
      { id: 'blog.edit', name: 'Edit Blog Posts', description: 'Edit existing blog posts', category: 'Content' },
      { id: 'blog.delete', name: 'Delete Blog Posts', description: 'Delete blog posts', category: 'Content' },
      { id: 'packages.create', name: 'Create Packages', description: 'Create new travel packages', category: 'Content' },
      { id: 'packages.edit', name: 'Edit Packages', description: 'Edit existing packages', category: 'Content' },
      { id: 'packages.delete', name: 'Delete Packages', description: 'Delete travel packages', category: 'Content' },
      { id: 'destinations.create', name: 'Create Destinations', description: 'Create new destinations', category: 'Content' },
      { id: 'destinations.edit', name: 'Edit Destinations', description: 'Edit existing destinations', category: 'Content' },
      
      // Booking Management
      { id: 'bookings.view', name: 'View Bookings', description: 'View all bookings', category: 'Bookings' },
      { id: 'bookings.edit', name: 'Edit Bookings', description: 'Modify booking details', category: 'Bookings' },
      { id: 'bookings.cancel', name: 'Cancel Bookings', description: 'Cancel customer bookings', category: 'Bookings' },
      
      // Customer Management
      { id: 'customers.view', name: 'View Customers', description: 'View customer information', category: 'Customers' },
      { id: 'customers.edit', name: 'Edit Customers', description: 'Edit customer details', category: 'Customers' },
      
      // Travel Agent B2B
      { id: 'travel-agent.view', name: 'View Partners', description: 'View travel agent partners', category: 'B2B' },
      { id: 'travel-agent.manage', name: 'Manage Partners', description: 'Manage partner accounts', category: 'B2B' },
      { id: 'travel-agent.messages', name: 'Partner Messages', description: 'Communicate with partners', category: 'B2B' },
      
      // System Administration
      { id: 'users.view', name: 'View Users', description: 'View system users', category: 'System' },
      { id: 'users.create', name: 'Create Users', description: 'Create new users', category: 'System' },
      { id: 'users.edit', name: 'Edit Users', description: 'Edit user accounts', category: 'System' },
      { id: 'users.delete', name: 'Delete Users', description: 'Delete user accounts', category: 'System' },
      { id: 'roles.manage', name: 'Manage Roles', description: 'Manage user roles and permissions', category: 'System' },
      { id: 'system.logs', name: 'System Logs', description: 'View system logs', category: 'System' },
      { id: 'system.settings', name: 'System Settings', description: 'Modify system settings', category: 'System' },
      
      // Technical Support
      { id: 'technical.support', name: 'Technical Support', description: 'Provide technical support', category: 'Support' }
    ];

    const mockRoles: Role[] = [
      {
        id: 'admin',
        name: 'Administrator',
        description: 'Full system access and user management',
        permissions: ['all'],
        userCount: 1,
        color: 'bg-red-100 text-red-800',
        isSystem: true
      },
      {
        id: 'content-creator',
        name: 'Content Creator',
        description: 'Manage blog posts, packages, and destinations',
        permissions: ['blog.create', 'blog.edit', 'packages.create', 'packages.edit', 'destinations.create', 'destinations.edit'],
        userCount: 3,
        color: 'bg-blue-100 text-blue-800',
        isSystem: false
      },
      {
        id: 'tour-specialist',
        name: 'Tour Specialist',
        description: 'Manage bookings, customers, and travel operations',
        permissions: ['bookings.view', 'bookings.edit', 'customers.view', 'customers.edit', 'travel-agent.view'],
        userCount: 5,
        color: 'bg-green-100 text-green-800',
        isSystem: false
      },
      {
        id: 'it-support',
        name: 'IT Support',
        description: 'System maintenance and technical support',
        permissions: ['system.logs', 'system.settings', 'users.view', 'technical.support'],
        userCount: 2,
        color: 'bg-purple-100 text-purple-800',
        isSystem: false
      }
    ];

    setPermissions(mockPermissions);
    setRoles(mockRoles);
  }, []);

  const permissionCategories = permissions.reduce((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  const RoleCard = ({ role }: { role: Role }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center mb-2">
            <h3 className="text-lg font-semibold text-gray-900 mr-3">{role.name}</h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${role.color}`}>
              {role.userCount} user{role.userCount !== 1 ? 's' : ''}
            </span>
            {role.isSystem && (
              <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                System Role
              </span>
            )}
          </div>
          <p className="text-gray-600 text-sm mb-4">{role.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setEditingRole(role)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Edit Role"
          >
            <Edit size={16} />
          </button>
          {!role.isSystem && (
            <button
              className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete Role"
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      </div>

      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-2">Permissions:</h4>
        {role.permissions.includes('all') ? (
          <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">
            All Permissions
          </span>
        ) : (
          <div className="flex flex-wrap gap-1">
            {role.permissions.slice(0, 5).map(permissionId => {
              const permission = permissions.find(p => p.id === permissionId);
              return permission ? (
                <span key={permissionId} className="inline-block px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                  {permission.name}
                </span>
              ) : null;
            })}
            {role.permissions.length > 5 && (
              <span className="inline-block px-2 py-1 bg-gray-200 text-gray-600 rounded text-xs">
                +{role.permissions.length - 5} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Shield className="mr-3 text-purple-600" />
                  Roles & Permissions
                </h1>
                <p className="text-gray-600 mt-1">Manage user roles and their permissions</p>
              </div>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
            >
              <Plus size={16} className="mr-2" />
              Create Role
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center">
                <Shield className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-purple-600">Total Roles</p>
                  <p className="text-2xl font-bold text-purple-900">{roles.length}</p>
                </div>
              </div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-blue-600">Total Users</p>
                  <p className="text-2xl font-bold text-blue-900">{roles.reduce((sum, role) => sum + role.userCount, 0)}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <Check className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-green-600">Permissions</p>
                  <p className="text-2xl font-bold text-green-900">{permissions.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Roles Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {roles.map(role => (
              <RoleCard key={role.id} role={role} />
            ))}
          </div>

          {/* Permissions Reference */}
          <div className="mt-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Available Permissions</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(permissionCategories).map(([category, categoryPermissions]) => (
                <div key={category} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <h3 className="font-semibold text-gray-900 mb-3">{category}</h3>
                  <div className="space-y-2">
                    {categoryPermissions.map(permission => (
                      <div key={permission.id} className="text-sm">
                        <div className="font-medium text-gray-700">{permission.name}</div>
                        <div className="text-gray-500 text-xs">{permission.description}</div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Create/Edit Role Modal */}
      {(showCreateModal || editingRole) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900">
                  {editingRole ? 'Edit Role' : 'Create New Role'}
                </h2>
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingRole(null);
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role Name</label>
                  <input
                    type="text"
                    defaultValue={editingRole?.name || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Enter role name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    defaultValue={editingRole?.description || ''}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Describe this role"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-4">Permissions</label>
                  {Object.entries(permissionCategories).map(([category, categoryPermissions]) => (
                    <div key={category} className="mb-6">
                      <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                      <div className="space-y-2">
                        {categoryPermissions.map(permission => (
                          <label key={permission.id} className="flex items-center">
                            <input
                              type="checkbox"
                              defaultChecked={editingRole?.permissions.includes(permission.id)}
                              className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                            />
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-700">{permission.name}</div>
                              <div className="text-xs text-gray-500">{permission.description}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </form>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setEditingRole(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--btn)] transition-colors"
              >
                {editingRole ? 'Update Role' : 'Create Role'}
              </button>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
