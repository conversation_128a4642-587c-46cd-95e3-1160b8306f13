// Test script to check package data structure
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testPackageStructure() {
  try {
    const testSlug = '4-day-rwenzori-hiking-cultural-experience';
    
    console.log('--- Testing Package Data Structure ---');
    console.log(`Testing slug: ${testSlug}`);
    
    // Test the full query that's failing
    console.log('\n1. Testing full complex query...');
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select(`
        *,
        sas_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_package_itinerary (
          id,
          day_number,
          title,
          description,
          activities,
          accommodation,
          meals,
          sort_order
        ),
        sas_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', testSlug)
      .eq('status', 'active')
      .single();
    
    if (packageError) {
      console.error('Complex query failed:', packageError);
      
      // Try simpler query
      console.log('\n2. Testing simple query...');
      const { data: simpleData, error: simpleError } = await supabase
        .from('sas_packages')
        .select('*')
        .eq('slug', testSlug)
        .eq('status', 'active')
        .single();
      
      if (simpleError) {
        console.error('Simple query also failed:', simpleError);
      } else {
        console.log('✓ Simple query successful');
        console.log('Package data:', simpleData);
        console.log('Pricing structure:', {
          solo: simpleData.pricing_solo,
          honeymoon: simpleData.pricing_honeymoon,
          family: simpleData.pricing_family,
          group: simpleData.pricing_group
        });
      }
    } else {
      console.log('✓ Complex query successful');
      console.log('Package title:', packageData.title);
      console.log('Content blocks:', packageData.sas_package_content_blocks?.length || 0);
      console.log('Itinerary items:', packageData.sas_package_itinerary?.length || 0);
      console.log('Images:', packageData.sas_package_images?.length || 0);
      console.log('Pricing structure:', {
        solo: packageData.pricing_solo,
        honeymoon: packageData.pricing_honeymoon,
        family: packageData.pricing_family,
        group: packageData.pricing_group
      });
    }
    
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

testPackageStructure();
