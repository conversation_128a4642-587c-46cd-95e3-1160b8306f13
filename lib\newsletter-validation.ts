// Simple HTML sanitization without DOMPurify for now
// import DOMPurify from 'isomorphic-dompurify';

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Rate limiting storage (in production, use Redis or database)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Validate email format
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  // Basic format validation
  if (!EMAIL_REGEX.test(email)) {
    return false;
  }
  
  // Length validation
  if (email.length > 254) {
    return false;
  }
  
  // Local part validation (before @)
  const [localPart, domain] = email.split('@');
  if (localPart.length > 64 || domain.length > 253) {
    return false;
  }
  
  return true;
}

// Sanitize email address
export function sanitizeEmail(email: string): string {
  if (!email || typeof email !== 'string') {
    return '';
  }
  
  return email.trim().toLowerCase();
}

// Validate and sanitize newsletter content
export function validateNewsletterContent(content: string, contentType: 'html' | 'text'): {
  isValid: boolean;
  sanitizedContent: string;
  errors: string[];
} {
  const errors: string[] = [];
  let sanitizedContent = content;
  
  // Basic validation
  if (!content || typeof content !== 'string') {
    errors.push('Content is required');
    return { isValid: false, sanitizedContent: '', errors };
  }
  
  // Length validation
  if (content.length > 100000) { // 100KB limit
    errors.push('Content is too long (maximum 100KB)');
  }
  
  // Content type specific validation and sanitization
  if (contentType === 'html') {
    try {
      // Basic HTML sanitization - remove dangerous elements
      sanitizedContent = content
        .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
        .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // Remove iframe tags
        .replace(/<object[^>]*>.*?<\/object>/gi, '') // Remove object tags
        .replace(/<embed[^>]*>/gi, '') // Remove embed tags
        .replace(/javascript:/gi, '') // Remove javascript: protocols
        .replace(/on\w+\s*=/gi, ''); // Remove event handlers like onclick, onload, etc.

      // Check for potentially malicious content
      if (content.includes('<script') || content.includes('javascript:') || content.includes('onload=')) {
        errors.push('Content contains potentially unsafe elements');
      }
    } catch (error) {
      errors.push('Invalid HTML content');
    }
  } else {
    // Plain text sanitization
    sanitizedContent = content
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .trim();
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedContent,
    errors
  };
}

// Validate newsletter subject
export function validateNewsletterSubject(subject: string): {
  isValid: boolean;
  sanitizedSubject: string;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!subject || typeof subject !== 'string') {
    errors.push('Subject is required');
    return { isValid: false, sanitizedSubject: '', errors };
  }
  
  const sanitizedSubject = subject.trim();
  
  // Length validation
  if (sanitizedSubject.length === 0) {
    errors.push('Subject cannot be empty');
  } else if (sanitizedSubject.length > 200) {
    errors.push('Subject is too long (maximum 200 characters)');
  }
  
  // Check for spam-like content
  const spamKeywords = ['FREE', 'URGENT', 'WINNER', 'CONGRATULATIONS', '!!!', '$$$'];
  const upperCaseSubject = sanitizedSubject.toUpperCase();
  const spamScore = spamKeywords.reduce((score, keyword) => {
    return score + (upperCaseSubject.includes(keyword) ? 1 : 0);
  }, 0);
  
  if (spamScore >= 2) {
    errors.push('Subject may be flagged as spam');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedSubject,
    errors
  };
}

// Rate limiting for subscription endpoints
export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 5, 
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; resetTime: number } {
  const now = Date.now();
  const key = `newsletter_${identifier}`;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    });
    return { allowed: true, resetTime: now + windowMs };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime };
  }
  
  // Increment count
  rateLimitStore.set(key, {
    count: current.count + 1,
    resetTime: current.resetTime
  });
  
  return { allowed: true, resetTime: current.resetTime };
}

// Clean up expired rate limit entries
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Validate SMTP settings
export function validateSMTPSettings(settings: {
  smtp_email: string;
  smtp_password: string;
  smtp_host: string;
  smtp_port: number;
  smtp_secure: boolean;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Email validation
  if (!validateEmail(settings.smtp_email)) {
    errors.push('Invalid SMTP email address');
  }
  
  // Password validation
  if (!settings.smtp_password || settings.smtp_password.length < 8) {
    errors.push('SMTP password must be at least 8 characters long');
  }
  
  // Host validation
  if (!settings.smtp_host || settings.smtp_host.length < 3) {
    errors.push('Invalid SMTP host');
  }
  
  // Port validation
  if (!settings.smtp_port || settings.smtp_port < 1 || settings.smtp_port > 65535) {
    errors.push('Invalid SMTP port (must be between 1 and 65535)');
  }
  
  // Common secure ports validation
  const securePorts = [465, 587, 993, 995];
  if (settings.smtp_secure && !securePorts.includes(settings.smtp_port)) {
    errors.push('Secure connection enabled but port is not typically secure');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Generate secure unsubscribe token
export function generateUnsubscribeToken(email: string): string {
  const crypto = require('crypto');
  const secret = process.env.NEWSLETTER_SECRET || 'swift-africa-safaris-newsletter-2024';
  
  return crypto
    .createHmac('sha256', secret)
    .update(email + Date.now().toString())
    .digest('hex')
    .substring(0, 32);
}

// Verify unsubscribe token
export function verifyUnsubscribeToken(email: string, token: string): boolean {
  // In a production environment, you would store and verify tokens in the database
  // For now, we'll use a simple validation based on email format
  return !!(token && token.length === 32 && validateEmail(email));
}

// Sanitize HTML for email content
export function sanitizeEmailHTML(html: string): string {
  // Basic HTML sanitization without external dependencies
  return html
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // Remove iframe tags
    .replace(/<object[^>]*>.*?<\/object>/gi, '') // Remove object tags
    .replace(/<embed[^>]*>/gi, '') // Remove embed tags
    .replace(/<form[^>]*>.*?<\/form>/gi, '') // Remove form tags
    .replace(/<input[^>]*>/gi, '') // Remove input tags
    .replace(/<button[^>]*>.*?<\/button>/gi, '') // Remove button tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/style\s*=\s*["'][^"']*expression[^"']*["']/gi, '') // Remove CSS expressions
    .trim();
}
