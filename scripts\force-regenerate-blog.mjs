#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function forceRegenerateBlogPosts() {
  console.log('🔄 Force regenerating all blog posts...\n');
  
  try {
    // Get all published blog posts
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status, published_at, updated_at')
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('❌ Error fetching blog posts:', error);
      return;
    }
    
    if (!posts || posts.length === 0) {
      console.log('❌ No published blog posts found');
      return;
    }
    
    console.log(`✅ Found ${posts.length} published blog posts:`);
    posts.forEach((post, index) => {
      console.log(`   ${index + 1}. ${post.title}`);
      console.log(`      Slug: ${post.slug}`);
      console.log(`      Published: ${post.published_at}`);
      console.log(`      Updated: ${post.updated_at}`);
      console.log('');
    });
    
    // Update the updated_at timestamp to force ISR regeneration
    console.log('🔄 Updating timestamps to force ISR regeneration...\n');
    
    for (const post of posts) {
      const { error: updateError } = await supabase
        .from('sas_blog_posts')
        .update({ 
          updated_at: new Date().toISOString(),
          // Add a small metadata update to ensure the change is detected
          seo_keywords: post.seo_keywords || []
        })
        .eq('id', post.id);
      
      if (updateError) {
        console.error(`❌ Error updating ${post.slug}:`, updateError);
      } else {
        console.log(`✅ Updated ${post.slug}`);
      }
    }
    
    console.log('\n🎉 All blog posts have been updated!');
    console.log('📝 Next steps:');
    console.log('   1. Run: npm run build');
    console.log('   2. Deploy to production');
    console.log('   3. All blog posts should now be accessible');
    
  } catch (error) {
    console.error('❌ Error during regeneration:', error);
  }
}

forceRegenerateBlogPosts().catch(console.error);
