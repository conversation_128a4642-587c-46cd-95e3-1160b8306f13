import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch published mini packages for frontend
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '12');
    const location = searchParams.get('location');
    const category = searchParams.get('category');

    let query = supabase
      .from('sas_mini_packages')
      .select(`
        id,
        title,
        slug,
        location,
        duration,
        pricing_solo,
        pricing_honeymoon,
        pricing_family,
        pricing_group,
        image_url,
        image_alt,
        category,
        status
      `)
      .eq('status', 'published')
      .order('created_at', { ascending: false });

    // Apply filters
    if (location && location !== 'all') {
      query = query.eq('location', location);
    }

    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    // Apply limit
    if (limit > 0) {
      query = query.limit(limit);
    }

    const { data: miniPackages, error } = await query;

    if (error) {
      console.error('Error fetching mini packages:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch mini packages' },
        { status: 500 }
      );
    }

    // Transform data to match the expected format
    const transformedData = (miniPackages || []).map(pkg => ({
      id: pkg.id,
      title: pkg.title,
      slug: pkg.slug,
      location: pkg.location,
      duration: pkg.duration,
      image: pkg.image_url,
      alt: pkg.image_alt || pkg.title,
      category: pkg.category,
      // Calculate lowest price
      price: Math.min(
        pkg.pricing_solo || Infinity,
        pkg.pricing_honeymoon || Infinity,
        pkg.pricing_family || Infinity,
        pkg.pricing_group || Infinity
      )
    }));

    return NextResponse.json({
      success: true,
      data: transformedData
    });

  } catch (error) {
    console.error('Error in GET /api/mini-packages:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
