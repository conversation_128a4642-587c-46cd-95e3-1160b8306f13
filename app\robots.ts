import { MetadataRoute } from 'next'
import { defaultSEO } from '@/lib/seo'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = defaultSEO.siteUrl

  return {
    rules: [
      // General crawling rules for all search engines
      {
        userAgent: '*',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
          '/faqs',
          '/journey',
          '/community',
          '/shop',
          '/privacy-policy',
          '/booking-policy',
        ],
        disallow: [
          // Admin and authentication areas
          '/admin/',
          '/partner/',
          '/login',
          '/api/admin/',
          '/api/auth/',

          // Private and sensitive areas
          '/private/',
          '/debug/',
          '/test-*',

          // Technical files and directories
          '/_next/',
          '/.next/',
          '/node_modules/',

          // API endpoints (except public ones)
          '/api/upload',
          '/api/revalidate',
          '/api/csrf-token',
          '/api/test-*',
          '/api/debug',

          // File types that shouldn't be crawled
          '*.json$',
          '*.xml$',
          '*.txt$',
          '*.log$',
          '*.env*',
          '*.config.*',

          // Dynamic routes with parameters that might cause issues
          '/package?*',
          '/blog?*',
          '/search?*',

          // Temporary or staging content
          '/temp/',
          '/staging/',
          '/draft/',
        ],
        crawlDelay: 1, // 1 second delay to be respectful of server resources
      },

      // Specific rules for Google's main crawler
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
          '/faqs',
          '/journey',
          '/community',
          '/shop',
          '/privacy-policy',
          '/booking-policy',
          '/sitemap.xml',
          '/hreflang-sitemap.xml',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/login',
          '/api/admin/',
          '/api/auth/',
          '/private/',
          '/debug/',
          '/test-*',
          '/_next/',
          '/api/upload',
          '/api/revalidate',
          '/api/csrf-token',
        ],
        // No crawl delay for Googlebot as it's generally well-behaved
      },

      // Specific rules for Bing
      {
        userAgent: 'Bingbot',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
          '/faqs',
          '/journey',
          '/community',
          '/shop',
          '/privacy-policy',
          '/booking-policy',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/login',
          '/api/',
          '/private/',
          '/debug/',
          '/_next/',
        ],
        crawlDelay: 2, // Slightly more conservative for Bing
      },

      // Rules for social media crawlers
      {
        userAgent: 'facebookexternalhit',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
          '/private/',
        ],
      },

      {
        userAgent: 'Twitterbot',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
          '/private/',
        ],
      },

      // Rules for other search engines
      {
        userAgent: 'DuckDuckBot',
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/mini-package/',
          '/iconic/',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
          '/private/',
          '/debug/',
        ],
        crawlDelay: 2,
      },

      // Block aggressive or problematic crawlers
      {
        userAgent: [
          'AhrefsBot',
          'SemrushBot',
          'MJ12bot',
          'DotBot',
          'BLEXBot',
          'YandexBot', // Can be aggressive
          'PetalBot',
          'CCBot',
          'GPTBot', // AI training bots
          'ChatGPT-User',
          'CCBot',
          'anthropic-ai',
        ],
        disallow: '/',
        crawlDelay: 86400, // 24 hours - effectively blocking
      },

      // Allow specific crawlers for performance monitoring
      {
        userAgent: [
          'GooglePageSpeedInsights',
          'GTmetrix',
          'Pingdom',
          'UptimeRobot',
        ],
        allow: [
          '/',
          '/package/',
          '/blog/',
          '/about',
          '/contact',
        ],
        disallow: [
          '/admin/',
          '/partner/',
          '/api/',
        ],
      },
    ],

    // Multiple sitemap references for comprehensive SEO
    sitemap: [
      `${baseUrl}/sitemap.xml`,           // Main sitemap with all content
      `${baseUrl}/hreflang-sitemap.xml`,  // International SEO sitemap
    ],

    // Specify the canonical host
    host: baseUrl,
  }
}
