/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useRef } from "react";
import { Info, Save, Upload, Users, Award, Target } from "lucide-react";

interface AboutPageFormData {
  pageTitle: string;
  pageSubtitle: string;
  heroDescription: string;
  heroImage: string;
  missionStatement: string;
  visionStatement: string;
  companyStory: string;
  teamDescription: string;
  whyChooseUs: string[];
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function AboutPageManagement() {
  const [activeTab, setActiveTab] = useState("content");
  const [formData, setFormData] = useState<AboutPageFormData>({
    pageTitle: "About Swift Africa Safaris",
    pageSubtitle: "Your Trusted Safari Partner Since 2010",
    heroDescription: "We are passionate about creating unforgettable African safari experiences that connect you with the incredible wildlife, stunning landscapes, and rich cultures of East Africa.",
    heroImage: "",
    missionStatement: "To provide authentic, sustainable, and transformative safari experiences that create lasting memories while supporting local communities and wildlife conservation.",
    visionStatement: "To be the leading safari company in East Africa, recognized for our commitment to excellence, sustainability, and authentic cultural experiences.",
    companyStory: "Founded in 2010 by a team of passionate wildlife enthusiasts and local guides, Swift Africa Safaris has grown from a small local operation to one of Tanzania's most trusted safari companies. Our deep knowledge of East African wildlife and commitment to sustainable tourism has earned us the trust of thousands of travelers from around the world.",
    teamDescription: "Our team consists of experienced local guides, wildlife experts, and hospitality professionals who are passionate about sharing the wonders of Africa with our guests.",
    whyChooseUs: [
      "Expert local guides with extensive wildlife knowledge",
      "Sustainable tourism practices",
      "Customized safari experiences",
      "24/7 customer support",
      "Competitive pricing with no hidden costs"
    ],
    seoTitle: "About Swift Africa Safaris - Expert Safari Company in Tanzania",
    seoDescription: "Learn about Swift Africa Safaris, your trusted safari partner in Tanzania. Discover our mission, vision, and commitment to providing authentic African safari experiences.",
    seoKeywords: "about swift africa safaris, Tanzania safari company, safari experts, wildlife guides, sustainable tourism"
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [newReason, setNewReason] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof AboutPageFormData, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you would upload to a server
      const imageUrl = URL.createObjectURL(file);
      setFormData((prev) => ({ ...prev, heroImage: imageUrl }));
    }
  };

  const addReason = () => {
    if (newReason.trim()) {
      setFormData(prev => ({
        ...prev,
        whyChooseUs: [...prev.whyChooseUs, newReason.trim()]
      }));
      setNewReason("");
    }
  };

  const removeReason = (index: number) => {
    setFormData(prev => ({
      ...prev,
      whyChooseUs: prev.whyChooseUs.filter((_, i) => i !== index)
    }));
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.pageTitle || formData.pageTitle.length < 5) {
      newErrors.pageTitle = "Please enter a valid page title (5+ characters)";
    }

    if (!formData.pageSubtitle || formData.pageSubtitle.length < 5) {
      newErrors.pageSubtitle = "Please enter a valid subtitle (5+ characters)";
    }

    if (!formData.heroDescription || formData.heroDescription.length < 20) {
      newErrors.heroDescription = "Please enter a valid description (20+ characters)";
    }

    if (!formData.missionStatement || formData.missionStatement.length < 20) {
      newErrors.missionStatement = "Please enter a valid mission statement (20+ characters)";
    }

    if (!formData.visionStatement || formData.visionStatement.length < 20) {
      newErrors.visionStatement = "Please enter a valid vision statement (20+ characters)";
    }

    if (!formData.seoTitle || formData.seoTitle.length < 10) {
      newErrors.seoTitle = "Please enter a valid SEO title (10+ characters)";
    }

    if (!formData.seoDescription || formData.seoDescription.length < 50) {
      newErrors.seoDescription = "Please enter a valid SEO description (50+ characters)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Form submitted successfully:", formData);
      alert("About page settings saved successfully!");
    }
  };

  const tabs = [
    { id: "content", label: "Page Content" },
    { id: "company", label: "Company Info" },
    { id: "seo", label: "SEO Settings" },
  ];

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Info className="mr-3 text-blue-600" />
                About Page Management
              </h1>
              <p className="text-gray-600 mt-1">Manage your about page content and company information</p>
            </div>
            <button
              className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? "border-[var(--accent)] text-[var(--accent)]"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <form onSubmit={handleSubmit} className="p-6">
                {/* Page Content Tab */}
                {activeTab === "content" && (
                  <div className="space-y-6">
                    {/* Page Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Title *
                      </label>
                      <input
                        type="text"
                        value={formData.pageTitle}
                        onChange={(e) => handleInputChange("pageTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your about page title"
                      />
                      {errors.pageTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageTitle}</p>
                      )}
                    </div>

                    {/* Page Subtitle */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Subtitle *
                      </label>
                      <input
                        type="text"
                        value={formData.pageSubtitle}
                        onChange={(e) => handleInputChange("pageSubtitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageSubtitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your about page subtitle"
                      />
                      {errors.pageSubtitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageSubtitle}</p>
                      )}
                    </div>

                    {/* Hero Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Description *
                      </label>
                      <textarea
                        value={formData.heroDescription}
                        onChange={(e) => handleInputChange("heroDescription", e.target.value)}
                        rows={4}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.heroDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your hero section description"
                      />
                      {errors.heroDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.heroDescription}</p>
                      )}
                    </div>

                    {/* Hero Image Upload */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Image
                      </label>
                      <div
                        onClick={handleFileSelect}
                        className="relative border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-[var(--accent)] transition-colors cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div className="text-center">
                          {formData.heroImage ? (
                            <div className="space-y-2">
                              <img
                                src={formData.heroImage}
                                alt="Hero Image Preview"
                                className="mx-auto h-32 w-auto object-cover rounded-lg"
                              />
                              <p className="text-sm text-gray-600">
                                Click to change image
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto h-12 w-12 text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">
                                  Click to upload hero image
                                </p>
                                <p className="text-xs text-gray-500">
                                  PNG, JPG, GIF up to 10MB
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Company Info Tab */}
                {activeTab === "company" && (
                  <div className="space-y-6">
                    {/* Mission Statement */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Target size={16} className="inline mr-1" />
                        Mission Statement *
                      </label>
                      <textarea
                        value={formData.missionStatement}
                        onChange={(e) => handleInputChange("missionStatement", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.missionStatement ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your company mission statement"
                      />
                      {errors.missionStatement && (
                        <p className="mt-1 text-sm text-red-600">{errors.missionStatement}</p>
                      )}
                    </div>

                    {/* Vision Statement */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Award size={16} className="inline mr-1" />
                        Vision Statement *
                      </label>
                      <textarea
                        value={formData.visionStatement}
                        onChange={(e) => handleInputChange("visionStatement", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.visionStatement ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your company vision statement"
                      />
                      {errors.visionStatement && (
                        <p className="mt-1 text-sm text-red-600">{errors.visionStatement}</p>
                      )}
                    </div>

                    {/* Company Story */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Company Story
                      </label>
                      <textarea
                        value={formData.companyStory}
                        onChange={(e) => handleInputChange("companyStory", e.target.value)}
                        rows={5}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Tell your company's story"
                      />
                    </div>

                    {/* Team Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Users size={16} className="inline mr-1" />
                        Team Description
                      </label>
                      <textarea
                        value={formData.teamDescription}
                        onChange={(e) => handleInputChange("teamDescription", e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Describe your team"
                      />
                    </div>

                    {/* Why Choose Us */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Why Choose Us
                      </label>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={newReason}
                            onChange={(e) => setNewReason(e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                            placeholder="Add a reason why customers should choose you"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addReason())}
                          />
                          <button
                            type="button"
                            onClick={addReason}
                            className="px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--btn)] transition-colors"
                          >
                            Add
                          </button>
                        </div>
                        <div className="space-y-2">
                          {formData.whyChooseUs.map((reason, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <span className="text-gray-700">{reason}</span>
                              <button
                                type="button"
                                onClick={() => removeReason(index)}
                                className="text-red-600 hover:text-red-800 font-medium"
                              >
                                Remove
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* SEO Settings Tab */}
                {activeTab === "seo" && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Title *
                      </label>
                      <input
                        type="text"
                        value={formData.seoTitle}
                        onChange={(e) => handleInputChange("seoTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO title for about page"
                      />
                      {errors.seoTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoTitle}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoTitle.length}/70 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Description *
                      </label>
                      <textarea
                        value={formData.seoDescription}
                        onChange={(e) => handleInputChange("seoDescription", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO description for about page"
                      />
                      {errors.seoDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoDescription}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoDescription.length}/160 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Keywords
                      </label>
                      <input
                        type="text"
                        value={formData.seoKeywords}
                        onChange={(e) => handleInputChange("seoKeywords", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Enter keywords separated by commas"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Separate keywords with commas
                      </p>
                    </div>

                    {/* SEO Preview */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Search Engine Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="space-y-1">
                          <div className="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                            {formData.seoTitle || "Your SEO Title"}
                          </div>
                          <div className="text-green-700 text-sm">
                            https://swiftafricasafaris.com/about
                          </div>
                          <div className="text-gray-600 text-sm">
                            {formData.seoDescription || "Your SEO description will appear here"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
