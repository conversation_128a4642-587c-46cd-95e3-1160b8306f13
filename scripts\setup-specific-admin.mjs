#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupSpecificAdmin() {
  console.log('� Creating Admin User with Specific Credentials...\n');

  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123';
  const adminName = 'Admin User';

  try {
    // First, create the user in Supabase Auth
    console.log('1. Creating user in Supabase Auth...');

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: adminEmail,
      password: adminPassword,
      options: {
        data: {
          name: adminName,
          role: 'admin',
          status: 'active'
        }
      }
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('ℹ️  User already exists in Auth, checking profile...');
      } else {
        console.error('❌ Auth creation failed:', authError.message);
        return;
      }
    } else {
      console.log('✅ User created in Supabase Auth');
    }

    // Get the user ID (either from new creation or existing user)
    let userId;
    if (authData?.user) {
      userId = authData.user.id;
    } else {
      // Try to sign in to get the user ID
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: adminEmail,
        password: adminPassword
      });

      if (signInError) {
        console.error('❌ Could not sign in to get user ID:', signInError.message);
        return;
      }
      userId = signInData.user.id;
    }

    // Check if user profile exists
    console.log('2. Checking user profile...');
    const { data: existingProfiles, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId);

    if (profileError) {
      console.error('❌ Profile check failed:', profileError.message);
      return;
    }

    const existingProfile = existingProfiles && existingProfiles.length > 0 ? existingProfiles[0] : null;

    if (existingProfile) {
      console.log('ℹ️  User profile already exists');

      // Update the profile to ensure it's an admin
      const { error: updateError } = await supabase
        .from('users')
        .update({
          role: 'admin',
          status: 'active',
          name: adminName,
          email: adminEmail
        })
        .eq('id', userId);

      if (updateError) {
        console.error('❌ Profile update failed:', updateError.message);
        return;
      }

      console.log('✅ User profile updated to admin');
    } else {
      console.log('ℹ️  User profile not found, creating...');

      // Create the user profile
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: adminEmail,
          name: adminName,
          role: 'admin',
          status: 'active'
        });

      if (insertError) {
        console.error('❌ Profile creation failed:', insertError.message);
        return;
      }

      console.log('✅ User profile created');
    }

    // Sign out
    await supabase.auth.signOut();

    console.log('\n🎉 Admin user created successfully!');
    console.log('\n📝 Login Credentials:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('\n🔗 Access the admin dashboard:');
    console.log('   http://localhost:3000/admin/login');
    console.log('\n✅ You can now login with these credentials!');

  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    console.log('\n🔧 Manual creation required:');
    console.log('   1. Go to your Supabase dashboard');
    console.log('   2. Navigate to Authentication > Users');
    console.log(`   3. Create a new user with email: ${adminEmail}`);
    console.log(`   4. Set password to: ${adminPassword}`);
    console.log('   5. In SQL Editor, run:');
    console.log(`      UPDATE users SET role = 'admin', status = 'active' WHERE email = '${adminEmail}';`);
  }
}

// Run the creation
setupSpecificAdmin();
