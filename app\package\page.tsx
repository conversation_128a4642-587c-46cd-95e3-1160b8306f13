// Next.js Page Component for /package
//package page.tsx
import type { Metadata } from 'next';
import { Suspense } from 'react';
import MegaMenuHeader from '@/components/header';
import HeroComponent from '@/components/heroes';
import { heroData } from '@/components/data/heroesdata';
import Footer from '@/components/footer';
import Packages from '@/components/package/packages';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Explore Custom Safari Packages in Rwanda, Uganda & Beyond',
  description: 'Browse expertly curated safari packages across Africa. From gorilla trekking, cultural tours to the Serengeti, find your dream wildlife adventure here.',
  url: '/package',
  keywords: [
    'African safari packages',
    'Rwanda gorilla trekking tours',
    'Tanzania safari packages',
    'Uganda wildlife tours',
    'South Africa safari deals',
    'luxury safari packages',
    'budget safari tours',
    'family safari packages',
    'honeymoon safari Africa',
    'group safari tours',
    'custom safari itinerary',
    'Big Five safari tours',
    'Serengeti safari packages',
    'Masai Mara safari tours',
    'safari holiday packages',
    'African adventure tours',
    'wildlife photography tours',
    'cultural safari experiences'
  ],
  type: 'website'
});


const Package = () => {
  const defaultProps = {
    title: heroData.packages.title,
    subtitle: heroData.packages.subtitle,
    backgroundImage: heroData.packages.backgroundImage
  };

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Safari Packages', url: '/package' }
  ]);

  const packagePageSchema = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: 'Explore Custom Safari Packages in Rwanda, Uganda & Beyond',
    description: 'Browse expertly curated safari packages across Africa. From gorilla trekking, cultural tours to the Serengeti, find your dream wildlife adventure here.',
    url: `${defaultSEO.siteUrl}/package`,
    keywords: [
      'African safari packages',
      'Rwanda gorilla trekking tours',
      'Tanzania safari packages',
      'Uganda wildlife tours',
      'South Africa safari deals',
      'luxury safari packages',
      'budget safari tours',
      'family safari packages',
      'honeymoon safari Africa',
      'group safari tours',
      'custom safari itinerary',
      'Big Five safari tours',
      'Serengeti safari packages',
      'Masai Mara safari tours',
      'safari holiday packages',
      'African adventure tours',
      'wildlife photography tours',
      'cultural safari experiences'
    ],
    mainEntity: {
      '@type': 'ItemList',
      name: 'Safari Tour Packages',
      description: 'Browse expertly curated safari packages across Africa. From gorilla trekking, cultural tours to the Serengeti, find your dream wildlife adventure here.',
      numberOfItems: 12,
      itemListElement: [
        {
          '@type': 'TouristTrip',
          name: 'Rwanda Gorilla Trekking Safari',
          description: 'Experience mountain gorillas in Volcanoes National Park',
          image: '/images/packages/rwanda-gorilla-trekking.jpg'
        },
        {
          '@type': 'TouristTrip',
          name: 'Tanzania Serengeti Safari',
          description: 'Witness the Great Migration and Big Five wildlife',
          image: '/images/packages/tanzania-serengeti-safari.jpg'
        },
        {
          '@type': 'TouristTrip',
          name: 'Uganda Wildlife Safari',
          description: 'Explore Queen Elizabeth and Murchison Falls National Parks',
          image: '/images/packages/uganda-wildlife-safari.jpg'
        }
      ]
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, packagePageSchema]} />
      <MegaMenuHeader />
      {/* Hero Section */}
      <HeroComponent {...defaultProps} />
      <Suspense fallback={<div className="flex justify-center items-center py-20">Loading packages...</div>}>
        <Packages />
      </Suspense>
      <Footer />
    </div>
  );
};

export default Package;