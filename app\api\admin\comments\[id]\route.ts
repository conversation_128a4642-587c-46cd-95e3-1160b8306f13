import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// GET - Fetch single comment for admin
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { data: comment, error } = await supabaseAdmin
      .from('sas_blog_comments')
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        status,
        likes_count,
        created_at,
        updated_at,
        parent_comment_id,
        blog_post_id,
        sas_blog_posts!inner(
          title,
          slug
        )
      `)
      .eq('id', id)
      .is('deleted_at', null)
      .single();

    if (error || !comment) {
      return NextResponse.json(
        { success: false, error: 'Comment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: comment
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update comment status (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Validate status
    const validStatuses = ['approved', 'pending', 'rejected'];
    if (body.status && !validStatuses.includes(body.status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Get existing comment
    const { data: existingComment, error: fetchError } = await supabaseAdmin
      .from('sas_blog_comments')
      .select('id')
      .eq('id', id)
      .is('deleted_at', null)
      .single();

    if (fetchError || !existingComment) {
      return NextResponse.json(
        { success: false, error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {};
    
    if (body.status) {
      updateData.status = body.status;
    }

    if (body.content) {
      updateData.content = body.content.trim();
    }

    // Update comment
    const { data: updatedComment, error: updateError } = await supabaseAdmin
      .from('sas_blog_comments')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        status,
        likes_count,
        created_at,
        updated_at,
        parent_comment_id,
        blog_post_id
      `)
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update comment' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedComment,
      message: 'Comment updated successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete comment (admin only) - Soft delete
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get existing comment
    const { data: existingComment, error: fetchError } = await supabaseAdmin
      .from('sas_blog_comments')
      .select('id')
      .eq('id', id)
      .is('deleted_at', null)
      .single();

    if (fetchError || !existingComment) {
      return NextResponse.json(
        { success: false, error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Soft delete the comment and its replies
    const { error: deleteError } = await supabaseAdmin
      .from('sas_blog_comments')
      .update({ deleted_at: new Date().toISOString() })
      .or(`id.eq.${id},parent_comment_id.eq.${id}`);

    if (deleteError) {
      console.error('Delete error:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Failed to delete comment' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Comment deleted successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PATCH - Update comment likes (public endpoint)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    if (body.action !== 'like' && body.action !== 'unlike') {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }

    // Get current comment
    const { data: comment, error: fetchError } = await supabase
      .from('sas_blog_comments')
      .select('id, likes_count')
      .eq('id', id)
      .eq('status', 'approved')
      .is('deleted_at', null)
      .single();

    if (fetchError || !comment) {
      return NextResponse.json(
        { success: false, error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Update likes count
    const currentLikes = comment.likes_count || 0;
    const newLikes = body.action === 'like' 
      ? currentLikes + 1 
      : Math.max(0, currentLikes - 1);

    const { error: updateError } = await supabase
      .from('sas_blog_comments')
      .update({ likes_count: newLikes })
      .eq('id', id);

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update likes' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        likes_count: newLikes
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
