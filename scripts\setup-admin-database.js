#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

async function setupAdminDatabase() {
  console.log('🚀 Setting up Swift Africa Safaris Admin Database...\n');

  // Check for required environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing required environment variables:');
    console.error('   - NEXT_PUBLIC_SUPABASE_URL');
    console.error('   - SUPABASE_SERVICE_ROLE_KEY');
    console.error('\nPlease check your .env.local file and ensure these variables are set.');
    process.exit(1);
  }

  try {
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    console.log('📡 Connecting to Supabase...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'setup-admin-database.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('📝 Executing database setup script...');

    // Execute the SQL script
    const { error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      // If exec_sql doesn't exist, try direct query
      console.log('⚠️  exec_sql function not available, trying direct execution...');
      
      // Split the SQL into individual statements and execute them
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i];
        if (statement.trim()) {
          try {
            const { error: stmtError } = await supabase.rpc('exec_sql', { sql: statement });
            if (stmtError) {
              console.log(`⚠️  Statement ${i + 1} failed (this might be expected):`, stmtError.message);
            }
          } catch (e) {
            console.log(`⚠️  Statement ${i + 1} failed (this might be expected):`, e.message);
          }
        }
      }
    }

    console.log('✅ Database setup completed successfully!\n');

    // Create sample data
    console.log('📊 Creating sample data...');
    await createSampleData(supabase);

    console.log('🎉 Admin database setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Check your Supabase dashboard to verify all tables were created');
    console.log('   2. Review the storage buckets in the Storage section');
    console.log('   3. Test the admin dashboard functionality');
    console.log('   4. Update your environment variables if needed');

  } catch (error) {
    console.error('❌ Error setting up database:', error.message);
    console.error('\n💡 If you see permission errors, make sure:');
    console.error('   - Your SUPABASE_SERVICE_ROLE_KEY is correct');
    console.error('   - You have admin access to your Supabase project');
    console.error('   - The database is accessible');
    process.exit(1);
  }
}

async function createSampleData(supabase) {
  try {
    // Create sample destinations
    const { error: destError } = await supabase
      .from('destinations')
      .insert([
        {
          name: 'Queen Elizabeth National Park',
          slug: 'queen-elizabeth-national-park',
          description: 'Uganda\'s most popular savanna park, home to tree-climbing lions and diverse wildlife.',
          country: 'Uganda',
          region: 'Western Uganda',
          highlights: ['Tree-climbing lions', 'Kazinga Channel', 'Boat safaris', 'Bird watching'],
          best_time_to_visit: 'June to September and December to February'
        },
        {
          name: 'Bwindi Impenetrable Forest',
          slug: 'bwindi-impenetrable-forest',
          description: 'Home to half of the world\'s remaining mountain gorillas.',
          country: 'Uganda',
          region: 'Southwestern Uganda',
          highlights: ['Mountain gorillas', 'Primate tracking', 'Bird watching', 'Forest walks'],
          best_time_to_visit: 'June to September and December to February'
        },
        {
          name: 'Murchison Falls National Park',
          slug: 'murchison-falls-national-park',
          description: 'Uganda\'s largest national park, famous for the powerful Murchison Falls.',
          country: 'Uganda',
          region: 'Northwestern Uganda',
          highlights: ['Murchison Falls', 'Nile River', 'Game drives', 'Boat safaris'],
          best_time_to_visit: 'December to February and June to September'
        }
      ]);

    if (destError) {
      console.log('⚠️  Could not create sample destinations:', destError.message);
    } else {
      console.log('✅ Created sample destinations');
    }

    // Create sample packages
    const { error: pkgError } = await supabase
      .from('packages')
      .insert([
        {
          title: 'Gorilla Trekking Adventure',
          slug: 'gorilla-trekking-adventure',
          description: 'Experience the thrill of tracking mountain gorillas in their natural habitat.',
          short_description: '3-day gorilla trekking experience in Bwindi Forest',
          duration_days: 3,
          duration_nights: 2,
          price_usd: 1500.00,
          difficulty_level: 'moderate',
          highlights: ['Gorilla trekking permit', 'Professional guide', 'Accommodation', 'Meals'],
          status: 'active',
          featured: true
        },
        {
          title: 'Queen Elizabeth Safari',
          slug: 'queen-elizabeth-safari',
          description: 'Discover the diverse wildlife of Queen Elizabeth National Park.',
          short_description: '2-day safari experience in Queen Elizabeth National Park',
          duration_days: 2,
          duration_nights: 1,
          price_usd: 800.00,
          difficulty_level: 'easy',
          highlights: ['Game drives', 'Boat safari', 'Accommodation', 'Meals'],
          status: 'active',
          featured: true
        }
      ]);

    if (pkgError) {
      console.log('⚠️  Could not create sample packages:', pkgError.message);
    } else {
      console.log('✅ Created sample packages');
    }

    // Create sample blog posts
    const { error: blogError } = await supabase
      .from('blog_posts')
      .insert([
        {
          title: 'Ultimate Guide to Gorilla Trekking in Uganda',
          slug: 'ultimate-guide-gorilla-trekking-uganda',
          excerpt: 'Everything you need to know about gorilla trekking in Uganda\'s Bwindi Forest.',
          content: 'Gorilla trekking in Uganda is one of the most incredible wildlife experiences...',
          status: 'published',
          featured: true,
          published_at: new Date().toISOString()
        },
        {
          title: 'Top 10 Safari Destinations in East Africa',
          slug: 'top-10-safari-destinations-east-africa',
          excerpt: 'Discover the best safari destinations across East Africa.',
          content: 'East Africa is home to some of the world\'s most spectacular wildlife...',
          status: 'published',
          featured: true,
          published_at: new Date().toISOString()
        }
      ]);

    if (blogError) {
      console.log('⚠️  Could not create sample blog posts:', blogError.message);
    } else {
      console.log('✅ Created sample blog posts');
    }

    // Create sample reviews
    const { error: reviewError } = await supabase
      .from('reviews')
      .insert([
        {
          customer_name: 'Sarah Johnson',
          customer_email: '<EMAIL>',
          rating: 5,
          title: 'Amazing Gorilla Trekking Experience',
          review: 'The gorilla trekking experience was absolutely incredible. Our guide was knowledgeable and the gorillas were magnificent.',
          status: 'approved',
          featured: true
        },
        {
          customer_name: 'Michael Chen',
          customer_email: '<EMAIL>',
          rating: 5,
          title: 'Unforgettable Safari Adventure',
          review: 'The safari in Queen Elizabeth National Park exceeded all expectations. We saw so much wildlife!',
          status: 'approved',
          featured: true
        }
      ]);

    if (reviewError) {
      console.log('⚠️  Could not create sample reviews:', reviewError.message);
    } else {
      console.log('✅ Created sample reviews');
    }

    // Create sample donation campaign
    const { error: campaignError } = await supabase
      .from('donation_campaigns')
      .insert([
        {
          title: 'Community Education Initiative',
          slug: 'community-education-initiative',
          description: 'Supporting education for children in rural communities across East Africa.',
          short_description: 'Help provide education to children in need',
          goal_amount: 10000.00,
          current_amount: 2500.00,
          status: 'active',
          featured: true
        }
      ]);

    if (campaignError) {
      console.log('⚠️  Could not create sample donation campaign:', campaignError.message);
    } else {
      console.log('✅ Created sample donation campaign');
    }

    console.log('✅ Sample data created successfully!');

  } catch (error) {
    console.log('⚠️  Error creating sample data:', error.message);
  }
}

// Run the setup
if (require.main === module) {
  setupAdminDatabase();
}

module.exports = { setupAdminDatabase }; 