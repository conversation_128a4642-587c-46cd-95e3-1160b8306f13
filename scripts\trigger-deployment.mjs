#!/usr/bin/env node

/**
 * Deployment Trigger Script
 * This script makes a small change to trigger a new Vercel deployment
 */

import fs from 'fs';
import path from 'path';

console.log('🚀 Triggering new deployment...\n');

try {
  // Create a deployment trigger file with current timestamp
  const deploymentInfo = {
    timestamp: new Date().toISOString(),
    reason: 'Manual deployment trigger - fixing blog 404 issues',
    build_id: Date.now().toString(),
    changes: [
      'Fixed blog static generation',
      'Updated blog content blocks handling',
      'Enhanced error handling for production',
      'Force regenerated all blog posts'
    ]
  };
  
  const deploymentPath = path.join(process.cwd(), 'deployment-trigger.json');
  fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));
  
  console.log('✅ Created deployment trigger file');
  console.log(`   Timestamp: ${deploymentInfo.timestamp}`);
  console.log(`   Build ID: ${deploymentInfo.build_id}`);
  
  console.log('\n📋 Next steps:');
  console.log('1. Commit and push this change to trigger deployment');
  console.log('2. Monitor Vercel dashboard for deployment progress');
  console.log('3. Test blog pages after deployment completes');
  
  console.log('\n🔧 Commands to run:');
  console.log('   git add deployment-trigger.json');
  console.log('   git commit -m "Trigger deployment - fix blog 404 issues"');
  console.log('   git push origin main');
  
} catch (error) {
  console.error('❌ Error creating deployment trigger:', error);
}
