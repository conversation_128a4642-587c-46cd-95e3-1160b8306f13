import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDatabase() {
  console.log('🔍 Testing database connection...');

  try {
    // Test sas_packages table
    const { data: packages, error: packagesError } = await supabase
      .from('sas_packages')
      .select('id, title, status')
      .limit(5);

    if (packagesError) {
      console.error('❌ sas_packages table error:', packagesError.message);
    } else {
      console.log('✅ sas_packages table working!');
      console.log(`   Found ${packages?.length || 0} packages`);
      if (packages && packages.length > 0) {
        console.log(`   Sample: ${packages[0].title}`);
      }
    }

    // Test sas_bookings table
    const { data: bookings, error: bookingsError } = await supabase
      .from('sas_bookings')
      .select('id, status')
      .limit(1);

    if (bookingsError) {
      console.error('❌ sas_bookings table error:', bookingsError.message);
    } else {
      console.log('✅ sas_bookings table working!');
      console.log(`   Found ${bookings?.length || 0} bookings`);
    }

    // Test sas_package_content_blocks table
    const { data: blocks, error: blocksError } = await supabase
      .from('sas_package_content_blocks')
      .select('id, block_type')
      .limit(1);

    if (blocksError) {
      console.error('❌ sas_package_content_blocks table error:', blocksError.message);
    } else {
      console.log('✅ sas_package_content_blocks table working!');
    }

    // Test sas_package_itinerary table
    const { data: itinerary, error: itineraryError } = await supabase
      .from('sas_package_itinerary')
      .select('id, title')
      .limit(1);

    if (itineraryError) {
      console.error('❌ sas_package_itinerary table error:', itineraryError.message);
    } else {
      console.log('✅ sas_package_itinerary table working!');
    }

    // Test sas_package_images table
    const { data: images, error: imagesError } = await supabase
      .from('sas_package_images')
      .select('id, image_url')
      .limit(1);

    if (imagesError) {
      console.error('❌ sas_package_images table error:', imagesError.message);
    } else {
      console.log('✅ sas_package_images table working!');
    }

    console.log('\n🎉 Database test completed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

testDatabase();
