'use client';

import React, { useState, useEffect } from 'react';
import { FileText, Search, Download, RefreshCw, AlertTriangle, Info, CheckCircle, XCircle, Clock } from 'lucide-react';

interface LogEntry {
  id: number;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  category: string;
  message: string;
  details?: string;
  userId?: number;
  userName?: string;
  ipAddress?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function SystemLogsPage() {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [levelFilter, setLevelFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const itemsPerPage = 20;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockLogs: LogEntry[] = [
      {
        id: 1,
        timestamp: '2024-12-20T10:30:15Z',
        level: 'info',
        category: 'Authentication',
        message: 'User login successful',
        details: 'User <EMAIL> logged in successfully',
        userId: 1,
        userName: 'John Admin',
        ipAddress: '*************'
      },
      {
        id: 2,
        timestamp: '2024-12-20T10:25:42Z',
        level: 'success',
        category: 'Booking',
        message: 'New booking created',
        details: 'Booking SA-B2B-001 created for Serengeti 5-Day Safari',
        userId: 3,
        userName: 'Michael Tours',
        ipAddress: '*************'
      },
      {
        id: 3,
        timestamp: '2024-12-20T10:20:33Z',
        level: 'warning',
        category: 'System',
        message: 'High memory usage detected',
        details: 'Memory usage reached 85% on server instance web-01',
        ipAddress: '*********'
      },
      {
        id: 4,
        timestamp: '2024-12-20T10:15:18Z',
        level: 'error',
        category: 'Payment',
        message: 'Payment processing failed',
        details: 'Payment gateway timeout for transaction TXN-12345',
        ipAddress: '*************'
      },
      {
        id: 5,
        timestamp: '2024-12-20T10:10:55Z',
        level: 'info',
        category: 'Content',
        message: 'Blog post published',
        details: 'Blog post "Best Safari Destinations 2024" published successfully',
        userId: 2,
        userName: 'Sarah Content',
        ipAddress: '*************'
      },
      {
        id: 6,
        timestamp: '2024-12-20T10:05:22Z',
        level: 'warning',
        category: 'Security',
        message: 'Multiple failed login attempts',
        details: 'IP address ************ attempted login 5 times with invalid credentials',
        ipAddress: '************'
      },
      {
        id: 7,
        timestamp: '2024-12-20T10:00:11Z',
        level: 'success',
        category: 'Backup',
        message: 'Database backup completed',
        details: 'Daily database backup completed successfully (2.3GB)',
        ipAddress: '*********'
      },
      {
        id: 8,
        timestamp: '2024-12-20T09:55:44Z',
        level: 'info',
        category: 'User Management',
        message: 'User role updated',
        details: 'User <EMAIL> role changed from IT Support to Administrator',
        userId: 1,
        userName: 'John Admin',
        ipAddress: '*************'
      }
    ];
    setLogs(mockLogs);
  }, []);

  const categories = ['Authentication', 'Booking', 'System', 'Payment', 'Content', 'Security', 'Backup', 'User Management'];

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = levelFilter === 'all' || log.level === levelFilter;
    const matchesCategory = categoryFilter === 'all' || log.category === categoryFilter;
    return matchesSearch && matchesLevel && matchesCategory;
  });

  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  const currentLogs = filteredLogs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'success': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'info': return <Info size={14} />;
      case 'warning': return <AlertTriangle size={14} />;
      case 'error': return <XCircle size={14} />;
      case 'success': return <CheckCircle size={14} />;
      default: return <Clock size={14} />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const handleExport = () => {
    const csvContent = [
      ['Timestamp', 'Level', 'Category', 'Message', 'User', 'IP Address'],
      ...filteredLogs.map(log => [
        log.timestamp,
        log.level,
        log.category,
        log.message,
        log.userName || '',
        log.ipAddress || ''
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const logStats = {
    total: logs.length,
    errors: logs.filter(l => l.level === 'error').length,
    warnings: logs.filter(l => l.level === 'warning').length,
    info: logs.filter(l => l.level === 'info').length,
    success: logs.filter(l => l.level === 'success').length
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <FileText className="mr-3 text-gray-600" />
                System Logs
              </h1>
              <p className="text-gray-600 mt-1">Monitor system activities and troubleshoot issues</p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center disabled:opacity-50"
              >
                <RefreshCw size={16} className={`mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <button
                onClick={handleExport}
                className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
              >
                <Download size={16} className="mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center">
                <FileText className="h-6 w-6 text-gray-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Logs</p>
                  <p className="text-xl font-bold text-gray-900">{logStats.total}</p>
                </div>
              </div>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="flex items-center">
                <XCircle className="h-6 w-6 text-red-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-red-600">Errors</p>
                  <p className="text-xl font-bold text-red-900">{logStats.errors}</p>
                </div>
              </div>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-6 w-6 text-yellow-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-yellow-600">Warnings</p>
                  <p className="text-xl font-bold text-yellow-900">{logStats.warnings}</p>
                </div>
              </div>
            </div>
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <Info className="h-6 w-6 text-blue-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-blue-600">Info</p>
                  <p className="text-xl font-bold text-blue-900">{logStats.info}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                <div>
                  <p className="text-sm font-medium text-green-600">Success</p>
                  <p className="text-xl font-bold text-green-900">{logStats.success}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                />
              </div>
              <select
                value={levelFilter}
                onChange={(e) => setLevelFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
              >
                <option value="all">All Levels</option>
                <option value="error">Error</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
                <option value="success">Success</option>
              </select>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            <div className="text-sm text-gray-600">
              {filteredLogs.length} log{filteredLogs.length !== 1 ? 's' : ''} found
            </div>
          </div>
        </div>

        {/* Logs Table */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Timestamp</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Level</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Category</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Message</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">User</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">IP Address</th>
                </tr>
              </thead>
              <tbody>
                {currentLogs.map((log) => {
                  const { date, time } = formatTimestamp(log.timestamp);
                  return (
                    <tr key={log.id} className="border-b border-gray-200 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{date}</div>
                          <div className="text-xs text-gray-500">{time}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getLevelColor(log.level)}`}>
                          {getLevelIcon(log.level)}
                          <span className="ml-1 capitalize">{log.level}</span>
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm text-gray-900">{log.category}</span>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{log.message}</div>
                          {log.details && (
                            <div className="text-xs text-gray-500 mt-1">{log.details}</div>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {log.userName ? (
                          <div className="text-sm text-gray-900">{log.userName}</div>
                        ) : (
                          <span className="text-sm text-gray-400">System</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm text-gray-600 font-mono">{log.ipAddress}</span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredLogs.length)} of {filteredLogs.length} logs
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-1 border rounded-md ${
                      currentPage === page
                        ? 'bg-[var(--accent)] text-white border-[var(--accent)]'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
