import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch public packages (only active packages)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || 'all';
    const location = searchParams.get('location') || 'all';
    const difficulty = searchParams.get('difficulty') || 'all';

    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');

    // Build query for active packages only
    let query = supabase
      .from('sas_packages')
      .select(`
        id,
        title,
        slug,
        location,
        pricing_solo,
        pricing_honeymoon,
        pricing_family,
        pricing_group,
        difficulty,
        category,

        image_url,
        image_alt,
        hero_image_url,
        hero_image_alt,
        duration,
        highlights,
        created_at
      `)
      .eq('status', 'active'); // Only show active packages

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,location.ilike.%${search}%`);
    }

    if (category !== 'all') {
      query = query.eq('category', category);
    }

    if (location !== 'all') {
      query = query.eq('location', location);
    }

    if (difficulty !== 'all') {
      query = query.eq('difficulty', difficulty);
    }



    // Apply pagination and sorting
    const startIndex = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false }) // Order by newest
      .range(startIndex, startIndex + limit - 1);

    const { data: packages, error, count } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch packages' },
        { status: 500 }
      );
    }

    // Debug: Log first package's image data
    if (packages && packages.length > 0) {
      console.log('Packages List API - First package image data:', {
        id: packages[0].id,
        title: packages[0].title,
        image_url: packages[0].image_url,
        hero_image_url: packages[0].hero_image_url,
        image_alt: packages[0].image_alt,
        hero_image_alt: packages[0].hero_image_alt
      });
    }

    // Transform data to match frontend expectations
    const transformedPackages = packages?.map(pkg => {
      // Calculate lowest price
      const prices = [pkg.pricing_solo, pkg.pricing_honeymoon, pkg.pricing_family, pkg.pricing_group];
      const lowestPrice = Math.min(...prices);

      return {
        id: pkg.id,
        title: pkg.title,
        slug: pkg.slug,
        location: pkg.location,
        price: `$${lowestPrice}`, // Display lowest price
        pricing: {
          solo: pkg.pricing_solo,
          honeymoon: pkg.pricing_honeymoon,
          family: pkg.pricing_family,
          group: pkg.pricing_group
        },
        difficulty: pkg.difficulty,
        category: pkg.category,

        image: pkg.image_url,
        imageAlt: pkg.image_alt,
        image_url: pkg.image_url,
        image_alt: pkg.image_alt,
        heroImageUrl: pkg.hero_image_url || pkg.image_url,
        heroImageAlt: pkg.hero_image_alt || pkg.image_alt,
        hero_image_url: pkg.hero_image_url || pkg.image_url,
        hero_image_alt: pkg.hero_image_alt || pkg.image_alt,
        duration: pkg.duration,
        highlights: pkg.highlights || [],
        createdAt: pkg.created_at
      };
    }) || [];

    return NextResponse.json({
      success: true,
      data: transformedPackages,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching public packages:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch packages' },
      { status: 500 }
    );
  }
}

// GET package categories and locations for filters
export async function OPTIONS(request: NextRequest) {
  try {
    // Get unique categories
    const { data: categories, error: categoriesError } = await supabase
      .from('sas_packages')
      .select('category')
      .eq('status', 'active')
      .not('category', 'is', null);

    // Get unique locations
    const { data: locations, error: locationsError } = await supabase
      .from('sas_packages')
      .select('location')
      .eq('status', 'active')
      .not('location', 'is', null);

    // Get unique difficulties
    const { data: difficulties, error: difficultiesError } = await supabase
      .from('sas_packages')
      .select('difficulty')
      .eq('status', 'active')
      .not('difficulty', 'is', null);

    if (categoriesError || locationsError || difficultiesError) {
      console.error('Supabase errors:', { categoriesError, locationsError, difficultiesError });
      return NextResponse.json(
        { success: false, error: 'Failed to fetch filter options' },
        { status: 500 }
      );
    }

    // Extract unique values
    const uniqueCategories = [...new Set(categories?.map(c => c.category) || [])];
    const uniqueLocations = [...new Set(locations?.map(l => l.location) || [])];
    const uniqueDifficulties = [...new Set(difficulties?.map(d => d.difficulty) || [])];

    return NextResponse.json({
      success: true,
      data: {
        categories: uniqueCategories.sort(),
        locations: uniqueLocations.sort(),
        difficulties: uniqueDifficulties.sort()
      }
    });

  } catch (error) {
    console.error('Error fetching filter options:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch filter options' },
      { status: 500 }
    );
  }
}
