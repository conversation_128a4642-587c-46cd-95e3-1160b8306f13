"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Facebook, Instagram, Linkedin } from 'lucide-react';
import Modal from '@/components/common/Modal';
import TravelAgentForm from '@/components/common/travelAgent';
import PlanTourForm from '@/components/common/planTourForm';
import ApartmentBookingForm from '@/components/common/apartmentBooking';
import CarBookingForm from '@/components/common/carBooking';
import NewsletterSignup from '@/components/newsLetter';

function TourismFooter() {
    const [activeModal, setActiveModal] = useState(null);
    const [year, setYear] = useState('');

    useEffect(() => {
        if (typeof window !== 'undefined') {
            setYear(new Date().getFullYear().toString());
        }
    }, []);

    const openModal = (modalType) => {
        setActiveModal(modalType);
    };

    const closeModal = () => {
        setActiveModal(null);
    };

    const footerLinks = [
        { name: 'Explore', path: '/' },
        { name: 'Our Journey', path: '/journey' },
        { name: 'About Us', path: '/about' },
        { name: 'Travel Agent', onClick: () => openModal('travelAgent') },
        { name: 'Our Blog', path: '/blog' },
        { name: 'Contact Us', path: '/contact' },
        { name: 'Schedule meeting', path: 'https://calendly.com/reservation-swiftafricasafaris/30min' },
        { name: 'FAQs', path: '/faqs' }
    ];

    const communityLinks = [
        { name: 'Shop', path: '/shop' },
        { name: 'Community', path: '/community' }
    ];

    return (
        <main>
            <NewsletterSignup />
            
            <footer style={{ fontFamily: 'Jost, sans-serif' }} className="bg-gray/900 text-white relative overflow-hidden text-left">
                <div style={{ backgroundColor: 'var(--btn)' }} className="relative">
                    {/* Main Footer Content */}
                    <div className="max-w-7xl mx-auto px-6 py-8">
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 px-0 md:px-[4em]">
                            {/* Quick Links */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4" style={{ color: '#d35400' }}>
                                    Useful Link
                                </h3>
                                <ul className="space-y-2">
                                    {footerLinks.map((item) => (
                                        <li key={item.name}>
                                            {item.onClick ? (
                                                <button
                                                    onClick={item.onClick}
                                                    className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group text-left bg-transparent border-none cursor-pointer"
                                                >
                                                    {item.name}
                                                </button>
                                            ) : (
                                                <Link
                                                    href={item.path || '#'}
                                                    className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group"
                                                >
                                                    {item.name}
                                                </Link>
                                            )}
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Destinations */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4" style={{ color: '#d35400' }}>
                                    Destinations
                                </h3>
                                <ul className="space-y-2">
                                    {['South Africa', 'Tanzania', 'Rwanda', 'Uganda'].map((item) => (
                                        <li key={item}>
                                            <Link
                                                href={`/package?destination=${encodeURIComponent(item)}`}
                                                className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group"
                                            >
                                                {item}
                                            </Link>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Community & Shop */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4" style={{ color: '#d35400' }}>
                                    Community & Shop
                                </h3>
                                <ul className="space-y-2">
                                    {communityLinks.map((item) => (
                                        <li key={item.name}>
                                            <Link href={item.path} className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group">
                                                {item.name}
                                            </Link>
                                        </li>
                                    ))}
                                </ul>
                            </div>

                            {/* Services */}
                            <div>
                                <h3 className="text-lg font-semibold mb-4" style={{ color: '#d35400' }}>
                                    Our Services
                                </h3>
                                <ul className="space-y-2">
                                    <li>
                                        <button
                                            onClick={() => openModal('planTour')}
                                            className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group text-left"
                                        >
                                            Tour Arrangement
                                        </button>
                                    </li>
                                    <li>
                                        <button
                                            onClick={() => openModal('carHire')}
                                            className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group text-left"
                                        >
                                            Car Hire
                                        </button>
                                    </li>
                                    <li>
                                        <button
                                            onClick={() => openModal('apartmentRental')}
                                            className="mb-2 text-gray-300 hover:text-white transition-colors duration-300 flex items-center group text-left"
                                        >
                                            Apartment Rental
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        {/* Social Media & Bottom Bar */}
                        <div className="border-t border-gray-700 mt-8 pt-6">
                            <div className="flex flex-col md:flex-row justify-between items-center gap-6">

                                {/* Social Media */}
                                <div className="flex items-center gap-1">
                                    <span className="text-gray-400 mr-4">Follow our adventures:</span>
                                    { [
                                        { Icon: Facebook, label: 'Facebook', url: 'https://web.facebook.com/people/Swift-Africa-Safaris/61550743088246/' },
                                        { Icon: Instagram, label: 'Instagram', url: 'https://www.instagram.com/swiftafricasafaris/' },
                                        { Icon: Linkedin, label: 'LinkedIn', url: 'https://www.linkedin.com/company/swift-africa-safaris1/' }
                                    ].map(({ Icon, label, url }) => (
                                        <a
                                            key={label}
                                            href={url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center hover:bg-white/20 transition-all duration-300 group"
                                            aria-label={label}
                                        >
                                            <Icon className="w-5 h-5 text-gray-300 group-hover:text-white transition-colors" />
                                        </a>
                                    ))}
                                </div>

                                {/* Bottom Links */}
                                <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                                    <a href="/privacy-policy" className="hover:text-white transition-colors">Privacy Policy</a>
                                    <a href="/booking-policy" className="hover:text-white transition-colors">Booking & Refund Policy</a>

                                </div>
                            </div>

                            {/* Copyright */}
                            <div className="text-center mt-6 pt-4 border-t border-gray-700">
                                <p className="text-gray-400 text-sm">
                                    © {year} Swift Africa Safaris. All rights reserved. |
                                    <span style={{ color: '#d35400' }} className="ml-1">Roam Responsibly!</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>

            {/* Modals */}
            {activeModal === 'travelAgent' && (
                <Modal isOpen={true} onClose={closeModal}>
                    <TravelAgentForm onClose={closeModal} />
                </Modal>
            )}

            {activeModal === 'planTour' && (
                <Modal isOpen={true} onClose={closeModal}>
                    <PlanTourForm onClose={closeModal} />
                </Modal>
            )}

            {activeModal === 'carHire' && (
                <Modal isOpen={true} onClose={closeModal}>
                    <CarBookingForm onClose={closeModal} />
                </Modal>
            )}

            {activeModal === 'apartmentRental' && (
                <Modal isOpen={true} onClose={closeModal}>
                    <ApartmentBookingForm onClose={closeModal} />
                </Modal>
            )}
        </main>
    );
}

export default TourismFooter;