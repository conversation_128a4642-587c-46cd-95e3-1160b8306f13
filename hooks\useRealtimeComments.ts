'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

interface Comment {
  id: string;
  author_name: string;
  author_email: string;
  content: string;
  is_admin_reply: boolean;
  status: 'approved' | 'pending' | 'rejected';
  likes_count: number;
  created_at: string;
  updated_at: string;
  parent_comment_id: string | null;
  blog_post_id: string;
  replies?: Comment[];
}

interface UseRealtimeCommentsProps {
  blogSlug: string;
  initialComments?: Comment[];
}

interface UseRealtimeCommentsReturn {
  comments: Comment[];
  loading: boolean;
  error: string | null;
  addComment: (comment: Omit<Comment, 'id' | 'created_at' | 'updated_at' | 'likes_count'>) => Promise<boolean>;
  updateComment: (id: string, updates: Partial<Comment>) => Promise<boolean>;
  deleteComment: (id: string) => Promise<boolean>;
  likeComment: (id: string) => Promise<boolean>;
  refreshComments: () => Promise<void>;
}

export const useRealtimeComments = ({
  blogSlug,
  initialComments = []
}: UseRealtimeCommentsProps): UseRealtimeCommentsReturn => {
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [blogPostId, setBlogPostId] = useState<string | null>(null);

  // Get blog post ID from slug
  const getBlogPostId = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('sas_blog_posts')
        .select('id')
        .eq('slug', blogSlug)
        .eq('status', 'published')
        .is('deleted_at', null)
        .single();

      if (error) throw error;
      return data?.id || null;
    } catch (err) {
      console.error('Error getting blog post ID:', err);
      return null;
    }
  }, [blogSlug]);

  // Fetch comments from API
  const fetchComments = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/blog/${blogSlug}/comments`);
      const data = await response.json();

      if (data.success) {
        setComments(data.data.comments);
      } else {
        setError(data.error || 'Failed to fetch comments');
      }
    } catch (err) {
      console.error('Error fetching comments:', err);
      setError('Failed to fetch comments');
    } finally {
      setLoading(false);
    }
  }, [blogSlug]);

  // Set up real-time subscription
  useEffect(() => {
    let subscription: any = null;

    const setupRealtimeSubscription = async () => {
      const postId = await getBlogPostId();
      if (!postId) return;

      setBlogPostId(postId);

      // Subscribe to comment changes for this blog post
      subscription = supabase
        .channel(`blog-comments-${postId}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'sas_blog_comments',
            filter: `blog_post_id=eq.${postId}`
          },
          (payload) => {
            console.log('Real-time comment update:', payload);
            
            switch (payload.eventType) {
              case 'INSERT':
                handleCommentInsert(payload.new as Comment);
                break;
              case 'UPDATE':
                handleCommentUpdate(payload.new as Comment);
                break;
              case 'DELETE':
                handleCommentDelete(payload.old.id);
                break;
            }
          }
        )
        .subscribe((status) => {
          console.log('Subscription status:', status);
        });
    };

    setupRealtimeSubscription();

    return () => {
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, [blogSlug, getBlogPostId]);

  // Handle real-time comment insert
  const handleCommentInsert = (newComment: Comment) => {
    setComments(prevComments => {
      // Check if comment already exists (avoid duplicates)
      if (prevComments.some(c => c.id === newComment.id)) {
        return prevComments;
      }

      // If it's a reply, add it to the parent comment's replies
      if (newComment.parent_comment_id) {
        return prevComments.map(comment => {
          if (comment.id === newComment.parent_comment_id) {
            return {
              ...comment,
              replies: [...(comment.replies || []), newComment]
            };
          }
          return comment;
        });
      }

      // If it's a top-level comment, add it to the beginning
      return [{ ...newComment, replies: [] }, ...prevComments];
    });
  };

  // Handle real-time comment update
  const handleCommentUpdate = (updatedComment: Comment) => {
    setComments(prevComments => {
      return prevComments.map(comment => {
        if (comment.id === updatedComment.id) {
          return { ...updatedComment, replies: comment.replies };
        }
        
        // Check replies
        if (comment.replies) {
          const updatedReplies = comment.replies.map(reply => 
            reply.id === updatedComment.id ? updatedComment : reply
          );
          return { ...comment, replies: updatedReplies };
        }
        
        return comment;
      });
    });
  };

  // Handle real-time comment delete
  const handleCommentDelete = (deletedCommentId: string) => {
    setComments(prevComments => {
      return prevComments.filter(comment => {
        if (comment.id === deletedCommentId) {
          return false;
        }
        
        // Filter out deleted replies
        if (comment.replies) {
          comment.replies = comment.replies.filter(reply => reply.id !== deletedCommentId);
        }
        
        return true;
      });
    });
  };

  // Add new comment
  const addComment = async (commentData: Omit<Comment, 'id' | 'created_at' | 'updated_at' | 'likes_count'>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/blog/${blogSlug}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          author_name: commentData.author_name,
          author_email: commentData.author_email,
          content: commentData.content,
          parent_comment_id: commentData.parent_comment_id
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Real-time subscription will handle adding the comment to the UI
        return true;
      } else {
        setError(data.error || 'Failed to add comment');
        return false;
      }
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment');
      return false;
    }
  };

  // Update comment
  const updateComment = async (id: string, updates: Partial<Comment>): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/comments/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const data = await response.json();
      
      if (data.success) {
        // Real-time subscription will handle updating the comment in the UI
        return true;
      } else {
        setError(data.error || 'Failed to update comment');
        return false;
      }
    } catch (err) {
      console.error('Error updating comment:', err);
      setError('Failed to update comment');
      return false;
    }
  };

  // Delete comment
  const deleteComment = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/comments/${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      
      if (data.success) {
        // Real-time subscription will handle removing the comment from the UI
        return true;
      } else {
        setError(data.error || 'Failed to delete comment');
        return false;
      }
    } catch (err) {
      console.error('Error deleting comment:', err);
      setError('Failed to delete comment');
      return false;
    }
  };

  // Like/unlike comment
  const likeComment = async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/comments/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'like' }),
      });

      const data = await response.json();
      
      if (data.success) {
        // Update the comment's like count locally for immediate feedback
        setComments(prevComments => 
          prevComments.map(comment => {
            if (comment.id === id) {
              return { ...comment, likes_count: data.data.likes_count };
            }
            
            // Check replies
            if (comment.replies) {
              const updatedReplies = comment.replies.map(reply => 
                reply.id === id ? { ...reply, likes_count: data.data.likes_count } : reply
              );
              return { ...comment, replies: updatedReplies };
            }
            
            return comment;
          })
        );
        return true;
      } else {
        setError(data.error || 'Failed to like comment');
        return false;
      }
    } catch (err) {
      console.error('Error liking comment:', err);
      setError('Failed to like comment');
      return false;
    }
  };

  // Refresh comments manually
  const refreshComments = async (): Promise<void> => {
    await fetchComments();
  };

  // Initial fetch
  useEffect(() => {
    if (initialComments.length === 0) {
      fetchComments();
    }
  }, [fetchComments, initialComments.length]);

  return {
    comments,
    loading,
    error,
    addComment,
    updateComment,
    deleteComment,
    likeComment,
    refreshComments
  };
};
