import React, { useState, useEffect } from 'react';
import ProductCard from './productCard';
import { useShop } from './ShopContext';
import { SkeletonGrid } from '../skeleton';

const ProductGrid = () => {
  const { filteredProducts } = useShop();
  const [loading, setLoading] = useState(true);

  // Simulate loading for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, [filteredProducts]);

  const getValidImageUrl = (product) => {
    // Use a reliable default product image URL
    const defaultImage = '/images/product-placeholder.jpg';
    if (!product.images || product.images.length === 0) {
      return defaultImage;
    }
    // Return first available image or default
    return product.images[0] || defaultImage;
  };

  if (loading) {
    return (
      <SkeletonGrid
        variant="product"
        count={8}
        cardProps={{ showPrice: true, showButton: true }}
      />
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {filteredProducts.map(product => (
        <ProductCard
          key={product.slug}
          {...product}
          image={getValidImageUrl(product)}
        />
      ))}
    </div>
  );
};

export default ProductGrid;
