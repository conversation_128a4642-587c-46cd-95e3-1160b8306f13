import React, { createContext, useContext, useState } from 'react';
import { products } from '../data/productData';

const ShopContext = createContext();

export const ShopProvider = ({ children }) => {
  const [filteredProducts, setFilteredProducts] = useState(products);
  const [searchTerm, setSearchTerm] = useState('');

  const handleFilterChange = (newProducts) => {
    setFilteredProducts(newProducts);
  };

  const handleSearch = (term) => {
    setSearchTerm(term);
  };

  return (
    <ShopContext.Provider value={{
      filteredProducts,
      searchTerm,
      handleFilterChange,
      handleSearch
    }}>
      {children}
    </ShopContext.Provider>
  );
};

export const useShop = () => {
  const context = useContext(ShopContext);
  if (!context) {
    throw new Error('useShop must be used within a ShopProvider');
  }
  return context;
};
