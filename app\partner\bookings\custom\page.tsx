'use client';

import React, { useState } from 'react';
import { 
  ArrowLeft, 
  Calendar, 
  Users, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Save,
  Package,
  DollarSign,
  AlertCircle,
  FileText,
  Star,
  Camera,
  Mountain,
  Compass
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCommonNotifications } from '../../../../components/partner/NotificationProvider';

interface CustomBookingForm {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerCountry: string;
  travelers: number;
  preferredDates: string;
  alternativeDates: string;
  budget: string;
  duration: string;
  destinations: string;
  interests: string[];
  accommodationType: string;
  transportPreference: string;
  specialRequests: string;
  customRequirements: string;
  emergencyContact: string;
  emergencyPhone: string;
  dietaryRequirements: string;
  medicalConditions: string;
  previousExperience: string;
  hearAboutUs: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function CustomBookingPage() {
  const router = useRouter();
  const notifications = useCommonNotifications();
  
  const [formData, setFormData] = useState<CustomBookingForm>({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerCountry: '',
    travelers: 2,
    preferredDates: '',
    alternativeDates: '',
    budget: '',
    duration: '',
    destinations: '',
    interests: [],
    accommodationType: '',
    transportPreference: '',
    specialRequests: '',
    customRequirements: '',
    emergencyContact: '',
    emergencyPhone: '',
    dietaryRequirements: '',
    medicalConditions: '',
    previousExperience: '',
    hearAboutUs: ''
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const countries = [
    'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 
    'Italy', 'Spain', 'Netherlands', 'Sweden', 'Norway', 'Denmark', 'Kenya', 
    'South Africa', 'Tanzania', 'Uganda', 'Rwanda', 'Botswana', 'Namibia', 'Zambia'
  ];

  const interestOptions = [
    { id: 'wildlife', label: 'Wildlife Safari', icon: <Camera className="h-4 w-4" /> },
    { id: 'climbing', label: 'Mountain Climbing', icon: <Mountain className="h-4 w-4" /> },
    { id: 'cultural', label: 'Cultural Experiences', icon: <Users className="h-4 w-4" /> },
    { id: 'adventure', label: 'Adventure Activities', icon: <Compass className="h-4 w-4" /> },
    { id: 'photography', label: 'Photography Tours', icon: <Camera className="h-4 w-4" /> },
    { id: 'luxury', label: 'Luxury Travel', icon: <Star className="h-4 w-4" /> },
    { id: 'budget', label: 'Budget Travel', icon: <DollarSign className="h-4 w-4" /> },
    { id: 'family', label: 'Family-Friendly', icon: <Users className="h-4 w-4" /> }
  ];

  const accommodationTypes = [
    'Luxury Safari Lodges',
    'Mid-range Safari Camps',
    'Budget Camping',
    'Boutique Hotels',
    'Eco-lodges',
    'Mountain Huts',
    'Cultural Homestays',
    'Mixed Accommodation',
    'No Preference'
  ];

  const transportOptions = [
    'Private 4x4 Safari Vehicle',
    'Shared Safari Vehicle',
    'Domestic Flights',
    'Walking Safaris',
    'Boat Transfers',
    'Helicopter Transfers',
    'Mixed Transport',
    'No Preference'
  ];

  const budgetRanges = [
    'Under $1,000 per person',
    '$1,000 - $2,000 per person',
    '$2,000 - $3,000 per person',
    '$3,000 - $5,000 per person',
    '$5,000 - $10,000 per person',
    'Over $10,000 per person',
    'Flexible budget'
  ];

  const handleInputChange = (field: keyof CustomBookingForm, value: string | number | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleInterestToggle = (interestId: string) => {
    const currentInterests = formData.interests;
    const newInterests = currentInterests.includes(interestId)
      ? currentInterests.filter(id => id !== interestId)
      : [...currentInterests, interestId];
    
    handleInputChange('interests', newInterests);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (!formData.customerEmail.trim()) {
      newErrors.customerEmail = 'Customer email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerPhone.trim()) {
      newErrors.customerPhone = 'Customer phone is required';
    }

    if (!formData.preferredDates) {
      newErrors.preferredDates = 'Preferred travel dates are required';
    }

    if (!formData.destinations.trim()) {
      newErrors.destinations = 'Please specify desired destinations';
    }

    if (!formData.customRequirements.trim()) {
      newErrors.customRequirements = 'Please describe your custom requirements';
    }

    if (formData.travelers < 1) {
      newErrors.travelers = 'Number of travelers must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would make an API call to submit the custom booking request
      console.log('Submitting custom booking request:', formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      notifications.showSuccess(
        'Custom Request Submitted',
        'Our team will create a personalized package and contact you within 24 hours.',
        {
          actionUrl: '/partner/messages',
          actionText: 'Check Messages',
          duration: 8000
        }
      );

      router.push('/partner/bookings');
    } catch (error) {
      console.error('Error submitting custom booking request:', error);
      notifications.notifyFormError('submit custom request', 'Please check your information and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center mb-8">
        <button
          onClick={() => router.back()}
          className="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft size={20} />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Request Custom Package</h1>
          <p className="text-gray-600 mt-1">Tell us exactly what your customer wants - we&apos;ll create a personalized safari experience</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Introduction */}
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
          <div className="flex items-start">
            <Package className="h-6 w-6 text-blue-600 mr-3 mt-1" />
            <div>
              <h2 className="text-lg font-semibold text-blue-900 mb-2">Custom Safari Package Request</h2>
              <p className="text-blue-800">
                Can&apos;t find the perfect package for your customer? No problem! Fill out this form with as much detail as possible, 
                and our expert team will create a completely customized safari experience. From unique destinations to special 
                activities, dietary requirements to luxury preferences - tell us everything your customer dreams of!
              </p>
            </div>
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customer Name *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange('customerName', e.target.value)}
                  className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.customerName ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter customer's full name"
                />
              </div>
              {errors.customerName && <p className="mt-1 text-sm text-red-600">{errors.customerName}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="email"
                  value={formData.customerEmail}
                  onChange={(e) => handleInputChange('customerEmail', e.target.value)}
                  className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.customerEmail ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.customerEmail && <p className="mt-1 text-sm text-red-600">{errors.customerEmail}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number *
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="tel"
                  value={formData.customerPhone}
                  onChange={(e) => handleInputChange('customerPhone', e.target.value)}
                  className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.customerPhone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="****** 123 4567"
                />
              </div>
              {errors.customerPhone && <p className="mt-1 text-sm text-red-600">{errors.customerPhone}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country
              </label>
              <select
                value={formData.customerCountry}
                onChange={(e) => handleInputChange('customerCountry', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select country</option>
                {countries.map(country => (
                  <option key={country} value={country}>{country}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Travel Preferences */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Travel Preferences</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of Travelers *
              </label>
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={formData.travelers}
                  onChange={(e) => handleInputChange('travelers', parseInt(e.target.value) || 1)}
                  className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.travelers ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
              </div>
              {errors.travelers && <p className="mt-1 text-sm text-red-600">{errors.travelers}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Duration
              </label>
              <input
                type="text"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g., 7 days, 2 weeks, flexible"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Travel Dates *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  value={formData.preferredDates}
                  onChange={(e) => handleInputChange('preferredDates', e.target.value)}
                  className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.preferredDates ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="e.g., June 15-25, 2025 or Summer 2025"
                />
              </div>
              {errors.preferredDates && <p className="mt-1 text-sm text-red-600">{errors.preferredDates}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Alternative Dates
              </label>
              <input
                type="text"
                value={formData.alternativeDates}
                onChange={(e) => handleInputChange('alternativeDates', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Backup dates if preferred dates unavailable"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Budget Range
              </label>
              <select
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select budget range</option>
                {budgetRanges.map(range => (
                  <option key={range} value={range}>{range}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Accommodation Preference
              </label>
              <select
                value={formData.accommodationType}
                onChange={(e) => handleInputChange('accommodationType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select accommodation type</option>
                {accommodationTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Destinations & Interests */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Destinations & Interests</h2>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Desired Destinations *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                value={formData.destinations}
                onChange={(e) => handleInputChange('destinations', e.target.value)}
                rows={3}
                className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.destinations ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., Serengeti, Ngorongoro Crater, Kilimanjaro, Zanzibar, Maasai Mara, specific parks or regions..."
              />
            </div>
            {errors.destinations && <p className="mt-1 text-sm text-red-600">{errors.destinations}</p>}
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Interests & Activities (Select all that apply)
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {interestOptions.map((interest) => (
                <button
                  key={interest.id}
                  type="button"
                  onClick={() => handleInterestToggle(interest.id)}
                  className={`p-3 rounded-lg border-2 transition-colors text-left ${
                    formData.interests.includes(interest.id)
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 text-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    {interest.icon}
                    <span className="ml-2 text-sm font-medium">{interest.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transport Preference
            </label>
            <select
              value={formData.transportPreference}
              onChange={(e) => handleInputChange('transportPreference', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select transport preference</option>
              {transportOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Custom Requirements */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Tell Us Everything!</h2>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Requirements & Special Requests *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                value={formData.customRequirements}
                onChange={(e) => handleInputChange('customRequirements', e.target.value)}
                rows={6}
                className={`pl-10 pr-4 py-2 border rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.customRequirements ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Describe everything your customer wants! Include specific animals they want to see, activities they're interested in, luxury preferences, cultural experiences, photography needs, celebration occasions (honeymoon, anniversary, birthday), accessibility requirements, group dynamics, or anything else that would make this trip perfect for them..."
              />
            </div>
            {errors.customRequirements && <p className="mt-1 text-sm text-red-600">{errors.customRequirements}</p>}
            <p className="mt-2 text-sm text-gray-500">
              The more details you provide, the better we can customize the perfect safari experience!
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Dietary Requirements & Food Preferences
              </label>
              <textarea
                value={formData.dietaryRequirements}
                onChange={(e) => handleInputChange('dietaryRequirements', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Vegetarian, vegan, allergies, cultural dietary restrictions, favorite cuisines..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Medical Conditions & Accessibility Needs
              </label>
              <textarea
                value={formData.medicalConditions}
                onChange={(e) => handleInputChange('medicalConditions', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Any medical conditions, mobility requirements, medication needs we should know about..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Previous Safari/Travel Experience
              </label>
              <textarea
                value={formData.previousExperience}
                onChange={(e) => handleInputChange('previousExperience', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="First-time safari, experienced travelers, places they've been, what they loved/didn't love..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                How did they hear about us?
              </label>
              <input
                type="text"
                value={formData.hearAboutUs}
                onChange={(e) => handleInputChange('hearAboutUs', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Referral, website, social media, travel agent, etc."
              />
            </div>
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Emergency Contact</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Name
              </label>
              <input
                type="text"
                value={formData.emergencyContact}
                onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Emergency contact full name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact Phone
              </label>
              <input
                type="tel"
                value={formData.emergencyPhone}
                onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="****** 987 6543"
              />
            </div>
          </div>
        </div>

        {/* Important Note */}
        <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-1" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-900 mb-2">What Happens Next?</h3>
              <div className="text-yellow-800 space-y-2">
                <p>1. Our expert safari planners will review your custom requirements within 24 hours</p>
                <p>2. We&apos;ll create a personalized itinerary with pricing and send it to you for review</p>
                <p>3. You can request modifications until it&apos;s perfect for your customer</p>
                <p>4. Once approved, we&apos;ll handle all the booking details and confirmations</p>
                <p className="font-medium">Your commission rate will be clearly outlined in the custom quote!</p>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting Request...
              </div>
            ) : (
              <div className="flex items-center">
                <Save className="h-4 w-4 mr-2" />
                Submit Custom Request
              </div>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
