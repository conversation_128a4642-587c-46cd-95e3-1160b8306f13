import React from 'react';
import ImageSlider from '../journey/imageSlider';
import Button from './Button';


const GroupJourney = () => {
  const groupImages = [
    {
      src: '/images/journey/Group-tour-Mist-mountain.webp',
      alt: 'Group of travelers together enjoying the hiking in Rwanda'
    },
    {
      src: '/images/journey/Group-tour-nature-walk.webp',
      alt: 'Group of travelers enjoying the nature'
    }
  ];

  return (
    <section className="flex flex-col md:flex-row-reverse items-center bg-[var(--primary-background)] px-6 py-16 md:px-16">
      <div className="flex-1 px-0 md:px-10 order-1 md:order-none lg:text-left md:text-left lg:pr-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-5 text-gray-900 leading-tight">
          Group Journey: Shared Adventures, Lasting Bonds
        </h2>
        <div className="text-base leading-relaxed text-gray-700 mb-8 space-y-4 text-justify lg:justify-start">
          <p>
            Some experiences are simply better when shared. Our group journeys through
            Africa are designed for travelers who thrive on connection. those who seek laughter around a campfire,
            teamwork on rugged trails, and friendships forged in the heart of unforgettable landscapes.
          </p>
          <p>
            You&apos;ll become part of a like minded community bound by curiosity and the spirit of discovery. Together,
            you&apos;ll witness the thundering majesty of Victoria Falls, navigate vibrant city streets in Cape Town, or
            track wildlife in the golden light of the Serengeti.
          </p>
          <p>
            Our expertly guided group tours balance structured exploration with moments of freedom, offering the
            perfect blend of comfort, camaraderie, and cultural immersion. Come not just to see Africa but to feel
            it, learn from it, and experience it through the stories and smiles of those beside you.
          </p>
        </div>

        {/* Button for desktop */}
        <Button variant="desktop">Learn More</Button>
      </div>

      
      <div className="flex-1 order-2 md:order-none mt-8 md:mt-0 w-full">
        <ImageSlider images={groupImages} aspectRatio="portrait" />
        {/* Button for mobile and tablet */}
        <Button variant="mobile">Learn More</Button>
      </div>
    </section>
  );
};

export default GroupJourney;