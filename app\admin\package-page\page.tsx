/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useRef } from "react";
import { Package, Save, Upload, Filter, Grid, List } from "lucide-react";

interface PackagePageFormData {
  pageTitle: string;
  pageSubtitle: string;
  pageDescription: string;
  headerImage: string;
  packagesPerPage: number;
  showFilters: boolean;
  showSorting: boolean;
  defaultView: 'grid' | 'list';
  showPricing: boolean;
  showDuration: boolean;
  showDifficulty: boolean;
  featuredPackagesCount: number;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PackagePageManagement() {
  const [activeTab, setActiveTab] = useState("content");
  const [formData, setFormData] = useState<PackagePageFormData>({
    pageTitle: "Safari Packages",
    pageSubtitle: "Choose Your Perfect African Adventure",
    pageDescription: "Explore our carefully crafted safari packages designed to give you the ultimate African wildlife experience. From budget-friendly options to luxury adventures, we have something for every traveler.",
    headerImage: "",
    packagesPerPage: 12,
    showFilters: true,
    showSorting: true,
    defaultView: 'grid',
    showPricing: true,
    showDuration: true,
    showDifficulty: true,
    featuredPackagesCount: 6,
    seoTitle: "Safari Packages - Best Tanzania Safari Tours | Swift Africa Safaris",
    seoDescription: "Discover amazing safari packages in Tanzania. Choose from budget to luxury safari tours with expert guides. Book your African adventure today!",
    seoKeywords: "safari packages, Tanzania safari tours, wildlife tours, safari deals, Africa travel packages"
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof PackagePageFormData, value: string | number | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you would upload to a server
      const imageUrl = URL.createObjectURL(file);
      setFormData((prev) => ({ ...prev, headerImage: imageUrl }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.pageTitle || formData.pageTitle.length < 3) {
      newErrors.pageTitle = "Please enter a valid page title (3+ characters)";
    }

    if (!formData.pageSubtitle || formData.pageSubtitle.length < 5) {
      newErrors.pageSubtitle = "Please enter a valid subtitle (5+ characters)";
    }

    if (!formData.pageDescription || formData.pageDescription.length < 20) {
      newErrors.pageDescription = "Please enter a valid description (20+ characters)";
    }

    if (formData.packagesPerPage < 1 || formData.packagesPerPage > 50) {
      newErrors.packagesPerPage = "Packages per page must be between 1 and 50";
    }

    if (formData.featuredPackagesCount < 0 || formData.featuredPackagesCount > 20) {
      newErrors.featuredPackagesCount = "Featured packages count must be between 0 and 20";
    }

    if (!formData.seoTitle || formData.seoTitle.length < 10) {
      newErrors.seoTitle = "Please enter a valid SEO title (10+ characters)";
    }

    if (!formData.seoDescription || formData.seoDescription.length < 50) {
      newErrors.seoDescription = "Please enter a valid SEO description (50+ characters)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Form submitted successfully:", formData);
      alert("Package page settings saved successfully!");
    }
  };

  const tabs = [
    { id: "content", label: "Page Content" },
    { id: "display", label: "Display Settings" },
    { id: "seo", label: "SEO Settings" },
  ];

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Package className="mr-3 text-orange-600" />
                Package Page Management
              </h1>
              <p className="text-gray-600 mt-1">Manage your safari packages page settings and layout</p>
            </div>
            <button
              className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? "border-[var(--accent)] text-[var(--accent)]"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <form onSubmit={handleSubmit} className="p-6">
                {/* Page Content Tab */}
                {activeTab === "content" && (
                  <div className="space-y-6">
                    {/* Preview Section */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Package Page Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                        <div className="text-center space-y-4">
                          {formData.headerImage && (
                            <img
                              src={formData.headerImage}
                              alt="Package page header"
                              className="w-full h-32 object-cover rounded-lg mb-4"
                            />
                          )}
                          <h1 className="text-2xl font-bold text-gray-900">
                            {formData.pageTitle || "Your Package Page Title"}
                          </h1>
                          <h2 className="text-lg text-gray-700">
                            {formData.pageSubtitle || "Your Package Page Subtitle"}
                          </h2>
                          <p className="text-gray-600 max-w-2xl mx-auto">
                            {formData.pageDescription || "Your package page description will appear here"}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Page Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Title *
                      </label>
                      <input
                        type="text"
                        value={formData.pageTitle}
                        onChange={(e) => handleInputChange("pageTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your package page title"
                      />
                      {errors.pageTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageTitle}</p>
                      )}
                    </div>

                    {/* Page Subtitle */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Subtitle *
                      </label>
                      <input
                        type="text"
                        value={formData.pageSubtitle}
                        onChange={(e) => handleInputChange("pageSubtitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageSubtitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your package page subtitle"
                      />
                      {errors.pageSubtitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageSubtitle}</p>
                      )}
                    </div>

                    {/* Page Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Description *
                      </label>
                      <textarea
                        value={formData.pageDescription}
                        onChange={(e) => handleInputChange("pageDescription", e.target.value)}
                        rows={4}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your package page description"
                      />
                      {errors.pageDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageDescription}</p>
                      )}
                    </div>

                    {/* Header Image Upload */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Header Image
                      </label>
                      <div
                        onClick={handleFileSelect}
                        className="relative border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-[var(--accent)] transition-colors cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div className="text-center">
                          {formData.headerImage ? (
                            <div className="space-y-2">
                              <img
                                src={formData.headerImage}
                                alt="Header Image Preview"
                                className="mx-auto h-32 w-auto object-cover rounded-lg"
                              />
                              <p className="text-sm text-gray-600">
                                Click to change image
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto h-12 w-12 text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">
                                  Click to upload header image
                                </p>
                                <p className="text-xs text-gray-500">
                                  PNG, JPG, GIF up to 10MB
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Display Settings Tab */}
                {activeTab === "display" && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Packages Per Page *
                        </label>
                        <input
                          type="number"
                          value={formData.packagesPerPage}
                          onChange={(e) => handleInputChange("packagesPerPage", parseInt(e.target.value))}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.packagesPerPage ? "border-red-500" : "border-gray-300"
                          }`}
                          min="1"
                          max="50"
                        />
                        {errors.packagesPerPage && (
                          <p className="mt-1 text-sm text-red-600">{errors.packagesPerPage}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Featured Packages Count *
                        </label>
                        <input
                          type="number"
                          value={formData.featuredPackagesCount}
                          onChange={(e) => handleInputChange("featuredPackagesCount", parseInt(e.target.value))}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.featuredPackagesCount ? "border-red-500" : "border-gray-300"
                          }`}
                          min="0"
                          max="20"
                        />
                        {errors.featuredPackagesCount && (
                          <p className="mt-1 text-sm text-red-600">{errors.featuredPackagesCount}</p>
                        )}
                        <p className="mt-1 text-xs text-gray-500">
                          Number of featured packages to highlight at the top
                        </p>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Default View
                      </label>
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="defaultView"
                            value="grid"
                            checked={formData.defaultView === 'grid'}
                            onChange={(e) => handleInputChange("defaultView", e.target.value as 'grid' | 'list')}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300"
                          />
                          <Grid size={16} className="ml-2 mr-1" />
                          <span className="text-sm text-gray-700">Grid View</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="defaultView"
                            value="list"
                            checked={formData.defaultView === 'list'}
                            onChange={(e) => handleInputChange("defaultView", e.target.value as 'grid' | 'list')}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300"
                          />
                          <List size={16} className="ml-2 mr-1" />
                          <span className="text-sm text-gray-700">List View</span>
                        </label>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-base font-medium text-gray-700">Display Options</h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showFilters"
                            checked={formData.showFilters}
                            onChange={(e) => handleInputChange("showFilters", e.target.checked)}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                          />
                          <label htmlFor="showFilters" className="ml-2 block text-sm text-gray-700">
                            <Filter size={16} className="inline mr-1" />
                            Show filter options
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showSorting"
                            checked={formData.showSorting}
                            onChange={(e) => handleInputChange("showSorting", e.target.checked)}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                          />
                          <label htmlFor="showSorting" className="ml-2 block text-sm text-gray-700">
                            Show sorting options
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showPricing"
                            checked={formData.showPricing}
                            onChange={(e) => handleInputChange("showPricing", e.target.checked)}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                          />
                          <label htmlFor="showPricing" className="ml-2 block text-sm text-gray-700">
                            Show package pricing
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showDuration"
                            checked={formData.showDuration}
                            onChange={(e) => handleInputChange("showDuration", e.target.checked)}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                          />
                          <label htmlFor="showDuration" className="ml-2 block text-sm text-gray-700">
                            Show package duration
                          </label>
                        </div>

                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="showDifficulty"
                            checked={formData.showDifficulty}
                            onChange={(e) => handleInputChange("showDifficulty", e.target.checked)}
                            className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                          />
                          <label htmlFor="showDifficulty" className="ml-2 block text-sm text-gray-700">
                            Show difficulty level
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* SEO Settings Tab */}
                {activeTab === "seo" && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Title *
                      </label>
                      <input
                        type="text"
                        value={formData.seoTitle}
                        onChange={(e) => handleInputChange("seoTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO title for package page"
                      />
                      {errors.seoTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoTitle}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoTitle.length}/70 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Description *
                      </label>
                      <textarea
                        value={formData.seoDescription}
                        onChange={(e) => handleInputChange("seoDescription", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO description for package page"
                      />
                      {errors.seoDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoDescription}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoDescription.length}/160 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Keywords
                      </label>
                      <input
                        type="text"
                        value={formData.seoKeywords}
                        onChange={(e) => handleInputChange("seoKeywords", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Enter keywords separated by commas"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Separate keywords with commas
                      </p>
                    </div>

                    {/* SEO Preview */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Search Engine Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="space-y-1">
                          <div className="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                            {formData.seoTitle || "Your SEO Title"}
                          </div>
                          <div className="text-green-700 text-sm">
                            https://swiftafricasafaris.com/packages
                          </div>
                          <div className="text-gray-600 text-sm">
                            {formData.seoDescription || "Your SEO description will appear here"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
