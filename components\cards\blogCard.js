"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import LazyImage from '../common/LazyImage';

const TravelCard = ({
    image,
    title,
    description,
    slug,
    onButtonClick
}) => {
    const router = useRouter();

    const handleReadMore = () => {
        if (slug) {
            router.push(`/blog/${slug}`);
        } else if (onButtonClick) {
            onButtonClick();
        }
    };

    return (
        <>
            <style>{`
                @media screen and (maxWidth: 768px) {
                    .card-container {
                        aspect-ratio: auto !important;
                        height: auto !important;
                        max-height: 450px !important;
                    }
                    .image-section {
                        height: 200px !important;
                    }
                    .content-section {
                        height: auto !important;
                        min-height: 200px !important;
                        padding: 16px !important;
                    }
                    .card-title {
                        font-size: 1rem !important;
                        margin-bottom: 0.75rem !important;
                    }
                    .card-description {
                        font-size: 0.875rem !important;
                        margin-bottom: 1rem !important;
                    }
                }
            `}</style>
            {/* Image Section */}
            <div className="bg-[var(--primary-background)] shadow-lg overflow-hidden w-full text-left card-container flex flex-col">
                <div className="w-full h-48 overflow-hidden image-section">
                    <LazyImage
                        src={image}
                        alt={title}
                        className="w-full h-full object-cover"
                        width={600}
                        height={200}
                        style={{objectFit: 'cover'}}
                        skeletonVariant="card"
                    />
                </div>
                {/* Content Section */}
                <div className="p-4 flex flex-col flex-grow content-section">
                    <h2
                        className="text-lg font-semibold text-gray-800 mb-2 leading-tight line-clamp-2 card-title"
                        style={{ fontFamily: 'Jost, sans-serif' }}
                    >
                        {title}
                    </h2>
                    <p
                        className="text-gray-600 text-base mb-4 line-clamp-3 flex-grow card-description"
                        style={{ fontFamily: 'Jost, sans-serif' }}
                    >
                        {description}
                    </p>
                    {/* Button */}
                    <div className="flex justify-center">
                        <button
                            onClick={handleReadMore}
                            className="btn py-2 px-6 rounded-lg font-medium"
                            style={{ fontFamily: 'Jost, sans-serif' }}
                        >
                            Read More
                        </button>
                    </div>
                </div>
            </div>
        </>
    );
};

export default TravelCard;