/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState, useRef } from 'react';
import { X } from 'lucide-react';

const SafariBookingModal = ({ onClose }) => {
  const modalRef = useRef(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    whatsapp: '',
    numberOfPeople: '',
    message: ''
  });

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleBookNow = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.fullName || !formData.email || !formData.whatsapp || !formData.numberOfPeople) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/bookings/tour', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fullName: formData.fullName,
          email: formData.email,
          whatsapp: formData.whatsapp,
          numberOfPeople: parseInt(formData.numberOfPeople),
          message: formData.message
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit booking');
      }

      const result = await response.json();
      if (result.success) {
        setShowSuccess(true);
        // Set timeout to close the modal after 3 seconds
        setTimeout(() => {
          resetForm();
          onClose && onClose();
        }, 3000);
      } else {
        throw new Error(result.error || 'Failed to submit booking');
      }
    } catch (error) {
      console.error('Error submitting booking:', error);
      alert('Failed to submit booking. Please try again.');
    }
  };

  const resetForm = () => {
    setShowSuccess(false);
    setFormData({
      fullName: '',
      email: '',
      whatsapp: '',
      numberOfPeople: '',
      message: ''
    });
  };

  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      onClose && onClose();
    }
  };

  return (
    <div className="flex items-center justify-center">
      {/* Modal Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        {/* Modal Content */}
        <div ref={modalRef} className="bg-[var(--white)] rounded-lg shadow-xl max-w-md w-full relative">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-2 right-2 text-[var(--text)] hover:text-[var(--accent)] z-10"
          >
            <X size={20} />
          </button>

          {!showSuccess ? (
            /* Booking Form */
            <div className="p-4">
              <h2 className="text-xl font-bold text-center text-[var(--text)] mb-1">
                Plan Your Tour
              </h2>
              <div className="w-12 h-0.5 bg-[var(--accent)] mx-auto mb-4"></div>

              <div className="space-y-3">
                <input
                  type="text"
                  name="fullName"
                  placeholder="Full Name"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[var(--hero)] rounded-lg border-0 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                />

                <input
                  type="email"
                  name="email"
                  placeholder="Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[var(--hero)] rounded-lg border-0 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                />

                <input
                  type="tel"
                  name="whatsapp"
                  placeholder="WhatsApp"
                  value={formData.whatsapp}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[var(--hero)] rounded-lg border-0 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                />

                <input
                  type="number"
                  name="numberOfPeople"
                  placeholder="Number of people"
                  value={formData.numberOfPeople}
                  onChange={handleInputChange}
                  className="w-full p-3 bg-[var(--hero)] rounded-lg border-0 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                  min="1"
                />

                <textarea
                  name="message"
                  placeholder="Message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full p-3 bg-[var(--hero)] rounded-lg border-0 placeholder-gray-500 resize-none focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                ></textarea>

                <button
                  onClick={handleBookNow}
                  className="w-full btn text-[var(--white)] py-3 rounded-lg font-semibold text-base transition-colors"
                >
                  BOOK NOW
                </button>
              </div>
            </div>
          ) : (
            /* Success Message */
            <div className="p-4 text-center">
              <h2 className="text-xl font-bold text-[var(--text)] mb-1">
                Plan Your Tour
              </h2>
              <div className="w-12 h-0.5 bg-[var(--accent)] mx-auto mb-4"></div>

              <div className="bg-[var(--card-bg)] border-l-4 border-[var(--light-green)] p-4 rounded-lg">
                {/* Animated Checkmark */}
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-[var(--white)] rounded-full flex items-center justify-center shadow-sm">
                    <svg
                      className="w-8 h-8 text-[var(--light-green)] animate-bounce"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      style={{
                        animation: 'checkmark 0.6s ease-in-out'
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={3}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>

                <h3 className="text-xl font-semibold text-[var(--btn)] mb-4">
                  Booking Successful!
                </h3>
                
                <p className="text-gray-600 mb-4">
                  We&apos;re here to help you plan the perfect tour experience.
                </p>
              </div>
            </div>
          )}

          <style jsx>{`
            @keyframes checkmark {
              0% {
                transform: scale(0);
                opacity: 0;
              }
              50% {
                transform: scale(1.2);
              }
              100% {
                transform: scale(1);
                opacity: 1;
              }
            }
          `}</style>
        </div>
      </div>
    </div>
  );
};

export default SafariBookingModal;