import React, { useState } from 'react';
import ReviewCard from '@/components/cards/reviewsCard';

const ReviewsCarousel = ({ reviews, setIsModalOpen }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handlePrev = () => {
    setCurrentIndex((prev) => (prev === 0 ? reviews.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === reviews.length - 1 ? 0 : prev + 1));
  };

  return (
    <div className="w-full overflow-hidden px-4 sm:px-0">
      <div className="w-full sm:max-w-[500px] mx-auto relative">
        <div className="relative">
          <ReviewCard review={reviews[currentIndex]} setIsModalOpen={setIsModalOpen} />

          <button
            onClick={handlePrev}
            className="absolute left-0 sm:left-[-20px] top-1/2 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-md z-10 flex items-center justify-center hover:bg-gray-50"
            style={{ color: 'var(--btn)' }}
          >
            <span className="text-xl sm:text-2xl">&lsaquo;</span>
          </button>

          <button
            onClick={handleNext}
            className="absolute right-0 sm:right-[-20px] top-1/2 transform -translate-y-1/2 w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full shadow-md z-10 flex items-center justify-center hover:bg-gray-50"
            style={{ color: 'var(--btn)' }}
          >
            <span className="text-xl sm:text-2xl">&rsaquo;</span>
          </button>
        </div>

        <div className="flex justify-center gap-2 mt-6">
          {reviews.map((_, idx) => (
            <button
              key={idx}
              className={`w-2 h-2 rounded-full transition-colors ${
                idx === currentIndex ? 'bg-[var(--btn)]' : 'bg-gray-300'
              }`}
              onClick={() => setCurrentIndex(idx)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReviewsCarousel;
