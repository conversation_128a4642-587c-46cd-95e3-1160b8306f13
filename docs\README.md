# Swift Africa Safaris - Development Documentation

## Table of Contents

### 🚀 Getting Started
- [Project Setup](./setup/project-setup.md)
- [Environment Configuration](./setup/environment-config.md)
- [Database Setup](./setup/database-setup.md)

### 🔐 Authentication & Security
- [**Bypass Authentication for Development**](./authentication/bypass-auth-development.md) ⭐
- [Authentication System Overview](./authentication/auth-overview.md)
- [User Management](./authentication/user-management.md)
- [Role-Based Access Control](./authentication/rbac.md)

### 🛠️ Development Guides
- [Admin Panel Development](./development/admin-panel.md)
- [API Development](./development/api-development.md)
- [Component Development](./development/component-development.md)
- [Database Operations](./development/database-operations.md)

### 🎨 Frontend Development
- [UI Components](./frontend/ui-components.md)
- [Styling Guidelines](./frontend/styling-guidelines.md)
- [Form Handling](./frontend/form-handling.md)
- [Image Management](./frontend/image-management.md)

### 🗄️ Backend Development
- [API Routes](./backend/api-routes.md)
- [Database Schema](./backend/database-schema.md)
- [File Upload System](./backend/file-upload.md)
- [Error Handling](./backend/error-handling.md)

### 📦 Package Management
- [Package CRUD Operations](./packages/package-crud.md)
- [Content Block System](./packages/content-blocks.md)
- [Package Editor](./packages/package-editor.md)
- [SEO Management](./packages/seo-management.md)

### 🔧 Troubleshooting
- [Common Issues](./troubleshooting/common-issues.md)
- [Authentication Problems](./troubleshooting/auth-problems.md)
- [API Debugging](./troubleshooting/api-debugging.md)
- [Performance Issues](./troubleshooting/performance.md)

### 🚀 Deployment
- [Production Deployment](./deployment/production.md)
- [Environment Variables](./deployment/environment-variables.md)
- [Security Considerations](./deployment/security.md)

---

## Quick Links

### 🔥 Most Important Guides
1. **[Bypass Authentication for Development](./authentication/bypass-auth-development.md)** - Essential for development workflow
2. [Project Setup](./setup/project-setup.md) - Get started quickly
3. [Admin Panel Development](./development/admin-panel.md) - Build admin features
4. [Package CRUD Operations](./packages/package-crud.md) - Manage travel packages

### 🆘 Need Help?
- Check [Common Issues](./troubleshooting/common-issues.md) first
- Review [Authentication Problems](./troubleshooting/auth-problems.md) for auth-related issues
- Look at [API Debugging](./troubleshooting/api-debugging.md) for API problems

---

## Contributing
When adding new documentation:
1. Follow the existing structure
2. Add entries to this table of contents
3. Use clear, descriptive titles
4. Include code examples where applicable
5. Update related troubleshooting guides

## Documentation Standards
- Use markdown format
- Include code snippets with syntax highlighting
- Add screenshots for UI-related guides
- Keep examples up-to-date with current codebase
- Cross-reference related documentation
