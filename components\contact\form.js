'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/no-unescaped-entities */

import React, { useState } from 'react';

export default function SafariContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    whatsapp: '',
    email: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.whatsapp || !formData.email) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      const response = await fetch('/api/bookings/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          whatsapp: formData.whatsapp,
          email: formData.email,
          message: formData.message
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to submit contact form');
      }

      const result = await response.json();
      if (result.success) {
        alert('Thank you for your message! We will get back to you soon.');
        // Reset form
        setFormData({
          name: '',
          whatsapp: '',
          email: '',
          message: ''
        });
        console.log('Contact form submitted successfully:', result.data);
      } else {
        throw new Error(result.error || 'Failed to submit contact form');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  return (
    <div className="bg-transparent">
      <div className="bg-[var(--secondary-background)] rounded-lg shadow-lg p-6 border border-gray-200 text-left">
        {/* Form Title */}
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          Reach out, Let's plan your Safari
        </h2>

        <form onSubmit={handleSubmit}>
        {/* Name and WhatsApp Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-gray-900 font-medium mb-2">
              Your Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Your Name"
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)] focus:border-transparent placeholder-gray-500 bg-[var(--primary-background)]"
            />
          </div>
          
          <div>
            <label className="block text-gray-900 font-medium mb-2">
              Whatsapp Number <span className="text-red-500">*</span>
            </label>
            <input
              type="tel"
              name="whatsapp"
              value={formData.whatsapp}
              onChange={handleChange}
              placeholder="Whatsapp Number"
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)] focus:border-transparent placeholder-gray-500 bg-[var(--primary-background)]"
            />
          </div>
        </div>

        {/* Email Field */}
        <div className="mb-6">
          <label className="block text-gray-900 font-medium mb-2">
            Email <span className="text-red-500">*</span>
          </label>
          <input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="<EMAIL>"
            required
            className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)] focus:border-transparent placeholder-gray-500 bg-[var(--primary-background)]"
          />
        </div>

        {/* Message Field */}
        <div className="mb-8">
          <label className="block text-gray-900 font-medium mb-2">
            Message
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleChange}
            placeholder="Type your message here"
            rows={4}
            className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)] focus:border-transparent placeholder-gray-500 resize-none h-[80px] md:h-[120px] lg:h-[130px] bg-[var(--primary-background)]"
          />
        </div>


        <div className="lg:flex lg:justify-center lg:items-center lg:gap-4 lg:mb-6">
        {/* Submit Button */}
        <button
          type="submit"
          className="btn w-full lg:w-1/2 md:w-full text-white font-semibold py-4 px-6 rounded-md transition duration-200"
        >
          Send Message
        </button>
        </div>
        </form>
        </div>
    </div>
  );
}