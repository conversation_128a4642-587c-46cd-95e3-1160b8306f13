/* eslint-disable react/no-unescaped-entities */
'use client';

import React from 'react';
import { useCommonNotifications } from '../../../components/partner/NotificationProvider';
import { 
  Bell, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  AlertTriangle,
  Calendar,
  DollarSign,
  Package,
  MessageSquare
} from 'lucide-react';

export default function TestNotificationsPage() {
  const notifications = useCommonNotifications();

  const testNotifications = [
    {
      title: 'Success Notification',
      description: 'Test a success notification',
      action: () => notifications.showSuccess('Success!', 'This is a success message'),
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Error Notification',
      description: 'Test an error notification',
      action: () => notifications.showError('Error!', 'This is an error message'),
      icon: <AlertCircle className="h-5 w-5 text-red-600" />,
      color: 'bg-red-50 border-red-200'
    },
    {
      title: 'Warning Notification',
      description: 'Test a warning notification',
      action: () => notifications.showWarning('Warning!', 'This is a warning message'),
      icon: <AlertTriangle className="h-5 w-5 text-yellow-600" />,
      color: 'bg-yellow-50 border-yellow-200'
    },
    {
      title: 'Info Notification',
      description: 'Test an info notification',
      action: () => notifications.showInfo('Info!', 'This is an info message'),
      icon: <Info className="h-5 w-5 text-blue-600" />,
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'Booking Notification',
      description: 'Test a booking notification',
      action: () => notifications.showBookingNotification('New Booking', 'Booking SA-B2B-001 has been created', {
        actionUrl: '/partner/bookings',
        actionText: 'View Bookings'
      }),
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'Payment Notification',
      description: 'Test a payment notification',
      action: () => notifications.showPaymentNotification('Payment Received', 'Commission payment of $1,500 processed', {
        actionUrl: '/partner/reports',
        actionText: 'View Reports'
      }),
      icon: <DollarSign className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Message Notification',
      description: 'Test a message notification',
      action: () => notifications.showMessageNotification('New Message', 'Admin replied to your inquiry', {
        actionUrl: '/partner/messages',
        actionText: 'Read Message'
      }),
      icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
      color: 'bg-purple-50 border-purple-200'
    },
    {
      title: 'Package Notification',
      description: 'Test a package notification',
      action: () => notifications.showPackageNotification('New Package', 'Zanzibar Beach package is now available', {
        actionUrl: '/partner/packages',
        actionText: 'Browse Packages'
      }),
      icon: <Package className="h-5 w-5 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200'
    }
  ];

  const testCommonNotifications = [
    {
      title: 'Booking Created',
      description: 'Test booking creation notification',
      action: () => notifications.notifyBookingCreated('SA-B2B-TEST', 'Serengeti Safari'),
      icon: <Calendar className="h-5 w-5 text-blue-600" />,
      color: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'Booking Confirmed',
      description: 'Test booking confirmation notification',
      action: () => notifications.notifyBookingConfirmed('SA-B2B-TEST'),
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Payment Received',
      description: 'Test payment notification',
      action: () => notifications.notifyPaymentReceived(2500),
      icon: <DollarSign className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'New Message',
      description: 'Test message notification',
      action: () => notifications.notifyNewMessage('Sarah Johnson'),
      icon: <MessageSquare className="h-5 w-5 text-purple-600" />,
      color: 'bg-purple-50 border-purple-200'
    },
    {
      title: 'Custom Package Approved',
      description: 'Test custom package approval',
      action: () => notifications.notifyCustomPackageResponse('approved'),
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Custom Package Needs Revision',
      description: 'Test custom package revision',
      action: () => notifications.notifyCustomPackageResponse('needs_revision'),
      icon: <AlertTriangle className="h-5 w-5 text-yellow-600" />,
      color: 'bg-yellow-50 border-yellow-200'
    },
    {
      title: 'Form Success',
      description: 'Test form success notification',
      action: () => notifications.notifyFormSuccess('Profile updated'),
      icon: <CheckCircle className="h-5 w-5 text-green-600" />,
      color: 'bg-green-50 border-green-200'
    },
    {
      title: 'Form Error',
      description: 'Test form error notification',
      action: () => notifications.notifyFormError('save profile', 'Network connection failed'),
      icon: <AlertCircle className="h-5 w-5 text-red-600" />,
      color: 'bg-red-50 border-red-200'
    }
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
          <Bell className="mr-3 text-blue-600" />
          Test Notification System
        </h1>
        <p className="text-gray-600 mt-1">Test all notification types and behaviors</p>
      </div>

      {/* Clear All Button */}
      <div className="mb-8">
        <button
          onClick={() => notifications.clearAllNotifications()}
          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Clear All Notifications
        </button>
      </div>

      {/* Basic Notification Types */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Basic Notification Types</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {testNotifications.map((test, index) => (
            <button
              key={index}
              onClick={test.action}
              className={`p-4 rounded-lg border-2 transition-colors hover:shadow-md ${test.color}`}
            >
              <div className="flex items-center mb-2">
                {test.icon}
                <span className="ml-2 font-medium text-gray-900">{test.title}</span>
              </div>
              <p className="text-sm text-gray-600">
                Test notification with &quot;quotes&quot; and special characters
              </p>
            </button>
          ))}
        </div>
      </div>

      {/* Common Business Notifications */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Business Notification Patterns</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {testCommonNotifications.map((test, index) => (
            <button
              key={index}
              onClick={test.action}
              className={`p-4 rounded-lg border-2 transition-colors hover:shadow-md ${test.color}`}
            >
              <div className="flex items-center mb-2">
                {test.icon}
                <span className="ml-2 font-medium text-gray-900">{test.title}</span>
              </div>
              <p className="text-sm text-gray-600 text-left">{test.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Test</h3>
        <div className="space-y-2 text-sm text-gray-600">
          <p>• Click any button above to trigger that notification type</p>
          <p>• Toast notifications will appear in the top-right corner</p>
          <p>• Check the notification bell in the header for persistent notifications</p>
          <p>• Click "Clear All Notifications" to remove all toast notifications</p>
          <p>• Some notifications include action buttons that navigate to relevant pages</p>
          <p>• Notifications auto-dismiss after 5-8 seconds (configurable)</p>
        </div>
      </div>
    </div>
  );
}
