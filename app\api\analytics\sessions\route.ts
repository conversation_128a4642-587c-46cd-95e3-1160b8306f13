import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      sessionId,
      startTime,
      endTime,
      duration,
      pageViews,
      interactions,
      errors,
      deviceInfo,
      url
    } = body

    // Validate required fields
    if (!sessionId || !startTime || !deviceInfo) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, startTime, deviceInfo' },
        { status: 400 }
      )
    }

    // Store session data in Supabase
    const { error } = await supabase
      .from('user_sessions')
      .insert({
        session_id: sessionId,
        start_time: new Date(startTime).toISOString(),
        end_time: endTime ? new Date(endTime).toISOString() : null,
        duration: duration || null,
        page_views: pageViews || 1,
        interactions: interactions || 0,
        errors: errors || 0,
        device_type: deviceInfo.type,
        user_agent: deviceInfo.userAgent,
        viewport_width: deviceInfo.viewport.width,
        viewport_height: deviceInfo.viewport.height,
        connection_type: deviceInfo.connection,
        final_url: url,
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
      })

    if (error) {
      console.error('Error storing session data:', error)
      return NextResponse.json(
        { error: 'Failed to store session data' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Sessions API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    const deviceType = searchParams.get('device_type')
    const minDuration = parseInt(searchParams.get('min_duration') || '0')

    let query = supabase
      .from('user_sessions')
      .select('*')
      .gte('start_time', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .order('start_time', { ascending: false })

    if (deviceType) {
      query = query.eq('device_type', deviceType)
    }

    if (minDuration > 0) {
      query = query.gte('duration', minDuration)
    }

    const { data, error } = await query.limit(1000)

    if (error) {
      console.error('Error fetching session data:', error)
      return NextResponse.json(
        { error: 'Failed to fetch session data' },
        { status: 500 }
      )
    }

    // Calculate session analytics
    const analytics = calculateSessionAnalytics(data || [])

    return NextResponse.json({
      sessions: data || [],
      analytics,
      period_days: days,
      total_sessions: data?.length || 0
    })

  } catch (error) {
    console.error('Sessions GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function calculateSessionAnalytics(sessions: any[]) {
  if (sessions.length === 0) {
    return {
      total_sessions: 0,
      avg_duration: 0,
      avg_page_views: 0,
      avg_interactions: 0,
      bounce_rate: 0,
      device_breakdown: {},
      connection_breakdown: {},
      error_rate: 0
    }
  }

  const totalDuration = sessions.reduce((sum, session) => sum + (session.duration || 0), 0)
  const totalPageViews = sessions.reduce((sum, session) => sum + (session.page_views || 0), 0)
  const totalInteractions = sessions.reduce((sum, session) => sum + (session.interactions || 0), 0)
  const totalErrors = sessions.reduce((sum, session) => sum + (session.errors || 0), 0)
  
  // Bounce rate: sessions with only 1 page view and duration < 30 seconds
  const bounces = sessions.filter(session => 
    session.page_views <= 1 && (session.duration || 0) < 30000
  ).length

  // Device breakdown
  const deviceBreakdown = sessions.reduce((acc: any, session) => {
    acc[session.device_type] = (acc[session.device_type] || 0) + 1
    return acc
  }, {})

  // Connection breakdown
  const connectionBreakdown = sessions.reduce((acc: any, session) => {
    acc[session.connection_type] = (acc[session.connection_type] || 0) + 1
    return acc
  }, {})

  return {
    total_sessions: sessions.length,
    avg_duration: Math.round(totalDuration / sessions.length),
    avg_page_views: Math.round((totalPageViews / sessions.length) * 10) / 10,
    avg_interactions: Math.round((totalInteractions / sessions.length) * 10) / 10,
    bounce_rate: Math.round((bounces / sessions.length) * 100),
    device_breakdown: deviceBreakdown,
    connection_breakdown: connectionBreakdown,
    error_rate: Math.round((totalErrors / sessions.length) * 100) / 100,
    engagement_metrics: {
      high_engagement: sessions.filter(s => (s.duration || 0) > 120000 && s.interactions > 5).length,
      medium_engagement: sessions.filter(s => (s.duration || 0) > 60000 && s.interactions > 2).length,
      low_engagement: sessions.filter(s => (s.duration || 0) < 30000 || s.interactions <= 1).length
    }
  }
}

// PUT endpoint to update session data (for ongoing sessions)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, updates } = body

    if (!sessionId || !updates) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, updates' },
        { status: 400 }
      )
    }

    const { error } = await supabase
      .from('user_sessions')
      .update(updates)
      .eq('session_id', sessionId)

    if (error) {
      console.error('Error updating session data:', error)
      return NextResponse.json(
        { error: 'Failed to update session data' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Sessions PUT error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE endpoint to clean up old sessions
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '90')

    const { error } = await supabase
      .from('user_sessions')
      .delete()
      .lt('start_time', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())

    if (error) {
      console.error('Error deleting old sessions:', error)
      return NextResponse.json(
        { error: 'Failed to delete old sessions' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      message: `Deleted sessions older than ${days} days` 
    })

  } catch (error) {
    console.error('Sessions DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
