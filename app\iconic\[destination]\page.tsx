import type { Metadata } from 'next';
import React from 'react';
import { notFound } from 'next/navigation';
import { MapP<PERSON>, Star, Camera, Heart } from 'lucide-react';
import { iconicData } from '@/components/data/iconicData';
import Navbar from '@/components/header';
import TourismFooter from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata as generateSEOMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

interface PageProps {
  params: Promise<{ destination: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { destination } = await params;
  const selectedDestination = iconicData.destinations.find(d => d.name.toLowerCase() === destination.toLowerCase());

  if (!selectedDestination) {
    return generateSEOMetadata({
      title: 'Destination Not Found',
      description: 'The requested destination could not be found.',
      url: `/iconic/${destination}`
    });
  }

  // Use user-inputted SEO metadata if available, otherwise fallback to defaults
  if (selectedDestination.seo) {
    return generateSEOMetadata({
      title: selectedDestination.seo.title,
      description: selectedDestination.seo.description,
      keywords: selectedDestination.seo.keywords,
      type: 'website',
      url: `/iconic/${destination}`,
      image: selectedDestination.seo.image,
      author: selectedDestination.seo.author,
      publishedTime: selectedDestination.seo.publishedTime,
      modifiedTime: selectedDestination.seo.modifiedTime
    });
  }

  // Fallback to auto-generated metadata
  return generateSEOMetadata({
    title: `${selectedDestination.countryTitle} - Iconic African Safari Destination | Swift Africa Safaris`,
    description: `${selectedDestination.hero.description} Discover ${selectedDestination.name}'s unique wildlife, culture, and conservation efforts with Swift Africa Safaris.`,
    keywords: [
      `${selectedDestination.name} safari`,
      `${selectedDestination.name} wildlife tours`,
      `${selectedDestination.name} travel guide`,
      `${selectedDestination.countryTitle} destinations`,
      'African safari destinations',
      'East Africa travel',
      'wildlife photography',
      'conservation tourism',
      'cultural experiences Africa',
      `visit ${selectedDestination.name}`,
      `${selectedDestination.name} national parks`,
      'sustainable tourism Africa'
    ],
    type: 'website',
    url: `/iconic/${destination}`,
    image: selectedDestination.hero.image
  });
}

const DestinationPage = async ({ params }: PageProps) => {
    const { destination } = await params;

    const selectedDestination = destination
        ? iconicData.destinations.find(d => d.name.toLowerCase() === destination.toLowerCase())
        : null;

    // If destination is provided but not found, show 404
    if (destination && !selectedDestination) {
        notFound();
    }

    if (!selectedDestination) {
        return null;
    }

    const breadcrumbSchema = generateBreadcrumbSchema([
        { name: 'Home', url: '/' },
        { name: 'Iconic Destinations', url: '/iconic' },
        { name: selectedDestination.name, url: `/iconic/${destination}` }
    ]);

    // Use user-inputted schema if available, otherwise generate default
    let destinationPageSchema;
    if (selectedDestination.schema) {
        destinationPageSchema = {
            '@context': 'https://schema.org',
            ...selectedDestination.schema,
            url: `${defaultSEO.siteUrl}/iconic/${destination}`,
            mainEntity: {
                '@type': 'Place',
                name: selectedDestination.name,
                description: selectedDestination.overview.description,
                image: selectedDestination.overview.image,
                containsPlace: selectedDestination.topDestinations.map((dest, index) => ({
                    '@type': 'TouristAttraction',
                    name: dest.name,
                    description: dest.description,
                    image: dest.image,
                    position: index + 1
                })),
                hasOfferCatalog: {
                    '@type': 'OfferCatalog',
                    name: `${selectedDestination.name} Activities`,
                    itemListElement: selectedDestination.topActivities.map((activity, index) => ({
                        '@type': 'Offer',
                        itemOffered: {
                            '@type': 'TouristTrip',
                            name: activity.name,
                            description: activity.description,
                            image: activity.image
                        },
                        position: index + 1
                    }))
                }
            }
        };
    } else {
        // Fallback to auto-generated schema
        destinationPageSchema = {
            '@context': 'https://schema.org',
            '@type': 'TouristDestination',
            name: selectedDestination.countryTitle,
            description: selectedDestination.hero.description,
            url: `${defaultSEO.siteUrl}/iconic/${destination}`,
            image: selectedDestination.hero.image,
            geo: {
                '@type': 'Country',
                name: selectedDestination.name
            },
            touristType: 'Safari Enthusiast',
            mainEntity: {
                '@type': 'Place',
                name: selectedDestination.name,
                description: selectedDestination.overview.description,
                image: selectedDestination.overview.image,
                containsPlace: selectedDestination.topDestinations.map((dest, index) => ({
                    '@type': 'TouristAttraction',
                    name: dest.name,
                    description: dest.description,
                    image: dest.image,
                    position: index + 1
                })),
                hasOfferCatalog: {
                    '@type': 'OfferCatalog',
                    name: `${selectedDestination.name} Activities`,
                    itemListElement: selectedDestination.topActivities.map((activity, index) => ({
                        '@type': 'Offer',
                        itemOffered: {
                            '@type': 'TouristTrip',
                            name: activity.name,
                            description: activity.description,
                            image: activity.image
                        },
                        position: index + 1
                    }))
                }
            }
        };
    }

    return (
        <div>
            <StructuredData data={[breadcrumbSchema, destinationPageSchema]} />
            <Navbar />
            <div className="min-h-screen bg-[var(--primary-background)]">
                {/* Hero Section */}
                <section className="relative h-[70vh] w-full overflow-hidden">
                    <div className="h-full w-full">
                        <img
                            src={selectedDestination.hero.image}
                            alt={selectedDestination.hero.alt}
                            className="h-full w-full object-cover transition-transform duration-800 hover:scale-110"
                            loading="lazy"
                        />
                    </div>
                    <div className="absolute inset-0 bg-black/40" />
                    <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center text-white w-[90%] z-10">
                        <h1 className="text-4xl md:text-6xl font-bold mb-6">{selectedDestination.countryTitle}</h1>
                        <p className="text-lg md:text-xl max-w-3xl mx-auto opacity-90">{selectedDestination.hero.description}</p>
                    </div>
                </section>

                {/* Main Content */}
                <div className="container mx-auto px-4 py-12">
                    {/* Overview Section */}
                    <div className="mb-16">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--text)]">{selectedDestination.overview.title}</h2>
                            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-12 items-center">
                            <div>
                                <p className="text-lg text-gray-700 leading-relaxed mb-6">{selectedDestination.overview.description}</p>

                                {/* Quick Facts */}
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="bg-[var(--secondary-background)] p-4 ">
                                        <div className="flex items-center mb-2">
                                            <MapPin className="w-5 h-5 text-[var(--accent)] mr-2" />
                                            <span className="font-semibold">Country</span>
                                        </div>
                                        <p className="text-gray-700">{selectedDestination.name}</p>
                                    </div>
                                    <div className="bg-[var(--secondary-background)] p-4 ">
                                        <div className="flex items-center mb-2">
                                            <Star className="w-5 h-5 text-[var(--accent)] mr-2" />
                                            <span className="font-semibold">Best For</span>
                                        </div>
                                        <p className="text-gray-700">Wildlife & Culture</p>
                                    </div>
                                </div>
                            </div>

                            <div className="relative">
                                <img
                                    src={selectedDestination.overview.image}
                                    alt={selectedDestination.overview.alt}
                                    className="w-full h-[400px] object-cover  shadow-lg"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Top Destinations Section */}
                    <div className="mb-16">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--text)]">Top Destinations in {selectedDestination.name}</h2>
                            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {selectedDestination.topDestinations.map((destination, index) => (
                                <div key={index} className="bg-[var(--secondary-background)] shadow-md overflow-hidden">
                                    <div className="relative h-48">
                                        <img
                                            src={destination.image}
                                            alt={destination.alt}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div className="p-6">
                                        <div className="flex items-center mb-3">
                                            <MapPin className="w-5 h-5 text-[var(--accent)] mr-2" />
                                            <h3 className="text-xl font-bold text-[var(--text)]">{destination.name}</h3>
                                        </div>
                                        <p className="text-gray-700 leading-relaxed">{destination.description}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Top Activities Section */}
                    <div className="mb-16">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--text)]">Top Activities</h2>
                            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {selectedDestination.topActivities.map((activity, index) => (
                                <div key={index} className="bg-[var(--secondary-background)] shadow-md overflow-hidden">
                                    <div className="relative h-48">
                                        <img
                                            src={activity.image}
                                            alt={activity.alt}
                                            className="w-full h-full object-cover"
                                        />
                                    </div>
                                    <div className="p-6">
                                        <div className="flex items-center mb-3">
                                            <Camera className="w-5 h-5 text-[var(--accent)] mr-2" />
                                            <h3 className="text-xl font-bold text-[var(--text)]">{activity.name}</h3>
                                        </div>
                                        <p className="text-gray-700 leading-relaxed">{activity.description}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Uniqueness Section */}
                    <div className="mb-16">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--text)]">{selectedDestination.uniqueness.title}</h2>
                            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
                        </div>

                        <div className="bg-[var(--secondary-background)] p-8">
                            <p className="text-lg text-gray-700 leading-relaxed mb-8 text-center max-w-4xl mx-auto">
                                {selectedDestination.uniqueness.description}
                            </p>

                            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {selectedDestination.uniqueness.highlights.map((highlight, index) => (
                                    <div key={index} className="flex items-start space-x-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-[var(--accent)] rounded-full flex items-center justify-center mt-1">
                                            <Star className="w-3 h-3 text-white" />
                                        </div>
                                        <p className="text-gray-700 leading-relaxed">{highlight}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Conservation Section */}
                    <div className="mb-16">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[var(--text)]">{selectedDestination.conservation.title}</h2>
                            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
                        </div>

                        <div className="bg-[var(--secondary-background)] p-8">
                            <p className="text-lg text-gray-700 leading-relaxed mb-8 text-center max-w-4xl mx-auto">
                                {selectedDestination.conservation.description}
                            </p>

                            <div className="grid md:grid-cols-2 gap-6">
                                {selectedDestination.conservation.initiatives.map((initiative, index) => (
                                    <div key={index} className="flex items-start space-x-3">
                                        <div className="flex-shrink-0 w-6 h-6 bg-[var(--light-green)] rounded-full flex items-center justify-center mt-1">
                                            <Heart className="w-3 h-3 text-white" />
                                        </div>
                                        <p className="text-gray-700 leading-relaxed">{initiative}</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Call to Action Section */}
                    <div className="bg-[var(--secondary-background)] p-8 text-center">
                        <h2 className="text-3xl font-bold mb-4">Ready to Explore {selectedDestination.name}?</h2>
                        <p className="text-lg text-gray-700 mb-6 max-w-2xl mx-auto">
                            Discover the wonders of {selectedDestination.name} with our expertly crafted safari packages.
                            Contact us today to plan your unforgettable adventure.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <button className="btn text-white font-semibold py-3 px-8 transition-colors duration-200">
                                View Safari Packages
                            </button>
                            <button className="bg-transparent border-2 border-[var(--btn)] text-[var(--btn)] font-semibold py-3 px-8 rounded-lg hover:bg-[var(--btn)] hover:text-white transition-colors duration-200">
                                Contact Us
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <TourismFooter />
        </div>
    );
};

export default DestinationPage;