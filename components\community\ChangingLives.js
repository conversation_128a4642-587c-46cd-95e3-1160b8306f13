"use client";
/* eslint-disable @next/next/no-img-element */


import React, { useState } from 'react';

const ChangingLives = () => {
  const [activeImage, setActiveImage] = useState(null);

  return (
    <section className="py-8 sm:py-12 md:py-16 bg-[var(--secondary-background)] lg:px-12">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12">
          {/* Image Section - First on desktop */}
          <div className="relative w-full lg:w-1/2 h-[400px] sm:h-[450px] md:h-[500px] mt-8 lg:mt-0 animate-fade-right order-2 lg:order-1">
            {/* Main center image */}
            <img
              src={'/images/community/africa-community-impactful-travel.webp'}
              alt="children from Virunga Massif playing around the pyrethrum"
              className={`rounded-full w-[200px] h-[200px] sm:w-[250px] sm:h-[250px] md:w-[280px] md:h-[280px] object-cover border-4 border-white shadow-lg absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-300 ${activeImage === 'main' ? 'z-30 scale-110' : 'z-20'
                }`}
              onClick={() => setActiveImage('main')}
              onMouseEnter={() => setActiveImage('main')}
              onMouseLeave={() => setActiveImage(null)}
            />
            {/* First small image */}
            <img
              src={'/images/community/africa-handcraft-community-experience.webp'}
              alt="a woman making traditional handcraft"
              className={`rounded-full w-[90px] h-[90px] sm:w-[120px] sm:h-[120px] object-cover border-2 border-white shadow-md absolute top-[10%] left-[20%] sm:left-1/4 cursor-pointer transition-all duration-300 ${activeImage === 'first' ? 'z-30 scale-110' : 'z-10 hover:scale-110'
                }`}
              onClick={() => setActiveImage('first')}
              onMouseEnter={() => setActiveImage('first')}
              onMouseLeave={() => setActiveImage(null)}
            />
            {/* New third small image */}
            <img
              src={'/images/community/tea-plantation-women.webp'}
              alt="a woman planting tea with energy"
              className={`rounded-full w-[90px] h-[90px] sm:w-[120px] sm:h-[120px] object-cover border-2 border-white shadow-md absolute bottom-[15%] right-[20%] sm:right-1/4 cursor-pointer transition-all duration-300 ${activeImage === 'third' ? 'z-30 scale-110' : 'z-10 hover:scale-110'
                }`}
              onClick={() => setActiveImage('third')}
              onMouseEnter={() => setActiveImage('third')}
              onMouseLeave={() => setActiveImage(null)}
            />
            {/* Animated dots */}
            <div className="absolute left-[65%] sm:left-[70%] top-[20%] w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-[#d35400] animate-pulse"></div>
            <div className="absolute right-[15%] sm:right-[20%] bottom-[55%] sm:bottom-[60%] w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-[#03a9f4] animate-pulse"></div>
          </div>

          {/* Text Content - Second on desktop */}
          <div className="w-full lg:w-1/2 lg:pl-12 animate-fade-left text-center lg:text-left order-1 lg:order-2">
            <p className="text-[#d35400] text-xs sm:text-sm uppercase tracking-wider mb-3 sm:mb-4">About Us</p>
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-6">In Every Journey, Driven by Impact</h2>
            <p className="text-gray-600 mb-4">
              We champion sustainable travel connecting conscious explorers with authentic African experiences that protect wild places, uplift local communities, and turn every step into lasting impact.
            </p>
            <p className="text-sm sm:text-base mb-6 sm:mb-8"></p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ChangingLives;
