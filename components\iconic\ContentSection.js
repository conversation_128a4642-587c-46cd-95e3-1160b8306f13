import React from 'react';

const ContentSection = ({ title, description, image, alt }) => {
  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4 flex flex-col items-center gap-10">
        <div className="w-full text-center">
          <h2 className="text-4xl font-bold mb-6 text-gray-800">{title}</h2>
          <p className="text-lg text-gray-600 mb-6 max-w-3xl mx-auto">{description}</p>
        </div>
        <div className="w-full max-w-5xl">
          <img
            src={image}
            alt={alt}
            className="w-full h-[500px] object-cover shadow-xl"
          />
        </div>
      </div>
    </section>
  );
};

export default ContentSection;
