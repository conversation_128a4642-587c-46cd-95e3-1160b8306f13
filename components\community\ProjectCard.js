import React from 'react';

const ProjectCard = ({ tag, icon, title, description, image }) => {
  return (
    <div className="relative bg-[var(--primary-background)] shadow-lg transition-all duration-300 hover:-translate-y-2">
      <span className="absolute top-5 left-0 bg-[#d35400] text-white px-4 py-1 rounded-r-full text-sm font-bold z-10">
        {tag}
      </span>
      <div className="h-[220px] overflow-hidden">
        <img src={image} alt={title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
      </div>
      <div className="p-6">
        <div className="w-15 h-15 rounded-full bg-[#d35400] text-white flex items-center justify-center text-2xl mb-4">
          <i className={icon}></i>
        </div>
        <h3 className="text-xl font-bold mb-4 text-left">{title}</h3>
        <p className="text-gray-700 mb-4 text-left">{description}</p>
        <a href="#" className="text-[#163201] font-bold hover:text-[#d35400] transition-colors duration-300 flex items-center gap-2">
          Learn More <i className="fas fa-arrow-right"></i>
        </a>
      </div>
    </div>
  );
};

export default ProjectCard;
