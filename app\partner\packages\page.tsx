'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  MapPin, 
  Clock, 
  Users, 
  Star, 
  Eye,
  ShoppingCart,
  Mountain,
  Camera,
  Compass
} from 'lucide-react';
import Link from 'next/link';

interface Package {
  id: string;
  name: string;
  description: string;
  destination: string;
  duration: string;
  difficulty: 'Easy' | 'Moderate' | 'Challenging';
  category: 'Safari' | 'Cultural' | 'Climbing' | 'Day Trip';
  images: string[];
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  commission: number;
  rating: number;
  reviewCount: number;
  highlights: string[];
  included: string[];
  maxTravelers: number;
  availability: 'Available' | 'Limited' | 'Sold Out';
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerPackagesPage() {
  const [packages, setPackages] = useState<Package[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockPackages: Package[] = [
      {
        id: 'pkg-001',
        name: 'Serengeti & Ngorongoro 5-Day Safari',
        description: 'Experience the best of Tanzania with this comprehensive safari covering Serengeti National Park and Ngorongoro Crater.',
        destination: 'Serengeti & Ngorongoro',
        duration: '5 Days',
        difficulty: 'Easy',
        category: 'Safari',
        images: ['/images/serengeti-1.jpg', '/images/serengeti-2.jpg'],
        pricing: {
          solo: 2400,
          honeymoon: 2200,
          family: 1800,
          group: 1600
        },
        commission: 15,
        rating: 4.8,
        reviewCount: 124,
        highlights: ['Big Five Wildlife', 'Great Migration', 'Ngorongoro Crater', 'Professional Guide'],
        included: ['Accommodation', 'All Meals', 'Game Drives', 'Park Fees'],
        maxTravelers: 8,
        availability: 'Available'
      },
      {
        id: 'pkg-002',
        name: 'Kilimanjaro Climbing Adventure',
        description: 'Conquer Africa\'s highest peak with our experienced guides on the Machame route.',
        destination: 'Mount Kilimanjaro',
        duration: '7 Days',
        difficulty: 'Challenging',
        category: 'Climbing',
        images: ['/images/kilimanjaro-1.jpg', '/images/kilimanjaro-2.jpg'],
        pricing: {
          solo: 2800,
          honeymoon: 2600,
          family: 2400,
          group: 2200
        },
        commission: 12,
        rating: 4.9,
        reviewCount: 89,
        highlights: ['Uhuru Peak Summit', 'Machame Route', 'Professional Guides', 'All Equipment'],
        included: ['Camping Equipment', 'All Meals', 'Guides & Porters', 'Park Fees'],
        maxTravelers: 6,
        availability: 'Limited'
      },
      {
        id: 'pkg-003',
        name: 'Tarangire Day Trip',
        description: 'Perfect day safari to Tarangire National Park, famous for its elephant herds and baobab trees.',
        destination: 'Tarangire National Park',
        duration: '1 Day',
        difficulty: 'Easy',
        category: 'Day Trip',
        images: ['/images/tarangire-1.jpg', '/images/tarangire-2.jpg'],
        pricing: {
          solo: 200,
          honeymoon: 180,
          family: 150,
          group: 120
        },
        commission: 20,
        rating: 4.6,
        reviewCount: 67,
        highlights: ['Elephant Herds', 'Baobab Trees', 'Bird Watching', 'Lunch Included'],
        included: ['Transport', 'Lunch', 'Game Drive', 'Park Fees'],
        maxTravelers: 12,
        availability: 'Available'
      },
      {
        id: 'pkg-004',
        name: 'Maasai Cultural Experience',
        description: 'Immerse yourself in authentic Maasai culture with village visits and traditional ceremonies.',
        destination: 'Maasai Village',
        duration: '2 Days',
        difficulty: 'Easy',
        category: 'Cultural',
        images: ['/images/maasai-1.jpg', '/images/maasai-2.jpg'],
        pricing: {
          solo: 400,
          honeymoon: 350,
          family: 300,
          group: 250
        },
        commission: 18,
        rating: 4.7,
        reviewCount: 45,
        highlights: ['Traditional Dances', 'Village Tour', 'Craft Making', 'Local Meals'],
        included: ['Accommodation', 'All Meals', 'Cultural Activities', 'Guide'],
        maxTravelers: 10,
        availability: 'Available'
      }
    ];
    setPackages(mockPackages);
  }, []);

  const filteredPackages = packages.filter(pkg => {
    const matchesSearch = pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pkg.destination.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pkg.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || pkg.category === categoryFilter;
    const matchesDifficulty = difficultyFilter === 'all' || pkg.difficulty === difficultyFilter;
    
    let matchesPrice = true;
    if (priceRange !== 'all') {
      const lowestPrice = Math.min(...Object.values(pkg.pricing));
      switch (priceRange) {
        case 'under-500':
          matchesPrice = lowestPrice < 500;
          break;
        case '500-1000':
          matchesPrice = lowestPrice >= 500 && lowestPrice < 1000;
          break;
        case '1000-2000':
          matchesPrice = lowestPrice >= 1000 && lowestPrice < 2000;
          break;
        case 'over-2000':
          matchesPrice = lowestPrice >= 2000;
          break;
      }
    }
    
    return matchesSearch && matchesCategory && matchesDifficulty && matchesPrice;
  });

  const totalPages = Math.ceil(filteredPackages.length / itemsPerPage);
  const currentPackages = filteredPackages.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Safari': return <Camera className="h-4 w-4" />;
      case 'Climbing': return <Mountain className="h-4 w-4" />;
      case 'Cultural': return <Users className="h-4 w-4" />;
      case 'Day Trip': return <Compass className="h-4 w-4" />;
      default: return <MapPin className="h-4 w-4" />;
    }
  };

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'Available': return 'bg-green-100 text-green-800';
      case 'Limited': return 'bg-yellow-100 text-yellow-800';
      case 'Sold Out': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getLowestPrice = (pricing: Package['pricing']) => {
    return Math.min(...Object.values(pricing));
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Safari Packages</h1>
        <p className="text-gray-600">Browse and book our exclusive safari packages for your customers</p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search packages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="Safari">Safari</option>
              <option value="Cultural">Cultural</option>
              <option value="Climbing">Climbing</option>
              <option value="Day Trip">Day Trip</option>
            </select>
          </div>

          {/* Difficulty Filter */}
          <div>
            <select
              value={difficultyFilter}
              onChange={(e) => setDifficultyFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Difficulties</option>
              <option value="Easy">Easy</option>
              <option value="Moderate">Moderate</option>
              <option value="Challenging">Challenging</option>
            </select>
          </div>

          {/* Price Range Filter */}
          <div>
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Prices</option>
              <option value="under-500">Under $500</option>
              <option value="500-1000">$500 - $1,000</option>
              <option value="1000-2000">$1,000 - $2,000</option>
              <option value="over-2000">Over $2,000</option>
            </select>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {filteredPackages.length} package{filteredPackages.length !== 1 ? 's' : ''} found
          </div>
          <button className="text-blue-600 hover:text-blue-500 text-sm font-medium">
            Clear all filters
          </button>
        </div>
      </div>

      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {currentPackages.map((pkg) => (
          <div key={pkg.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Package Image */}
            <div className="relative h-48 bg-gray-200">
              <div className="absolute top-3 left-3 flex items-center space-x-2">
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryIcon(pkg.category)} ${getDifficultyColor(pkg.difficulty)}`}>
                  {getCategoryIcon(pkg.category)}
                  <span className="ml-1">{pkg.category}</span>
                </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(pkg.availability)}`}>
                  {pkg.availability}
                </span>
              </div>
              <div className="absolute top-3 right-3 bg-white rounded-full px-2 py-1 text-xs font-medium text-gray-700">
                {pkg.commission}% commission
              </div>
            </div>

            {/* Package Content */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{pkg.name}</h3>
              </div>

              <div className="flex items-center text-sm text-gray-600 mb-3">
                <MapPin className="h-4 w-4 mr-1" />
                <span className="mr-4">{pkg.destination}</span>
                <Clock className="h-4 w-4 mr-1" />
                <span>{pkg.duration}</span>
              </div>

              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{pkg.description}</p>

              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="ml-1 text-sm font-medium text-gray-900">{pkg.rating}</span>
                </div>
                <span className="ml-2 text-sm text-gray-500">({pkg.reviewCount} reviews)</span>
              </div>

              {/* Pricing */}
              <div className="mb-4">
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-sm text-gray-500">Starting from</span>
                    <div className="text-xl font-bold text-gray-900">
                      {formatCurrency(getLowestPrice(pkg.pricing))}
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-sm text-green-600 font-medium">
                      {formatCurrency(getLowestPrice(pkg.pricing) * pkg.commission / 100)} commission
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                <Link href={`/partner/packages/${pkg.id}`} className="flex-1">
                  <button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </button>
                </Link>
                <Link href={`/partner/bookings/create?package=${pkg.id}`} className="flex-1">
                  <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Book Now
                  </button>
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredPackages.length)} of {filteredPackages.length} packages
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-3 py-1 border rounded-md ${
                  currentPage === page
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
