"use client";

import React, { useState, useEffect } from 'react';

function SafariAfricaSection() {
    const images = [
        {
            url: "/images/welcome/mountain-gorilla-trekking-rwanda-adventure.webp",
            alt: "a group of happy tourist after mountain gorilla trekking in Volcanoes National Park"
        },
        {
            url: "/images/welcome/africa-safaris-tanzania.webp",
            alt: "a safari vehicle parked for arranging breakfast before touring"
        },
        {
            url: "/images/welcome/ecotourism-nature-wildlife-adventure.webp",
            alt: "a happy old man walking into the scenic natural of Buhanga eco-park"
        }
    ];

    const [currentImage, setCurrentImage] = useState(0);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentImage((prev) => (prev + 1) % images.length);
        }, 3000); // Change image every 3 seconds

        return () => clearInterval(timer);
    }, [images.length]);

    return (
        <section className="py-4 px-4 bg-[var(--primary-background)]">
            <div className="max-w-7xl mx-auto">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Text Content */}
                    <div className="space-y-6">
                        <div className="text-left">
                            <h2 className="text-2xl lg:text-4xl font-bold text-gray-900 leading-tight mb-4 text-center md:text-center lg:text-justify"
                                style={{ fontFamily: 'Jost, sans-serif', fontSize: '28px' }}>
                                Most Welcome to Swift Africa Safaris,
                                <br />
                                <span className="block">Your Gateway to unforgettable Adventures</span>
                            </h2>

                            <p className="text-justify md:text-justify lg:text-base text-gray-700 leading-relaxed "
                                style={{ fontFamily: 'Jost, sans-serif' }}>
                                Get ready to explore the wonderful landscapes, incredible wildlife, and rich cultural
                                diversity of Rwanda, Tanzania, Uganda, and South Africa. With our experienced local
                                driver guides, every journey becomes a once in a lifetime experience. We bring you
                                closer to nature and culture experience like never before with extrordinary adventures
                                that inspire and make real impact.
                            </p>
                        </div>
                    </div>

                    {/* Image Carousel */}
                    <div className="relative">
                        <div className="relative overflow-hidden shadow-xl">
                            <div className="flex transition-transform duration-500 ease-in-out"
                                style={{ transform: `translateX(-${currentImage * 100}%)` }}>
                                {images.map((image, index) => (
                                    <img
                                        key={index}
                                        src={image.url}
                                        alt={image.alt}
                                        className="w-full h-80 lg:h-[400px] object-cover flex-shrink-0"
                                    />
                                ))}
                            </div>
                            {/* Overlay */}
                            <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent"></div>

                            {/* Indicators */}
                            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                                {images.map((_, index) => (
                                    <div
                                        key={index}
                                        className={`h-2 w-2 rounded-full ${
                                            currentImage === index ? 'bg-white' : 'bg-white/50'
                                        }`}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export default SafariAfricaSection;