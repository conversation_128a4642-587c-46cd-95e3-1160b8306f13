'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Settings, Mail, Shield, TestTube } from 'lucide-react';

interface NewsletterSettings {
  id?: string;
  smtp_email: string;
  smtp_host: string;
  smtp_port: number;
  smtp_secure: boolean;
  created_at?: string;
  updated_at?: string;
}

export default function NewsletterSettingsPage() {
  const router = useRouter();
  const [settings, setSettings] = useState<NewsletterSettings>({
    smtp_email: '<EMAIL>',
    smtp_host: 'smtp.hostinger.com',
    smtp_port: 465,
    smtp_secure: true
  });
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [testingEmail, setTestingEmail] = useState(false);
  const [testEmail, setTestEmail] = useState('');

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/newsletter/settings');
        const data = await response.json();
        
        if (data.success && data.data) {
          setSettings(data.data);
        }
      } catch (error) {
        console.error('Error fetching settings:', error);
      } finally {
        setInitialLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle form submission
  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!settings.smtp_email || !password) {
      alert('SMTP email and password are required');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/newsletter/settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...settings,
          smtp_password: password
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('Newsletter settings saved successfully!');
        setPassword(''); // Clear password field for security
      } else {
        alert(data.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof NewsletterSettings, value: string | number | boolean) => {
    setSettings(prev => ({ ...prev, [field]: value }));
  };

  // Test email configuration
  const handleTestEmail = async () => {
    if (!testEmail.trim()) {
      alert('Please enter a test email address');
      return;
    }

    if (!settings.smtp_email || !password) {
      alert('Please configure and save SMTP settings first');
      return;
    }

    try {
      setTestingEmail(true);
      // This would be a test endpoint that sends a test email
      const response = await fetch('/api/newsletter/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          testEmail: testEmail.trim(),
          settings: { ...settings, smtp_password: password }
        })
      });

      const data = await response.json();
      if (data.success) {
        alert(`Test email sent successfully to ${testEmail}!`);
        setTestEmail('');
      } else {
        alert(data.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Error sending test email:', error);
      alert('Failed to send test email');
    } finally {
      setTestingEmail(false);
    }
  };

  if (initialLoading) {
    return (
      <main className="ml-16 pt-16 min-h-screen" style={{ backgroundColor: 'var(--background)' }}>
        <div className="p-6 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading settings...</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-screen" style={{ backgroundColor: 'var(--background)' }}>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                <ArrowLeft className="w-6 h-6" />
              </button>
              <div className="flex items-center">
                <div 
                  className="p-3 rounded-xl text-white mr-4"
                  style={{ backgroundColor: 'var(--btn)' }}
                >
                  <Settings className="w-8 h-8" />
                </div>
                <div>
                  <h1 
                    className="text-4xl font-bold"
                    style={{ color: 'var(--text)' }}
                  >
                    Newsletter Settings
                  </h1>
                  <p className="text-gray-600 mt-2 text-lg">
                    Configure SMTP settings for newsletter delivery
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Settings Form */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <form onSubmit={handleSaveSettings} className="p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* SMTP Configuration */}
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <Mail className="w-6 h-6 text-blue-600" />
                  <h2 className="text-xl font-semibold text-gray-900">SMTP Configuration</h2>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    SMTP Email Address
                  </label>
                  <input
                    type="email"
                    value={settings.smtp_email}
                    onChange={(e) => handleInputChange('smtp_email', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-600">
                    This email will be used as the sender address
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    SMTP Password
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter SMTP password"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <p className="mt-1 text-sm text-gray-600">
                    Password will be encrypted and stored securely
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    SMTP Host
                  </label>
                  <input
                    type="text"
                    value={settings.smtp_host}
                    onChange={(e) => handleInputChange('smtp_host', e.target.value)}
                    placeholder="smtp.hostinger.com"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      SMTP Port
                    </label>
                    <input
                      type="number"
                      value={settings.smtp_port}
                      onChange={(e) => handleInputChange('smtp_port', parseInt(e.target.value))}
                      placeholder="465"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Secure Connection
                    </label>
                    <select
                      value={settings.smtp_secure.toString()}
                      onChange={(e) => handleInputChange('smtp_secure', e.target.value === 'true')}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="true">SSL/TLS (Secure)</option>
                      <option value="false">No Encryption</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Test Email */}
              <div className="space-y-6">
                <div className="flex items-center gap-3 mb-6">
                  <TestTube className="w-6 h-6 text-green-600" />
                  <h2 className="text-xl font-semibold text-gray-900">Test Configuration</h2>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-blue-900 mb-4">Send Test Email</h3>
                  <p className="text-blue-800 mb-4">
                    Test your SMTP configuration by sending a test email.
                  </p>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-semibold text-blue-900 mb-2">
                        Test Email Address
                      </label>
                      <input
                        type="email"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                        placeholder="Enter email to test"
                        className="w-full px-4 py-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    
                    <button
                      type="button"
                      onClick={handleTestEmail}
                      disabled={testingEmail || !testEmail.trim()}
                      className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                    >
                      <TestTube className="w-4 h-4" />
                      {testingEmail ? 'Sending Test...' : 'Send Test Email'}
                    </button>
                  </div>
                </div>

                {/* Security Notice */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <div className="flex items-start gap-3">
                    <Shield className="w-6 h-6 text-yellow-600 mt-1" />
                    <div>
                      <h3 className="text-lg font-semibold text-yellow-900 mb-2">Security Notice</h3>
                      <ul className="text-yellow-800 space-y-1 text-sm">
                        <li>• SMTP passwords are encrypted before storage</li>
                        <li>• Use app-specific passwords when available</li>
                        <li>• Regularly rotate your SMTP credentials</li>
                        <li>• Monitor email delivery logs for security</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-between items-center pt-8 border-t border-gray-200 mt-8">
              <button
                type="button"
                onClick={() => router.push('/admin/newsletter')}
                className="px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>

              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 text-white rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                style={{ backgroundColor: 'var(--light-green)' }}
              >
                <Save className="w-4 h-4" />
                {loading ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
}
