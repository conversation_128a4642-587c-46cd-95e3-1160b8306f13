# Mini Package Itinerary Migration Summary

## Overview
This document summarizes the changes made to update mini packages to use hours instead of days in their itineraries and remove the Activities, Accommodation, and Meals columns from both package and mini package itinerary tables.

## Database Changes

### 1. Migration Script
- **File**: `scripts/migrate-itinerary-to-hours.sql`
- **Purpose**: Updates database schema to use hours instead of days
- **Changes**:
  - Adds `hour_number` column to both `sas_package_itinerary` and `sas_mini_package_itinerary` tables
  - Copies existing `day_number` values to `hour_number`
  - Removes `day_number`, `activities`, `accommodation`, `meals` columns
  - Updates triggers for both tables

### 2. Test Script
- **File**: `scripts/test-itinerary-migration.sql`
- **Purpose**: Verifies migration was successful
- **Features**: Checks table structures, verifies removed columns, confirms data preservation

## API Changes

### 1. Mini Packages Admin API (`app/api/admin/mini-packages/route.ts`)
- Updated `ItineraryDay` interface to use `hour_number` instead of `day_number`
- Removed `activities`, `accommodation`, `meals` fields from interface
- Updated itinerary data mapping in POST endpoint

### 2. Mini Packages Admin Slug API (`app/api/admin/mini-packages/[slug]/route.ts`)
- Updated itinerary transformation to use `hour` instead of `day`
- Removed activities, accommodation, meals from data transformation
- Updated PUT endpoint to handle new structure

### 3. Public Mini Packages API (`app/api/mini-packages/[slug]/route.ts`)
- Updated SELECT query to use `hour_number` instead of `day_number`
- Removed activities, accommodation, meals from SELECT query

### 4. Database Utils (`lib/database-utils.ts`)
- Updated `fetchMiniPackageSafe` function to use new column structure
- Removed activities, accommodation, meals from SELECT query

## Frontend Changes

### 1. Mini Package Itinerary Component (`components/admin/mini-package/miniPackageItinerary.tsx`)
- **Interface Updates**:
  - Changed `day` to `hour` in `ItineraryItem` interface
  - Removed `activities`, `accommodation`, `meals` fields
- **Function Updates**:
  - Renamed `addNewDay()` to `addNewHour()`
  - Renamed `removeDay()` to `removeHour()`
  - Removed `updateActivities()` and `updateMeals()` functions
- **UI Updates**:
  - Changed "Day" labels to "Hour" throughout component
  - Removed activities, accommodation, meals form fields
  - Updated button text from "Add New Day" to "Add New Hour"

### 2. Package Itinerary Display (`components/readpackage/PackageItinerary.jsx`)
- Updated to use `hour_number` instead of `day_number`
- Changed display from "Day X" to "Hour X"
- Removed activities, accommodation, meals display sections
- Updated state management to use `expandedHour` instead of `expandedDay`

### 3. Admin Pages
- **Add Page** (`app/admin/mini-packages/add/page.tsx`):
  - Updated `ItineraryDay` interface to use `hour` instead of `day`
  - Removed activities, accommodation, meals fields
  - Updated API transformation to use `hour_number`

- **Edit Page** (`app/admin/mini-packages/edit/[slug]/page.tsx`):
  - Same changes as add page
  - Updated data transformation for API calls

## Migration Steps

### 1. Database Migration
```sql
-- Run the migration script
\i scripts/migrate-itinerary-to-hours.sql

-- Verify migration
\i scripts/test-itinerary-migration.sql
```

### 2. Code Deployment
1. Deploy updated API endpoints
2. Deploy updated frontend components
3. Test CRUD operations for mini packages

### 3. Verification
- Verify existing itinerary data is preserved
- Test creating new mini packages with hour-based itineraries
- Test editing existing mini packages
- Verify frontend displays hours correctly

## Breaking Changes

### API Changes
- `day_number` field changed to `hour_number` in all itinerary responses
- `activities`, `accommodation`, `meals` fields removed from itinerary objects
- Frontend components expect `hour` instead of `day` in itinerary data

### Database Schema
- Column names changed in both itinerary tables
- Removed columns: `day_number`, `activities`, `accommodation`, `meals`
- Added column: `hour_number`

## Rollback Plan

If rollback is needed:
1. Backup current data
2. Add back removed columns with default values
3. Copy `hour_number` back to `day_number`
4. Revert code changes
5. Test functionality

## Testing Checklist

- [ ] Run database migration script
- [ ] Run test verification script
- [ ] Test mini package creation (admin)
- [ ] Test mini package editing (admin)
- [ ] Test mini package display (frontend)
- [ ] Verify existing data is preserved
- [ ] Test itinerary CRUD operations
- [ ] Verify no broken API endpoints

## Notes

- The migration preserves existing data by copying day numbers to hour numbers
- All references to "day" in the UI have been changed to "hour"
- The simplified structure removes complex nested data (activities, accommodation, meals)
- This change affects both regular packages and mini packages for consistency
