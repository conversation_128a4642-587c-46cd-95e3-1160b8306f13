import React from 'react';
import Header from '@/components/admin/common/header';
import Sidebar from '@/components/admin/common/sidebar';
import AuthGuard from '@/components/admin/common/AuthGuard';

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AuthGuard>
      <div className="min-h-screen bg-[#b8bbc0]">
        <style dangerouslySetInnerHTML={{ __html: styles }} />
        <Header />
        <Sidebar />
        {children}
      </div>
    </AuthGuard>
  );
}