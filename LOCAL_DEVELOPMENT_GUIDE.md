# Local Development Guide

This guide explains how to set up and troubleshoot your local development environment for the Swift Africa Safaris website.

## 🚨 Common Issues & Solutions

### Issue: Blog Pages Show 404 Errors Locally

**Problem**: Blog pages and mini packages work in production but show "404 Not Found" errors in local development.

**Root Cause**: Network timeout issues when fetching data from Supabase during static page generation in local development.

**Solution**: We've implemented the following fixes:

1. **Disabled Static Generation in Development**: Pages are now generated dynamically in development mode
2. **Added Timeout Handling**: Database operations have proper timeout and error handling
3. **Graceful Fallbacks**: Failed operations fall back to empty data instead of crashing

### Issue: Development Server Hangs During Compilation

**Problem**: The development server gets stuck on "Compiling /blog/[slug]..." or similar messages.

**Root Cause**: Static generation trying to fetch all blog posts/packages from Supabase with network timeouts.

**Solution**: Static generation is now skipped in development mode.

## 🛠️ Setup Instructions

### 1. Environment Variables

Ensure your `.env.local` file contains:

```env
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=https://mtqdzkhkpjutyvorwzjk.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 2. Test Your Environment

Run the development environment test:

```bash
node scripts/test-local-development.mjs
```

This will check:
- Database connectivity
- Blog posts availability
- Mini packages availability
- Local server status

### 3. Start Development Server

```bash
npm run dev
```

The server should start without hanging on compilation.

## 🔧 How the Fixes Work

### 1. Dynamic vs Static Generation

**Production**: Pages are pre-generated at build time for better performance
**Development**: Pages are generated on-demand to avoid timeout issues

### 2. Timeout Handling

All database operations now have:
- 10-second timeouts for data fetching
- 5-second timeouts for static generation
- Graceful error handling with fallbacks

### 3. Development-Specific Configuration

The following configurations are applied in development:
- ISR cache disabled
- Static generation skipped
- Enhanced error logging
- Timeout-aware database utilities

## 📱 Testing Your Local Environment

### Test Blog Pages

1. Visit: `http://localhost:3000/blog`
2. Click on any blog post
3. The page should load (may take a few seconds initially)

### Test Mini Packages

1. Visit: `http://localhost:3000/mini-package`
2. Click on any mini package
3. The page should load with all content

### Expected Behavior

- **First Load**: May take 5-10 seconds as data is fetched from Supabase
- **Subsequent Loads**: Should be faster due to caching
- **No 404 Errors**: Pages should load even if some data fails to fetch
- **Graceful Degradation**: Missing content blocks or images won't break the page

## 🐛 Troubleshooting

### If Pages Still Show 404

1. Check if the development server is running: `http://localhost:3000`
2. Check browser console for JavaScript errors
3. Check terminal for server errors
4. Run the test script: `node scripts/test-local-development.mjs`

### If Database Operations Timeout

This is expected in local development. The fixes ensure:
- Pages still load with available data
- No crashes or infinite loading
- Proper error messages in console

### If Build Process Hangs

1. Stop the build process (Ctrl+C)
2. Clear Next.js cache: `rm -rf .next`
3. Restart: `npm run dev`

## 🚀 Production vs Development Differences

| Feature | Development | Production |
|---------|-------------|------------|
| Static Generation | Disabled | Enabled |
| Page Loading | Dynamic | Pre-generated |
| Timeout Handling | Aggressive (5-10s) | Generous (30s) |
| Error Handling | Graceful fallbacks | Full error reporting |
| Caching | Minimal | Full ISR caching |

## 📞 Need Help?

If you continue to experience issues:

1. Run the diagnostic script: `node scripts/test-local-development.mjs`
2. Check the console logs for specific error messages
3. Verify your Supabase credentials are correct
4. Ensure your internet connection is stable

The local development environment is now configured to handle network issues gracefully while maintaining full functionality in production.
