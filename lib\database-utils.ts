import { supabase } from './supabase';
import { dbConfig, devHelpers } from './config';

/**
 * Utility function to handle database operations with timeout and error handling
 */
export async function withTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number = dbConfig.defaultTimeout,
  operationName: string = 'Database operation'
): Promise<T | null> {
  try {
    if (devHelpers.logFetches) {
      console.log(`Starting ${operationName} with ${timeoutMs}ms timeout`);
    }

    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`${operationName} timed out after ${timeoutMs}ms`));
      }, timeoutMs);
    });

    // Race between the operation and timeout
    const result = await Promise.race([
      operation(),
      timeoutPromise
    ]);

    if (devHelpers.logFetches) {
      console.log(`${operationName} completed successfully`);
    }

    return result;
  } catch (error) {
    if (devHelpers.enableDetailedErrors) {
      console.error(`${operationName} failed:`, error);
    }
    
    // In development, we want to continue gracefully rather than crash
    if (process.env.NODE_ENV === 'development') {
      console.warn(`${operationName} failed in development - continuing with fallback`);
      return null;
    }
    
    throw error;
  }
}

/**
 * Fetch blog post with timeout handling
 */
export async function fetchBlogPostSafe(slug: string) {
  return withTimeout(async () => {
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count,
        seo_title,
        seo_description,
        seo_keywords,
        og_title,
        og_description,
        og_image_url,
        canonical_url,
        robots_index,
        robots_follow,
        schema_data
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();

    if (postError) {
      throw new Error(`Blog post fetch error: ${postError.message}`);
    }

    return post;
  }, dbConfig.defaultTimeout, `Blog post fetch for slug: ${slug}`);
}

/**
 * Fetch blog content blocks with timeout handling
 */
export async function fetchBlogContentBlocksSafe(blogPostId: string) {
  return withTimeout(async () => {
    const { data, error } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', blogPostId)
      .order('sort_order');

    if (error) {
      throw new Error(`Content blocks fetch error: ${error.message}`);
    }

    return data || [];
  }, dbConfig.defaultTimeout, `Content blocks fetch for blog: ${blogPostId}`);
}

/**
 * Fetch related blog posts with timeout handling
 */
export async function fetchRelatedPostsSafe(category: string, excludeId: string) {
  return withTimeout(async () => {
    const { data, error } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, description, hero_image_url, category, published_at')
      .eq('category', category)
      .eq('status', 'published')
      .is('deleted_at', null)
      .neq('id', excludeId)
      .limit(3);

    if (error) {
      throw new Error(`Related posts fetch error: ${error.message}`);
    }

    return data || [];
  }, dbConfig.defaultTimeout, `Related posts fetch for category: ${category}`);
}

/**
 * Fetch mini package data with timeout handling
 */
export async function fetchMiniPackageSafe(slug: string) {
  return withTimeout(async () => {
    const { data, error } = await supabase
      .from('sas_mini_packages')
      .select(`
        *,
        sas_mini_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_mini_package_itinerary (
          id,
          hour_number,
          title,
          description,
          sort_order
        ),
        sas_mini_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .single();

    if (error) {
      throw new Error(`Mini package fetch error: ${error.message}`);
    }

    return data;
  }, dbConfig.defaultTimeout, `Mini package fetch for slug: ${slug}`);
}

/**
 * Generate static params with timeout handling and additional filters
 */
export async function generateStaticParamsSafe(
  tableName: string,
  statusField: string = 'status',
  statusValue: string = 'published'
) {
  // Skip in development
  if (!dbConfig.enableStaticGeneration) {
    console.log(`Skipping static generation for ${tableName} in development mode`);
    return [];
  }

  return withTimeout(async () => {
    let query = supabase
      .from(tableName)
      .select('slug, created_at')
      .eq(statusField, statusValue);

    // Add deleted_at filter for blog posts
    if (tableName === 'sas_blog_posts') {
      query = query.is('deleted_at', null);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Static params fetch error for ${tableName}: ${error.message}`);
    }

    const slugs = data?.map(item => ({ slug: item.slug })) || [];

    if (devHelpers.logFetches) {
      console.log(`Generated ${slugs.length} static params for ${tableName}:`,
        slugs.map(s => s.slug).join(', '));
    }

    return slugs;
  }, dbConfig.staticGenerationTimeout, `Static params generation for ${tableName}`);
}
