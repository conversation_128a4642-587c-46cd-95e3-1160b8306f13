/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  DollarSign
} from 'lucide-react';

interface PackageData {
  id: string;
  title: string;
  slug: string;
  location: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  difficulty: string;
  category: string;
  status: 'active' | 'inactive' | 'draft';
  imageUrl?: string;
  createdAt: string;
  updatedAt: string;
}

const styles = `
  ::-webkit-scrollbar {
    width: 8px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PackagesPage() {
  const router = useRouter();
  const [packages, setPackages] = useState<PackageData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedPackages, setSelectedPackages] = useState<string[]>([]);

  // Load packages
  useEffect(() => {
    fetchPackages();
  }, []);

  const fetchPackages = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        category: categoryFilter,
        limit: '50' // Fetch more for client-side filtering
      });

      const response = await fetch(`/api/admin/packages?${params}`);
      const result = await response.json();

      if (result.success) {
        setPackages(result.data);
      } else {
        console.error('Failed to fetch packages:', result.error);
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPackages = packages.filter(pkg => {
    const matchesSearch = pkg.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pkg.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || pkg.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || pkg.category.toLowerCase() === categoryFilter.toLowerCase();
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getLowestPrice = (pricing: PackageData['pricing']): number => {
    return Math.min(...Object.values(pricing));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'inactive': return 'text-red-600 bg-red-100';
      case 'draft': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-600';
      case 'moderate': return 'text-yellow-600';
      case 'hard': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackages(prev => 
      prev.includes(packageId) 
        ? prev.filter(id => id !== packageId)
        : [...prev, packageId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPackages.length === filteredPackages.length) {
      setSelectedPackages([]);
    } else {
      setSelectedPackages(filteredPackages.map(pkg => pkg.id));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedPackages.length > 0) {
      const confirmed = window.confirm(`Are you sure you want to delete ${selectedPackages.length} package(s)?`);
      if (confirmed) {
        try {
          const response = await fetch('/api/admin/packages', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'delete',
              packageIds: selectedPackages
            })
          });

          const result = await response.json();
          if (result.success) {
            setPackages(prev => prev.filter(pkg => !selectedPackages.includes(pkg.id)));
            setSelectedPackages([]);
          } else {
            console.error('Failed to delete packages:', result.error);
            alert('Failed to delete packages. Please try again.');
          }
        } catch (error) {
          console.error('Error deleting packages:', error);
          alert('Failed to delete packages. Please try again.');
        }
      }
    }
  };

  const handleEdit = (packageSlug: string) => {
    // Navigate to edit page using package slug
    router.push(`/admin/packages/edit/${packageSlug}`);
  };

  const handleView = (packageId: string) => {
    // Navigate to package view page or open modal
    console.log('View package:', packageId);
  };

  const handleDelete = async (packageId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this package?');
    if (confirmed) {
      try {
        const response = await fetch(`/api/admin/packages/${packageId}`, {
          method: 'DELETE'
        });

        const result = await response.json();
        if (result.success) {
          setPackages(prev => prev.filter(pkg => pkg.id !== packageId));
        } else {
          console.error('Failed to delete package:', result.error);
          alert('Failed to delete package. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting package:', error);
        alert('Failed to delete package. Please try again.');
      }
    }
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300 bg-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">P</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Packages Management</h1>
                <p className="text-gray-600">Manage tour packages and itineraries</p>
              </div>
            </div>
            <button
              onClick={() => router.push('/admin/packages/add')}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add New Package
            </button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 bg-white border-b border-gray-200">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search packages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
              </select>

              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Categories</option>
                <option value="wildlife">Wildlife</option>
                <option value="adventure">Adventure</option>
                <option value="cultural">Cultural</option>
                <option value="beach">Beach</option>
              </select>
            </div>

            {/* Bulk Actions */}
            {selectedPackages.length > 0 && (
              <div className="flex gap-2">
                <button
                  onClick={handleBulkDelete}
                  className="flex items-center gap-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  Delete ({selectedPackages.length})
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6" style={{ scrollbarWidth: 'thin', scrollbarColor: '#6B7280 transparent' }}>
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              {/* Table Header */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedPackages.length === filteredPackages.length && filteredPackages.length > 0}
                    onChange={handleSelectAll}
                    className="mr-4"
                  />
                  <div className="grid grid-cols-11 gap-4 w-full text-sm font-medium text-gray-700">
                    <div className="col-span-4">Package</div>
                    <div className="col-span-2">Location</div>
                    <div className="col-span-1">Price</div>
                    <div className="col-span-1">Difficulty</div>
                    <div className="col-span-1">Category</div>
                    <div className="col-span-1">Status</div>
                    <div className="col-span-1">Actions</div>
                  </div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-200">
                {filteredPackages.length === 0 ? (
                  <div className="px-6 py-12 text-center">
                    <div className="mx-auto h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-gray-400 font-bold">P</span>
                    </div>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No packages found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Get started by creating a new package.
                    </p>
                    {!searchTerm && statusFilter === 'all' && categoryFilter === 'all' && (
                      <div className="mt-6">
                        <button
                          onClick={() => router.push('/admin/packages/add')}
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Add New Package
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  filteredPackages.map((pkg) => (
                    <div key={pkg.id} className="px-6 py-4 hover:bg-gray-50">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedPackages.includes(pkg.id)}
                          onChange={() => handleSelectPackage(pkg.id)}
                          className="mr-4"
                        />
                        <div className="grid grid-cols-11 gap-4 w-full">
                          {/* Package Info */}
                          <div className="col-span-4 flex items-center">
                            <div className="flex-shrink-0 h-12 w-12">
                              {pkg.imageUrl ? (
                                <img
                                  className="h-12 w-12 rounded-lg object-cover"
                                  src={pkg.imageUrl}
                                  alt={pkg.title}
                                />
                              ) : (
                                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                  <span className="text-gray-400 font-bold">P</span>
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                                {pkg.title}
                              </div>
                              <div className="text-sm text-gray-500 flex items-center">
                                <Calendar className="w-3 h-3 mr-1" />
                                {new Date(pkg.createdAt).toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          {/* Location */}
                          <div className="col-span-2 flex items-center">
                            <span className="text-sm text-gray-900">{pkg.location}</span>
                          </div>

                          {/* Price */}
                          <div className="col-span-1 flex items-center">
                            <DollarSign className="w-4 h-4 text-gray-400 mr-1" />
                            <span className="text-sm font-medium text-gray-900">
                              ${getLowestPrice(pkg.pricing)}
                            </span>
                          </div>

                          {/* Difficulty */}
                          <div className="col-span-1 flex items-center">
                            <span className={`text-sm font-medium ${getDifficultyColor(pkg.difficulty)}`}>
                              {pkg.difficulty}
                            </span>
                          </div>

                          {/* Category */}
                          <div className="col-span-1 flex items-center">
                            <span className="text-sm text-gray-900">{pkg.category}</span>
                          </div>

                          {/* Status */}
                          <div className="col-span-1 flex items-center">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(pkg.status)}`}>
                              {pkg.status}
                            </span>
                          </div>

                          {/* Actions */}
                          <div className="col-span-1 flex items-center justify-end">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handleView(pkg.id)}
                                className="text-gray-400 hover:text-blue-600 transition-colors"
                                title="View"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleEdit(pkg.slug)}
                                className="text-gray-400 hover:text-blue-600 transition-colors"
                                title="Edit"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(pkg.id)}
                                className="text-gray-400 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
