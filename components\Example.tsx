import React, { useState } from 'react';
import ErrorBoundary from './ErrorBoundary';

// Sample component that might throw an error
const BuggyComponent = () => {
  const [count, setCount] = useState(0);

  if (count === 3) {
    throw new Error('Simulated error!');
  }

  return (
    <button onClick={() => setCount(count + 1)}>
      Click me {count} times
    </button>
  );
};

const Example = () => {
  return (
    <ErrorBoundary fallback={<div>Something went wrong! Please try again.</div>}>
      <BuggyComponent />
    </ErrorBoundary>
  );
};

export default Example;
