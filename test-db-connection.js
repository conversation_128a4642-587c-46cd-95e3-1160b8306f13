// Test script to check Supabase database connection
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Environment variables:');
console.log('SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
console.log('SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set (length: ' + supabaseAnonKey.length + ')' : 'Missing');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  try {
    console.log('\n--- Testing Supabase Connection ---');
    
    // Test 1: Basic connection
    console.log('1. Testing basic connection...');
    const { data: tables, error: tablesError } = await supabase
      .from('sas_packages')
      .select('count', { count: 'exact', head: true });
    
    if (tablesError) {
      console.error('Basic connection failed:', tablesError);
      return;
    }
    
    console.log('✓ Basic connection successful');
    console.log('Total packages in database:', tables);
    
    // Test 2: List some packages
    console.log('\n2. Testing package listing...');
    const { data: packages, error: packagesError } = await supabase
      .from('sas_packages')
      .select('id, title, slug, status')
      .limit(5);
    
    if (packagesError) {
      console.error('Package listing failed:', packagesError);
      return;
    }
    
    console.log('✓ Package listing successful');
    console.log('Sample packages:', packages);
    
    // Test 3: Check for specific slug
    const testSlug = '4-day-rwenzori-hiking-cultural-experience';
    console.log(`\n3. Testing specific slug: ${testSlug}...`);
    const { data: specificPackage, error: specificError } = await supabase
      .from('sas_packages')
      .select('id, title, slug, status')
      .eq('slug', testSlug);
    
    if (specificError) {
      console.error('Specific package query failed:', specificError);
      return;
    }
    
    console.log('✓ Specific package query successful');
    console.log('Found packages with slug:', specificPackage);
    
    // Test 4: Check table structure
    console.log('\n4. Testing table structure...');
    const { data: structure, error: structureError } = await supabase
      .from('sas_packages')
      .select('*')
      .limit(1);
    
    if (structureError) {
      console.error('Table structure query failed:', structureError);
      return;
    }
    
    if (structure && structure.length > 0) {
      console.log('✓ Table structure query successful');
      console.log('Available columns:', Object.keys(structure[0]));
    } else {
      console.log('⚠ Table is empty');
    }
    
    console.log('\n--- All tests completed successfully ---');
    
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

testConnection();
