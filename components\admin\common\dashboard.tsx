/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Users,
  Package,
  Star,
  Calendar,
  MapPin,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  Clock,
  Globe,
  Award,
  Zap,
  Target,
  Heart,
  ShoppingBag,
  Activity,
  BarChart3,
  PieChart,
  MessageSquare
} from 'lucide-react';
import { getDashboardStats, getBookings } from '@/lib/admin-api';
import type { Booking, DashboardStats } from '@/lib/supabase';

interface DashboardProps {
  isExpanded?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ReactNode;
  color: string;
  gradient: string;
  description?: string;
}

interface ActivityItem {
  id: string;
  type: 'booking' | 'review' | 'package' | 'user' | 'donation';
  title: string;
  description: string;
  time: string;
  icon: React.ReactNode;
  color: string;
}

const Dashboard: React.FC<DashboardProps> = () => {
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [stats, setStats] = useState<DashboardStats>({
    totalBookings: 0,
    totalPackages: 0,
    totalRevenue: 0,
    totalReviews: 0,
    totalSubscribers: 0,
    totalUsers: 0,
    totalDonations: 0
  });
  const [recentBookings, setRecentBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30');

  // Button handlers with enhanced feedback
  const handleAddPackage = () => {
    router.push('/admin/tours');
  };

  const handleManageUsers = () => {
    router.push('/admin/user-management');
  };

  const handleViewReports = () => {
    router.push('/admin/reports');
  };

  const handleReviews = () => {
    router.push('/admin/reviews');
  };

  const handleBookings = () => {
    router.push('/admin/bookings');
  };

  const handleBlog = () => {
    router.push('/admin/blog');
  };

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [statsData, bookingsData] = await Promise.all([
          getDashboardStats(),
          getBookings(5, 0)
        ]);
        
        setStats(statsData);
        setRecentBookings(bookingsData.data || []);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Enhanced stats cards with gradients and descriptions
  const statsCards: StatCardProps[] = [
    {
      title: 'Total Bookings',
      value: stats.totalBookings,
      change: '+12.5%',
      changeType: 'increase',
      icon: <ShoppingBag className="w-6 h-6" />,
      color: 'bg-blue-500',
      gradient: 'from-blue-500 to-blue-600',
      description: 'Active reservations'
    },
    {
      title: 'Active Packages',
      value: stats.totalPackages,
      change: '+3',
      changeType: 'increase',
      icon: <Package className="w-6 h-6" />,
      color: 'bg-emerald-500',
      gradient: 'from-emerald-500 to-emerald-600',
      description: 'Available tours'
    },
    {
      title: 'Total Revenue',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      change: '+8.2%',
      changeType: 'increase',
      icon: <DollarSign className="w-6 h-6" />,
      color: 'bg-amber-500',
      gradient: 'from-amber-500 to-amber-600',
      description: 'This month'
    },
    {
      title: 'Customer Reviews',
      value: stats.totalReviews,
      change: '+15.3%',
      changeType: 'increase',
      icon: <Star className="w-6 h-6" />,
      color: 'bg-purple-500',
      gradient: 'from-purple-500 to-purple-600',
      description: 'Average 4.8★'
    },
    {
      title: 'New Subscribers',
      value: stats.totalSubscribers,
      change: '+22.1%',
      changeType: 'increase',
      icon: <Users className="w-6 h-6" />,
      color: 'bg-rose-500',
      gradient: 'from-rose-500 to-rose-600',
      description: 'Newsletter signups'
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      change: '+5.7%',
      changeType: 'increase',
      icon: <Users className="w-6 h-6" />,
      color: 'bg-indigo-500',
      gradient: 'from-indigo-500 to-indigo-600',
      description: 'Partners & agents'
    }
  ];

  const topDestinations = [
    { name: 'Queen Elizabeth National Park', bookings: 89, percentage: 35, image: '/images/hero/kruger-national-park-game-drive.webp' },
    { name: 'Murchison Falls', bookings: 67, percentage: 26, image: '/images/hero/murchison-falls-uganda-safaris.webp' },
    { name: 'Bwindi Impenetrable Forest', bookings: 54, percentage: 21, image: '/images/hero/mountain-gorillas-trekking-rwanda.webp' },
    { name: 'Lake Bunyonyi', bookings: 45, percentage: 18, image: '/images/hero/zanzibar-beach-tanzania-safaris.webp' }
  ];

  const recentActivity: ActivityItem[] = [
    {
      id: '1',
      type: 'booking',
      title: 'New booking received',
      description: 'John Smith booked Gorilla Trekking Adventure',
      time: '2 minutes ago',
      icon: <ShoppingBag className="w-4 h-4" />,
      color: 'bg-blue-500'
    },
    {
      id: '2',
      type: 'review',
      title: 'New review posted',
      description: 'Sarah Johnson gave 5 stars to Serengeti Safari',
      time: '15 minutes ago',
      icon: <Star className="w-4 h-4" />,
      color: 'bg-amber-500'
    },
    {
      id: '3',
      type: 'package',
      title: 'Package updated',
      description: 'Gorilla Trekking package details modified',
      time: '1 hour ago',
      icon: <Package className="w-4 h-4" />,
      color: 'bg-emerald-500'
    },
    {
      id: '4',
      type: 'user',
      title: 'New partner joined',
      description: 'Travel Experts registered as partner',
      time: '3 hours ago',
      icon: <Users className="w-4 h-4" />,
      color: 'bg-purple-500'
    },
    {
      id: '5',
      type: 'donation',
      title: 'Donation received',
      description: 'Anonymous donation of $500 for community project',
      time: '5 hours ago',
      icon: <Heart className="w-4 h-4" />,
      color: 'bg-rose-500'
    }
  ];

  const StatCard: React.FC<StatCardProps> = ({ title, value, change, changeType, icon, color, gradient, description }) => (
    <div className="group relative overflow-hidden bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
      <div className="absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-300" style={{ background: `linear-gradient(135deg, ${color}, ${color}dd)` }}></div>
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-xl bg-gradient-to-br ${gradient} text-white shadow-lg`}>
            {icon}
          </div>
          <div className="text-right">
            <div className="flex items-center">
            {changeType === 'increase' ? (
              <ArrowUpRight className="w-4 h-4 text-green-500 mr-1" />
            ) : (
              <ArrowDownRight className="w-4 h-4 text-red-500 mr-1" />
            )}
              <span className={`text-sm font-semibold ${changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`}>
              {change}
            </span>
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium text-gray-600 mb-1">{title}</h3>
          <p className="text-3xl font-bold text-gray-900 mb-1">{value}</p>
          {description && (
            <p className="text-sm text-gray-500">{description}</p>
          )}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <main className="ml-16 pt-16 min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="p-6 min-h-full flex items-center justify-center">
          <div className="text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
              <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-400 animate-ping"></div>
            </div>
            <p className="text-gray-600 mt-6 text-lg font-medium">Loading your dashboard...</p>
            <p className="text-gray-500 mt-2">Fetching the latest data</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      <div className="p-6 min-h-full">
        {/* Enhanced Header Section */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="mb-6 lg:mb-0">
                <div className="flex items-center mb-4">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white mr-4">
                    <Globe className="w-8 h-8" />
                  </div>
            <div>
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent">
                      Dashboard Overview
                    </h1>
                    <p className="text-gray-600 mt-2 text-lg">
                      Welcome back! Here&apos;s what&apos;s happening with Swift Africa Safaris today.
                    </p>
                  </div>
                </div>
            </div>
              <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
                <div className="text-center sm:text-right">
                  <p className="text-sm text-gray-500 mb-1">Current Time</p>
                  <p className="text-xl font-semibold text-gray-900">
                    {currentTime.toLocaleTimeString()}
                  </p>
                  <p className="text-lg text-gray-600">
                    {currentTime.toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={selectedPeriod}
                    onChange={(e) => setSelectedPeriod(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                  >
                    <option value="7">Last 7 days</option>
                    <option value="30">Last 30 days</option>
                    <option value="90">Last 90 days</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {statsCards.map((stat, index) => (
            <div key={index} className="transform hover:scale-105 transition-transform duration-300">
              <StatCard {...stat} />
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 mb-8">
          {/* Enhanced Recent Bookings */}
          <div className="xl:col-span-2 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg text-white mr-3">
                  <Calendar className="w-5 h-5" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Recent Bookings</h2>
              </div>
              <button 
                onClick={handleBookings}
                className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium text-sm"
              >
                View All
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-4 px-3 font-semibold text-gray-600 text-sm">Customer</th>
                    <th className="text-left py-4 px-3 font-semibold text-gray-600 text-sm">Package</th>
                    <th className="text-left py-4 px-3 font-semibold text-gray-600 text-sm">Amount</th>
                    <th className="text-left py-4 px-3 font-semibold text-gray-600 text-sm">Status</th>
                    <th className="text-left py-4 px-3 font-semibold text-gray-600 text-sm">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {recentBookings.map((booking) => (
                    <tr key={booking.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                      <td className="py-4 px-3">
                        <div>
                          <div className="font-semibold text-gray-900">{booking.customer_name}</div>
                          <div className="text-sm text-gray-500">{booking.customer_email}</div>
                        </div>
                      </td>
                      <td className="py-4 px-3">
                        <div className="text-sm text-gray-900 max-w-xs truncate">{booking.package_name}</div>
                      </td>
                      <td className="py-4 px-3">
                        <div className="font-bold text-gray-900">${booking.amount.toLocaleString()}</div>
                      </td>
                      <td className="py-4 px-3">
                        <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                          booking.status === 'confirmed'
                            ? 'bg-green-100 text-green-800'
                            : booking.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {booking.status}
                        </span>
                      </td>
                      <td className="py-4 px-3">
                        <div className="text-sm text-gray-600">{new Date(booking.created_at).toLocaleDateString()}</div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Enhanced Top Destinations */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg text-white mr-3">
                  <MapPin className="w-5 h-5" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Top Destinations</h2>
              </div>
              <PieChart className="w-5 h-5 text-emerald-500" />
            </div>
            <div className="space-y-4">
              {topDestinations.map((destination, index) => (
                <div key={index} className="group p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-lg overflow-hidden mr-3">
                        <img 
                          src={destination.image} 
                          alt={destination.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <span className="font-semibold text-gray-900 text-sm">{destination.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">{destination.bookings}</div>
                      <div className="text-xs text-gray-500">{destination.percentage}%</div>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${destination.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Bottom Section */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Enhanced Quick Actions */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg text-white mr-3">
                <Zap className="w-5 h-5" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <button 
                onClick={handleAddPackage}
                className="group p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-300 border border-blue-200 hover:border-blue-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Package className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">Add Package</span>
                  <span className="text-xs text-gray-500 mt-1">Create new tour</span>
                </div>
              </button>
              
              <button 
                onClick={handleManageUsers}
                className="group p-6 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-xl hover:from-emerald-100 hover:to-emerald-200 transition-all duration-300 border border-emerald-200 hover:border-emerald-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Users className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">Manage Users</span>
                  <span className="text-xs text-gray-500 mt-1">Partners & agents</span>
                </div>
              </button>
              
              <button 
                onClick={handleViewReports}
                className="group p-6 bg-gradient-to-br from-amber-50 to-amber-100 rounded-xl hover:from-amber-100 hover:to-amber-200 transition-all duration-300 border border-amber-200 hover:border-amber-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <BarChart3 className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">View Reports</span>
                  <span className="text-xs text-gray-500 mt-1">Analytics & insights</span>
                </div>
              </button>
              
              <button 
                onClick={handleReviews}
                className="group p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-300 border border-purple-200 hover:border-purple-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <MessageSquare className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">Reviews</span>
                  <span className="text-xs text-gray-500 mt-1">Customer feedback</span>
                </div>
              </button>

              <button 
                onClick={handleBookings}
                className="group p-6 bg-gradient-to-br from-rose-50 to-rose-100 rounded-xl hover:from-rose-100 hover:to-rose-200 transition-all duration-300 border border-rose-200 hover:border-rose-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-rose-500 to-rose-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <ShoppingBag className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">Bookings</span>
                  <span className="text-xs text-gray-500 mt-1">Manage reservations</span>
                </div>
              </button>

              <button 
                onClick={handleBlog}
                className="group p-6 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl hover:from-indigo-100 hover:to-indigo-200 transition-all duration-300 border border-indigo-200 hover:border-indigo-300"
              >
                <div className="flex flex-col items-center">
                  <div className="p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg text-white mb-3 group-hover:scale-110 transition-transform duration-300">
                    <Award className="w-6 h-6" />
                  </div>
                  <span className="font-semibold text-gray-900 text-sm">Blog</span>
                  <span className="text-xs text-gray-500 mt-1">Content management</span>
                </div>
              </button>
            </div>
          </div>

          {/* Enhanced Recent Activity */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-rose-500 to-rose-600 rounded-lg text-white mr-3">
                  <Activity className="w-5 h-5" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Recent Activity</h2>
              </div>
              <Clock className="w-5 h-5 text-rose-500" />
            </div>
            <div className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-xl hover:bg-gray-50 transition-colors duration-200">
                  <div className={`p-2 rounded-lg text-white ${activity.color}`}>
                    {activity.icon}
                </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900">{activity.title}</p>
                    <p className="text-sm text-gray-600 truncate">{activity.description}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
              </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced Footer */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <p className="text-sm text-gray-500">
            Swift Africa Safaris Dashboard - Last updated: {currentTime.toLocaleString()}
          </p>
            <div className="flex items-center justify-center mt-2 space-x-4">
              <div className="flex items-center text-xs text-gray-400">
                <Target className="w-3 h-3 mr-1" />
                <span>Real-time data</span>
              </div>
              <div className="flex items-center text-xs text-gray-400">
                <TrendingUp className="w-3 h-3 mr-1" />
                <span>Performance optimized</span>
              </div>
              <div className="flex items-center text-xs text-gray-400">
                <Heart className="w-3 h-3 mr-1" />
                <span>Built with care</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default Dashboard;
