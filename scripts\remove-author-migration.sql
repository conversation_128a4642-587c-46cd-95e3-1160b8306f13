-- Migration Script: Remove Author Field from Blog System
-- Execute this script in your Supabase SQL Editor to remove author column
-- Run this AFTER backing up your data if needed

-- =============================================
-- 1. REMOVE AUTHOR COLUMN FROM BLOG POSTS TABLE
-- =============================================

-- Remove the author column from sas_blog_posts table
ALTER TABLE sas_blog_posts DROP COLUMN IF EXISTS author;

-- =============================================
-- 2. UPDATE ANY EXISTING INDEXES (if any reference author)
-- =============================================

-- Drop any indexes that might reference the author column
-- (Add specific index drops here if you have any author-related indexes)

-- =============================================
-- 3. VERIFICATION QUERY
-- =============================================

-- Run this to verify the column has been removed
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'sas_blog_posts' 
-- ORDER BY ordinal_position;

-- =============================================
-- NOTES:
-- =============================================
-- 1. This migration removes the author column permanently
-- 2. Make sure to backup your data before running this migration
-- 3. Update your application code to remove author references before running this
-- 4. The author_name and author_email columns in sas_blog_comments table are kept
--    as they are for comment authors, not blog post authors
