'use client'

import React, { useState, useEffect } from 'react'

interface RobotsTestResult {
  test_type: string
  timestamp: string
  result: any
}

export default function RobotsTestingDashboard() {
  const [testResults, setTestResults] = useState<RobotsTestResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedTest, setSelectedTest] = useState('comprehensive')
  const [customContent, setCustomContent] = useState('')
  const [showCustomTester, setShowCustomTester] = useState(false)

  const runTest = async (testType: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/robots-test?test=${testType}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setTestResults(data)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const testCustomContent = async () => {
    if (!customContent.trim()) {
      setError('Please enter robots.txt content to test')
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/robots-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: customContent })
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setTestResults(data)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'protected': return 'text-green-600 bg-green-100'
      case 'vulnerable': return 'text-red-600 bg-red-100'
      case 'allowed': return 'text-green-600 bg-green-100'
      case 'blocked': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Robots.txt Testing & Validation</h1>
          <p className="text-gray-600">Test and validate robots.txt implementation for SEO compliance</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={selectedTest}
            onChange={(e) => setSelectedTest(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="comprehensive">Comprehensive Test</option>
            <option value="validate">Validation Only</option>
            <option value="crawlability">Crawlability Test</option>
            <option value="security">Security Test</option>
            <option value="user-agent">User Agent Test</option>
          </select>
          
          <button
            onClick={() => runTest(selectedTest)}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Testing...
              </>
            ) : (
              'Run Test'
            )}
          </button>
          
          <button
            onClick={() => setShowCustomTester(!showCustomTester)}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm"
          >
            {showCustomTester ? 'Hide' : 'Custom'} Tester
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Test Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
              <button 
                onClick={() => setError(null)}
                className="mt-3 bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Content Tester */}
      {showCustomTester && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Custom Robots.txt Content</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Robots.txt Content
              </label>
              <textarea
                value={customContent}
                onChange={(e) => setCustomContent(e.target.value)}
                placeholder="User-agent: *&#10;Disallow: /admin/&#10;Allow: /&#10;&#10;Sitemap: https://example.com/sitemap.xml"
                className="w-full h-40 border border-gray-300 rounded-md px-3 py-2 text-sm font-mono"
              />
            </div>
            <button
              onClick={testCustomContent}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm"
            >
              Test Custom Content
            </button>
          </div>
        </div>
      )}

      {/* Test Results */}
      {testResults && (
        <>
          {/* Comprehensive Results */}
          {testResults.test_type === 'comprehensive' && (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-lg shadow p-6 text-center">
                  <div className={`text-3xl font-bold ${getScoreColor(testResults.result.overall_score)}`}>
                    {testResults.result.overall_score}%
                  </div>
                  <div className="text-sm text-gray-600">Overall Score</div>
                </div>
                
                <div className="bg-white rounded-lg shadow p-6 text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(testResults.result.validation.score)}`}>
                    {testResults.result.validation.score}%
                  </div>
                  <div className="text-sm text-gray-600">Validation</div>
                </div>
                
                <div className="bg-white rounded-lg shadow p-6 text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(testResults.result.crawlability.score)}`}>
                    {testResults.result.crawlability.score}%
                  </div>
                  <div className="text-sm text-gray-600">Crawlability</div>
                </div>
                
                <div className="bg-white rounded-lg shadow p-6 text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(testResults.result.security.score)}`}>
                    {testResults.result.security.score}%
                  </div>
                  <div className="text-sm text-gray-600">Security</div>
                </div>
              </div>

              {/* Issues Summary */}
              {testResults.result.validation.issues.length > 0 && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Issues Found</h3>
                  <div className="space-y-3">
                    {testResults.result.validation.issues.map((issue: any, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{issue.message}</h4>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              issue.type === 'error' ? 'bg-red-100 text-red-800' :
                              issue.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {issue.type.toUpperCase()}
                            </span>
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              issue.impact === 'high' ? 'bg-red-100 text-red-800' :
                              issue.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {issue.impact.toUpperCase()}
                            </span>
                          </div>
                        </div>
                        
                        {issue.fix && (
                          <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded mt-2">
                            <strong>Fix:</strong> {issue.fix}
                          </div>
                        )}
                        
                        {issue.details && (
                          <div className="text-sm text-gray-600 mt-2">
                            <strong>Details:</strong>
                            <pre className="mt-1 bg-gray-50 p-2 rounded text-xs overflow-x-auto">
                              {JSON.stringify(issue.details, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Crawlability Details */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Crawlability Test Results</h3>
                <div className="space-y-4">
                  {testResults.result.crawlability.details.map((test: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">User Agent: {test.user_agent}</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {test.results.map((result: any, resultIndex: number) => (
                          <div key={resultIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm font-medium">{result.path}</span>
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                              result.allowed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {result.allowed ? 'ALLOWED' : 'BLOCKED'}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Security Details */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Test Results</h3>
                <div className="space-y-4">
                  {testResults.result.security.details.map((test: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">User Agent: {test.user_agent}</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {test.results.map((result: any, resultIndex: number) => (
                          <div key={resultIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="text-sm font-medium">{result.path}</span>
                            <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(result.security_status)}`}>
                              {result.security_status.toUpperCase()}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Other Test Results */}
          {testResults.test_type !== 'comprehensive' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 capitalize">
                {testResults.test_type.replace('-', ' ')} Results
              </h3>
              <pre className="bg-gray-50 p-4 rounded text-sm overflow-x-auto">
                {JSON.stringify(testResults.result, null, 2)}
              </pre>
            </div>
          )}

          {/* Recommendations */}
          {testResults.result.recommendations && testResults.result.recommendations.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Recommendations</h3>
              <ul className="space-y-2">
                {testResults.result.recommendations.map((recommendation: string, index: number) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                    <span className="text-blue-800">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Test Timestamp */}
          <div className="text-center text-sm text-gray-500">
            Last tested: {new Date(testResults.timestamp).toLocaleString()}
          </div>
        </>
      )}

      {/* Initial State */}
      {!testResults && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test Robots.txt</h3>
          <p className="text-gray-600 mb-4">
            Test your robots.txt implementation for SEO compliance, crawlability, and security.
          </p>
          <button
            onClick={() => runTest('comprehensive')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium"
          >
            Start Comprehensive Test
          </button>
        </div>
      )}
    </div>
  )
}
