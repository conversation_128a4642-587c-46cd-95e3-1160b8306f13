/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { Mountain, Plus, Search, Eye, Edit, Trash2, MapPin, Star, Image } from 'lucide-react';
import Link from 'next/link';

interface IconicDestination {
  id: number;
  name: string;
  location: string;
  description: string;
  highlights: string[];
  featuredImage: string;
  gallery: string[];
  status: 'active' | 'inactive';
  featured: boolean;
  rating: number;
  visitorsPerYear: number;
  bestTimeToVisit: string;
  activities: string[];
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function IconicDestinationsPage() {
  const [destinations, setDestinations] = useState<IconicDestination[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockDestinations: IconicDestination[] = [
      {
        id: 1,
        name: 'Serengeti National Park',
        location: 'Northern Tanzania',
        description: 'Home to the Great Migration and endless plains teeming with wildlife.',
        highlights: ['Great Migration', 'Big Five', 'Endless Plains', 'Hot Air Balloon Safaris'],
        featuredImage: '/images/destinations/serengeti.jpg',
        gallery: ['/images/destinations/serengeti-1.jpg', '/images/destinations/serengeti-2.jpg'],
        status: 'active',
        featured: true,
        rating: 4.9,
        visitorsPerYear: 350000,
        bestTimeToVisit: 'June - October',
        activities: ['Game Drives', 'Hot Air Balloon', 'Walking Safaris', 'Photography']
      },
      {
        id: 2,
        name: 'Ngorongoro Crater',
        location: 'Northern Tanzania',
        description: 'A UNESCO World Heritage Site and natural wonder of the world.',
        highlights: ['Crater Floor', 'Dense Wildlife', 'Maasai Culture', 'Scenic Views'],
        featuredImage: '/images/destinations/ngorongoro.jpg',
        gallery: ['/images/destinations/ngorongoro-1.jpg', '/images/destinations/ngorongoro-2.jpg'],
        status: 'active',
        featured: true,
        rating: 4.8,
        visitorsPerYear: 200000,
        bestTimeToVisit: 'Year Round',
        activities: ['Game Drives', 'Cultural Tours', 'Crater Rim Walks', 'Bird Watching']
      },
      {
        id: 3,
        name: 'Mount Kilimanjaro',
        location: 'Northern Tanzania',
        description: 'Africa\'s highest peak and the world\'s tallest free-standing mountain.',
        highlights: ['Uhuru Peak', 'Multiple Climatic Zones', 'Glaciers', 'Sunrise Views'],
        featuredImage: '/images/destinations/kilimanjaro.jpg',
        gallery: ['/images/destinations/kilimanjaro-1.jpg', '/images/destinations/kilimanjaro-2.jpg'],
        status: 'active',
        featured: false,
        rating: 4.7,
        visitorsPerYear: 50000,
        bestTimeToVisit: 'January - March, June - October',
        activities: ['Mountain Climbing', 'Trekking', 'Photography', 'Cultural Tours']
      }
    ];
    setDestinations(mockDestinations);
  }, []);

  const filteredDestinations = destinations.filter(destination => {
    const matchesSearch = destination.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         destination.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         destination.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || destination.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredDestinations.length / itemsPerPage);
  const currentDestinations = filteredDestinations.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={14}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Mountain className="mr-3 text-green-600" />
                Iconic Destinations
              </h1>
              <p className="text-gray-600 mt-1">Manage featured destinations and attractions</p>
            </div>
            <Link href="/admin/iconic-destinations/create">
              <button className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center">
                <Plus size={16} className="mr-2" />
                Add New Destination
              </button>
            </Link>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search destinations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
            <div className="text-sm text-gray-600">
              {filteredDestinations.length} destination{filteredDestinations.length !== 1 ? 's' : ''} found
            </div>
          </div>
        </div>

        {/* Destinations Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentDestinations.map((destination) => (
              <div key={destination.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                {/* Destination Image */}
                <div className="h-48 bg-gray-200 relative">
                  {destination.featuredImage ? (
                    <img
                      src={destination.featuredImage}
                      alt={destination.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Image size={48} className="text-gray-400" />
                    </div>
                  )}
                  <div className="absolute top-3 left-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(destination.status)}`}>
                      {destination.status}
                    </span>
                  </div>
                  {destination.featured && (
                    <div className="absolute top-3 right-3">
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                        Featured
                      </span>
                    </div>
                  )}
                </div>

                {/* Destination Content */}
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                    {destination.name}
                  </h3>
                  
                  <div className="flex items-center mb-2">
                    <MapPin size={14} className="text-gray-400 mr-1" />
                    <span className="text-sm text-gray-600">{destination.location}</span>
                  </div>

                  <div className="flex items-center mb-3">
                    {renderStars(destination.rating)}
                    <span className="ml-2 text-sm text-gray-600">{destination.rating}</span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {destination.description}
                  </p>

                  {/* Highlights */}
                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-1">Highlights:</div>
                    <div className="flex flex-wrap gap-1">
                      {destination.highlights.slice(0, 3).map((highlight, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded">
                          {highlight}
                        </span>
                      ))}
                      {destination.highlights.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          +{destination.highlights.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <div className="text-gray-500">Visitors/Year</div>
                      <div className="font-medium">{destination.visitorsPerYear.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Best Time</div>
                      <div className="font-medium">{destination.bestTimeToVisit}</div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye size={16} />
                      </button>
                      <Link href={`/admin/iconic-destinations/${destination.id}`}>
                        <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                          <Edit size={16} />
                        </button>
                      </Link>
                      <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                        <Trash2 size={16} />
                      </button>
                    </div>
                    <div className="text-xs text-gray-500">
                      {destination.activities.length} activities
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-8">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredDestinations.length)} of {filteredDestinations.length} destinations
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-1 border rounded-md ${
                      currentPage === page
                        ? 'bg-[var(--accent)] text-white border-[var(--accent)]'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
