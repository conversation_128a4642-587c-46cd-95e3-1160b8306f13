import React, { useState } from 'react';

export default function NewsletterSignup() {
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [messageType, setMessageType] = useState(''); // 'success' or 'error'

    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!email.trim()) {
            setMessage('Please enter your email address');
            setMessageType('error');
            return;
        }

        if (!validateEmail(email)) {
            setMessage('Please enter a valid email address');
            setMessageType('error');
            return;
        }

        setLoading(true);
        setMessage('');

        try {
            const response = await fetch('/api/newsletter/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email.trim().toLowerCase() }),
            });

            const data = await response.json();

            if (data.success) {
                setMessage('Thank you for subscribing to our newsletter!');
                setMessageType('success');
                setEmail('');
            } else {
                setMessage(data.error || 'Something went wrong. Please try again.');
                setMessageType('error');
            }
        } catch (error) {
            console.error('Newsletter subscription error:', error);
            setMessage('Network error. Please check your connection and try again.');
            setMessageType('error');
        } finally {
            setLoading(false);
            // Clear message after 5 seconds
            setTimeout(() => {
                setMessage('');
                setMessageType('');
            }, 5000);
        }
    };

    return (
        <div
            className="w-full py-8 px-6"
            style={{ 
                backgroundColor: 'var(--btn)',
                borderBottom: '1px solid var(--accent)'
            }}
        >
            <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
                <h2
                    className="text-2xl md:text-3xl font-bold text-left"
                    style={{ color: 'var(--white)' }}
                >
                    JOIN THE NEWSLETTER
                </h2>

                <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
                    <div className="flex flex-col">
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            placeholder="Enter your Email"
                            disabled={loading}
                            className="px-4 py-2 min-w-0 sm:min-w-72 rounded-md border-2 focus:outline-none focus:border-[var(--accent)] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                            style={{
                                backgroundColor: 'var(--white)',
                                borderColor: messageType === 'error' ? 'var(--accent)' : 'var(--btn-background)',
                                color: 'var(--text)'
                            }}
                        />
                        {message && (
                            <div
                                className="mt-2 text-sm px-2 py-1 rounded"
                                style={{
                                    color: messageType === 'success' ? 'var(--light-green)' : 'var(--accent)',
                                    backgroundColor: messageType === 'success' ? 'rgba(49, 113, 0, 0.1)' : 'rgba(211, 84, 0, 0.1)'
                                }}
                            >
                                {message}
                            </div>
                        )}
                    </div>
                    <button
                        type="submit"
                        onClick={handleSubmit}
                        disabled={loading}
                        className="px-6 py-2 rounded-md font-semibold text-white transition-all hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
                        style={{
                            backgroundColor: 'var(--light-green)'
                        }}
                    >
                        {loading ? 'SUBSCRIBING...' : 'SUBSCRIBE'}
                    </button>
                </div>
            </div>
        </div>
    );
}