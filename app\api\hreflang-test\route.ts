import { NextRequest, NextResponse } from 'next/server'
import { 
  hreflangConfigs, 
  generateHreflangTags, 
  getPriorityRegions,
  HreflangRegion 
} from '@/lib/seo'
import { 
  detectRegionFromRequest, 
  getRegionalContent, 
  shouldShowRegionalContent,
  localizePackage 
} from '@/lib/hreflang-utils'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'validate'
    const url = searchParams.get('url') || '/'
    const region = searchParams.get('region') as HreflangRegion

    switch (action) {
      case 'validate':
        return await validateHreflang(url)
      
      case 'detect-region':
        return detectRegion(request)
      
      case 'test-localization':
        return testLocalization(region || 'x-default', url)
      
      case 'generate-tags':
        return generateTags(url)
      
      case 'audit':
        return await auditHreflangImplementation()
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('Hreflang test API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function validateHreflang(url: string) {
  const results = {
    url,
    timestamp: new Date().toISOString(),
    validation: {
      hreflang_tags: [],
      errors: [],
      warnings: [],
      recommendations: []
    }
  }

  try {
    // Generate hreflang tags for the URL
    const hreflangTags = generateHreflangTags(url, { priorityRegionsOnly: true })
    results.validation.hreflang_tags = hreflangTags

    // Validate each hreflang tag
    for (const tag of hreflangTags) {
      // Check if region code is valid
      if (!hreflangConfigs[tag.hrefLang as HreflangRegion]) {
        results.validation.errors.push({
          type: 'invalid_region',
          message: `Invalid hreflang region: ${tag.hrefLang}`,
          tag: tag.hrefLang
        })
      }

      // Check URL format
      try {
        new URL(tag.href)
      } catch {
        results.validation.errors.push({
          type: 'invalid_url',
          message: `Invalid URL format: ${tag.href}`,
          tag: tag.hrefLang
        })
      }

      // Check for self-referencing x-default
      if (tag.hrefLang === 'x-default' && !tag.href.includes('swiftafricasafaris.com')) {
        results.validation.warnings.push({
          type: 'x_default_warning',
          message: 'x-default should point to the main domain',
          tag: tag.hrefLang
        })
      }
    }

    // Check for required regions
    const requiredRegions = ['x-default', 'en-US', 'en-GB', 'en-ZA']
    const presentRegions = hreflangTags.map(tag => tag.hrefLang)
    
    for (const required of requiredRegions) {
      if (!presentRegions.includes(required)) {
        results.validation.warnings.push({
          type: 'missing_required_region',
          message: `Missing recommended region: ${required}`,
          recommendation: `Add hreflang tag for ${required}`
        })
      }
    }

    // Check for reciprocal links (simplified check)
    if (hreflangTags.length > 0) {
      results.validation.recommendations.push({
        type: 'reciprocal_check',
        message: 'Ensure all hreflang URLs have reciprocal links pointing back',
        action: 'Verify each regional page includes complete hreflang set'
      })
    }

    return NextResponse.json(results)

  } catch (error) {
    results.validation.errors.push({
      type: 'validation_error',
      message: `Validation failed: ${error}`,
      tag: 'system'
    })
    
    return NextResponse.json(results, { status: 500 })
  }
}

function detectRegion(request: NextRequest) {
  const detection = detectRegionFromRequest(request)
  const regionalContent = getRegionalContent(detection.region)
  
  return NextResponse.json({
    detected_region: detection.region,
    confidence: detection.confidence,
    detection_method: detection.method,
    regional_content: regionalContent,
    headers_analyzed: {
      'accept-language': request.headers.get('accept-language'),
      'cf-ipcountry': request.headers.get('cf-ipcountry'),
      'x-forwarded-for': request.headers.get('x-forwarded-for'),
      'user-agent': request.headers.get('user-agent')?.substring(0, 100) + '...'
    }
  })
}

function testLocalization(region: HreflangRegion, url: string) {
  const regionalContent = getRegionalContent(region)
  
  // Test package localization
  const samplePackage = {
    title: 'Gorilla Trekking Safari',
    description: 'Experience the magnificent mountain gorillas in their natural habitat',
    price: 1500,
    features: ['Professional guide', 'All meals included', 'Transportation', 'Permits included']
  }
  
  const localizedPackage = localizePackage(samplePackage, region)
  
  // Test content visibility rules
  const contentVisibility = {
    pricing: shouldShowRegionalContent(region, 'pricing'),
    testimonials: shouldShowRegionalContent(region, 'testimonials'),
    contact: shouldShowRegionalContent(region, 'contact'),
    features: shouldShowRegionalContent(region, 'features')
  }
  
  return NextResponse.json({
    region,
    url,
    regional_content: regionalContent,
    localized_package: localizedPackage,
    content_visibility: contentVisibility,
    region_config: hreflangConfigs[region]
  })
}

function generateTags(url: string) {
  const allTags = generateHreflangTags(url)
  const priorityTags = generateHreflangTags(url, { priorityRegionsOnly: true })
  
  // Generate HTML format
  const htmlTags = allTags.map(tag => 
    `<link rel="alternate" hreflang="${tag.hrefLang}" href="${tag.href}" />`
  )
  
  // Generate XML sitemap format
  const xmlTags = allTags.map(tag => 
    `<xhtml:link rel="alternate" hreflang="${tag.hrefLang}" href="${tag.href}" />`
  )
  
  // Generate HTTP header format
  const httpHeaders = allTags.map(tag => 
    `<${tag.href}>; rel="alternate"; hreflang="${tag.hrefLang}"`
  ).join(', ')
  
  return NextResponse.json({
    url,
    all_tags: allTags,
    priority_tags: priorityTags,
    formats: {
      html: htmlTags,
      xml: xmlTags,
      http_header: httpHeaders
    },
    statistics: {
      total_regions: allTags.length,
      priority_regions: priorityTags.length,
      regions_by_area: {
        africa: allTags.filter(tag => {
          const config = hreflangConfigs[tag.hrefLang as HreflangRegion]
          return config?.region === 'africa'
        }).length,
        europe: allTags.filter(tag => {
          const config = hreflangConfigs[tag.hrefLang as HreflangRegion]
          return config?.region === 'europe'
        }).length,
        'north-america': allTags.filter(tag => {
          const config = hreflangConfigs[tag.hrefLang as HreflangRegion]
          return config?.region === 'north-america'
        }).length,
        oceania: allTags.filter(tag => {
          const config = hreflangConfigs[tag.hrefLang as HreflangRegion]
          return config?.region === 'oceania'
        }).length
      }
    }
  })
}

async function auditHreflangImplementation() {
  const audit = {
    timestamp: new Date().toISOString(),
    configuration: {
      total_regions: Object.keys(hreflangConfigs).length,
      priority_regions: getPriorityRegions().length,
      regions_by_priority: {}
    },
    implementation_status: {
      metadata_generation: 'implemented',
      sitemap_integration: 'implemented',
      middleware_headers: 'implemented',
      react_components: 'implemented'
    },
    recommendations: [],
    next_steps: []
  }

  // Analyze region distribution by priority
  const priorityDistribution: Record<number, number> = {}
  Object.values(hreflangConfigs).forEach(config => {
    priorityDistribution[config.priority] = (priorityDistribution[config.priority] || 0) + 1
  })
  audit.configuration.regions_by_priority = priorityDistribution

  // Generate recommendations
  const highPriorityRegions = Object.values(hreflangConfigs).filter(config => config.priority >= 8)
  if (highPriorityRegions.length < 5) {
    audit.recommendations.push({
      type: 'expand_high_priority',
      message: 'Consider adding more high-priority regions for better international coverage',
      priority: 'medium'
    })
  }

  // Check for regional balance
  const regionCounts = Object.values(hreflangConfigs).reduce((acc, config) => {
    acc[config.region] = (acc[config.region] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  if (regionCounts.africa && regionCounts.africa < 3) {
    audit.recommendations.push({
      type: 'expand_african_markets',
      message: 'Consider adding more African markets given the safari focus',
      priority: 'high'
    })
  }

  // Next steps
  audit.next_steps = [
    'Test hreflang implementation with Google Search Console',
    'Monitor international organic traffic growth',
    'A/B test regional content variations',
    'Implement currency conversion API for real-time pricing',
    'Add more regional testimonials and case studies'
  ]

  return NextResponse.json(audit)
}
