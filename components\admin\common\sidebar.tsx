'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Home,
  Package,
  Users,
  FileText,
  ShoppingCart,
  BarChart3,
  LogOut,
  ChevronDown,
  User,
  MessageSquare,
  Calendar,
  DollarSign,
  MapPin,
  Globe,
  BookOpen,
  Mail} from 'lucide-react';

interface MenuItem {
  name: string;
  icon: React.ReactNode;
  submenu?: SubMenuItem[];
  href?: string;
}

interface SubMenuItem {
  name: string;
  href: string;
}

interface MenuSection {
  title: string;
  items: MenuItem[];
}

const Sidebar: React.FC = () => {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState<string>('Dashboard');
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  const menuItems: MenuSection[] = [
    {
      title: "Main Menu",
      items: [
        { name: 'Dashboard', icon: <BarChart3 size={20} />, href: '/admin' },
        { name: 'Comments', icon: <MessageSquare size={20} />, href: '/admin/comments' },
        { name: 'Orders', icon: <ShoppingCart size={20} />, href: '/admin/orders' },
        { name: 'Subscribers', icon: <Users size={20} />, href: '/admin/subscribers' },
        {
          name: 'Newsletter',
          icon: <Mail size={20} />,
          submenu: [
            { name: 'Subscribers', href: '/admin/newsletter' },
            { name: 'Compose Newsletter', href: '/admin/newsletter/compose' },
            { name: 'Settings', href: '/admin/newsletter/settings' }
          ]
        },
        {
          name: 'Bookings',
          icon: <Calendar size={20} />,
          submenu: [
            { name: 'Packages', href: '/admin/bookings/packages' },
            { name: 'Mini Package Bookings', href: '/admin/bookings/mini-packages' },
            { name: 'Tour Bookings', href: '/admin/bookings/tour' },
            { name: 'Apartment Bookings', href: '/admin/bookings/apartment' },
            { name: 'Car Hire Bookings', href: '/admin/bookings/car' },
            { name: 'Volunteering Applications', href: '/admin/bookings/volunteering' },
            { name: 'Contact Submissions', href: '/admin/bookings/contact' },
            { name: 'Email Notifications', href: '/admin/bookings/emails' }
          ]
        },
        { name: 'Donations Raised', icon: <DollarSign size={20} />, href: '/admin/donations' },
        { name: 'Travel Agent', icon: <Package size={20} />, href: '/admin/travel-agent' }
      ]
    },
    {
      title: "Content Management",
      items: [
        {
          name: 'Blog',
          icon: <BookOpen size={20} />,
          submenu: [
            { name: 'All Blog Posts', href: '/admin/blog' },
            { name: 'Add New Post', href: '/admin/blog/add' }
          ]
        },
        {
          name: 'Projects',
          icon: <Package size={20} />,
          submenu: [
            { name: 'All Projects', href: '/admin/projects' },
            { name: 'Add New Project', href: '/admin/projects/add' }
          ]
        },
        {
          name: 'Volunteering Projects',
          icon: <Users size={20} />,
          submenu: [
            { name: 'All Projects', href: '/admin/volunteering-project' },
            { name: 'Add New Project', href: '/admin/volunteering-project/create' }
          ]
        },
        {
          name: 'Packages',
          icon: <Package size={20} />,
          submenu: [
            { name: 'All Packages', href: '/admin/packages' },
            { name: 'Add New Package', href: '/admin/packages/add' }
          ]
        },
        {
          name: 'Iconic Destinations',
          icon: <Globe size={20} />,
          submenu: [
            { name: 'All Destinations', href: '/admin/iconic-destinations' },
            { name: 'Add New Destination', href: '/admin/iconic-destinations/create' }
          ]
        },
        {
          name: 'Mini Packages',
          icon: <Package size={20} />,
          submenu: [
            { name: 'All Mini Packages', href: '/admin/mini-packages' },
            { name: 'Add New Package', href: '/admin/mini-packages/create' }
          ]
        }
      ]
    },
    {
      title: "Page Management",
      items: [
        { name: 'Homepage Hero', icon: <Home size={20} />, href: '/admin/home' },
        { name: 'Blog Page', icon: <FileText size={20} />, href: '/admin/blog-page' },
        { name: 'Contact Us Page', icon: <MapPin size={20} />, href: '/admin/contact' },
        { name: 'About Us Page', icon: <Home size={20} />, href: '/admin/about' },
        { name: 'Package Page', icon: <Package size={20} />, href: '/admin/package-page' },
        {
          name: 'Partners',
          icon: <Users size={20} />,
          submenu: [
            { name: 'All Partners', href: '/admin/partners' },
            { name: 'Add New Partner', href: '/admin/partners/create' }
          ]
        }
      ]
    },
    {
      title: "Settings",
      items: [
        {
          name: 'User Management',
          icon: <User size={20} />,
          submenu: [
            { name: 'All Users', href: '/admin/user-management' },
            { name: 'Add New User', href: '/admin/user-management/create' },
            { name: 'Roles & Permissions', href: '/admin/user-management/roles' }
          ]
        },
        { name: 'System Logs', icon: <LogOut size={20} />, href: '/admin/system-logs' }
      ]
    }
  ];

  return (
    <aside
      className={`fixed left-0 top-16 ${isExpanded ? 'w-64' : 'w-16'} h-[calc(100vh-4rem)] bg-[var(--primary-background)] transition-all duration-300 ease-in-out z-40`}
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      <div 
        className={`h-full overflow-y-auto overflow-x-hidden ${
          isExpanded ? 'custom-scrollbar' : 'no-scrollbar'
        }`}
        style={{
          '--scrollbar-color': '#718096',
          '--scrollbar-track': '#EDF2F7',
          '--scrollbar-width': '8px',
        } as React.CSSProperties}
      >
        <style jsx>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: var(--scrollbar-width);
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background-color: var(--scrollbar-color);
            border-radius: 4px;
          }
          .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: var(--scrollbar-color) var(--scrollbar-track);
          }
          .no-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .no-scrollbar::-webkit-scrollbar {
            display: none;
          }
        `}</style>
        <nav className="space-y-4 px-3 pt-6 text-justify">
          {menuItems.map((section, index) => (
            <div key={index} className="nav-section pb-3">
              {isExpanded && (
                <div className="text-xs uppercase font-bold text-gray-600 mb-3 px-3 tracking-wider">
                  {section.title}
                </div>
              )}
              <div className="space-y-1">
                {section.items.map((item) => (
                  <div key={item.name} className="group">
                    {item.href ? (
                      <Link href={item.href}>
                        <div
                          className={`w-full flex items-center ${isExpanded ? 'space-x-3 px-3' : 'justify-center px-2'
                            } py-2.5 rounded-lg text-left transition-all duration-200 cursor-pointer ${pathname === item.href
                              ? 'bg-[#d3d3d3] text-[var(--accent)] shadow-sm'
                              : 'text-gray-700 hover:bg-[#d3d3d3]/50'
                            }`}
                        >
                          <span className="transition-transform duration-200">
                            {item.icon}
                          </span>
                          {isExpanded && (
                            <span className="font-medium truncate">
                              {item.name}
                            </span>
                          )}
                        </div>
                      </Link>
                    ) : (
                      <button
                        onClick={() => setActiveItem(item.name)}
                        className={`w-full flex items-center ${isExpanded ? 'space-x-3 px-3' : 'justify-center px-2'
                          } py-2.5 rounded-lg text-left transition-all duration-200 ${activeItem === item.name
                            ? 'bg-[#d3d3d3] text-[var(--accent)] shadow-sm'
                            : 'text-gray-700 hover:bg-[#d3d3d3]/50'
                          }`}
                      >
                        <span className="transition-transform duration-200">
                          {item.icon}
                        </span>
                        {isExpanded && (
                          <>
                            <span className="font-medium truncate">
                              {item.name}
                            </span>
                            {item.submenu && (
                              <ChevronDown
                                size={16}
                                className="ml-auto transform transition-transform duration-200 group-hover:rotate-90"
                              />
                            )}
                          </>
                        )}
                      </button>
                    )}
                    {item.submenu && (
                      <div className={`
                        overflow-hidden transition-all duration-200
                        ${isExpanded ? 'max-h-0 group-hover:max-h-[500px]' : 'max-h-0'}
                      `}>
                        <div className="bg-[#d3d3d3] rounded-b-lg mt-1 py-1 px-2">
                          {item.submenu.map((subItem) => (
                            <Link key={subItem.name} href={subItem.href}>
                              <div
                                className={`w-full text-left py-2 px-4 text-sm rounded-md cursor-pointer
                                  ${pathname === subItem.href
                                    ? 'bg-[#b8bbc0]/60 text-[var(--accent)] font-medium'
                                    : 'text-gray-600 hover:bg-[#b8bbc0]/60 hover:text-gray-900'
                                  }
                                `}
                              >
                                {subItem.name}
                              </div>
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
