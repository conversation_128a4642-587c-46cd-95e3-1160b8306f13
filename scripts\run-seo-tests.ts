#!/usr/bin/env tsx

/**
 * Automated SEO Testing Script
 * Run comprehensive SEO tests and generate reports
 */

import { 
  validatePageMetadata, 
  validateHreflang, 
  validateStructuredData, 
  validateContentSEO,
  SEOValidationResult 
} from '../lib/seo-validation'

interface TestConfig {
  baseUrl: string
  testPages: Array<{
    url: string
    type: 'homepage' | 'package' | 'blog' | 'static'
    expectedData?: any
  }>
  contentTests: Array<{
    type: 'package' | 'blog'
    slug: string
  }>
}

const defaultConfig: TestConfig = {
  baseUrl: 'https://swiftafricasafaris.com',
  testPages: [
    { url: '/', type: 'homepage' },
    { url: '/about', type: 'static' },
    { url: '/package', type: 'static' },
    { url: '/blog', type: 'static' },
    { url: '/contact', type: 'static' }
  ],
  contentTests: [
    { type: 'package', slug: 'gorilla-trekking-rwanda' },
    { type: 'blog', slug: 'best-time-visit-rwanda' }
  ]
}

class SEOTestRunner {
  private config: TestConfig
  private results: Array<{
    test: string
    result: SEOValidationResult
    timestamp: string
  }> = []

  constructor(config: TestConfig = defaultConfig) {
    this.config = config
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting comprehensive SEO tests...\n')

    // Test metadata for all pages
    await this.testPageMetadata()

    // Test hreflang implementation
    await this.testHreflangImplementation()

    // Test structured data
    await this.testStructuredDataSchemas()

    // Test content SEO
    await this.testContentSEO()

    // Generate report
    this.generateReport()
  }

  private async testPageMetadata(): Promise<void> {
    console.log('📄 Testing page metadata...')

    for (const page of this.config.testPages) {
      try {
        const result = await validatePageMetadata(page.url, page.expectedData)
        this.results.push({
          test: `Metadata: ${page.url}`,
          result,
          timestamp: new Date().toISOString()
        })

        const status = result.isValid ? '✅' : '❌'
        console.log(`  ${status} ${page.url} (Score: ${result.score}%)`)
        
        if (result.issues.length > 0) {
          result.issues.forEach(issue => {
            const icon = issue.type === 'error' ? '🔴' : issue.type === 'warning' ? '🟡' : '🔵'
            console.log(`    ${icon} ${issue.message}`)
          })
        }
      } catch (error) {
        console.log(`  ❌ ${page.url} - Test failed: ${error}`)
      }
    }
    console.log()
  }

  private async testHreflangImplementation(): Promise<void> {
    console.log('🌍 Testing hreflang implementation...')

    const testUrls = ['/', '/package/gorilla-trekking-rwanda', '/blog/best-time-visit-rwanda']

    for (const url of testUrls) {
      try {
        const result = await validateHreflang(url)
        this.results.push({
          test: `Hreflang: ${url}`,
          result,
          timestamp: new Date().toISOString()
        })

        const status = result.isValid ? '✅' : '❌'
        console.log(`  ${status} ${url} (Score: ${result.score}%)`)
        
        if (result.issues.length > 0) {
          result.issues.forEach(issue => {
            const icon = issue.type === 'error' ? '🔴' : issue.type === 'warning' ? '🟡' : '🔵'
            console.log(`    ${icon} ${issue.message}`)
          })
        }
      } catch (error) {
        console.log(`  ❌ ${url} - Test failed: ${error}`)
      }
    }
    console.log()
  }

  private async testStructuredDataSchemas(): Promise<void> {
    console.log('📊 Testing structured data schemas...')

    const schemaTests = [
      { type: 'organization', data: null },
      { type: 'website', data: null },
      { 
        type: 'article', 
        data: {
          title: 'Best Time to Visit Rwanda',
          description: 'Discover the perfect time to visit Rwanda for gorilla trekking',
          author: 'Swift Africa Safaris',
          publishedTime: '2024-01-01T00:00:00Z',
          url: '/blog/best-time-visit-rwanda'
        }
      },
      {
        type: 'product',
        data: {
          name: 'Gorilla Trekking Rwanda',
          description: 'Experience mountain gorillas in their natural habitat',
          price: 1500,
          currency: 'USD',
          availability: 'InStock',
          url: '/package/gorilla-trekking-rwanda'
        }
      }
    ]

    for (const test of schemaTests) {
      try {
        const result = await validateStructuredData(test.type, test.data)
        this.results.push({
          test: `Structured Data: ${test.type}`,
          result,
          timestamp: new Date().toISOString()
        })

        const status = result.isValid ? '✅' : '❌'
        console.log(`  ${status} ${test.type} schema (Score: ${result.score}%)`)
        
        if (result.issues.length > 0) {
          result.issues.forEach(issue => {
            const icon = issue.type === 'error' ? '🔴' : issue.type === 'warning' ? '🟡' : '🔵'
            console.log(`    ${icon} ${issue.message}`)
          })
        }
      } catch (error) {
        console.log(`  ❌ ${test.type} schema - Test failed: ${error}`)
      }
    }
    console.log()
  }

  private async testContentSEO(): Promise<void> {
    console.log('📝 Testing content SEO...')

    for (const content of this.config.contentTests) {
      try {
        const result = await validateContentSEO(content.type, content.slug)
        this.results.push({
          test: `Content SEO: ${content.type}/${content.slug}`,
          result,
          timestamp: new Date().toISOString()
        })

        const status = result.isValid ? '✅' : '❌'
        console.log(`  ${status} ${content.type}/${content.slug} (Score: ${result.score}%)`)
        
        if (result.issues.length > 0) {
          result.issues.forEach(issue => {
            const icon = issue.type === 'error' ? '🔴' : issue.type === 'warning' ? '🟡' : '🔵'
            console.log(`    ${icon} ${issue.message}`)
          })
        }
      } catch (error) {
        console.log(`  ❌ ${content.type}/${content.slug} - Test failed: ${error}`)
      }
    }
    console.log()
  }

  private generateReport(): void {
    console.log('📊 SEO Test Report')
    console.log('==================\n')

    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.result.isValid).length
    const failedTests = totalTests - passedTests

    const averageScore = this.results.reduce((sum, r) => sum + r.result.score, 0) / totalTests

    console.log(`Total Tests: ${totalTests}`)
    console.log(`Passed: ${passedTests} ✅`)
    console.log(`Failed: ${failedTests} ❌`)
    console.log(`Average Score: ${Math.round(averageScore)}%`)
    console.log(`Overall Status: ${passedTests === totalTests ? '✅ ALL PASSED' : '❌ ISSUES FOUND'}\n`)

    // Group issues by category
    const allIssues = this.results.flatMap(r => r.result.issues)
    const issuesByCategory = allIssues.reduce((acc, issue) => {
      if (!acc[issue.category]) {
        acc[issue.category] = []
      }
      acc[issue.category].push(issue)
      return acc
    }, {} as Record<string, any[]>)

    if (Object.keys(issuesByCategory).length > 0) {
      console.log('Issues by Category:')
      console.log('-------------------')
      
      Object.entries(issuesByCategory).forEach(([category, issues]) => {
        console.log(`\n${category.toUpperCase()}:`)
        const errorCount = issues.filter(i => i.type === 'error').length
        const warningCount = issues.filter(i => i.type === 'warning').length
        const infoCount = issues.filter(i => i.type === 'info').length
        
        console.log(`  Errors: ${errorCount} 🔴`)
        console.log(`  Warnings: ${warningCount} 🟡`)
        console.log(`  Info: ${infoCount} 🔵`)
      })
    }

    // Generate recommendations
    const allRecommendations = this.results.flatMap(r => r.result.recommendations)
    const uniqueRecommendations = [...new Set(allRecommendations)]

    if (uniqueRecommendations.length > 0) {
      console.log('\nRecommendations:')
      console.log('----------------')
      uniqueRecommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`)
      })
    }

    console.log('\n🎉 SEO testing completed!')
    
    // Save detailed report to file
    this.saveDetailedReport()
  }

  private saveDetailedReport(): void {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total_tests: this.results.length,
        passed: this.results.filter(r => r.result.isValid).length,
        failed: this.results.filter(r => !r.result.isValid).length,
        average_score: Math.round(this.results.reduce((sum, r) => sum + r.result.score, 0) / this.results.length)
      },
      detailed_results: this.results,
      config: this.config
    }

    const fs = require('fs')
    const path = require('path')
    
    const reportPath = path.join(process.cwd(), 'seo-test-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    console.log(`📄 Detailed report saved to: ${reportPath}`)
  }
}

// CLI execution
async function main() {
  const args = process.argv.slice(2)
  const configPath = args.find(arg => arg.startsWith('--config='))?.split('=')[1]
  
  let config = defaultConfig
  
  if (configPath) {
    try {
      const fs = require('fs')
      const customConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'))
      config = { ...defaultConfig, ...customConfig }
      console.log(`📋 Using custom config: ${configPath}\n`)
    } catch (error) {
      console.error(`❌ Failed to load config file: ${error}`)
      process.exit(1)
    }
  }

  const runner = new SEOTestRunner(config)
  
  try {
    await runner.runAllTests()
    process.exit(0)
  } catch (error) {
    console.error(`❌ Test execution failed: ${error}`)
    process.exit(1)
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

export { SEOTestRunner }
