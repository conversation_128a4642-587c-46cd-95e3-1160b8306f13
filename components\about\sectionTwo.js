import React from 'react';

export default function SectionTwo() {
  return (
    <div className="w-full bg-[var(--secondary-background)]">
      {/* Desktop Layout */}
      <div className="hidden lg:grid lg:grid-cols-3 py-16 px-4 md:px-12 gap-6">
        {/* Left Column - Images */}
        <div className="flex flex-col gap-6">
          <div className="flex-1 h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/buhanga-eco-park-nature-and-cave.webp" 
              alt="a man and his wife on vacation at Buhanga Eco park under Musanze cave"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
          <div className="flex-1 h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/volcanoes-national-park.webp" 
              alt="The volcanoes national park scenic view"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </div>

        {/* Center Column - Mission & Vision */}
        <div className="bg-green-800 text-white flex flex-col shadow-xl"
        style={{ backgroundColor: 'var(--btn)' }}
        >
          {/* Mission Section */}
          <div className="flex-1 flex flex-col justify-center px-6 lg:px-8 py-6 lg:py-8">
            <h2 className="text-2xl lg:text-3xl font-bold mb-4 lg:mb-6">Our Mission</h2>
            <p className="text-sm lg:text-base leading-relaxed">
              Our mission is to create memorable travel experiences that connect people with: <span className="font-bold">Wildlife</span>, and <span className="font-bold">Local Community</span>, of Africa while promoting <span className="font-bold">responsible tourism practices</span>.
            </p>
          </div>

          {/* Vision Section */}
          <div className="flex-1 flex flex-col justify-center px-6 lg:px-8 py-6 lg:py-8 border-t border-green-700">
            <h2 className="text-2xl lg:text-3xl font-bold mb-4 lg:mb-6">Our Vision</h2>
            <p className="text-sm lg:text-base leading-relaxed">
              Our vision is to be the leading tour operator company in Africa, recognized for our commitment to: <span className="font-bold">Quality</span>, <span className="font-bold">Customer satisfaction</span> and <span className="font-bold">Conservation</span>.
            </p>
          </div>
        </div>

        {/* Right Column - Images */}
        <div className="flex flex-col gap-6">
          <div className="flex-1 h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/akagera-game-drive-safaris.webp" 
              alt="A zebra in the Akagera national park grazzing"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
          <div className="flex-1 h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/reforastation-community-project.webp" 
              alt="Waterfall in lush African landscape"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden px-4 py-6 space-y-4">
        {/* Top Images Row */}
        <div className="grid grid-cols-2 gap-2">
          <div className="h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/buhanga-eco-park-nature-and-cave.webp" 
              alt="a man and his wife on vacation at Buhanga Eco park under Musanze cave"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
          <div className="h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/volcanoes-national-park.webp" 
              alt="The volcanoes national park scenic view"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </div>

        {/* Mission & Vision Content */}
        <div className="bg-green-800 text-white px-3 py-4 shadow-xl space-y-4"
        style={{ backgroundColor: 'var(--btn)' }}
        >
          {/* Mission Section */}
          <div className="mb-4">
            <h2 className="text-xl font-bold mb-2">Our Mission</h2>
            <p className="text-xs leading-relaxed">
              Our mission is to create memorable travel experiences that connect people with: <span className="font-bold">Wildlife</span>, and <span className="font-bold">Local Community</span>, of Africa while promoting <span className="font-bold">responsible tourism practices</span>.
            </p>
          </div>

          {/* Vision Section */}
          <div>
            <h2 className="text-xl font-bold mb-2">Our Vision</h2>
            <p className="text-xs leading-relaxed">
              Our vision is to be the leading tour operator company in Africa, recognized for our commitment to: <span className="font-bold">Quality</span>, <span className="font-bold">Customer satisfaction</span> and <span className="font-bold">Conservation</span>.
            </p>
          </div>
        </div>

        {/* Bottom Images Row */}
        <div className="grid grid-cols-2 gap-2">
          <div className="h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/akagera-game-drive-safaris.webp" 
              alt="A zebra in the Akagera national park grazzing"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
          <div className="h-[180px] overflow-hidden shadow-lg">
            <img 
              src="images/about-hero/reforastation-community-project.webp" 
              alt="Waterfall in lush African landscape"
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          </div>
        </div>
      </div>
    </div>
  );
}