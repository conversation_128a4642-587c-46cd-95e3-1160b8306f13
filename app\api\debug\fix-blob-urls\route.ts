import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { getTemporaryFallbackImage } from '@/lib/fallback-images';

export async function POST() {
  try {
    // Fetch all packages with blob URLs
    const { data: packages, error: fetchError } = await supabase
      .from('sas_packages')
      .select('id, title, slug, image_url, hero_image_url, category')
      .or('image_url.like.blob:%,hero_image_url.like.blob:%');

    if (fetchError) {
      console.error('Supabase fetch error:', fetchError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch packages with blob URLs' },
        { status: 500 }
      );
    }

    if (!packages || packages.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No packages with blob URLs found',
        updatedCount: 0
      });
    }

    console.log(`Found ${packages.length} packages with blob URLs`);

    let updatedCount = 0;
    const updatePromises = packages.map(async (pkg) => {
      const updates: any = {};

      // Get category-specific fallback image
      const fallbackImage = getTemporaryFallbackImage(pkg.category);
      const fallbackImageUrl = fallbackImage.url;
      const fallbackAltText = fallbackImage.alt;

      // Check if image_url is a blob URL
      if (pkg.image_url && pkg.image_url.startsWith('blob:')) {
        updates.image_url = fallbackImageUrl;
        updates.image_alt = fallbackAltText;
      }

      // Check if hero_image_url is a blob URL
      if (pkg.hero_image_url && pkg.hero_image_url.startsWith('blob:')) {
        updates.hero_image_url = fallbackImageUrl;
        updates.hero_image_alt = fallbackAltText;
      }

      // Only update if there are changes
      if (Object.keys(updates).length > 0) {
        console.log(`Updating package: ${pkg.title}`);
        console.log('Updates:', updates);

        const { error: updateError } = await supabase
          .from('sas_packages')
          .update(updates)
          .eq('id', pkg.id);

        if (updateError) {
          console.error(`Error updating package ${pkg.title}:`, updateError);
          throw updateError;
        }

        updatedCount++;
      }
    });

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${updatedCount} packages`,
      updatedCount,
      fallbackImageUrl
    });

  } catch (error) {
    console.error('Error fixing blob URLs:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fix blob URLs' },
      { status: 500 }
    );
  }
}
