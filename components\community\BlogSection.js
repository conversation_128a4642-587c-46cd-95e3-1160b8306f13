/* eslint-disable @next/next/no-img-element */
/* eslint-disable react/no-unescaped-entities */

import React from 'react';

const BlogCard = ({ image, category, date, title, excerpt }) => {
  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-lg transition-transform duration-300 hover:-translate-y-2">
      <div className="h-48 overflow-hidden">
        <img src={image} alt={title} className="w-full h-full object-cover" />
      </div>
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <span className="bg-[#163201] text-white px-4 py-1 rounded-full text-sm">{category}</span>
          <span className="text-gray-600 text-sm"><i className="far fa-calendar-alt mr-2"></i>{date}</span>
        </div>
        <div>
          <h3 className="text-xl font-bold mb-3">{title}</h3>
          <p className="text-gray-600 mb-4">{excerpt}</p>
          <div className="flex justify-end">
            <a href="#" className="text-[#163201] font-bold hover:text-[#d35400] transition-colors duration-300">
              Read More <i className="fas fa-arrow-right ml-2"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

const BlogSection = () => {
  const blogs = [
    // Add blog data here
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <p className="text-[#d35400] text-sm uppercase tracking-wider text-center mb-4">Latest News</p>
        <h2 className="text-4xl font-bold text-center mb-6">Community Stories</h2>
        <p className="text-center max-w-2xl mx-auto mb-12">
          Stay updated with our latest stories, project updates, and insights about community development and conservation efforts around the world.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogs.map((blog, index) => (
            <BlogCard key={index} {...blog} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogSection;
