# 🎨 Tailwind CSS Setup & Troubleshooting Guide

## 🚨 Common Issue: "Tailwind styles not working after git pull"

If you've pulled the latest changes and Tailwind CSS styles are not working, follow these steps:

## ✅ Step-by-Step Solution

### 1. **Clean Install Dependencies**
```bash
# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Fresh install
npm install
```

### 2. **Verify Tailwind Installation**
Check that these packages are installed:
```bash
npm list tailwindcss postcss autoprefixer
```

### 3. **Clear Next.js Cache**
```bash
# Clear Next.js cache
rm -rf .next

# For additional cleanup
npm run build
```

### 4. **Restart Development Server**
```bash
npm run dev
```

## 🔧 Manual Verification Steps

### Check Configuration Files

1. **Verify `tailwind.config.js` exists and has correct content paths:**
```javascript
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  // ... rest of config
}
```

2. **Verify `postcss.config.mjs` exists:**
```javascript
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
};
export default config;
```

3. **Verify `app/globals.css` has Tailwind directives:**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### Test Tailwind Classes

Add this test component to verify Tailwind is working:
```jsx
// Test component - add to any page temporarily
<div className="bg-red-500 text-white p-4 rounded-lg">
  ✅ If you see this with red background, Tailwind is working!
</div>
```

## 🚀 Complete Setup for New Collaborators

### 1. Clone Repository
```bash
git clone [repository-url]
cd sas-website-app
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm run dev
```

### 4. Verify Setup
- Open http://localhost:3000
- Check that styles are applied correctly
- Look for any console errors

## 🐛 Common Issues & Solutions

### Issue 1: "Module not found: Can't resolve 'tailwindcss'"
**Solution:**
```bash
npm install -D tailwindcss postcss autoprefixer
```

### Issue 2: Styles not updating in development
**Solution:**
```bash
# Stop dev server (Ctrl+C)
rm -rf .next
npm run dev
```

### Issue 3: Custom fonts not loading
**Solution:**
- Verify font files exist in `/public/font/`
- Check `globals.css` font-face declarations
- Clear browser cache (Ctrl+F5)

### Issue 4: CSS not building in production
**Solution:**
```bash
npm run build
# Check for any build errors
```

## 📋 Environment Requirements

- **Node.js**: 18.x or higher
- **npm**: 8.x or higher
- **Next.js**: 15.3.4
- **Tailwind CSS**: 3.4.17

## 🔍 Debug Commands

### Check installed versions:
```bash
node --version
npm --version
npx tailwindcss --version
```

### Test Tailwind compilation:
```bash
npx tailwindcss -i ./app/globals.css -o ./test-output.css --watch
```

### Check Next.js configuration:
```bash
npm run build -- --debug
```

## 📞 Still Having Issues?

1. **Check browser console** for JavaScript errors
2. **Inspect element** to see if Tailwind classes are being applied
3. **Compare your setup** with this guide
4. **Try incognito mode** to rule out browser cache issues

## 🎯 Quick Fix Checklist

- [ ] Deleted `node_modules` and `package-lock.json`
- [ ] Ran `npm install`
- [ ] Deleted `.next` folder
- [ ] Restarted dev server
- [ ] Checked browser console for errors
- [ ] Verified Tailwind config files exist
- [ ] Tested with simple Tailwind classes

---

**💡 Pro Tip:** Always run `npm install` after pulling changes that modify `package.json` or `package-lock.json`!
