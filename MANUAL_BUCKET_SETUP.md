# 🎯 Manual Bucket Setup (Alternative to SQL)

If the SQL approach gives permission errors, use this manual method through the Supabase dashboard.

## 📋 Step-by-Step Manual Setup

### Method 1: Supabase Dashboard Interface

1. **Go to your Supabase Dashboard**
2. **Click "Storage" in the left sidebar**
3. **Click "Create a new bucket"**
4. **Fill in the details:**
   - **Name:** `package-images`
   - **Public bucket:** ✅ **Check this box**
   - **File size limit:** `5242880` (5MB)
   - **Allowed MIME types:** `image/jpeg, image/png, image/webp, image/gif`
5. **Click "Create bucket"**

### Method 2: Simplified SQL (Try this first)

If the dashboard method doesn't work, try this minimal SQL:

```sql
-- Minimal bucket creation (should work without permission issues)
INSERT INTO storage.buckets (id, name, public)
VALUES ('package-images', 'package-images', true)
ON CONFLICT (id) DO NOTHING;
```

### Method 3: Service Role Key Approach

1. **Get your service role key:**
   - Go to Project Settings → API
   - Copy the "service_role" key
   - Add to your `.env.local`:
   ```
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

2. **Run the automatic creation script:**
   ```bash
   node scripts/fix-storage-bucket.mjs
   ```

## 🔍 Verify the Setup

After creating the bucket, run:
```bash
node scripts/test-public-bucket.mjs
```

## 🎉 Expected Results

Once the bucket is created:
- ✅ Image uploads work in admin dashboard
- ✅ No more "bucket does not exist" errors
- ✅ Public access for all operations
- ✅ No authentication required

## 🆘 If Still Having Issues

1. **Check your Supabase project permissions**
2. **Ensure you're the project owner**
3. **Try creating the bucket through the dashboard interface**
4. **Contact Supabase support if needed**

---

**The manual dashboard method should work even with permission restrictions!** 