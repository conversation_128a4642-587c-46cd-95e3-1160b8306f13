-- Simple Storage Fix - Copy and paste this into Supabase SQL Editor

-- Create the bucket (will be ignored if it already exists)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('sas-package-images', 'sas-package-images', true, 10485760, 
   ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Anyone can view package images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload package images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update package images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete package images" ON storage.objects;

-- Create fresh policies
CREATE POLICY "Anyone can view package images" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-package-images');

CREATE POLICY "Authenticated users can upload package images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update package images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete package images" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');
