import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ChevronDown, SlidersHorizontal } from 'lucide-react';
import { products } from '../data/productData';

const FilterSection = ({ onFilterChange, searchTerm }) => {
  const [filters, setFilters] = useState({
    category: '',
    priceRange: '',
    sortBy: ''
  });

  const [showFilters, setShowFilters] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Get unique categories from products
  const uniqueCategories = [...new Set(products.map(product => product.category))];

  const filterOptions = {
    category: uniqueCategories,
    priceRange: ['Under $100', '$100 - $300', '$300 - $500', 'Over $500'],
    sortBy: ['Featured', 'Price: Low to High', 'Price: High to Low', 'Newest']
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  const applyFilters = useCallback(() => {
    let result = [...products];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(product => 
        product.title.toLowerCase().includes(searchLower) ||
        product.description.toLowerCase().includes(searchLower)
      );
    }

    // Apply category filter
    if (filters.category) {
      result = result.filter(product => product.category === filters.category);
    }

    // Apply price range filter
    if (filters.priceRange) {
      result = result.filter(product => {
        const price = parseFloat(product.price);
        switch (filters.priceRange) {
          case 'Under $100':
            return price < 100;
          case '$100 - $300':
            return price >= 100 && price <= 300;
          case '$300 - $500':
            return price > 300 && price <= 500;
          case 'Over $500':
            return price > 500;
          default:
            return true;
        }
      });
    }

    // Apply sorting
    if (filters.sortBy) {
      result.sort((a, b) => {
        switch (filters.sortBy) {
          case 'Price: Low to High':
            return parseFloat(a.price) - parseFloat(b.price);
          case 'Price: High to Low':
            return parseFloat(b.price) - parseFloat(a.price);
          case 'Newest':
            // Only sort by date on client to avoid hydration mismatch
            if (isClient) {
              const dateA = a.createdAt ? new Date(a.createdAt) : 0;
              const dateB = b.createdAt ? new Date(b.createdAt) : 0;
              return dateB - dateA;
            }
            return 0;
          default:
            return 0;
        }
      });
    }

    return result;
  }, [filters, searchTerm, isClient]);

  useEffect(() => {
    const filteredResults = applyFilters();
    onFilterChange(filteredResults);
  }, [applyFilters, onFilterChange]);

  const FilterDropdown = ({ label, options, value, onChange }) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef(null);

    useEffect(() => {
      const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`flex items-center justify-between w-48 px-4 py-2 border ${
            isOpen ? 'border-blue-500 ring-2 ring-blue-100' : 'border-gray-300'
          } rounded-lg bg-white hover:bg-gray-50 transition-colors duration-200`}
        >
          <span className="text-sm font-medium text-gray-700">
            {value || label}
          </span>
          <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </button>
        
        {isOpen && (
          <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            <div className="py-1">
              {options.map((option) => (
                <button
                  key={option}
                  onClick={() => {
                    onChange(option);
                    setIsOpen(false);
                  }}
                  className={`w-full text-left px-4 py-2 text-sm ${
                    value === option 
                      ? 'bg-blue-50 text-blue-600' 
                      : 'text-gray-700 hover:bg-gray-100'
                  } transition-colors duration-150`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => {
      const newValue = prev[filterType] === value ? '' : value;
      return { ...prev, [filterType]: newValue };
    });
  };

  return (
    <div className="flex flex-wrap items-center gap-4 py-4 bg-gray-50 px-6 rounded-lg">
      <button 
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 transition-colors duration-200"
      >
        <SlidersHorizontal className="w-4 h-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">All Filters</span>
      </button>

      <div className={`flex flex-wrap items-center gap-4 transition-opacity duration-300 ease-in-out ${
        showFilters ? 'opacity-100 visible pointer-events-auto' : 'opacity-0 invisible pointer-events-none'
      }`}>
        <FilterDropdown
          label="Category"
          options={filterOptions.category}
          value={filters.category}
          onChange={(value) => handleFilterChange('category', value)}
        />
        
        <FilterDropdown
          label="Price Range"
          options={filterOptions.priceRange}
          value={filters.priceRange}
          onChange={(value) => handleFilterChange('priceRange', value)}
        />
      </div>
      
      <div className="ml-auto">
        <FilterDropdown
          label="Sort by"
          options={filterOptions.sortBy}
          value={filters.sortBy}
          onChange={(value) => handleFilterChange('sortBy', value)}
        />
      </div>

      {Object.values(filters).some(Boolean) && (
        <button
          onClick={() => setFilters({ category: '', priceRange: '', sortBy: '' })}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Clear All
        </button>
      )}
    </div>
  );
};

export default FilterSection;