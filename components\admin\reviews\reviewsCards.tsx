/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { useState, useEffect } from 'react';
import { Star, User, MapPin, Calendar, Filter, Search, ExternalLink, Eye, ChevronLeft, ChevronRight } from 'lucide-react';
import { JSX } from 'react/jsx-runtime';

interface Review {
  id: number;
  reviewerName: string;
  reviewTitle: string;
  rating: number;
  reviewText: string;
  reviewLink: string;
  location: string;
  reviewDate: string;
  businessType: 'hotel' | 'restaurant' | 'attraction';
  submittedAt: string;
}

interface Filters {
  rating: string;
  businessType: string;
  searchTerm: string;
}

const ExistingReviews: React.FC = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<Review[]>([]);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [filters, setFilters] = useState<Filters>({
    rating: 'all',
    businessType: 'all',
    searchTerm: ''
  });

  const reviewsPerPage = 6;

  // Sample data - replace with your actual data source
  const sampleReviews: Review[] = [
    {
      id: 1,
      reviewerName: 'Michael S.',
      reviewTitle: 'Amazing Experience at Beach Resort',
      rating: 5,
      reviewText: 'We had an incredible stay at this beach resort. The staff was extremely friendly and attentive. The rooms were spacious and clean with a beautiful ocean view. The food at the restaurant was exceptional, and the beach activities kept us entertained throughout our stay. Highly recommend this place for families and couples alike.',
      reviewLink: 'https://tripadvisor.com/review1',
      location: 'Miami, FL',
      reviewDate: '2024-03-15',
      businessType: 'hotel',
      submittedAt: '2024-03-20T10:30:00Z'
    },
    {
      id: 2,
      reviewerName: 'Jennifer L.',
      reviewTitle: 'Good Value for Money',
      rating: 4,
      reviewText: 'This hotel offers good value for the price point. The location is convenient, with easy access to local attractions and public transportation. The rooms are comfortable, though not luxurious. The breakfast buffet had a decent variety. Staff was helpful when we needed assistance. Overall, a solid choice for budget-conscious travelers.',
      reviewLink: 'https://tripadvisor.com/review2',
      location: 'Orlando, FL',
      reviewDate: '2024-02-28',
      businessType: 'hotel',
      submittedAt: '2024-03-01T14:20:00Z'
    },
    {
      id: 3,
      reviewerName: 'David T.',
      reviewTitle: 'Hidden Gem Restaurant',
      rating: 5,
      reviewText: 'What an incredible find! This small, family-owned restaurant completely exceeded our expectations. The authentic cuisine was bursting with flavor, and the service was impeccable. The chef even came out to explain some of the traditional dishes. The atmosphere is cozy and intimate. This place deserves more recognition!',
      reviewLink: 'https://tripadvisor.com/review3',
      location: 'New York, NY',
      reviewDate: '2024-04-02',
      businessType: 'restaurant',
      submittedAt: '2024-04-05T09:15:00Z'
    },
    {
      id: 4,
      reviewerName: 'Sarah M.',
      reviewTitle: 'Perfect Weekend Getaway',
      rating: 5,
      reviewText: 'This boutique hotel provided exactly what we were looking for in a romantic weekend escape. The room was beautifully decorated with attention to detail. The spa services were top-notch, and the rooftop bar had stunning city views. The concierge gave us excellent recommendations for local dining and entertainment.',
      reviewLink: 'https://tripadvisor.com/review4',
      location: 'San Francisco, CA',
      reviewDate: '2024-03-22',
      businessType: 'hotel',
      submittedAt: '2024-03-25T16:45:00Z'
    },
    {
      id: 5,
      reviewerName: 'Robert K.',
      reviewTitle: 'Disappointing Service',
      rating: 2,
      reviewText: 'Unfortunately, our experience fell short of expectations. The check-in process was lengthy and disorganized. Our room had maintenance issues that weren\'t addressed promptly. The restaurant service was slow, and the food quality was inconsistent. While the location is good, the service standards need significant improvement.',
      reviewLink: 'https://tripadvisor.com/review5',
      location: 'Las Vegas, NV',
      reviewDate: '2024-01-18',
      businessType: 'hotel',
      submittedAt: '2024-01-22T11:30:00Z'
    }
  ];

  useEffect(() => {
    setReviews(sampleReviews);
    setFilteredReviews(sampleReviews);
  }, []);

  // Filter reviews based on current filters
  useEffect(() => {
    let filtered = reviews;

    // Filter by rating
    if (filters.rating !== 'all') {
      const ratingValue = parseInt(filters.rating);
      filtered = filtered.filter(review => review.rating === ratingValue);
    }

    // Filter by business type
    if (filters.businessType !== 'all') {
      filtered = filtered.filter(review => review.businessType === filters.businessType);
    }

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(review =>
        review.reviewerName.toLowerCase().includes(searchLower) ||
        review.reviewTitle.toLowerCase().includes(searchLower) ||
        review.reviewText.toLowerCase().includes(searchLower) ||
        review.location.toLowerCase().includes(searchLower)
      );
    }

    setFilteredReviews(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [reviews, filters]);

  const handleFilterChange = (filterType: keyof Filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const renderStars = (rating: number): JSX.Element[] => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getRatingColor = (rating: number): string => {
    if (rating >= 4) return 'text-green-600 bg-green-100';
    if (rating >= 3) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getBusinessTypeIcon = (type: string): JSX.Element => {
    switch (type) {
      case 'hotel':
        return <div className="w-2 h-2 bg-blue-500 rounded-full"></div>;
      case 'restaurant':
        return <div className="w-2 h-2 bg-green-500 rounded-full"></div>;
      case 'attraction':
        return <div className="w-2 h-2 bg-purple-500 rounded-full"></div>;
      default:
        return <div className="w-2 h-2 bg-gray-500 rounded-full"></div>;
    }
  };

  // Pagination
  const totalPages = Math.ceil(filteredReviews.length / reviewsPerPage);
  const startIndex = (currentPage - 1) * reviewsPerPage;
  const endIndex = startIndex + reviewsPerPage;
  const currentReviews = filteredReviews.slice(startIndex, endIndex);

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <div className="max-w-7xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">Reviews Management</h1>
        <p className="text-gray-600">
          View and manage customer reviews from various platforms.
        </p>
      </div>

      {/* Filters */}
      <div className="mb-6 bg-gray-50 p-4 rounded-lg">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search reviews..."
              value={filters.searchTerm}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Rating Filter */}
          <select
            value={filters.rating}
            onChange={(e) => handleFilterChange('rating', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>

          {/* Business Type Filter */}
          <select
            value={filters.businessType}
            onChange={(e) => handleFilterChange('businessType', e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="hotel">Hotels</option>
            <option value="restaurant">Restaurants</option>
            <option value="attraction">Attractions</option>
          </select>

          {/* Results Count */}
          <div className="flex items-center text-sm text-gray-600">
            <Filter className="w-4 h-4 mr-2" />
            {filteredReviews.length} review{filteredReviews.length !== 1 ? 's' : ''} found
          </div>
        </div>
      </div>

      {/* Reviews Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {currentReviews.map((review) => (
          <div key={review.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                {getBusinessTypeIcon(review.businessType)}
                <span className="text-xs text-gray-500 capitalize">{review.businessType}</span>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getRatingColor(review.rating)}`}>
                {review.rating}/5
              </div>
            </div>

            {/* Rating Stars */}
            <div className="flex items-center mb-3">
              {renderStars(review.rating)}
              <span className="ml-2 text-sm text-gray-600">({review.rating}/5)</span>
            </div>

            {/* Review Title */}
            <h3 className="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">
              {review.reviewTitle}
            </h3>

            {/* Review Text */}
            <p className="text-gray-600 text-sm mb-4 line-clamp-3">
              {review.reviewText}
            </p>

            {/* Reviewer Info */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-gray-500">
                <User className="w-4 h-4 mr-2" />
                {review.reviewerName}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <MapPin className="w-4 h-4 mr-2" />
                {review.location}
              </div>
              <div className="flex items-center text-sm text-gray-500">
                <Calendar className="w-4 h-4 mr-2" />
                {new Date(review.reviewDate).toLocaleDateString()}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
              <button
                onClick={() => setSelectedReview(review)}
                className="flex items-center text-blue-600 hover:text-blue-800 text-sm"
              >
                <Eye className="w-4 h-4 mr-1" />
                View Details
              </button>
              <a
                href={review.reviewLink}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-gray-600 hover:text-gray-800 text-sm"
              >
                <ExternalLink className="w-4 h-4 mr-1" />
                Source
              </a>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <button
            onClick={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => goToPage(page)}
              className={`px-3 py-2 rounded-lg ${
                currentPage === page
                  ? 'bg-blue-600 text-white'
                  : 'border border-gray-300 hover:bg-gray-50'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Review Details Modal */}
      {selectedReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Review Details</h2>
                <button
                  onClick={() => setSelectedReview(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  {renderStars(selectedReview.rating)}
                  <span className="ml-2 font-semibold">{selectedReview.rating}/5</span>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getRatingColor(selectedReview.rating)}`}>
                  {selectedReview.businessType}
                </span>
              </div>
              
              <h3 className="text-lg font-semibold">{selectedReview.reviewTitle}</h3>
              
              <p className="text-gray-700 leading-relaxed">{selectedReview.reviewText}</p>
              
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                <div>
                  <span className="font-medium">Reviewer:</span> {selectedReview.reviewerName}
                </div>
                <div>
                  <span className="font-medium">Location:</span> {selectedReview.location}
                </div>
                <div>
                  <span className="font-medium">Review Date:</span> {new Date(selectedReview.reviewDate).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium">Submitted:</span> {new Date(selectedReview.submittedAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="pt-4">
                <a
                  href={selectedReview.reviewLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Original Review
                </a>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Empty State */}
      {filteredReviews.length === 0 && (
        <div className="text-center py-12">
          <Star className="w-12 h-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No reviews found</h3>
          <p className="text-gray-500">Try adjusting your filters to see more reviews.</p>
        </div>
      )}
    </div>
  );
};

export default ExistingReviews;
