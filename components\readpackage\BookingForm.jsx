"use client";

import { useEffect, useState } from "react";
import { Calendar, Users, Phone, Mail, User, Check } from "lucide-react";

const BookingForm = ({ pricing, packageTitle, packageId }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showNotification, setShowNotification] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState("");
  const [formData, setFormData] = useState({
    packageType: "",
    fullName: "",
    email: "",
    phone: "",
    people: "",
    date: "",
    checkOutDate: "",
    message: "",
  });
  const [today, setToday] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      setToday(new Date().toISOString().split("T")[0]);
    }
  }, []);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError("");

    try {
      // Calculate total amount based on package type and number of people
      const selectedPackagePrice = pricing[formData.packageType]?.price || 0;
      const numberOfPeople = parseInt(formData.people) || 1;
      const totalAmount = selectedPackagePrice * numberOfPeople;

      // Map form data to API expected format
      const bookingData = {
        packageId: packageId,
        packageTitle: packageTitle,
        packageType: formData.packageType,
        fullName: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        people: numberOfPeople,
        date: formData.date,
        checkOutDate: formData.checkOutDate,
        message: formData.message,
        totalAmount: totalAmount,
      };

      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      const result = await response.json();

      if (result.success) {
        setShowNotification(true);
        setTimeout(() => {
          setShowNotification(false);
        }, 6000);

        // Reset form after successful submission
        setFormData({
          packageType: "",
          fullName: "",
          email: "",
          phone: "",
          people: "",
          date: "",
          checkOutDate: "",
          message: "",
        });
        setCurrentStep(1);
      } else {
        setSubmitError(result.error || 'Failed to submit booking. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting booking:', error);
      setSubmitError('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4 ">
            <h3 className="font-medium text-gray-900">Select Package Type</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-3">
              {Object.entries(pricing).map(([type, details]) => (
                <button
                  key={type}
                  onClick={() => {
                    handleInputChange("packageType", type);
                    setCurrentStep(2);
                  }}
                  className={`p-4 border bg-[var(--primary-background)] rounded-lg text-left hover:border-[var(--btn)] transition-colors
                    ${
                      formData.packageType === type
                        ? "border-[var(--btn)] bg-orange-100"
                        : "border-gray-200"
                    }`}
                >
                  <div className="font-medium capitalize">{type}</div>
                  <div className="text-[var(--btn)] text-lg font-bold">
                    ${details.price}
                  </div>
                  <div className="text-sm text-gray-500">{details.text}</div>
                </button>
              ))}
            </div>
          </div>
        );

      case 2:
        return (
          <form
            onSubmit={(e) => {
              e.preventDefault();
              setCurrentStep(3);
            }}
            className="space-y-4"
          >
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <User className="w-4 h-4 mr-2" /> Full Name
              </label>
              <input
                required
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange("fullName", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Mail className="w-4 h-4 mr-2" /> Email
              </label>
              <input
                required
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Phone className="w-4 h-4 mr-2" /> Phone/WhatsApp
              </label>
              <input
                required
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Users className="w-4 h-4 mr-2" /> Number of People
              </label>
              <input
                required
                type="number"
                min="1"
                value={formData.people}
                onChange={(e) => handleInputChange("people", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <button
              type="submit"
              className="btn w-full text-white py-2 rounded-lg  transition-colors"
            >
              Continue
            </button>
          </form>
        );

      case 3:
        return (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Calendar className="w-4 h-4 mr-2" /> Check-in Date
              </label>
              <input
                required
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange("date", e.target.value)}
                min={today}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-1">
                <Calendar className="w-4 h-4 mr-2" /> Check-out Date
              </label>
              <input
                required
                type="date"
                value={formData.checkOutDate}
                onChange={(e) =>
                  handleInputChange("checkOutDate", e.target.value)
                }
                min={formData.date || today}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Additional Information
              </label>
              <textarea
                value={formData.message}
                onChange={(e) => handleInputChange("message", e.target.value)}
                rows={4}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="Any special requirements or questions?"
              />
            </div>

            {submitError && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {submitError}
              </div>
            )}

            <button
              type="submit"
              disabled={isSubmitting}
              className={`btn w-full text-white py-2 rounded-lg transition-colors ${
                isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Booking Request'}
            </button>
          </form>
        );
    }
  };

  return (
    <div className="booking-form-section bg-[var(--secondary-background)] shadow-lg p-6 relative ">
      {/* Notification */}
      <div
        className={`fixed top-20 right-4 bg-[var(--btn)] text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 flex items-center gap-2
          ${
            showNotification
              ? "translate-x-0 opacity-100"
              : "translate-x-full opacity-0"
          }`}
      >
        <Check className="w-5 h-5" />
        <div>
          <p className="font-medium">Thank you, {formData.fullName}!</p>
          <p className="text-sm">
            Your {packageTitle} booking request has been received. We'll contact you within 24 hours.
          </p>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Book This Safari</h2>
        <p className="text-gray-600">{packageTitle}</p>
      </div>

      {/* Progress Indicator */}
      <div className="flex mb-6 justify-between">
        {[1, 2, 3].map((step) => (
          <div
            key={step}
            className={`flex-1 h-2 rounded-full mx-1 ${
              step <= currentStep ? "bg-[var(--btn)]" : "bg-gray-200"
            }`}
          />
        ))}
      </div>

      {renderStep()}

      {currentStep > 1 && (
        <button
          onClick={() => setCurrentStep(currentStep - 1)}
          className="mt-4 text-gray-600 hover:text-gray-800 text-sm"
        >
          ← Back to previous step
        </button>
      )}
    </div>
  );
};

export default BookingForm;
