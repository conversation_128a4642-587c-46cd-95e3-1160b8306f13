import React, { useState } from 'react';
import { Heart, ImageIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCart } from './CartContext';
import { getTemporaryFallbackImage } from '@/lib/fallback-images';
import LazyImage from '../common/LazyImage';

const ProductCard = ({
    id,
    slug,
    coverImage,
    images,
    title,
    price,
    discount,
    description,
    onWishlist
}) => {
    const router = useRouter();
    const { addToCart } = useCart();
    const [imageError, setImageError] = useState(false);
    const discountedPrice = discount ? price - (price * discount / 100) : price;

    const handleProductClick = (e) => {
        // Prevent navigation if clicking the wishlist or add to cart buttons
        if (e.target.closest('button')) return;
        router.push(`/shop/product/${slug}`);
    };

    const handleAddToCart = (e) => {
        e.stopPropagation();
        const productData = {
            id: `${id}-${title}`, // Create unique ID combining product id and title
            name: title,
            price: discountedPrice,
            image: productImage || fallbackImage,
            slug: slug,
            originalId: id // Keep original ID for reference
        };
        addToCart(productData);
    };

    const handleWishlist = (e) => {
        e.stopPropagation();
        onWishlist();
    };

    // Use first image from images array as fallback if coverImage is not available
    const productImage = coverImage || (images && images.length > 0 ? images[0] : null);
    const fallbackImageData = getTemporaryFallbackImage('default');
    const fallbackImage = fallbackImageData.url;

    return (
        <div 
            onClick={handleProductClick}
            className="bg-[var(--secondary-background)] rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200 relative cursor-pointer"
        >
            {discount > 0 && (
                <div className="absolute top-4 left-4 z-10 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                    -{discount}%
                </div>
            )}

            {/* Wishlist Heart */}
            <button
                onClick={handleWishlist}
                className="absolute top-4 right-4 z-10 p-2 rounded-full bg-white shadow-sm hover:bg-gray-50"
            >
                <Heart className="w-4 h-4 text-gray-400 hover:text-red-500" />
            </button>

            {/* Product Image */}
            <div className="relative w-full aspect-[4/3] bg-gray-100 rounded-lg mb-4 overflow-hidden">
                <LazyImage
                    src={productImage || fallbackImage}
                    alt={title}
                    className="w-full h-full object-cover"
                    width={400}
                    height={300}
                    onError={() => setImageError(true)}
                    skeletonVariant="card"
                />
            </div>

            {/* Product Info */}
            <div className="space-y-2 text-left">
                <h3 className="font-medium text-gray-900 text-sm line-clamp-1">{title}</h3>
                <p className="text-xs text-gray-500 line-clamp-2">{description}</p>

                <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-gray-900">
                        ${discountedPrice.toFixed(2)}
                    </span>
                    {discount > 0 && (
                        <span className="text-sm text-gray-500 line-through">
                            ${price.toFixed(2)}
                        </span>
                    )}
                </div>

                <button
                    onClick={handleAddToCart}
                    className="btn w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors duration-200"
                >
                    Add to Cart
                </button>
            </div>
        </div>
    );
};

export default ProductCard;