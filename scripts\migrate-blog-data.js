/**
 * Blog Data Migration Script (JavaScript version)
 * 
 * This script migrates existing blog data from blogData.js to Supabase database.
 * Run this script once to populate the database with existing blog posts.
 * 
 * Usage: node scripts/migrate-blog-data.js
 */

const { createClient } = require('@supabase/supabase-js');
const { blogContents } = require('../components/data/blogData');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Make sure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateBlogData() {
  console.log('🚀 Starting blog data migration...');

  try {
    // Get existing blog posts to avoid duplicates
    const { data: existingPosts } = await supabase
      .from('sas_blog_posts')
      .select('slug');

    const existingSlugs = new Set(existingPosts?.map(post => post.slug) || []);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const [slug, blogData] of Object.entries(blogContents)) {
      if (existingSlugs.has(slug)) {
        console.log(`⏭️  Skipping ${slug} - already exists`);
        skippedCount++;
        continue;
      }

      console.log(`📝 Migrating: ${slug}`);

      // Prepare blog post data
      const blogPostData = {
        title: blogData.title,
        slug: slug,
        description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        hero_image_url: blogData.heroImage,
        hero_image_alt: `${blogData.title} - Swift Africa Safaris`,
        author: blogData.seo?.author || 'Swift Africa Safaris',
        category: blogData.seo?.category || 'Travel Guide',
        tags: blogData.seo?.tags || [],
        status: 'published',
        published_at: blogData.seo?.publishedTime || new Date().toISOString(),
        seo_title: blogData.seo?.title || blogData.title,
        seo_description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        seo_keywords: blogData.seo?.keywords || [],
        og_title: blogData.seo?.title || blogData.title,
        og_description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        og_image_url: blogData.seo?.image || blogData.heroImage,
        canonical_url: `https://swiftafricasafaris.com/blog/${slug}`,
        robots_index: 'index',
        robots_follow: 'follow',
        schema_data: blogData.schema || null
      };

      // Insert blog post
      const { data: newPost, error: postError } = await supabase
        .from('sas_blog_posts')
        .insert(blogPostData)
        .select('id')
        .single();

      if (postError) {
        console.error(`❌ Error inserting post ${slug}:`, postError);
        continue;
      }

      // Migrate content blocks
      if (blogData.sections && blogData.sections.length > 0) {
        const contentBlocks = blogData.sections.map((section, index) => {
          let blockContent = {};

          switch (section.type) {
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
              blockContent = {
                content: section.content,
                id: section.id || null
              };
              break;

            case 'paragraph':
              blockContent = {
                content: section.content
              };
              break;

            case 'image':
              blockContent = {
                src: section.src || section.content,
                alt: section.alt || `${blogData.title} image`,
                caption: section.caption || '',
                width: section.width || 'full'
              };
              break;

            case 'video':
              blockContent = {
                src: section.src || section.content,
                poster: section.poster || '',
                caption: section.caption || '',
                width: section.width || 'lg'
              };
              break;

            case 'listing':
              blockContent = {
                items: section.items || [],
                listType: section.listType || 'unordered'
              };
              break;

            case 'quote':
              blockContent = {
                content: section.content,
                author: section.author || '',
                source: section.source || ''
              };
              break;

            case 'divider':
              blockContent = {
                style: 'line'
              };
              break;

            default:
              blockContent = {
                content: section.content
              };
          }

          return {
            blog_post_id: newPost.id,
            block_type: section.type,
            content: blockContent,
            sort_order: index
          };
        });

        const { error: contentError } = await supabase
          .from('sas_blog_content_blocks')
          .insert(contentBlocks);

        if (contentError) {
          console.error(`❌ Error inserting content blocks for ${slug}:`, contentError);
        } else {
          console.log(`✅ Migrated ${contentBlocks.length} content blocks for ${slug}`);
        }
      }

      migratedCount++;
      console.log(`✅ Successfully migrated: ${slug}`);
    }

    console.log('\n🎉 Migration completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Migrated: ${migratedCount} posts`);
    console.log(`   - Skipped: ${skippedCount} posts (already exist)`);
    console.log(`   - Total processed: ${migratedCount + skippedCount} posts`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrateBlogData()
  .then(() => {
    console.log('✅ Migration script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  });
