// about page
import type { Metada<PERSON> } from 'next';
import HeroComponent from '@/components/heroes';
import { heroData } from '@/components/data/heroesdata';
import Footer from '@/components/footer';
import SectionOne from '@/components/about/sectionOne';
import SectionTwo from '@/components/about/sectionTwo';
import SectionThree from '@/components/about/sectionThree';
import SectionFour from '@/components/about/sectionFour';
import Navbar from '@/components/header';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - Passionate Experts in Tailored Tours',
  description: 'Discover the heart of Swift Africa Safaris driven by passion, purpose, and a love for creating unforgettable African travel experiences.',
  url: '/about',
  keywords: [
    'about Swift Africa Safaris',
    'African safari company',
    'safari tour operator East Africa',
    'gorilla trekking experts',
    'wildlife safari specialists',
    'Rwanda tour company',
    'Tanzania safari operator',
    'Uganda safari experts',
    'safari company history',
    'African travel specialists',
    'eco-tourism Africa',
    'sustainable safari tours',
    'local safari guides',
    'African adventure company'
  ],
  type: 'website'
});

export default function About() {
  const defaultProps = {
    title: heroData.about.title,
    subtitle: heroData.about.subtitle,
    backgroundImage: heroData.about.backgroundImage
  };

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'About Us', url: '/about' }
  ]);

  const aboutPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'AboutPage',
    name: 'Swift Africa Safaris - Passionate Experts in Tailored Tours',
    description: 'Discover the heart of Swift Africa Safaris driven by passion, purpose, and a love for creating unforgettable African travel experiences.',
    url: `${defaultSEO.siteUrl}/about`,
    keywords: [
      'about Swift Africa Safaris',
      'African safari company',
      'safari tour operator East Africa',
      'gorilla trekking experts',
      'wildlife safari specialists',
      'Rwanda tour company',
      'Tanzania safari operator',
      'Uganda safari experts',
      'safari company history',
      'African travel specialists',
      'eco-tourism Africa',
      'sustainable safari tours',
      'local safari guides',
      'African adventure company'
    ],
    mainEntity: {
      '@type': 'TravelAgency',
      name: defaultSEO.siteName,
      foundingDate: '2014',
      description: 'Discover the heart of Swift Africa Safaris driven by passion, purpose, and a love for creating unforgettable African travel experiences.',
      specialty: ['Gorilla Trekking', 'Wildlife Safaris', 'Cultural Tours', 'Photography Tours'],
      serviceArea: {
        '@type': 'Place',
        name: 'East Africa',
        description: 'Rwanda, Tanzania, Uganda, Kenya, South Africa'
      },
      founder: {
        '@type': 'Person',
        name: 'Swift Africa Safaris Team'
      },
      numberOfEmployees: '25-50',
      award: [
        'TripAdvisor Certificate of Excellence',
        'Top Rated Safari Operator 2023',
        'Sustainable Tourism Award'
      ]
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, aboutPageSchema]} />
      <Navbar />
      <main>
        <HeroComponent {...defaultProps} />
        <SectionOne />
        <SectionTwo />
        <SectionThree />
        <SectionFour />
      </main>
      <Footer />
    </div>
  );
}