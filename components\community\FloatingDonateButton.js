"use client";
import React, { useEffect, useState } from 'react';
import DonateForm from './donate';

const FloatingDonateButton = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isFooterVisible, setIsFooterVisible] = useState(false);
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const footer = document.querySelector('footer');
      if (!footer) return;

      const observer = new window.IntersectionObserver(
        ([entry]) => {
          setIsFooterVisible(entry.isIntersecting);
        },
        { threshold: 0 }
      );

      observer.observe(footer);

      const handleScroll = () => {
        setIsVisible(window.scrollY > 300);
      };

      window.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll();

      return () => {
        window.removeEventListener('scroll', handleScroll);
        observer.disconnect();
      };
    }
  }, []);

  // Handle modal scroll lock
  useEffect(() => {
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const body = document.body;
      if (showModal) {
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        body.style.overflow = 'hidden';
        body.style.paddingRight = `${scrollbarWidth}px`;
      } else {
        body.style.overflow = '';
        body.style.paddingRight = '';
      }
      return () => {
        body.style.overflow = '';
        body.style.paddingRight = '';
      };
    }
  }, [showModal]);

  return (
    <>
      <div
        style={{ position: 'fixed', bottom: '2rem', right: '2rem', zIndex: 1000 }}
        className={`transform-gpu transition-all duration-300 ease-in-out ${
          isVisible && !isFooterVisible ? 'translate-y-0 opacity-100' : 'translate-y-16 opacity-0'
        }`}
      >
        <button
          onClick={() => setShowModal(true)}
          className="fixed-button btn text-white px-6 py-3 rounded-full font-bold
           transition-colors duration-300 shadow-2xl
          hover:shadow-[0_0_15px_rgba(211,84,0,0.5)] flex items-center gap-2"
        >
          <i className="fas fa-heart text-red-500"></i>
          <span>Donate Now</span>
        </button>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-h-[90vh] overflow-y-auto relative">
            <button
              onClick={() => setShowModal(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 z-10"
            >
              ✕
            </button>
            <DonateForm />
          </div>
        </div>
      )}
    </>
  );
};

export default FloatingDonateButton;
