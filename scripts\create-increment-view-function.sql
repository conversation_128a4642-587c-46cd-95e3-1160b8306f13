-- Create function to increment blog post view count
-- This provides an atomic operation to safely increment view counts

CREATE OR REPLACE FUNCTION increment_blog_view_count(post_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE sas_blog_posts 
  SET 
    view_count = COALESCE(view_count, 0) + 1,
    updated_at = NOW()
  WHERE id = post_id;
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION increment_blog_view_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_blog_view_count(UUID) TO anon;

-- Test the function (optional)
-- SELECT increment_blog_view_count('your-post-id-here');
