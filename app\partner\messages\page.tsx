'use client';

import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  Send,
  Search,
  Plus,
  Clock,
  Package,
  User,
  Users,
  Calendar,
  Paperclip,
  Phone,
  Video
} from 'lucide-react';

interface Message {
  id: number;
  conversationId: number;
  sender: 'partner' | 'admin';
  senderName: string;
  content: string;
  timestamp: string;
  read: boolean;
  attachments?: string[];
}

interface Conversation {
  id: number;
  subject: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  status: 'open' | 'resolved';
  priority: 'low' | 'medium' | 'high';
  bookingReference?: string;
  adminName: string;
}

interface Booking {
  id: string;
  reference: string;
  packageName: string;
  customerName: string;
  travelDate: string;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  totalAmount: number;
  travelers: number;
  hasMessages: boolean;
  lastMessageTime?: string;
  unreadCount: number;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerMessagesPage() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<number | null>(null);
  const [selectedBooking, setSelectedBooking] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [bookingSearchTerm, setBookingSearchTerm] = useState('');

  const [activeTab, setActiveTab] = useState<'conversations' | 'bookings'>('conversations');

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockConversations: Conversation[] = [
      {
        id: 1,
        subject: 'Booking Modification Request - SA-B2B-001',
        lastMessage: 'We can accommodate the date change. Let me check availability.',
        lastMessageTime: '2024-12-20T10:30:00Z',
        unreadCount: 1,
        status: 'open',
        priority: 'medium',
        bookingReference: 'SA-B2B-001',
        adminName: 'Sarah Johnson'
      },
      {
        id: 2,
        subject: 'Payment Confirmation',
        lastMessage: 'Payment has been processed successfully. Thank you!',
        lastMessageTime: '2024-12-19T15:45:00Z',
        unreadCount: 0,
        status: 'resolved',
        priority: 'low',
        adminName: 'Michael Chen'
      },
      {
        id: 3,
        subject: 'Commission Rate Discussion',
        lastMessage: 'Based on your performance, we can offer an improved rate.',
        lastMessageTime: '2024-12-18T09:15:00Z',
        unreadCount: 2,
        status: 'open',
        priority: 'high',
        adminName: 'Emma Davis'
      }
    ];

    const mockMessages: Message[] = [
      {
        id: 1,
        conversationId: 1,
        sender: 'partner',
        senderName: 'John Safari Tours',
        content: 'Hello, I need to modify the booking SA-B2B-001. The client wants to change the travel date from January 20th to January 25th.',
        timestamp: '2024-12-20T09:00:00Z',
        read: true
      },
      {
        id: 2,
        conversationId: 1,
        sender: 'admin',
        senderName: 'Sarah Johnson',
        content: 'Hi John! I can help you with that modification. Let me check the availability for January 25th.',
        timestamp: '2024-12-20T09:15:00Z',
        read: true
      },
      {
        id: 3,
        conversationId: 1,
        sender: 'admin',
        senderName: 'Sarah Johnson',
        content: 'Good news! We can accommodate the date change. The package is available for January 25th. I\'ll update the booking and send you the revised confirmation.',
        timestamp: '2024-12-20T10:30:00Z',
        read: false
      }
    ];

    const mockBookings: Booking[] = [
      {
        id: 'SA-B2B-001',
        reference: 'SA-B2B-001',
        packageName: 'Serengeti & Ngorongoro 5-Day Safari',
        customerName: 'Michael Johnson',
        travelDate: '2025-01-20',
        status: 'confirmed',
        totalAmount: 7200,
        travelers: 4,
        hasMessages: true,
        lastMessageTime: '2024-12-20T10:30:00Z',
        unreadCount: 1
      },
      {
        id: 'SA-B2B-002',
        reference: 'SA-B2B-002',
        packageName: 'Kilimanjaro Climbing Adventure',
        customerName: 'Sarah Williams',
        travelDate: '2025-02-15',
        status: 'pending',
        totalAmount: 4500,
        travelers: 2,
        hasMessages: true,
        lastMessageTime: '2024-12-18T08:15:00Z',
        unreadCount: 0
      },
      {
        id: 'SA-B2B-003',
        reference: 'SA-B2B-003',
        packageName: 'Zanzibar Beach Extension',
        customerName: 'David Chen',
        travelDate: '2025-03-10',
        status: 'confirmed',
        totalAmount: 2800,
        travelers: 2,
        hasMessages: false,
        unreadCount: 0
      },
      {
        id: 'SA-B2B-004',
        reference: 'SA-B2B-004',
        packageName: 'Maasai Mara Wildlife Safari',
        customerName: 'Emma Thompson',
        travelDate: '2025-01-30',
        status: 'completed',
        totalAmount: 5600,
        travelers: 3,
        hasMessages: true,
        lastMessageTime: '2024-12-10T14:20:00Z',
        unreadCount: 0
      },
      {
        id: 'SA-B2B-005',
        reference: 'SA-B2B-005',
        packageName: 'Lake Nakuru & Amboseli Safari',
        customerName: 'Robert Garcia',
        travelDate: '2025-04-05',
        status: 'pending',
        totalAmount: 6200,
        travelers: 5,
        hasMessages: false,
        unreadCount: 0
      }
    ];

    setConversations(mockConversations);
    setMessages(mockMessages);
    setBookings(mockBookings);

    // Auto-select first conversation
    if (mockConversations.length > 0) {
      setSelectedConversation(mockConversations[0].id);
    }
  }, []);

  const filteredConversations = conversations.filter(conv =>
    conv.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.adminName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.bookingReference?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredBookings = bookings.filter(booking =>
    booking.reference.toLowerCase().includes(bookingSearchTerm.toLowerCase()) ||
    booking.packageName.toLowerCase().includes(bookingSearchTerm.toLowerCase()) ||
    booking.customerName.toLowerCase().includes(bookingSearchTerm.toLowerCase())
  );

  const selectedConversationData = conversations.find(c => c.id === selectedConversation);
  const conversationMessages = messages.filter(m => m.conversationId === selectedConversation);
  const selectedBookingData = bookings.find(b => b.id === selectedBooking);

  const handleBookingSelect = (bookingId: string) => {
    setSelectedBooking(bookingId);
    setSelectedConversation(null);

    // Check if there's already a conversation for this booking
    const existingConversation = conversations.find(c => c.bookingReference === bookingId);
    if (existingConversation) {
      setSelectedConversation(existingConversation.id);
      setActiveTab('conversations');
    } else {
      // Create a new conversation for this booking
      const booking = bookings.find(b => b.id === bookingId);
      if (booking) {
        const newConversation: Conversation = {
          id: conversations.length + 1,
          subject: `Discussion for ${booking.reference} - ${booking.packageName}`,
          lastMessage: 'Start a conversation about this booking...',
          lastMessageTime: new Date().toISOString(),
          unreadCount: 0,
          status: 'open',
          priority: 'medium',
          bookingReference: booking.reference,
          adminName: 'Support Team'
        };
        setConversations(prev => [newConversation, ...prev]);
        setSelectedConversation(newConversation.id);
        setActiveTab('conversations');
      }
    }
  };

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedConversation) {
      const message: Message = {
        id: messages.length + 1,
        conversationId: selectedConversation,
        sender: 'partner',
        senderName: 'John Safari Tours',
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        read: true
      };
      setMessages(prev => [...prev, message]);
      setNewMessage('');

      // Update conversation last message
      setConversations(prev => prev.map(conv => 
        conv.id === selectedConversation 
          ? { ...conv, lastMessage: newMessage.trim(), lastMessageTime: new Date().toISOString() }
          : conv
      ));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('en-US');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US');
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Messages</h1>
          <p className="text-gray-600">Communicate with the Swift Africa Safaris team</p>
        </div>
        <button
          onClick={() => alert('New conversation feature coming soon!')}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Message
        </button>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-[600px] flex">
        {/* Left Sidebar - Bookings & Conversations */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 bg-gray-50">
            <div className="flex">
              <button
                onClick={() => setActiveTab('conversations')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'conversations'
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <MessageSquare className="h-4 w-4 inline mr-2" />
                Conversations ({filteredConversations.length})
              </button>
              <button
                onClick={() => setActiveTab('bookings')}
                className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === 'bookings'
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Package className="h-4 w-4 inline mr-2" />
                Bookings ({filteredBookings.length})
              </button>
            </div>
          </div>

          {/* Conversations Tab */}
          {activeTab === 'conversations' && (
            <>
              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search conversations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Conversations List */}
              <div className="flex-1 overflow-y-auto">

            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation.id)}
                className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                  selectedConversation === conversation.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 text-sm line-clamp-1">{conversation.subject}</div>
                    <div className="text-xs text-gray-500 mt-1">with {conversation.adminName}</div>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(conversation.status)}`}>
                      {conversation.status}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(conversation.priority)}`}>
                      {conversation.priority}
                    </span>
                  </div>
                </div>
                
                {conversation.bookingReference && (
                  <div className="flex items-center text-xs text-blue-600 mb-2">
                    <Package className="h-3 w-3 mr-1" />
                    {conversation.bookingReference}
                  </div>
                )}
                
                <div className="text-sm text-gray-600 line-clamp-2 mb-2">{conversation.lastMessage}</div>
                
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatTime(conversation.lastMessageTime)}
                  </div>
                  {conversation.unreadCount > 0 && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                      {conversation.unreadCount}
                    </span>
                  )}
                </div>
              </div>
            ))}
              </div>
            </>
          )}

          {/* Bookings Tab */}
          {activeTab === 'bookings' && (
            <>
              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search bookings..."
                    value={bookingSearchTerm}
                    onChange={(e) => setBookingSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Bookings List */}
              <div className="flex-1 overflow-y-auto">
                {filteredBookings.map((booking) => (
                  <div
                    key={booking.id}
                    onClick={() => handleBookingSelect(booking.id)}
                    className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors ${
                      selectedBooking === booking.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 text-sm">{booking.reference}</div>
                        <div className="text-xs text-gray-500 mt-1">{booking.packageName}</div>
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                          booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          booking.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                          'bg-blue-100 text-blue-800'
                        }`}>
                          {booking.status}
                        </span>
                        {booking.hasMessages && (
                          <div className="flex items-center text-xs text-blue-600">
                            <MessageSquare className="h-3 w-3 mr-1" />
                            Messages
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="text-sm text-gray-600 mb-2">
                      <div className="flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        {booking.customerName}
                      </div>
                      <div className="flex items-center mt-1">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(booking.travelDate)}
                      </div>
                      <div className="flex items-center mt-1">
                        <Users className="h-3 w-3 mr-1" />
                        {booking.travelers} travelers
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-gray-500">
                        ${booking.totalAmount.toLocaleString()}
                      </div>
                      {booking.unreadCount > 0 && (
                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {booking.unreadCount}
                        </span>
                      )}
                      {booking.lastMessageTime && (
                        <div className="text-xs text-gray-500">
                          {formatTime(booking.lastMessageTime)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {filteredBookings.length === 0 && (
                  <div className="p-8 text-center">
                    <Package className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 font-medium">No bookings found</p>
                    <p className="text-gray-400 text-sm mt-1">
                      {bookingSearchTerm ? 'Try adjusting your search terms' : 'Your bookings will appear here'}
                    </p>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Message Area */}
        <div className="flex-1 flex flex-col">
          {selectedConversationData || selectedBookingData ? (
            <>
              {/* Header */}
              <div className="p-4 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <div>
                    {selectedConversationData ? (
                      <>
                        <div className="font-medium text-gray-900">{selectedConversationData.subject}</div>
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <User className="h-3 w-3 mr-1" />
                          {selectedConversationData.adminName}
                          {selectedConversationData.bookingReference && (
                            <>
                              <span className="mx-2">•</span>
                              <Package className="h-3 w-3 mr-1" />
                              {selectedConversationData.bookingReference}
                            </>
                          )}
                        </div>
                      </>
                    ) : selectedBookingData ? (
                      <>
                        <div className="font-medium text-gray-900">Discussion for {selectedBookingData.reference}</div>
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <Package className="h-3 w-3 mr-1" />
                          {selectedBookingData.packageName}
                          <span className="mx-2">•</span>
                          <User className="h-3 w-3 mr-1" />
                          {selectedBookingData.customerName}
                        </div>
                      </>
                    ) : null}
                  </div>
                  <div className="flex items-center space-x-2">
                    {selectedConversationData ? (
                      <>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedConversationData.status)}`}>
                          {selectedConversationData.status}
                        </span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedConversationData.priority)}`}>
                          {selectedConversationData.priority} priority
                        </span>
                      </>
                    ) : selectedBookingData ? (
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        selectedBookingData.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        selectedBookingData.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        selectedBookingData.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {selectedBookingData.status}
                      </span>
                    ) : null}
                    <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors">
                      <Phone className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors">
                      <Video className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Booking Context (when booking is selected) */}
              {selectedBookingData && !selectedConversationData && (
                <div className="p-4 bg-blue-50 border-b border-blue-200">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-blue-900">Booking Details</h4>
                    <button
                      onClick={() => window.open(`/partner/bookings/${selectedBookingData.id}`, '_blank')}
                      className="text-blue-600 hover:text-blue-500 text-sm font-medium"
                    >
                      View Full Details →
                    </button>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-blue-600 font-medium">Customer:</span>
                      <div className="text-blue-800">{selectedBookingData.customerName}</div>
                    </div>
                    <div>
                      <span className="text-blue-600 font-medium">Travel Date:</span>
                      <div className="text-blue-800">{formatDate(selectedBookingData.travelDate)}</div>
                    </div>
                    <div>
                      <span className="text-blue-600 font-medium">Travelers:</span>
                      <div className="text-blue-800">{selectedBookingData.travelers} people</div>
                    </div>
                    <div>
                      <span className="text-blue-600 font-medium">Total:</span>
                      <div className="text-blue-800">${selectedBookingData.totalAmount.toLocaleString()}</div>
                    </div>
                  </div>
                  <div className="mt-3 p-3 bg-white rounded border border-blue-200">
                    <p className="text-sm text-blue-800">
                      <strong>Start a conversation:</strong> Use the message box below to discuss this booking with our admin team.
                      You can ask questions about modifications, special requests, or any concerns about this booking.
                    </p>
                  </div>
                </div>
              )}

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {conversationMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'partner' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.sender === 'partner'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}>
                      <div className="text-sm">{message.content}</div>
                      <div className={`text-xs mt-1 ${
                        message.sender === 'partner' ? 'text-blue-100' : 'text-gray-500'
                      }`}>
                        {message.senderName} • {formatTime(message.timestamp)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Message Input */}
              <div className="p-4 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                    <Paperclip className="h-4 w-4" />
                  </button>
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Send
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Select a conversation</p>
                <p className="text-sm">Choose a conversation from the list to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
