import React from 'react';
import SkeletonBase from './SkeletonBase';

/**
 * Skeleton Image Component for hero images, gallery images, and thumbnails
 * Provides image placeholder with shimmer animation
 */
const SkeletonImage = ({ 
  variant = 'default', // 'hero', 'card', 'thumbnail', 'gallery', 'default'
  className = '',
  aspectRatio = 'aspect-video', // 'aspect-square', 'aspect-video', 'aspect-[4/3]', etc.
  showIcon = true
}) => {
  const getImageDimensions = () => {
    switch (variant) {
      case 'hero':
        return 'w-full h-[70vh]';
      case 'card':
        return 'w-full h-48';
      case 'thumbnail':
        return 'w-20 h-20';
      case 'gallery':
        return 'w-full h-64';
      default:
        return `w-full h-full ${aspectRatio}`;
    }
  };

  return (
    <div className={`relative ${getImageDimensions()} ${className}`}>
      <SkeletonBase 
        width="w-full" 
        height="h-full" 
        rounded="rounded-none"
      />
      
      {/* Optional image icon in center */}
      {showIcon && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center opacity-50">
            <svg 
              className="w-6 h-6 text-gray-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default SkeletonImage;
