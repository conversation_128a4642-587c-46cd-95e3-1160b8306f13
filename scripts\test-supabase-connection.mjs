#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Testing Supabase Connection...\n');
console.log('URL:', supabaseUrl);
console.log('Key:', supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'Not found');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    // Test basic connection
    console.log('\n1. Testing basic connection...');
    const { data, error } = await supabase.from('users').select('count').limit(1);
    
    if (error) {
      console.log('❌ Connection error:', error.message);
      console.log('Error details:', error);
    } else {
      console.log('✅ Connection successful');
    }

    // Test auth
    console.log('\n2. Testing auth...');
    const { data: session } = await supabase.auth.getSession();
    console.log('Current session:', session.session ? 'Active' : 'None');

    // Try to create a test user with a simple email
    console.log('\n3. Testing user creation...');
    const testEmail = '<EMAIL>';
    const testPassword = 'testpass123';

    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });

    if (authError) {
      console.log('❌ Auth signup error:', authError.message);
      console.log('Error details:', authError);
    } else {
      console.log('✅ Test user creation successful');
      console.log('User ID:', authData.user?.id);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testConnection();
