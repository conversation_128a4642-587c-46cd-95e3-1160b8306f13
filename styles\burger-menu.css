/* Position and sizing of burger button */
.bm-burger-button {
  position: relative;
  width: 24px;
  height: 24px;
}

/* Color/shape of burger icon bars */
.bm-burger-bars {
  background: #001b3e;
}

/* Position and sizing of clickable cross button */
.bm-cross-button {
  height: 24px;
  width: 24px;
  right: 24px !important;
  top: 24px !important;
}

/* Color/shape of close button cross */
.bm-cross {
  background: black;
}

/* Sidebar wrapper styles */
.bm-menu-wrap {
  position: fixed;
  height: 100%;
  top: 0;
}

/* General sidebar styles */
.bm-menu {
  overflow-y: auto;
  background: #001b3e;
  padding: 1rem 1.5rem;
  font-size: 1.15em;
}

/* Wrapper for item list */
.bm-item-list {
  padding: 0;
  height: auto !important;
}

/* Individual item */
.bm-item {
  display: inline-block;
  outline: none;
}

/* Overlay */
.bm-overlay {
  background: rgba(0, 0, 0, 0.3);
  top: 0;
  left: 0;
}

/* Sliding animation */
.mobile-submenu {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}
