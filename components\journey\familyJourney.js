import React from 'react';
import ImageSlider from '../journey/imageSlider';
import Button from './Button';


const FamilyJourney = () => {
  const familyImages = [
    {
      src: '/images/journey/Family-Gorilla-trekking-Swift-africa-safaris.webp',
      alt: 'Happy family in Volcanoes national park doing mountain gorilla trekking'
    },
    {
      src: '/images/journey/Game-drive-family-safari.webp',
      alt: 'Happy family on stunning game drive in Serengeti national park'
    }
  ];

  return (
    <section className="flex flex-col md:flex-row items-center bg-[var(--secondary-background)] px-6 py-16 md:px-16">
      <div className="flex-1 px-0 md:px-10 order-1 md:order-none lg:text-left md:text-left lg:pr-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-5 text-gray-900 leading-tight text-center md:text-left lg:text-left">
          Family Journey: Create Stories That Span Generations
        </h2>
        <div className="text-base leading-relaxed text-gray-700 mb-8 space-y-4 text-justify md:text-left max-w-2xl lg:text-left">
          <p>
            Traveling as a family is about more than just seeing new places, it&apos;s about
            creating lifelong memories, strengthening bonds, and inspiring wonder in every generation. Africa is a
            playground for discovery, offering enriching experiences that captivate both young minds and seasoned
            travelers alike.
          </p>
          <p>
            Picture your children&apos;s as they trek mountain gorillas, spot their first elephant on safari, the joy of
            sharing a traditional meal with a local family, or the quiet moments watching sunsets together over endless horizons.
          </p>
          <p>
            Our family journeys are thoughtfully designed with safety, comfort, and age appropriate activities in
            mind. Whether you&apos;re traveling with little explorers or teenagers hungry for adventure, we ensure a
            balance of excitement and ease.
          </p>
        </div>

        {/* Button for desktop */}
        <Button variant="desktop">Learn More</Button>
      </div>

      <div className="flex-1 order-2 md:order-none mt-8 md:mt-0 w-full">
        <ImageSlider images={familyImages} aspectRatio="portrait" />

        {/* Button for mobile and tablet */}
        <Button variant="mobile">Learn More</Button>
      </div>
    </section>
  );
};

export default FamilyJourney;