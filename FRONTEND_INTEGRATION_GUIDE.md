# Frontend Integration Guide

## Overview

This guide explains how to connect the existing frontend components to the new backend system. The backend is now fully functional and ready for integration.

## Quick Start

1. **Setup Database:**
   ```bash
   node scripts/setup-complete-backend.mjs
   ```

2. **Verify Setup:**
   Check that all tables and storage buckets were created successfully.

3. **Test API Endpoints:**
   Use the provided API documentation to test endpoints.

## Frontend Components to Update

### 1. Package List Page (`app/package/page.tsx`)

**Current:** Uses static `tourPackages` data
**Update:** Connect to `/api/packages` endpoint

```typescript
// Replace static import with API call
const fetchPackages = async () => {
  const response = await fetch('/api/packages?limit=12');
  const result = await response.json();
  return result.success ? result.data : [];
};
```

### 2. Package Detail Page (`app/package/[slug]/page.tsx`)

**Current:** Uses static `packageDetails` data
**Update:** Connect to `/api/packages/[slug]` endpoint

```typescript
// In generateMetadata function
const response = await fetch(`/api/packages/${slug}`);
const result = await response.json();
const packageData = result.success ? result.data : null;
```

### 3. Admin Package List (`app/admin/packages/page.tsx`)

**Current:** Already connected to `/api/admin/packages`
**Status:** ✅ Ready - Backend now fully supports this endpoint

### 4. Admin Package Edit (`app/admin/packages/edit/[slug]/page.tsx`)

**Current:** Partially connected
**Update:** Connect to `/api/admin/packages/[slug]` for full CRUD

```typescript
// Load package data
const response = await fetch(`/api/admin/packages/${slug}`);
const result = await response.json();

// Save package data
const saveResponse = await fetch(`/api/admin/packages/${slug}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(packageData)
});
```

### 5. Admin Package Add (`app/admin/packages/add/page.tsx`)

**Current:** Connected to `/api/admin/packages` POST
**Status:** ✅ Ready - Backend fully supports package creation

### 6. Booking Form (`components/readpackage/BookingForm.jsx`)

**Current:** Only logs to console
**Update:** Connect to `/api/bookings` endpoint

```javascript
const handleSubmit = async (e) => {
  e.preventDefault();
  
  try {
    const response = await fetch('/api/bookings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        packageTitle,
        packageType: formData.packageType,
        fullName: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        people: formData.people,
        date: formData.date,
        checkOutDate: formData.checkOutDate,
        message: formData.message
      })
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Show success message with booking reference
      setShowNotification(true);
    } else {
      // Handle error
      alert('Booking failed: ' + result.error);
    }
  } catch (error) {
    console.error('Booking error:', error);
    alert('Booking failed. Please try again.');
  }
};
```

## Admin Component Updates

### 1. Package Card Component (`components/admin/package/packageCard.tsx`)

**Update:** Connect form submission to backend

```typescript
const handleSave = async () => {
  const packageData = {
    title: formData.title,
    location: formData.location,
    difficulty: formData.difficulty,
    category: formData.category,
    pricing: formData.pricing,
    imageUrl: formData.imagePreview,
    imageAlt: 'Package image', // Always provide alt text
    // ... other fields
  };
  
  const response = await fetch('/api/admin/packages', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(packageData)
  });
  
  const result = await response.json();
  // Handle response
};
```

### 2. Package Editor (`components/admin/package/packageEditor.tsx`)

**Update:** Connect to content blocks API

```typescript
// Save content blocks
const saveContentBlocks = async (blocks) => {
  const response = await fetch(`/api/admin/packages/${packageSlug}/content`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ blocks })
  });
  
  return response.json();
};

// Add new content block
const addContentBlock = async (blockData) => {
  const response = await fetch(`/api/admin/packages/${packageSlug}/content`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      ...blockData,
      image_alt: blockData.image_alt || 'Image description' // Required!
    })
  });
  
  return response.json();
};
```

### 3. Itinerary Creator (`components/admin/package/packageTravelItineraryCreator.tsx`)

**Update:** Connect to itinerary API

```typescript
// Save itinerary
const saveItinerary = async (days) => {
  const response = await fetch(`/api/admin/packages/${packageSlug}/itinerary`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ days })
  });
  
  return response.json();
};
```

## File Upload Integration

### Image Upload Component

```typescript
const uploadImage = async (file, altText) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('altText', altText); // Required!
  formData.append('bucket', 'sas-package-images');
  
  const response = await fetch('/api/upload/image', {
    method: 'POST',
    body: formData
  });
  
  const result = await response.json();
  
  if (result.success) {
    return result.data.url;
  } else {
    throw new Error(result.error);
  }
};
```

## Data Migration

If you have existing data in `components/data/packageData.js`, you can migrate it:

1. **Extract Package Data:**
   ```javascript
   // Use the existing packageDetails object
   const packages = Object.values(packageDetails);
   ```

2. **Transform and Upload:**
   ```javascript
   for (const pkg of packages) {
     const packageData = {
       title: pkg.title,
       slug: pkg.slug || createSlug(pkg.title),
       overview: pkg.overview,
       difficulty: pkg.difficulty,
       // ... transform other fields
     };
     
     await fetch('/api/admin/packages', {
       method: 'POST',
       headers: { 'Content-Type': 'application/json' },
       body: JSON.stringify(packageData)
     });
   }
   ```

## Important Notes

### Alt Text Requirement
**CRITICAL:** All images must have alt text. The backend will reject image uploads without alt text.

```typescript
// ❌ Wrong - will be rejected
{ image_url: 'url', image_alt: '' }

// ✅ Correct
{ image_url: 'url', image_alt: 'Safari wildlife viewing in Uganda' }
```

### Error Handling
Always handle API errors properly:

```typescript
const response = await fetch('/api/endpoint');
const result = await response.json();

if (!result.success) {
  console.error('API Error:', result.error);
  // Show user-friendly error message
  return;
}

// Process successful result
const data = result.data;
```

### Loading States
Implement loading states for better UX:

```typescript
const [loading, setLoading] = useState(false);

const handleSubmit = async () => {
  setLoading(true);
  try {
    // API call
  } finally {
    setLoading(false);
  }
};
```

## Testing Checklist

After integration, test:

- ✅ Package list displays correctly
- ✅ Package details load properly
- ✅ Admin package CRUD operations work
- ✅ Content blocks can be added/edited
- ✅ Itinerary can be managed
- ✅ File uploads work with alt text
- ✅ Booking form submits successfully
- ✅ Emails are sent (check spam folder)
- ✅ Error handling works properly
- ✅ Loading states display correctly

## Support

If you encounter issues:

1. Check the browser console for errors
2. Verify API endpoints are responding
3. Check Supabase dashboard for data
4. Review the API documentation
5. Test with sample data first

The backend is now complete and ready for full integration!
