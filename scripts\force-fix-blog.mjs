import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function forceFix() {
  console.log('🔧 Force fixing oversized blog post...\n');
  
  try {
    // First, let's check the current state
    const { data: currentPost, error: fetchError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, hero_image_url')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (fetchError || !currentPost) {
      console.error('❌ Error fetching blog post:', fetchError);
      return;
    }
    
    console.log(`✅ Current post: ${currentPost.title}`);
    console.log(`📊 Current hero_image_url length: ${currentPost.hero_image_url.length} characters`);
    console.log(`📊 Is base64?: ${currentPost.hero_image_url.startsWith('data:image/')}`);
    
    if (!currentPost.hero_image_url.startsWith('data:image/')) {
      console.log('✅ Hero image URL is already fixed!');
      return;
    }
    
    // Force update with a proper image URL
    const newImageUrl = 'https://images.unsplash.com/photo-1551632811-561732d1e306?auto=format&fit=crop&w=1200&q=80';
    
    console.log(`\n🔄 Force updating with new image URL...`);
    console.log(`   New URL: ${newImageUrl}`);
    
    const { error: updateError } = await supabase
      .from('sas_blog_posts')
      .update({
        hero_image_url: newImageUrl,
        og_image_url: newImageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda');
    
    if (updateError) {
      console.error('❌ Error updating blog post:', updateError);
      return;
    }
    
    console.log('✅ Update completed!');
    
    // Verify the update
    const { data: verifyPost, error: verifyError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, hero_image_url, og_image_url')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (verifyError || !verifyPost) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }
    
    console.log(`\n🔍 Verification:`);
    console.log(`   Hero image URL: ${verifyPost.hero_image_url}`);
    console.log(`   OG image URL: ${verifyPost.og_image_url}`);
    console.log(`   Hero URL length: ${verifyPost.hero_image_url.length} characters`);
    console.log(`   Is base64?: ${verifyPost.hero_image_url.startsWith('data:image/')}`);
    
    if (!verifyPost.hero_image_url.startsWith('data:image/')) {
      console.log(`\n🎉 SUCCESS! Blog post has been fixed.`);
      console.log(`📊 Size reduction: ~7.36 MB`);
      console.log(`✅ The blog post should now deploy successfully on Vercel.`);
    } else {
      console.log(`\n❌ Update failed - still contains base64 data`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

forceFix();
