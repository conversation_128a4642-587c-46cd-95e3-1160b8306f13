#!/usr/bin/env node

/**
 * Test script for the bulk delete blog posts API
 * This script tests the /api/admin/blog/bulk-delete endpoint
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testBulkDeleteAPI() {
  console.log('🧪 Testing Bulk Delete Blog Posts API\n');

  try {
    // Step 1: Create some test blog posts for deletion
    console.log('📝 Creating test blog posts...');
    
    const testPosts = [];
    for (let i = 1; i <= 3; i++) {
      const testPost = {
        title: `Test Blog Post ${i} - Bulk Delete Test`,
        slug: `test-bulk-delete-${i}-${Date.now()}`,
        description: `This is a test blog post ${i} created for bulk delete testing`,
        hero_image_url: 'https://via.placeholder.com/800x400',
        hero_image_alt: `Test image ${i}`,
        category: 'test',
        tags: ['test', 'bulk-delete'],
        status: 'draft',
        content: [
          {
            type: 'paragraph',
            content: `This is test content for blog post ${i}.`
          }
        ]
      };

      const { data: createdPost, error } = await supabase
        .from('sas_blog_posts')
        .insert(testPost)
        .select('id, title, slug')
        .single();

      if (error) {
        console.error(`❌ Failed to create test post ${i}:`, error.message);
        continue;
      }

      testPosts.push(createdPost);
      console.log(`✅ Created test post: ${createdPost.title} (ID: ${createdPost.id})`);
    }

    if (testPosts.length === 0) {
      console.error('❌ No test posts were created. Cannot proceed with bulk delete test.');
      return;
    }

    console.log(`\n📊 Created ${testPosts.length} test posts for bulk deletion\n`);

    // Step 2: Test the bulk delete API
    console.log('🗑️  Testing bulk delete API...');
    
    const blogPostIds = testPosts.map(post => post.id);
    
    const response = await fetch('http://localhost:3000/api/admin/blog/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // In development mode, authentication is bypassed
      },
      body: JSON.stringify({
        blogPostIds: blogPostIds
      })
    });

    const result = await response.json();

    console.log(`📡 API Response Status: ${response.status}`);
    console.log('📄 API Response Body:');
    console.log(JSON.stringify(result, null, 2));

    // Step 3: Verify the deletion
    console.log('\n🔍 Verifying deletion results...');
    
    for (const postId of blogPostIds) {
      const { data: deletedPost, error } = await supabase
        .from('sas_blog_posts')
        .select('id, title, deleted_at')
        .eq('id', postId)
        .single();

      if (error) {
        console.log(`❌ Error checking post ${postId}: ${error.message}`);
        continue;
      }

      if (deletedPost.deleted_at) {
        console.log(`✅ Post ${postId} successfully soft deleted at ${deletedPost.deleted_at}`);
      } else {
        console.log(`❌ Post ${postId} was NOT deleted`);
      }
    }

    // Step 4: Test error cases
    console.log('\n🧪 Testing error cases...');

    // Test with invalid IDs
    console.log('Testing with invalid blog post IDs...');
    const invalidResponse = await fetch('http://localhost:3000/api/admin/blog/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        blogPostIds: ['invalid-id', 'another-invalid-id']
      })
    });

    const invalidResult = await invalidResponse.json();
    console.log(`📡 Invalid IDs Response Status: ${invalidResponse.status}`);
    console.log('📄 Invalid IDs Response:', JSON.stringify(invalidResult, null, 2));

    // Test with empty array
    console.log('\nTesting with empty array...');
    const emptyResponse = await fetch('http://localhost:3000/api/admin/blog/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        blogPostIds: []
      })
    });

    const emptyResult = await emptyResponse.json();
    console.log(`📡 Empty Array Response Status: ${emptyResponse.status}`);
    console.log('📄 Empty Array Response:', JSON.stringify(emptyResult, null, 2));

    // Test with non-existent IDs
    console.log('\nTesting with non-existent blog post IDs...');
    const nonExistentIds = [
      '00000000-0000-0000-0000-000000000001',
      '00000000-0000-0000-0000-000000000002'
    ];
    
    const nonExistentResponse = await fetch('http://localhost:3000/api/admin/blog/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        blogPostIds: nonExistentIds
      })
    });

    const nonExistentResult = await nonExistentResponse.json();
    console.log(`📡 Non-existent IDs Response Status: ${nonExistentResponse.status}`);
    console.log('📄 Non-existent IDs Response:', JSON.stringify(nonExistentResult, null, 2));

    console.log('\n✅ Bulk delete API testing completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testBulkDeleteAPI().catch(console.error);
