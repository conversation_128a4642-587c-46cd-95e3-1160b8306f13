import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      metric_name, 
      metric_value, 
      url, 
      user_agent, 
      connection_type,
      device_type,
      timestamp 
    } = body

    // Validate required fields
    if (!metric_name || metric_value === undefined || !url) {
      return NextResponse.json(
        { error: 'Missing required fields: metric_name, metric_value, url' },
        { status: 400 }
      )
    }

    // Store performance metrics in Supabase
    const { error } = await supabase
      .from('performance_metrics')
      .insert({
        metric_name,
        metric_value: parseFloat(metric_value),
        url,
        user_agent: user_agent || request.headers.get('user-agent'),
        connection_type: connection_type || 'unknown',
        device_type: device_type || 'unknown',
        timestamp: timestamp || new Date().toISOString(),
        ip_address: request.ip || 'unknown'
      })

    if (error) {
      console.error('Error storing performance metric:', error)
      return NextResponse.json(
        { error: 'Failed to store performance metric' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Performance API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const metric = searchParams.get('metric')
    const days = parseInt(searchParams.get('days') || '7')
    const url = searchParams.get('url')

    let query = supabase
      .from('performance_metrics')
      .select('*')
      .gte('timestamp', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())
      .order('timestamp', { ascending: false })

    if (metric) {
      query = query.eq('metric_name', metric)
    }

    if (url) {
      query = query.eq('url', url)
    }

    const { data, error } = await query.limit(1000)

    if (error) {
      console.error('Error fetching performance metrics:', error)
      return NextResponse.json(
        { error: 'Failed to fetch performance metrics' },
        { status: 500 }
      )
    }

    // Calculate aggregated metrics
    const aggregated = data?.reduce((acc: any, curr: any) => {
      const metricName = curr.metric_name
      if (!acc[metricName]) {
        acc[metricName] = {
          values: [],
          count: 0,
          sum: 0,
          min: Infinity,
          max: -Infinity
        }
      }
      
      acc[metricName].values.push(curr.metric_value)
      acc[metricName].count++
      acc[metricName].sum += curr.metric_value
      acc[metricName].min = Math.min(acc[metricName].min, curr.metric_value)
      acc[metricName].max = Math.max(acc[metricName].max, curr.metric_value)
      
      return acc
    }, {})

    // Calculate percentiles and averages
    const summary = Object.entries(aggregated || {}).map(([metricName, data]: [string, any]) => {
      const sortedValues = data.values.sort((a: number, b: number) => a - b)
      const p75Index = Math.floor(sortedValues.length * 0.75)
      const p95Index = Math.floor(sortedValues.length * 0.95)
      
      return {
        metric: metricName,
        count: data.count,
        average: data.sum / data.count,
        min: data.min,
        max: data.max,
        p75: sortedValues[p75Index] || 0,
        p95: sortedValues[p95Index] || 0
      }
    })

    return NextResponse.json({
      raw_data: data,
      summary,
      period_days: days
    })

  } catch (error) {
    console.error('Performance API GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
