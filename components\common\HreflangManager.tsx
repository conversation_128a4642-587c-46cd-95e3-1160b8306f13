'use client'

import React, { useEffect, useState } from 'react'
import { HreflangRegion, hreflangConfigs } from '@/lib/seo'
import { 
  getRegionalContent, 
  shouldShowRegionalContent, 
  getRegionalContact,
  RegionalContent 
} from '@/lib/hreflang-utils'

interface HreflangManagerProps {
  currentPath: string
  children?: React.ReactNode
}

interface RegionSelectorProps {
  currentRegion: HreflangRegion
  onRegionChange: (region: HreflangRegion) => void
  className?: string
}

/**
 * Main Hreflang Manager Component
 * Handles region detection, content localization, and regional preferences
 */
export default function HreflangManager({ currentPath, children }: HreflangManagerProps) {
  const [currentRegion, setCurrentRegion] = useState<HreflangRegion>('x-default')
  const [regionalContent, setRegionalContent] = useState<RegionalContent | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    detectAndSetRegion()
  }, [])

  useEffect(() => {
    if (currentRegion) {
      const content = getRegionalContent(currentRegion)
      setRegionalContent(content)
      
      // Store user's region preference
      localStorage.setItem('preferred-region', currentRegion)
      
      // Update document language
      document.documentElement.lang = currentRegion.split('-')[0] || 'en'
    }
  }, [currentRegion])

  const detectAndSetRegion = async () => {
    try {
      // Check for stored preference first
      const storedRegion = localStorage.getItem('preferred-region') as HreflangRegion
      if (storedRegion && hreflangConfigs[storedRegion]) {
        setCurrentRegion(storedRegion)
        setIsLoading(false)
        return
      }

      // Detect region from browser/location
      const detectedRegion = await detectRegionFromBrowser()
      setCurrentRegion(detectedRegion)
      setIsLoading(false)
    } catch (error) {
      console.error('Region detection failed:', error)
      setCurrentRegion('x-default')
      setIsLoading(false)
    }
  }

  const detectRegionFromBrowser = async (): Promise<HreflangRegion> => {
    // Method 1: Geolocation API (if user grants permission)
    if ('geolocation' in navigator) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, { timeout: 5000 })
        })
        
        const region = await getRegionFromCoordinates(position.coords.latitude, position.coords.longitude)
        if (region) return region
      } catch (error) {
        console.log('Geolocation not available or denied')
      }
    }

    // Method 2: Browser language preferences
    const languages = navigator.languages || [navigator.language]
    for (const lang of languages) {
      const normalizedLang = lang.toLowerCase().replace('_', '-')
      
      // Direct match
      if (hreflangConfigs[normalizedLang as HreflangRegion]) {
        return normalizedLang as HreflangRegion
      }
      
      // Language-only match (e.g., 'en' -> 'en-US')
      if (normalizedLang.startsWith('en')) {
        const priorityEnglishRegions = ['en-US', 'en-GB', 'en-CA', 'en-AU', 'en-ZA']
        for (const region of priorityEnglishRegions) {
          if (hreflangConfigs[region as HreflangRegion]) {
            return region as HreflangRegion
          }
        }
      }
    }

    // Method 3: Timezone-based detection
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
    const regionFromTimezone = getRegionFromTimezone(timezone)
    if (regionFromTimezone) return regionFromTimezone

    return 'x-default'
  }

  const getRegionFromCoordinates = async (lat: number, lng: number): Promise<HreflangRegion | null> => {
    // Simple coordinate-based region detection
    const regions = [
      { region: 'en-US', bounds: { north: 49, south: 25, east: -66, west: -125 } },
      { region: 'en-GB', bounds: { north: 61, south: 49, east: 2, west: -8 } },
      { region: 'en-CA', bounds: { north: 84, south: 41, east: -52, west: -141 } },
      { region: 'en-AU', bounds: { north: -10, south: -44, east: 154, west: 113 } },
      { region: 'en-ZA', bounds: { north: -22, south: -35, east: 33, west: 16 } },
      { region: 'en-KE', bounds: { north: 5, south: -5, east: 42, west: 34 } },
      { region: 'en-RW', bounds: { north: -1, south: -3, east: 31, west: 29 } },
      { region: 'en-NG', bounds: { north: 14, south: 4, east: 15, west: 3 } }
    ]

    for (const { region, bounds } of regions) {
      if (lat <= bounds.north && lat >= bounds.south && 
          lng <= bounds.east && lng >= bounds.west) {
        return region as HreflangRegion
      }
    }

    return null
  }

  const getRegionFromTimezone = (timezone: string): HreflangRegion | null => {
    const timezoneMap: Record<string, HreflangRegion> = {
      'America/New_York': 'en-US',
      'America/Los_Angeles': 'en-US',
      'America/Chicago': 'en-US',
      'Europe/London': 'en-GB',
      'America/Toronto': 'en-CA',
      'Australia/Sydney': 'en-AU',
      'Australia/Melbourne': 'en-AU',
      'Africa/Johannesburg': 'en-ZA',
      'Africa/Nairobi': 'en-KE',
      'Africa/Kigali': 'en-RW',
      'Africa/Lagos': 'en-NG',
      'Europe/Dublin': 'en-IE',
      'Pacific/Auckland': 'en-NZ'
    }

    return timezoneMap[timezone] || null
  }

  const handleRegionChange = (newRegion: HreflangRegion) => {
    setCurrentRegion(newRegion)
    
    // Optionally redirect to region-specific URL
    const currentUrl = new URL(window.location.href)
    currentUrl.searchParams.set('region', newRegion)
    
    // Update URL without page reload
    window.history.replaceState({}, '', currentUrl.toString())
  }

  if (isLoading) {
    return (
      <div className="hreflang-loading">
        {children}
      </div>
    )
  }

  return (
    <HreflangContext.Provider value={{
      currentRegion,
      regionalContent,
      setRegion: handleRegionChange,
      shouldShowContent: (contentType) => shouldShowRegionalContent(currentRegion, contentType),
      getContact: () => getRegionalContact(currentRegion)
    }}>
      {children}
    </HreflangContext.Provider>
  )
}

/**
 * Region Selector Component
 */
export function RegionSelector({ currentRegion, onRegionChange, className = '' }: RegionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  
  // Get priority regions for selector
  const priorityRegions = Object.entries(hreflangConfigs)
    .filter(([_, config]) => config.priority >= 6)
    .sort(([_, a], [__, b]) => b.priority - a.priority)

  const currentConfig = hreflangConfigs[currentRegion]

  return (
    <div className={`relative inline-block text-left ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <span className="mr-2">{getFlagEmoji(currentRegion)}</span>
        {currentConfig?.country || 'Global'}
        <svg className="w-5 h-5 ml-2 -mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 z-10 w-56 mt-2 origin-top-right bg-white border border-gray-300 rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
          <div className="py-1" role="menu">
            {priorityRegions.map(([region, config]) => (
              <button
                key={region}
                onClick={() => {
                  onRegionChange(region as HreflangRegion)
                  setIsOpen(false)
                }}
                className={`${
                  region === currentRegion ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                } group flex items-center w-full px-4 py-2 text-sm hover:bg-gray-100 hover:text-gray-900`}
                role="menuitem"
              >
                <span className="mr-3">{getFlagEmoji(region as HreflangRegion)}</span>
                <div className="flex flex-col items-start">
                  <span className="font-medium">{config.country}</span>
                  <span className="text-xs text-gray-500">{config.currencySymbol} {config.currency}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Get flag emoji for region
 */
function getFlagEmoji(region: HreflangRegion): string {
  const flags: Record<string, string> = {
    'x-default': '🌍',
    'en-US': '🇺🇸',
    'en-GB': '🇬🇧',
    'en-CA': '🇨🇦',
    'en-AU': '🇦🇺',
    'en-ZA': '🇿🇦',
    'en-KE': '🇰🇪',
    'en-RW': '🇷🇼',
    'en-NG': '🇳🇬',
    'en-IE': '🇮🇪',
    'en-NZ': '🇳🇿',
    'en-DE': '🇩🇪',
    'en-NL': '🇳🇱',
    'en-CH': '🇨🇭'
  }
  
  return flags[region] || '🌍'
}

/**
 * React Context for Hreflang
 */
export const HreflangContext = React.createContext<{
  currentRegion: HreflangRegion
  regionalContent: RegionalContent | null
  setRegion: (region: HreflangRegion) => void
  shouldShowContent: (contentType: 'pricing' | 'testimonials' | 'contact' | 'features') => boolean
  getContact: () => any
} | null>(null)

/**
 * Hook to use Hreflang context
 */
export function useHreflang() {
  const context = React.useContext(HreflangContext)
  if (!context) {
    throw new Error('useHreflang must be used within HreflangManager')
  }
  return context
}
