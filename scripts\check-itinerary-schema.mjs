import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkItinerarySchema() {
  console.log('🔍 Checking sas_mini_package_itinerary table schema...\n');
  
  try {
    // Try to query the table to see what columns exist
    const { data: testData, error: testError } = await supabase
      .from('sas_mini_package_itinerary')
      .select('*')
      .limit(1);

    if (testError) {
      console.error('❌ Error querying table:', testError.message);
      return;
    }

    console.log('📋 Table exists and can be queried');
    console.log('� Records found:', testData?.length || 0);

    if (testData && testData.length > 0) {
      console.log('\n📋 Available columns in first record:');
      const firstRecord = testData[0];
      Object.keys(firstRecord).forEach(key => {
        console.log(`   - ${key}: ${typeof firstRecord[key]} (${firstRecord[key]})`);
      });

      const hasHourNumber = 'hour_number' in firstRecord;
      const hasDayNumber = 'day_number' in firstRecord;

      console.log('\n' + '='.repeat(70));

      if (hasHourNumber) {
        console.log('✅ hour_number column EXISTS - table is correctly migrated');
      } else if (hasDayNumber) {
        console.log('⚠️  day_number column exists but hour_number is MISSING');
        console.log('🔧 SOLUTION: Run the migration script to add hour_number column');
        console.log('   File: scripts/migrate-itinerary-to-hours.sql');
      } else {
        console.log('❌ Neither hour_number nor day_number columns exist');
        console.log('🔧 SOLUTION: Table schema is incomplete');
      }
    } else {
      console.log('\n⚠️  No records found in table - cannot determine schema');
      console.log('🔧 Let\'s try to test specific column queries...');

      // Test for hour_number column
      const { error: hourError } = await supabase
        .from('sas_mini_package_itinerary')
        .select('hour_number')
        .limit(1);

      // Test for day_number column
      const { error: dayError } = await supabase
        .from('sas_mini_package_itinerary')
        .select('day_number')
        .limit(1);

      if (!hourError) {
        console.log('✅ hour_number column EXISTS');
      } else if (!dayError) {
        console.log('⚠️  day_number column exists but hour_number is MISSING');
        console.log('🔧 SOLUTION: Run the migration script');
      } else {
        console.log('❌ Neither hour_number nor day_number columns exist');
        console.log('   hour_number error:', hourError.message);
        console.log('   day_number error:', dayError.message);
      }
    }

  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

checkItinerarySchema();
