import React from 'react';
import { ShoppingCart as CartIcon, X, Plus, Minus, Trash2 } from 'lucide-react';
import { useCart } from './CartContext';

const ShoppingCart = () => {
    const { cartItems, isCartOpen, setIsCartOpen, updateQuantity, removeFromCart } = useCart();

    const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    if (!isCartOpen) {
        return (
            <button
                onClick={() => setIsCartOpen(true)}
                className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-green-800 text-white p-3 rounded-full shadow-lg hover:bg-green-900 transition-colors duration-200 z-50"
            >
                <CartIcon className="w-6 h-6" />
                {cartItems.length > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                    </span>
                )}
            </button>
        );
    }

    return (
        <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-50 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Shopping Cart</h2>
                <button
                    onClick={() => setIsCartOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                >
                    <X className="w-5 h-5 text-gray-500" />
                </button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-4">
                {cartItems.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-gray-500">
                        <CartIcon className="w-16 h-16 mb-4 text-gray-300" />
                        <p>Your cart is empty</p>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {cartItems.map((item) => (
                            <div key={item.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <img
                                    src={item.image}
                                    alt={item.name}
                                    className="w-16 h-16 object-cover rounded-lg"
                                />
                                <div className="flex-1">
                                    <h3 className="text-sm font-medium text-gray-900">{item.name}</h3>
                                    <p className="text-sm text-gray-500">${item.price}</p>

                                    <div className="flex items-center space-x-2 mt-2">
                                        <button
                                            onClick={() => updateQuantity(item.id, -1)}
                                            className="p-1 hover:bg-gray-200 rounded transition-colors duration-150"
                                        >
                                            <Minus className="w-3 h-3 text-gray-600" />
                                        </button>
                                        <span className="text-sm font-medium">{item.quantity}</span>
                                        <button
                                            onClick={() => updateQuantity(item.id, 1)}
                                            className="p-1 hover:bg-gray-200 rounded transition-colors duration-150"
                                        >
                                            <Plus className="w-3 h-3 text-gray-600" />
                                        </button>
                                        <button
                                            onClick={() => removeFromCart(item.id)}
                                            className="p-1 hover:bg-red-100 rounded transition-colors duration-150 ml-2"
                                        >
                                            <Trash2 className="w-3 h-3 text-red-500" />
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Footer */}
            {cartItems.length > 0 && (
                <div className="border-t p-4 space-y-4">
                    <div className="flex justify-between items-center">
                        <span className="text-lg font-semibold text-gray-900">Total:</span>
                        <span className="text-lg font-bold text-green-800">${total.toFixed(2)}</span>
                    </div>
                    <button className="w-1/2 btn text-white py-2 rounded-lg font-medium  transition-colors duration-200">
                        Start Order
                    </button>
                </div>
            )}
        </div>
    );
};

export default ShoppingCart;