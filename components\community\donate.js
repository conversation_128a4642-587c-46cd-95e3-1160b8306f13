/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react/no-unescaped-entities */

import React, { useState } from 'react';
import { Heart, CreditCard, User, Mail, DollarSign, Shield, CheckCircle } from 'lucide-react';
import { visaLogo, mastercardLogo } from '../../public/images';

export default function DonateForm() {
  const [selectedAmount, setSelectedAmount] = useState(25);
  const [customAmount, setCustomAmount] = useState('');
  const [donationType, setDonationType] = useState('one-time');
  const [formData, setFormData] = useState({
    amount: '',
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [paymentMethod] = useState('card');
  const [errors, setErrors] = useState({});

  const presetAmounts = [10, 25, 50, 100, 250, 500];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmount = (e) => {
    const value = e.target.value;
    setCustomAmount(value);
    if (value) {
      setSelectedAmount(parseFloat(value) || 0);
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.cardNumber.trim()) newErrors.cardNumber = 'Card number is required';
    if (!formData.cardHolder.trim()) newErrors.cardHolder = 'Card holder name is required';
    if (!formData.expiryDate.trim()) newErrors.expiryDate = 'Expiry date is required';
    if (!formData.cvv.trim()) newErrors.cvv = 'CVV is required';
    if (!formData.zipCode.trim()) newErrors.zipCode = 'ZIP code is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsComplete(true);
  };

  if (isComplete) {
    return (
      <div className="max-w-md w-full mx-auto bg-[var(--secondary-background)] p-4 sm:p-6 md:p-8 shadow-2xl border border-[var(--light-green)] my-6 md:my-10 rounded-xl">
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--btn)] rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold text-[var(--text)] mb-2">Thank You!</h2>
          <p className="text-[var(--text)] mb-4">
            Your ${selectedAmount} donation has been processed successfully.
          </p>
          <p className="text-sm text-[var(--text)]">
            You'll receive a confirmation email shortly.
          </p>
          <button
            onClick={() => {
              setIsComplete(false);
              setFormData({
                firstName: '',
                lastName: '',
                email: '',
                cardNumber: '',
                cardHolder: '',
                expiryDate: '',
                cvv: '',
                zipCode: ''
              });
            }}
            className="btn mt-6 px-6 py-2 text-white rounded-full transition-colors"
          >
            Make Another Donation
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-lg mx-auto bg-[var(--secondary-background)] overflow-hidden border border-gray-100 rounded-xl">
      {/* Header */}
      <div className="bg-[var(--btn)] p-4 sm:p-6 text-[var(--white)] relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-center mb-3">
            <div className="w-12 h-12 bg-[var(--accent)] bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Heart className="w-6 h-6 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-center">Make a Donation</h1>
          <p className="text-center text-white text-opacity-90 text-sm mt-1">
            Help us make a difference together
          </p>
        </div>
      </div>

      <div className="p-4 sm:p-6 md:p-8 space-y-6 sm:space-y-7 md:space-y-8">
        {/* Donation Type */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2 sm:mb-3">Donation Type</label>
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            {['one-time', 'monthly'].map((type) => (
              <button
                key={type}
                type="button"
                onClick={() => setDonationType(type)}
                className={`bg-[var(--primary-background)] p-3 rounded-xl border-2 transition-all duration-200 font-medium ${donationType === type
                  ? 'border-[var(--light-green)]  text-[var(--btn)]'
                  : 'border-gray-200 hover:border-gray-300 text-[var(--text)]'
                  }`}
              >
                {type === 'one-time' ? 'One-time' : 'Monthly'}
              </button>
            ))}
          </div>
        </div>

        {/* Amount Selection */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2 sm:mb-3">Select Amount</label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mb-2 sm:mb-3">
            {presetAmounts.map((amount) => (
              <button
                key={amount}
                type="button"
                onClick={() => handleAmountSelect(amount)}
                className={`bg-[var(--primary-background)] p-3 rounded-xl border-2 transition-all duration-200 font-medium ${selectedAmount === amount && !customAmount
                  ? 'border-[var(--light-green)] bg-[var(--card-bg)] text-[var(--btn)] scale-105'
                  : 'border-gray-200 hover:border-gray-300 text-[var(--text)] hover:scale-102'
                  }`}
              >
                ${amount}
              </button>
            ))}
          </div>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              placeholder="Custom amount"
              value={customAmount}
              onChange={handleCustomAmount}
              className="w-full pl-10 pr-4 bg-[var(--primary-background)] py-3 border-2 border-gray-200 rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors"
            />
          </div>
        </div>

        {/* Payment Method Section */}
        <div className="border-t border-gray-200 pt-4 sm:pt-6">
          <label className="block text-sm font-semibold text-gray-700 mb-3">Payment Method</label>
          <div className="p-4 rounded-xl border-2 border-[var(--light-green)] bg-[var(--card-bg)] flex items-center justify-center">
            <div className="flex items-center space-x-2">
              <img src={visaLogo} alt="Visa" className="h-6" />
              <img src={mastercardLogo} alt="Mastercard" className="h-6" />
            </div>
            <span className="ml-2">Credit Card</span>
          </div>
        </div>

        {/* Personal Information */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2 sm:mb-3">Personal Information</label>
          <div className="space-y-2 sm:space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  name="firstName"
                  placeholder="First name"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  className={`bg-[var(--primary-background)] w-full pl-10 pr-4 py-3 border-2 ${
                    errors.firstName ? 'border-red-500' : 'border-gray-200'
                  } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
                />
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
                )}
              </div>
              <input
                type="text"
                name="lastName"
                placeholder="Last name"
                value={formData.lastName}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full px-4 py-3 border-2 ${
                  errors.lastName ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
              )}
            </div>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="email"
                name="email"
                placeholder="Email address"
                value={formData.email}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full pl-10 pr-4 py-3 border-2 ${
                  errors.email ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>
          </div>
        </div>

        {/* Card Payment Information */}
        <div>
          <label className="block text-sm font-semibold text-gray-700 mb-2 sm:mb-3">Card Details</label>
          <div className="space-y-2 sm:space-y-3">
            <div className="relative">
              <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                name="cardNumber"
                placeholder="Card number"
                value={formData.cardNumber}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full pl-10 pr-24 py-3 border-2 ${
                  errors.cardNumber ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.cardNumber && (
                <p className="text-red-500 text-xs mt-1">{errors.cardNumber}</p>
              )}
            </div>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                name="cardHolder"
                placeholder="Card holder name"
                value={formData.cardHolder}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full pl-10 pr-4 py-3 border-2 ${
                  errors.cardHolder ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.cardHolder && (
                <p className="text-red-500 text-xs mt-1">{errors.cardHolder}</p>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
              <input
                type="text"
                name="expiryDate"
                placeholder="MM/YY"
                value={formData.expiryDate}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full px-4 py-3 border-2 ${
                  errors.expiryDate ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.expiryDate && (
                <p className="text-red-500 text-xs mt-1">{errors.expiryDate}</p>
              )}
              <input
                type="text"
                name="cvv"
                placeholder="CVV"
                value={formData.cvv}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full px-4 py-3 border-2 ${
                  errors.cvv ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.cvv && (
                <p className="text-red-500 text-xs mt-1">{errors.cvv}</p>
              )}
              <input
                type="text"
                name="zipCode"
                placeholder="ZIP"
                value={formData.zipCode}
                onChange={handleInputChange}
                required
                className={`bg-[var(--primary-background)] w-full px-4 py-3 border-2 ${
                  errors.zipCode ? 'border-red-500' : 'border-gray-200'
                } rounded-xl focus:border-[var(--btn)] focus:outline-none transition-colors`}
              />
              {errors.zipCode && (
                <p className="text-red-500 text-xs mt-1">{errors.zipCode}</p>
              )}
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-1 sm:space-y-0 sm:space-x-2 p-2 sm:p-3 bg-green-50 rounded-xl border border-green-200">
          <Shield className="w-4 h-4 text-green-600 flex-shrink-0" />
          <p className="text-xs text-green-700">
            Your payment is secured with 256-bit SSL encryption
          </p>
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || !selectedAmount}
          className={`btn w-full sm:w-1/2 py-3 rounded-xl font-bold text-lg transition-all duration-300 ${
            isSubmitting || !selectedAmount
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-[var(--white)] shadow-lg hover:shadow-xl'
          }`}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Processing...</span>
            </div>
          ) : (
            `Donate $${selectedAmount || 0} ${donationType === 'monthly' ? '/month' : ''}`
          )}
        </button>

        {/* Terms */}
        <p className="text-xs text-gray-500 text-center">
          By donating, you agree to our terms and conditions. Your donation is tax-deductible.
        </p>
      </div>
    </div>
  );
}