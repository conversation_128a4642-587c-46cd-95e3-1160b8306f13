"use client";
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import TourCard from '../cards/packageCard';
import { SkeletonGrid } from '../skeleton';

const PackageSection = () => {
    const [packages, setPackages] = useState([]);
    const [loading, setLoading] = useState(true);



    // Fetch packages from API
    useEffect(() => {
        const fetchPackages = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/packages?limit=6');
                const result = await response.json();

                if (result.success) {
                    console.log('PackageSection - Fetched packages:', result.data.length, 'packages');
                    console.log('PackageSection - Full result:', result);
                    console.log('PackageSection - First package:', result.data[0]);
                    setPackages(result.data);
                } else {
                    console.error('Failed to fetch packages:', result.error);
                    console.error('PackageSection - Full error result:', result);
                    setPackages([]);
                }
            } catch (error) {
                console.error('Error fetching packages:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchPackages();
    }, []);

    // Debug: Log when packages state changes
    useEffect(() => {
        console.log('PackageSection - Packages state updated:', packages.length, packages);
    }, [packages]);

    // Always select the first 6 packages for SSR/CSR consistency
    const packagesToShow = packages.slice(0, 6);

    // Debug: Log packages to show
    console.log('PackageSection - Packages state:', packages.length, packages);
    console.log('PackageSection - Packages to show:', packagesToShow.length, packagesToShow);

    return (
        <div className="px-4 sm:px-6 lg:px-8 py-12 bg-[var(--primary-background)]">
            <div className="mb-12 text-center">
                <h2 className="text-3xl font-bold mb-2">Popular Multi-Day Tour packages</h2>
                <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
            </div>

            {loading ? (
                <SkeletonGrid
                    variant="package"
                    count={6}
                    cardProps={{ showPrice: true, showButton: true }}
                />
            ) : packagesToShow.length === 0 ? (
                <div className="flex justify-center items-center min-h-[400px]">
                    <div className="text-lg text-gray-600">No packages available at the moment.</div>
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
                    {packagesToShow.map((tour, index) => (
                        <TourCard
                            key={tour.id || index}
                            tour={tour}
                            className="w-full h-full"
                        />
                    ))}
                </div>
            )}

            {/* View More button */}
            <div className="mt-12 flex justify-center">
                <Link 
                    href="/package"
                    className="btn px-8 py-2 text-white rounded-lg transition-colors duration-200 hover:opacity-90"
                >
                    View More
                </Link>
            </div>
        </div>
    );
};

export default PackageSection;
