'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>3,
  Pie<PERSON><PERSON>,
  TrendingUp,
  DollarSign,
  Calendar,
  MapPin,
  Download,
  RefreshCw,
  Eye,
  Target,
  Activity,
  Globe,
  Award,
  Clock,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

interface ReportData {
  revenue: {
    total: number;
    monthly: number;
    growth: number;
  };
  bookings: {
    total: number;
    confirmed: number;
    pending: number;
    cancelled: number;
  };
  destinations: {
    name: string;
    bookings: number;
    revenue: number;
    percentage: number;
  }[];
  performance: {
    month: string;
    bookings: number;
    revenue: number;
    reviews: number;
  }[];
}

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('30');
  const [selectedReport, setSelectedReport] = useState('overview');
  const [loading, setLoading] = useState(false);

  // Mock data - replace with real API calls
  const [reportData] = useState<ReportData>({
    revenue: {
      total: 125000,
      monthly: 45000,
      growth: 12.5
    },
    bookings: {
      total: 342,
      confirmed: 298,
      pending: 32,
      cancelled: 12
    },
    destinations: [
      { name: 'Queen Elizabeth NP', bookings: 89, revenue: 45000, percentage: 35 },
      { name: 'Murchison Falls', bookings: 67, revenue: 32000, percentage: 26 },
      { name: 'Bwindi Forest', bookings: 54, revenue: 28000, percentage: 21 },
      { name: 'Lake Bunyonyi', bookings: 45, revenue: 20000, percentage: 18 }
    ],
    performance: [
      { month: 'Jan', bookings: 45, revenue: 18000, reviews: 23 },
      { month: 'Feb', bookings: 52, revenue: 21000, reviews: 28 },
      { month: 'Mar', bookings: 48, revenue: 19500, reviews: 25 },
      { month: 'Apr', bookings: 61, revenue: 24500, reviews: 32 },
      { month: 'May', bookings: 58, revenue: 23000, reviews: 29 },
      { month: 'Jun', bookings: 67, revenue: 27000, reviews: 35 }
    ]
  });

  const handleRefresh = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLoading(false);
  };

  const handleExport = (type: string) => {
    console.log(`Exporting ${type} report`);
  };

  const StatCard = ({ title, value, change, changeType, icon, color }: {
    title: string;
    value: string | number;
    change: string;
    changeType: 'increase' | 'decrease';
    icon: React.ReactNode;
    color: string;
  }) => (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${color} text-white`}>
          {icon}
        </div>
        <div className="text-right">
          <div className="flex items-center">
            {changeType === 'increase' ? (
              <ArrowUpRight className="w-4 h-4 text-green-500 mr-1" />
            ) : (
              <ArrowDownRight className="w-4 h-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-semibold ${changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`}>
              {change}
            </span>
          </div>
        </div>
      </div>
      <h3 className="text-sm font-medium text-gray-600 mb-1">{title}</h3>
      <p className="text-2xl font-bold text-gray-900">{value}</p>
    </div>
  );

  return (
    <main className="ml-16 pt-16 min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      <div className="p-6">
        {/* Header */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl text-white mr-4">
                  <BarChart3 className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-purple-600 bg-clip-text text-transparent">
                    Analytics & Reports
                  </h1>
                  <p className="text-gray-600 mt-2 text-lg">
                    Comprehensive insights into your business performance
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white"
              >
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
              </select>
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-300 font-medium disabled:opacity-50"
              >
                {loading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Report Type Selector */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 mb-8">
          <div className="flex flex-wrap gap-4">
            {[
              { id: 'overview', label: 'Overview', icon: <BarChart3 className="w-5 h-5" /> },
              { id: 'revenue', label: 'Revenue', icon: <DollarSign className="w-5 h-5" /> },
              { id: 'bookings', label: 'Bookings', icon: <Calendar className="w-5 h-5" /> },
              { id: 'destinations', label: 'Destinations', icon: <MapPin className="w-5 h-5" /> },
              { id: 'performance', label: 'Performance', icon: <TrendingUp className="w-5 h-5" /> }
            ].map((report) => (
              <button
                key={report.id}
                onClick={() => setSelectedReport(report.id)}
                className={`flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-300 ${
                  selectedReport === report.id
                    ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {report.icon}
                <span className="ml-2">{report.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Revenue"
            value={`$${reportData.revenue.total.toLocaleString()}`}
            change="+12.5%"
            changeType="increase"
            icon={<DollarSign className="w-6 h-6" />}
            color="bg-emerald-500"
          />
          <StatCard
            title="Total Bookings"
            value={reportData.bookings.total}
            change="+8.2%"
            changeType="increase"
            icon={<Calendar className="w-6 h-6" />}
            color="bg-blue-500"
          />
          <StatCard
            title="Confirmed Bookings"
            value={reportData.bookings.confirmed}
            change="+15.3%"
            changeType="increase"
            icon={<Target className="w-6 h-6" />}
            color="bg-green-500"
          />
          <StatCard
            title="Conversion Rate"
            value="87.1%"
            change="+2.1%"
            changeType="increase"
            icon={<TrendingUp className="w-6 h-6" />}
            color="bg-purple-500"
          />
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          {/* Performance Chart */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg text-white mr-3">
                  <TrendingUp className="w-5 h-5" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Performance Trend</h2>
              </div>
              <button
                onClick={() => handleExport('performance')}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </button>
            </div>
            <div className="space-y-4">
              {reportData.performance.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold mr-4">
                      {item.month}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{item.bookings} Bookings</p>
                      <p className="text-sm text-gray-500">${item.revenue.toLocaleString()} Revenue</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{item.reviews} Reviews</p>
                    <div className="w-24 bg-gray-200 rounded-full h-2 mt-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full"
                        style={{ width: `${(item.bookings / 70) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Destinations */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg text-white mr-3">
                  <MapPin className="w-5 h-5" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Top Destinations</h2>
              </div>
              <button
                onClick={() => handleExport('destinations')}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </button>
            </div>
            <div className="space-y-4">
              {reportData.destinations.map((destination, index) => (
                <div key={index} className="group p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center text-white font-bold mr-3">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-semibold text-gray-900">{destination.name}</p>
                        <p className="text-sm text-gray-500">{destination.bookings} bookings</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">${destination.revenue.toLocaleString()}</p>
                      <p className="text-xs text-gray-500">{destination.percentage}% of total</p>
                    </div>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-emerald-500 to-emerald-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${destination.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Additional Reports */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
          {/* Booking Status */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg text-white mr-3">
                <Calendar className="w-5 h-5" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Booking Status</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <span className="font-medium text-gray-900">Confirmed</span>
                </div>
                <span className="font-bold text-gray-900">{reportData.bookings.confirmed}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                  <span className="font-medium text-gray-900">Pending</span>
                </div>
                <span className="font-bold text-gray-900">{reportData.bookings.pending}</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                  <span className="font-medium text-gray-900">Cancelled</span>
                </div>
                <span className="font-bold text-gray-900">{reportData.bookings.cancelled}</span>
              </div>
            </div>
          </div>

          {/* Revenue Breakdown */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg text-white mr-3">
                <PieChart className="w-5 h-5" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Revenue Breakdown</h2>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Total Revenue</span>
                <span className="font-bold text-gray-900">${reportData.revenue.total.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Monthly Revenue</span>
                <span className="font-bold text-gray-900">${reportData.revenue.monthly.toLocaleString()}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Growth Rate</span>
                <span className="font-bold text-green-600">+{reportData.revenue.growth}%</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg text-white mr-3">
                <Activity className="w-5 h-5" />
              </div>
              <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
            </div>
            <div className="space-y-3">
              <button className="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium flex items-center justify-center">
                <Download className="w-4 h-4 mr-2" />
                Export All Reports
              </button>
              <button className="w-full px-4 py-3 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white rounded-lg hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 font-medium flex items-center justify-center">
                <Eye className="w-4 h-4 mr-2" />
                View Detailed Analytics
              </button>
              <button className="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-300 font-medium flex items-center justify-center">
                <Globe className="w-4 h-4 mr-2" />
                Generate Insights
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <p className="text-sm text-gray-500">
              Reports generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
            </p>
            <div className="flex items-center justify-center mt-2 space-x-4">
              <div className="flex items-center text-xs text-gray-400">
                <Award className="w-3 h-3 mr-1" />
                <span>Real-time data</span>
              </div>
              <div className="flex items-center text-xs text-gray-400">
                <Clock className="w-3 h-3 mr-1" />
                <span>Updated every 5 minutes</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
