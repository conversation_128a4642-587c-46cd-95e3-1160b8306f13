'use client';

import { useState } from 'react';

export default function FixImagesPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [checkResult, setCheckResult] = useState<any>(null);

  const checkBlobUrls = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/check-blob-urls');
      const data = await response.json();
      setCheckResult(data);
    } catch (error) {
      console.error('Error checking blob URLs:', error);
      setCheckResult({ error: 'Failed to check blob URLs' });
    } finally {
      setLoading(false);
    }
  };

  const fixBlobUrls = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/debug/fix-blob-urls', {
        method: 'POST'
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('Error fixing blob URLs:', error);
      setResult({ error: 'Failed to fix blob URLs' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Debug: Fix Package Images</h1>
      
      <div className="space-y-6">
        {/* Check Blob URLs */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">1. Check for Blob URLs</h2>
          <button
            onClick={checkBlobUrls}
            disabled={loading}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Checking...' : 'Check Blob URLs'}
          </button>
          
          {checkResult && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(checkResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Fix Blob URLs */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">2. Fix Blob URLs</h2>
          <p className="text-gray-600 mb-4">
            This will replace all blob URLs with Supabase fallback images.
          </p>
          <button
            onClick={fixBlobUrls}
            disabled={loading}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50"
          >
            {loading ? 'Fixing...' : 'Fix Blob URLs'}
          </button>
          
          {result && (
            <div className="mt-4 p-4 bg-gray-100 rounded">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Test Fallback Images */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">3. Test Fallback Images</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[
              { category: 'wildlife', name: 'Wildlife Safari' },
              { category: 'adventure', name: 'Adventure Safari' },
              { category: 'cultural', name: 'Cultural Safari' },
              { category: 'nature', name: 'Nature Safari' },
              { category: 'luxury', name: 'Luxury Safari' },
              { category: 'default', name: 'Default Safari' }
            ].map((item) => {
              const baseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
              const imagePath = item.category === 'default' 
                ? 'fallback/safari-default.webp'
                : `fallback/${item.category}-safari.webp`;
              const imageUrl = `${baseUrl}/storage/v1/object/public/sas-package-images/${imagePath}`;
              
              return (
                <div key={item.category} className="text-center">
                  <img
                    src={imageUrl}
                    alt={item.name}
                    className="w-full h-32 object-cover rounded mb-2"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling!.style.display = 'block';
                    }}
                  />
                  <div style={{ display: 'none' }} className="w-full h-32 bg-red-100 rounded mb-2 flex items-center justify-center text-red-500">
                    Failed to load
                  </div>
                  <p className="text-sm font-medium">{item.name}</p>
                  <p className="text-xs text-gray-500 break-all">{imageUrl}</p>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
