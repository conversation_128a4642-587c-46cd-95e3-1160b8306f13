import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      metric, 
      value, 
      severity, 
      url, 
      timestamp,
      session_id 
    } = body

    // Validate required fields
    if (!metric || value === undefined || !severity || !url) {
      return NextResponse.json(
        { error: 'Missing required fields: metric, value, severity, url' },
        { status: 400 }
      )
    }

    // Store real-time alert in Supabase
    const { error } = await supabase
      .from('performance_alerts')
      .insert({
        metric,
        value: parseFloat(value),
        severity,
        url,
        session_id,
        timestamp: timestamp || new Date().toISOString(),
        user_agent: request.headers.get('user-agent'),
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
      })

    if (error) {
      console.error('Error storing performance alert:', error)
      return NextResponse.json(
        { error: 'Failed to store performance alert' },
        { status: 500 }
      )
    }

    // Check if we need to send notifications for critical alerts
    if (severity === 'critical') {
      await sendCriticalAlertNotification(metric, value, url)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Performance alerts API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const severity = searchParams.get('severity')
    const metric = searchParams.get('metric')
    const hours = parseInt(searchParams.get('hours') || '24')

    let query = supabase
      .from('performance_alerts')
      .select('*')
      .gte('timestamp', new Date(Date.now() - hours * 60 * 60 * 1000).toISOString())
      .order('timestamp', { ascending: false })

    if (severity) {
      query = query.eq('severity', severity)
    }

    if (metric) {
      query = query.eq('metric', metric)
    }

    const { data, error } = await query.limit(500)

    if (error) {
      console.error('Error fetching performance alerts:', error)
      return NextResponse.json(
        { error: 'Failed to fetch performance alerts' },
        { status: 500 }
      )
    }

    // Group alerts by metric and severity
    const alertSummary = data?.reduce((acc: any, alert: any) => {
      const key = `${alert.metric}_${alert.severity}`
      if (!acc[key]) {
        acc[key] = {
          metric: alert.metric,
          severity: alert.severity,
          count: 0,
          latest_value: 0,
          latest_timestamp: alert.timestamp,
          urls: new Set()
        }
      }
      
      acc[key].count++
      acc[key].urls.add(alert.url)
      
      if (new Date(alert.timestamp) > new Date(acc[key].latest_timestamp)) {
        acc[key].latest_value = alert.value
        acc[key].latest_timestamp = alert.timestamp
      }
      
      return acc
    }, {})

    // Convert sets to arrays for JSON serialization
    const summary = Object.values(alertSummary || {}).map((item: any) => ({
      ...item,
      urls: Array.from(item.urls),
      affected_pages: item.urls.size
    }))

    return NextResponse.json({
      alerts: data || [],
      summary,
      period_hours: hours,
      total_alerts: data?.length || 0
    })

  } catch (error) {
    console.error('Performance alerts GET error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function sendCriticalAlertNotification(metric: string, value: number, url: string) {
  try {
    // Here you could integrate with notification services like:
    // - Email notifications
    // - Slack webhooks
    // - Discord webhooks
    // - SMS alerts
    // - Push notifications
    
    console.log(`🚨 CRITICAL ALERT: ${metric} = ${value} on ${url}`)
    
    // Example: Send to a webhook (uncomment and configure as needed)
    /*
    await fetch(process.env.ALERT_WEBHOOK_URL!, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text: `🚨 Critical Performance Alert: ${metric} = ${value}ms on ${url}`,
        severity: 'critical',
        metric,
        value,
        url,
        timestamp: new Date().toISOString()
      })
    })
    */
    
  } catch (error) {
    console.error('Failed to send critical alert notification:', error)
  }
}

// DELETE endpoint to clear old alerts
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '30')

    const { error } = await supabase
      .from('performance_alerts')
      .delete()
      .lt('timestamp', new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString())

    if (error) {
      console.error('Error deleting old alerts:', error)
      return NextResponse.json(
        { error: 'Failed to delete old alerts' },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      message: `Deleted alerts older than ${days} days` 
    })

  } catch (error) {
    console.error('Performance alerts DELETE error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
