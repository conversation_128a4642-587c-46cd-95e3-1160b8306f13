import React from 'react';
import ImageSlider from '../journey/imageSlider';
import Button from './Button';

const HoneymoonJourney = () => {
  const honeymoonImages = [
    {
      src: '/images/journey/Honey-moon-Rwanda-mountain-gorilla-trekking.webp',
      alt: 'The lovers enjoying their honey moon at Zanzibar beautiful beach'
    },
    {
      src: '/images/journey/Honey-moon-Rwanda-nature.webp',
      alt: 'The lovers enjoying their honey moon in the nature of pyrethrum'
    }
  ];

  return (
    <section className="flex flex-col md:flex-row-reverse items-center bg-[var(--primary-background)] px-6 py-16 md:px-16">
      <div className="flex-1 px-0 md:px-10 order-1 md:order-none lg:text-left md:text-left lg:pr-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-5 text-gray-900 leading-tight text-center md:text-left lg:text-left">
          Honeymoon Journey: Begin Forever in The Heart of Africa
        </h2>
        <div className="text-base leading-relaxed text-gray-700 mb-8 space-y-4 text-justify md:text-left max-w-2xl lg:text-left">
          <p>
            There&apos;s no better way to begin your new chapter than with a journey that feels as extraordinary as your
            love. Africa offers a honeymoon like no other where romance is written into the landscapes, and every
            moment feels like a memory in the making.
          </p>
          <p>
            Imagine waking up in a luxury tented camp as the sun rises over the savannah,
            dining under the stars with the sounds of the wild in the distance, or walking hand in hand through ancient forests and coastal
            hideaways. Whether you dream of serene seclusion or thrilling adventure, Africa&apos;s magic sets the perfect
            tone for intimacy and discovery.
          </p>
        </div>
        
        {/* Button for desktop */}
        <Button variant="desktop">Learn More</Button>
      </div>

      <div className="flex-1 order-2 md:order-none mt-8 md:mt-0 w-full">
        <ImageSlider images={honeymoonImages} aspectRatio="portrait" />
        
        {/* Button for mobile and tablet */}
        <Button variant="mobile">Learn More</Button>
      </div>
    </section>
  );
};

export default HoneymoonJourney;