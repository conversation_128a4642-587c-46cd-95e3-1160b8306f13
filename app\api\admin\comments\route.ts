import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// GET - Fetch all comments for admin management
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || 'all';
    const search = searchParams.get('search') || '';
    const blogPostId = searchParams.get('blog_post_id') || '';

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Build query
    let query = supabaseAdmin
      .from('sas_blog_comments')
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        status,
        likes_count,
        created_at,
        updated_at,
        parent_comment_id,
        blog_post_id,
        sas_blog_posts!inner(
          title,
          slug
        )
      `)
      .is('deleted_at', null)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`author_name.ilike.%${search}%,content.ilike.%${search}%,author_email.ilike.%${search}%`);
    }

    if (blogPostId) {
      query = query.eq('blog_post_id', blogPostId);
    }

    // Get total count for pagination
    let countQuery = supabaseAdmin
      .from('sas_blog_comments')
      .select('*', { count: 'exact', head: true })
      .is('deleted_at', null);

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (search) {
      countQuery = countQuery.or(`author_name.ilike.%${search}%,content.ilike.%${search}%,author_email.ilike.%${search}%`);
    }

    if (blogPostId) {
      countQuery = countQuery.eq('blog_post_id', blogPostId);
    }

    const { count } = await countQuery;

    // Apply pagination
    const { data: comments, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch comments' },
        { status: 500 }
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        comments: comments || [],
        pagination: {
          currentPage: page,
          totalPages,
          totalComments: count || 0,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create admin reply to a comment
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['parent_comment_id', 'content'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get parent comment and blog post info
    const { data: parentComment, error: parentError } = await supabaseAdmin
      .from('sas_blog_comments')
      .select('id, blog_post_id')
      .eq('id', body.parent_comment_id)
      .is('deleted_at', null)
      .single();

    if (parentError || !parentComment) {
      return NextResponse.json(
        { success: false, error: 'Parent comment not found' },
        { status: 404 }
      );
    }

    // Prepare admin reply data
    const replyData = {
      blog_post_id: parentComment.blog_post_id,
      parent_comment_id: body.parent_comment_id,
      author_name: 'Swift Africa Safaris',
      author_email: '<EMAIL>',
      content: body.content.trim(),
      is_admin_reply: true,
      status: 'approved'
    };

    // Insert admin reply
    const { data: newReply, error: insertError } = await supabaseAdmin
      .from('sas_blog_comments')
      .insert(replyData)
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        status,
        likes_count,
        created_at,
        parent_comment_id,
        blog_post_id
      `)
      .single();

    if (insertError) {
      return NextResponse.json(
        { success: false, error: `Failed to create admin reply: ${insertError.message}` },
        { status: 500 }
      );
    }
    const response = NextResponse.json({
      success: true,
      data: newReply,
      message: 'Admin reply created successfully'
    });

    // Add cache-busting headers
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('Admin comment reply error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
