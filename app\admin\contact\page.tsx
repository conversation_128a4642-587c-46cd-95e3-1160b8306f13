/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useRef } from "react";
import { Phone, Save, Upload, MapPin, Mail, Clock } from "lucide-react";

interface ContactPageFormData {
  pageTitle: string;
  pageSubtitle: string;
  pageDescription: string;
  headerImage: string;
  officeAddress: string;
  phoneNumber: string;
  emailAddress: string;
  whatsappNumber: string;
  businessHours: string;
  mapEmbedCode: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function ContactPageManagement() {
  const [activeTab, setActiveTab] = useState("content");
  const [formData, setFormData] = useState<ContactPageFormData>({
    pageTitle: "Contact Us",
    pageSubtitle: "Get in Touch with Our Safari Experts",
    pageDescription: "Ready to plan your African adventure? Contact our experienced team for personalized safari planning and expert advice.",
    headerImage: "",
    officeAddress: "Arusha, Tanzania, East Africa",
    phoneNumber: "+*********** 789",
    emailAddress: "<EMAIL>",
    whatsappNumber: "+*********** 789",
    businessHours: "Monday - Friday: 8:00 AM - 6:00 PM EAT\nSaturday: 9:00 AM - 4:00 PM EAT\nSunday: Closed",
    mapEmbedCode: "",
    seoTitle: "Contact Swift Africa Safaris - Plan Your Safari Adventure",
    seoDescription: "Contact our safari experts to plan your perfect African adventure. Get personalized advice, quotes, and support for your Tanzania safari experience.",
    seoKeywords: "contact safari company, Tanzania safari contact, safari planning, Africa travel contact"
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof ContactPageFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you would upload to a server
      const imageUrl = URL.createObjectURL(file);
      setFormData((prev) => ({ ...prev, headerImage: imageUrl }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.pageTitle || formData.pageTitle.length < 3) {
      newErrors.pageTitle = "Please enter a valid page title (3+ characters)";
    }

    if (!formData.pageSubtitle || formData.pageSubtitle.length < 5) {
      newErrors.pageSubtitle = "Please enter a valid subtitle (5+ characters)";
    }

    if (!formData.pageDescription || formData.pageDescription.length < 20) {
      newErrors.pageDescription = "Please enter a valid description (20+ characters)";
    }

    if (!formData.officeAddress || formData.officeAddress.length < 10) {
      newErrors.officeAddress = "Please enter a valid office address";
    }

    if (!formData.phoneNumber) {
      newErrors.phoneNumber = "Please enter a phone number";
    }

    if (!formData.emailAddress || !/\S+@\S+\.\S+/.test(formData.emailAddress)) {
      newErrors.emailAddress = "Please enter a valid email address";
    }

    if (!formData.seoTitle || formData.seoTitle.length < 10) {
      newErrors.seoTitle = "Please enter a valid SEO title (10+ characters)";
    }

    if (!formData.seoDescription || formData.seoDescription.length < 50) {
      newErrors.seoDescription = "Please enter a valid SEO description (50+ characters)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Form submitted successfully:", formData);
      alert("Contact page settings saved successfully!");
    }
  };

  const tabs = [
    { id: "content", label: "Page Content" },
    { id: "contact", label: "Contact Info" },
    { id: "seo", label: "SEO Settings" },
  ];

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Phone className="mr-3 text-green-600" />
                Contact Page Management
              </h1>
              <p className="text-gray-600 mt-1">Manage your contact page content and information</p>
            </div>
            <button
              type="submit"
              form="contact-form"
              className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? "border-[var(--accent)] text-[var(--accent)]"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <form onSubmit={handleSubmit} className="p-6">
                {/* Page Content Tab */}
                {activeTab === "content" && (
                  <div className="space-y-6">
                    {/* Preview Section */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Contact Page Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-6 bg-gray-50">
                        <div className="text-center space-y-4">
                          {formData.headerImage && (
                            <img
                              src={formData.headerImage}
                              alt="Contact page header"
                              className="w-full h-32 object-cover rounded-lg mb-4"
                            />
                          )}
                          <h1 className="text-2xl font-bold text-gray-900">
                            {formData.pageTitle || "Your Contact Page Title"}
                          </h1>
                          <h2 className="text-lg text-gray-700">
                            {formData.pageSubtitle || "Your Contact Page Subtitle"}
                          </h2>
                          <p className="text-gray-600 max-w-2xl mx-auto">
                            {formData.pageDescription || "Your contact page description will appear here"}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Page Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Title *
                      </label>
                      <input
                        type="text"
                        value={formData.pageTitle}
                        onChange={(e) => handleInputChange("pageTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your contact page title"
                      />
                      {errors.pageTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageTitle}</p>
                      )}
                    </div>

                    {/* Page Subtitle */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Subtitle *
                      </label>
                      <input
                        type="text"
                        value={formData.pageSubtitle}
                        onChange={(e) => handleInputChange("pageSubtitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageSubtitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your contact page subtitle"
                      />
                      {errors.pageSubtitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageSubtitle}</p>
                      )}
                    </div>

                    {/* Page Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Page Description *
                      </label>
                      <textarea
                        value={formData.pageDescription}
                        onChange={(e) => handleInputChange("pageDescription", e.target.value)}
                        rows={4}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.pageDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your contact page description"
                      />
                      {errors.pageDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.pageDescription}</p>
                      )}
                    </div>

                    {/* Header Image Upload */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Header Image
                      </label>
                      <div
                        onClick={handleFileSelect}
                        className="relative border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-[var(--accent)] transition-colors cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div className="text-center">
                          {formData.headerImage ? (
                            <div className="space-y-2">
                              <img
                                src={formData.headerImage}
                                alt="Header Image Preview"
                                className="mx-auto h-32 w-auto object-cover rounded-lg"
                              />
                              <p className="text-sm text-gray-600">
                                Click to change image
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto h-12 w-12 text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">
                                  Click to upload header image
                                </p>
                                <p className="text-xs text-gray-500">
                                  PNG, JPG, GIF up to 10MB
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Contact Info Tab */}
                {activeTab === "contact" && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <MapPin size={16} className="inline mr-1" />
                          Office Address *
                        </label>
                        <textarea
                          value={formData.officeAddress}
                          onChange={(e) => handleInputChange("officeAddress", e.target.value)}
                          rows={3}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.officeAddress ? "border-red-500" : "border-gray-300"
                          }`}
                          placeholder="Enter your office address"
                        />
                        {errors.officeAddress && (
                          <p className="mt-1 text-sm text-red-600">{errors.officeAddress}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Clock size={16} className="inline mr-1" />
                          Business Hours
                        </label>
                        <textarea
                          value={formData.businessHours}
                          onChange={(e) => handleInputChange("businessHours", e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                          placeholder="Enter your business hours"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <Phone size={16} className="inline mr-1" />
                          Phone Number *
                        </label>
                        <input
                          type="tel"
                          value={formData.phoneNumber}
                          onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.phoneNumber ? "border-red-500" : "border-gray-300"
                          }`}
                          placeholder="Enter phone number"
                        />
                        {errors.phoneNumber && (
                          <p className="mt-1 text-sm text-red-600">{errors.phoneNumber}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          WhatsApp Number
                        </label>
                        <input
                          type="tel"
                          value={formData.whatsappNumber}
                          onChange={(e) => handleInputChange("whatsappNumber", e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                          placeholder="Enter WhatsApp number"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Mail size={16} className="inline mr-1" />
                        Email Address *
                      </label>
                      <input
                        type="email"
                        value={formData.emailAddress}
                        onChange={(e) => handleInputChange("emailAddress", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.emailAddress ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter email address"
                      />
                      {errors.emailAddress && (
                        <p className="mt-1 text-sm text-red-600">{errors.emailAddress}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Google Maps Embed Code
                      </label>
                      <textarea
                        value={formData.mapEmbedCode}
                        onChange={(e) => handleInputChange("mapEmbedCode", e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Paste Google Maps embed iframe code here"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Get embed code from Google Maps by clicking &quot;Share&quot; → &quot;Embed a map&quot;
                      </p>
                    </div>
                  </div>
                )}

                {/* SEO Settings Tab */}
                {activeTab === "seo" && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Title *
                      </label>
                      <input
                        type="text"
                        value={formData.seoTitle}
                        onChange={(e) => handleInputChange("seoTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO title for contact page"
                      />
                      {errors.seoTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoTitle}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoTitle.length}/70 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Description *
                      </label>
                      <textarea
                        value={formData.seoDescription}
                        onChange={(e) => handleInputChange("seoDescription", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO description for contact page"
                      />
                      {errors.seoDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoDescription}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoDescription.length}/160 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Keywords
                      </label>
                      <input
                        type="text"
                        value={formData.seoKeywords}
                        onChange={(e) => handleInputChange("seoKeywords", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Enter keywords separated by commas"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Separate keywords with commas
                      </p>
                    </div>

                    {/* SEO Preview */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Search Engine Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="space-y-1">
                          <div className="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                            {formData.seoTitle || "Your SEO Title"}
                          </div>
                          <div className="text-green-700 text-sm">
                            https://swiftafricasafaris.com/contact
                          </div>
                          <div className="text-gray-600 text-sm">
                            {formData.seoDescription || "Your SEO description will appear here"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
