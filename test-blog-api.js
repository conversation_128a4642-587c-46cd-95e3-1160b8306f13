// Test script to check blog API functionality
const testBlogAPI = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Blog API endpoints...\n');
  
  // Test 1: GET all blog posts
  try {
    console.log('1. Testing GET /api/admin/blog');
    const response = await fetch(`${baseUrl}/api/admin/blog`);
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    console.log('✅ GET endpoint working\n');
  } catch (error) {
    console.log('❌ GET endpoint failed:', error.message);
  }
  
  // Test 2: POST create blog post
  try {
    console.log('2. Testing POST /api/admin/blog');
    const testBlogData = {
      title: 'Test Blog Post',
      description: 'This is a test blog post description',
      hero_image_url: 'https://example.com/test-image.jpg',
      hero_image_alt: 'Test image alt text',
      category: 'test',
      tags: ['test', 'api'],
      status: 'draft',
      content: [
        {
          type: 'paragraph',
          content: 'This is a test paragraph content.'
        }
      ]
    };
    
    const response = await fetch(`${baseUrl}/api/admin/blog`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBlogData)
    });
    
    const data = await response.json();
    console.log('Status:', response.status);
    console.log('Response:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ POST endpoint working');
      
      // Test 3: PUT update blog post
      const slug = data.data.slug;
      console.log('\n3. Testing PUT /api/admin/blog/' + slug);
      
      const updateData = {
        ...testBlogData,
        title: 'Updated Test Blog Post',
        description: 'This is an updated test blog post description'
      };
      
      const updateResponse = await fetch(`${baseUrl}/api/admin/blog/${slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
      
      const updateResult = await updateResponse.json();
      console.log('Status:', updateResponse.status);
      console.log('Response:', JSON.stringify(updateResult, null, 2));
      
      if (updateResponse.ok && updateResult.success) {
        console.log('✅ PUT endpoint working');
      } else {
        console.log('❌ PUT endpoint failed');
      }
      
      // Test 4: GET single blog post
      console.log('\n4. Testing GET /api/admin/blog/' + slug);
      const getResponse = await fetch(`${baseUrl}/api/admin/blog/${slug}`);
      const getResult = await getResponse.json();
      console.log('Status:', getResponse.status);
      console.log('Response:', JSON.stringify(getResult, null, 2));
      
      if (getResponse.ok && getResult.success) {
        console.log('✅ GET single post endpoint working');
      } else {
        console.log('❌ GET single post endpoint failed');
      }
      
    } else {
      console.log('❌ POST endpoint failed');
    }
  } catch (error) {
    console.log('❌ POST endpoint failed:', error.message);
  }
};

// Run the test
testBlogAPI().catch(console.error);
