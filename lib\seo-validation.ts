/**
 * SEO Validation Utilities
 * Comprehensive validation functions for all SEO implementations
 */

import { supabase } from './supabase'
import { 
  generateMetadata, 
  generateHreflangTags, 
  hreflangConfigs,
  generateOrganizationSchema,
  generateWebsiteSchema,
  generateArticleSchema,
  generateProductSchema,
  defaultSEO
} from './seo'

export interface SEOValidationResult {
  isValid: boolean
  score: number
  issues: SEOIssue[]
  recommendations: string[]
  timestamp: string
}

export interface SEOIssue {
  type: 'error' | 'warning' | 'info'
  category: 'metadata' | 'hreflang' | 'structured-data' | 'performance' | 'indexing' | 'content'
  message: string
  details?: any
  fix?: string
}

/**
 * Validate page metadata
 */
export async function validatePageMetadata(url: string, expectedData?: any): Promise<SEOValidationResult> {
  const issues: SEOIssue[] = []
  let score = 100

  try {
    // Test metadata generation
    const metadata = generateMetadata({
      title: expectedData?.title || 'Test Page',
      description: expectedData?.description || 'Test description',
      url
    })

    // Check required fields
    if (!metadata.title) {
      issues.push({
        type: 'error',
        category: 'metadata',
        message: 'Missing page title',
        fix: 'Add a descriptive title to the page metadata'
      })
      score -= 20
    }

    if (!metadata.description) {
      issues.push({
        type: 'error',
        category: 'metadata',
        message: 'Missing meta description',
        fix: 'Add a compelling meta description (150-160 characters)'
      })
      score -= 15
    }

    if (!metadata.openGraph) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Missing Open Graph tags',
        fix: 'Add Open Graph metadata for better social media sharing'
      })
      score -= 10
    }

    if (!metadata.twitter) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Missing Twitter Card tags',
        fix: 'Add Twitter Card metadata for better Twitter sharing'
      })
      score -= 5
    }

    // Check title length
    if (metadata.title && typeof metadata.title === 'string' && metadata.title.length > 60) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Title too long (over 60 characters)',
        details: { length: metadata.title.length },
        fix: 'Shorten title to under 60 characters for better search results display'
      })
      score -= 5
    }

    // Check description length
    if (metadata.description && typeof metadata.description === 'string') {
      if (metadata.description.length > 160) {
        issues.push({
          type: 'warning',
          category: 'metadata',
          message: 'Meta description too long (over 160 characters)',
          details: { length: metadata.description.length },
          fix: 'Shorten description to 150-160 characters'
        })
        score -= 5
      } else if (metadata.description.length < 120) {
        issues.push({
          type: 'info',
          category: 'metadata',
          message: 'Meta description could be longer for better SEO',
          details: { length: metadata.description.length },
          fix: 'Consider expanding description to 150-160 characters'
        })
      }
    }

  } catch (error) {
    issues.push({
      type: 'error',
      category: 'metadata',
      message: 'Metadata generation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      fix: 'Check metadata generation function and fix any errors'
    })
    score -= 30
  }

  return {
    isValid: issues.filter(i => i.type === 'error').length === 0,
    score: Math.max(0, score),
    issues,
    recommendations: generateMetadataRecommendations(issues),
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate hreflang implementation
 */
export async function validateHreflang(url: string): Promise<SEOValidationResult> {
  const issues: SEOIssue[] = []
  let score = 100

  try {
    // Test hreflang generation
    const hreflangTags = generateHreflangTags(url)

    if (hreflangTags.length === 0) {
      issues.push({
        type: 'error',
        category: 'hreflang',
        message: 'No hreflang tags generated',
        fix: 'Ensure hreflang configuration is properly set up'
      })
      score -= 40
    } else {
      // Check for x-default
      const hasXDefault = hreflangTags.some(tag => tag.hrefLang === 'x-default')
      if (!hasXDefault) {
        issues.push({
          type: 'error',
          category: 'hreflang',
          message: 'Missing x-default hreflang tag',
          fix: 'Add x-default hreflang tag for fallback language'
        })
        score -= 20
      }

      // Check for duplicate hreflang codes
      const hreflangCodes = hreflangTags.map(tag => tag.hrefLang)
      const duplicates = hreflangCodes.filter((code, index) => hreflangCodes.indexOf(code) !== index)
      if (duplicates.length > 0) {
        issues.push({
          type: 'error',
          category: 'hreflang',
          message: 'Duplicate hreflang codes found',
          details: { duplicates },
          fix: 'Remove duplicate hreflang entries'
        })
        score -= 15
      }

      // Check URL format
      for (const tag of hreflangTags) {
        try {
          new URL(tag.href)
        } catch {
          issues.push({
            type: 'error',
            category: 'hreflang',
            message: `Invalid URL in hreflang tag: ${tag.hrefLang}`,
            details: { url: tag.href },
            fix: 'Ensure all hreflang URLs are valid and absolute'
          })
          score -= 10
        }
      }
    }

    // Validate hreflang configuration
    const configIssues = validateHreflangConfig()
    issues.push(...configIssues)
    score -= configIssues.filter(i => i.type === 'error').length * 10

  } catch (error) {
    issues.push({
      type: 'error',
      category: 'hreflang',
      message: 'Hreflang validation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      fix: 'Check hreflang implementation and fix any errors'
    })
    score -= 50
  }

  return {
    isValid: issues.filter(i => i.type === 'error').length === 0,
    score: Math.max(0, score),
    issues,
    recommendations: generateHreflangRecommendations(issues),
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate structured data schemas
 */
export async function validateStructuredData(pageType: string, data?: any): Promise<SEOValidationResult> {
  const issues: SEOIssue[] = []
  let score = 100

  try {
    let schema: any

    switch (pageType) {
      case 'organization':
        schema = generateOrganizationSchema()
        break
      case 'website':
        schema = generateWebsiteSchema()
        break
      case 'article':
        schema = generateArticleSchema(data || {
          title: 'Test Article',
          description: 'Test description',
          author: 'Test Author',
          publishedTime: new Date().toISOString(),
          url: '/test'
        })
        break
      case 'product':
        schema = generateProductSchema(data || {
          name: 'Test Product',
          description: 'Test description',
          price: 100,
          currency: 'USD',
          availability: 'InStock',
          url: '/test'
        })
        break
      default:
        issues.push({
          type: 'error',
          category: 'structured-data',
          message: `Unknown page type: ${pageType}`,
          fix: 'Use a valid page type (organization, website, article, product)'
        })
        return {
          isValid: false,
          score: 0,
          issues,
          recommendations: [],
          timestamp: new Date().toISOString()
        }
    }

    // Validate required fields
    const requiredFields = ['@context', '@type']
    for (const field of requiredFields) {
      if (!schema[field]) {
        issues.push({
          type: 'error',
          category: 'structured-data',
          message: `Missing required field: ${field}`,
          fix: `Add ${field} to the structured data schema`
        })
        score -= 20
      }
    }

    // Validate @context
    if (schema['@context'] && schema['@context'] !== 'https://schema.org') {
      issues.push({
        type: 'warning',
        category: 'structured-data',
        message: 'Non-standard @context value',
        details: { context: schema['@context'] },
        fix: 'Use https://schema.org as @context value'
      })
      score -= 5
    }

    // Type-specific validations
    if (pageType === 'article' && !schema.author) {
      issues.push({
        type: 'warning',
        category: 'structured-data',
        message: 'Article missing author information',
        fix: 'Add author information to article schema'
      })
      score -= 10
    }

    if (pageType === 'product' && !schema.offers) {
      issues.push({
        type: 'error',
        category: 'structured-data',
        message: 'Product missing offers information',
        fix: 'Add price and availability information to product schema'
      })
      score -= 25
    }

  } catch (error) {
    issues.push({
      type: 'error',
      category: 'structured-data',
      message: 'Structured data generation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      fix: 'Check structured data generation functions and fix any errors'
    })
    score -= 50
  }

  return {
    isValid: issues.filter(i => i.type === 'error').length === 0,
    score: Math.max(0, score),
    issues,
    recommendations: generateStructuredDataRecommendations(issues),
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate content SEO
 */
export async function validateContentSEO(contentType: 'package' | 'blog', slug: string): Promise<SEOValidationResult> {
  const issues: SEOIssue[] = []
  let score = 100

  try {
    let content: any

    if (contentType === 'package') {
      const { data, error } = await supabase
        .from('packages')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (error || !data) {
        issues.push({
          type: 'error',
          category: 'content',
          message: 'Package not found or not published',
          fix: 'Ensure package exists and is published'
        })
        return {
          isValid: false,
          score: 0,
          issues,
          recommendations: [],
          timestamp: new Date().toISOString()
        }
      }
      content = data
    } else {
      const { data, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (error || !data) {
        issues.push({
          type: 'error',
          category: 'content',
          message: 'Blog post not found or not published',
          fix: 'Ensure blog post exists and is published'
        })
        return {
          isValid: false,
          score: 0,
          issues,
          recommendations: [],
          timestamp: new Date().toISOString()
        }
      }
      content = data
    }

    // Check SEO fields
    if (!content.seoTitle && !content.seo_title) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Missing custom SEO title',
        fix: 'Add a custom SEO title optimized for search engines'
      })
      score -= 10
    }

    if (!content.seoDescription && !content.seo_description) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Missing SEO description',
        fix: 'Add an SEO description (150-160 characters)'
      })
      score -= 15
    }

    if (!content.seoKeywords && !content.seo_keywords) {
      issues.push({
        type: 'info',
        category: 'content',
        message: 'No SEO keywords specified',
        fix: 'Consider adding relevant SEO keywords'
      })
    }

    // Check content length
    const contentLength = (content.content || content.description || '').length
    if (contentLength < 300) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Content too short for good SEO',
        details: { length: contentLength },
        fix: 'Expand content to at least 300 characters for better SEO'
      })
      score -= 10
    }

    // Check for images
    if (!content.image && !content.featured_image) {
      issues.push({
        type: 'info',
        category: 'content',
        message: 'No featured image specified',
        fix: 'Add a featured image for better social media sharing'
      })
    }

  } catch (error) {
    issues.push({
      type: 'error',
      category: 'content',
      message: 'Content validation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      fix: 'Check database connection and content structure'
    })
    score -= 50
  }

  return {
    isValid: issues.filter(i => i.type === 'error').length === 0,
    score: Math.max(0, score),
    issues,
    recommendations: generateContentRecommendations(issues),
    timestamp: new Date().toISOString()
  }
}

// Helper functions
function validateHreflangConfig(): SEOIssue[] {
  const issues: SEOIssue[] = []

  Object.entries(hreflangConfigs).forEach(([code, config]) => {
    if (!config.currency || !config.country || !config.language) {
      issues.push({
        type: 'error',
        category: 'hreflang',
        message: `Incomplete hreflang configuration for ${code}`,
        details: { code, config },
        fix: 'Ensure all hreflang configurations have currency, country, and language'
      })
    }

    if (config.priority < 1 || config.priority > 10) {
      issues.push({
        type: 'warning',
        category: 'hreflang',
        message: `Invalid priority for ${code}: ${config.priority}`,
        details: { code, priority: config.priority },
        fix: 'Set priority between 1 and 10'
      })
    }
  })

  return issues
}

function generateMetadataRecommendations(issues: SEOIssue[]): string[] {
  const recommendations: string[] = []
  
  if (issues.some(i => i.message.includes('title'))) {
    recommendations.push('Optimize page titles to be descriptive and under 60 characters')
  }
  
  if (issues.some(i => i.message.includes('description'))) {
    recommendations.push('Write compelling meta descriptions between 150-160 characters')
  }
  
  if (issues.some(i => i.message.includes('Open Graph'))) {
    recommendations.push('Add Open Graph tags for better social media sharing')
  }

  return recommendations
}

function generateHreflangRecommendations(issues: SEOIssue[]): string[] {
  const recommendations: string[] = []
  
  if (issues.some(i => i.message.includes('x-default'))) {
    recommendations.push('Always include x-default hreflang for international SEO')
  }
  
  if (issues.some(i => i.message.includes('duplicate'))) {
    recommendations.push('Ensure each hreflang code appears only once per page')
  }

  return recommendations
}

function generateStructuredDataRecommendations(issues: SEOIssue[]): string[] {
  const recommendations: string[] = []
  
  if (issues.some(i => i.message.includes('author'))) {
    recommendations.push('Add author information to articles for better search results')
  }
  
  if (issues.some(i => i.message.includes('offers'))) {
    recommendations.push('Include pricing and availability in product structured data')
  }

  return recommendations
}

function generateContentRecommendations(issues: SEOIssue[]): string[] {
  const recommendations: string[] = []
  
  if (issues.some(i => i.message.includes('short'))) {
    recommendations.push('Create comprehensive content with at least 300 words')
  }
  
  if (issues.some(i => i.message.includes('image'))) {
    recommendations.push('Add featured images to improve social media sharing')
  }

  return recommendations
}
