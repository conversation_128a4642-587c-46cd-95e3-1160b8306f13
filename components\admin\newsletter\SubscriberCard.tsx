'use client';

import React from 'react';
import { Mail, Calendar, Edit, Trash2 } from 'lucide-react';

interface Subscriber {
  id: string;
  email: string;
  status: 'subscribed' | 'unsubscribed';
  created_at: string;
  updated_at: string;
}

interface SubscriberCardProps {
  subscriber: Subscriber;
  onUpdateStatus: (id: string, status: 'subscribed' | 'unsubscribed') => void;
  onDelete: (id: string) => void;
  onSelect: (id: string) => void;
  isSelected: boolean;
}

export default function SubscriberCard({
  subscriber,
  onUpdateStatus,
  onDelete,
  onSelect,
  isSelected
}: SubscriberCardProps) {
  const handleStatusToggle = () => {
    const newStatus = subscriber.status === 'subscribed' ? 'unsubscribed' : 'subscribed';
    onUpdateStatus(subscriber.id, newStatus);
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this subscriber?')) {
      onDelete(subscriber.id);
    }
  };

  return (
    <div className={`bg-white rounded-lg border-2 p-6 transition-all duration-200 hover:shadow-md ${
      isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(subscriber.id)}
            className="mt-1 rounded border-gray-300"
          />
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Mail className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-900">{subscriber.email}</span>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>Joined {new Date(subscriber.created_at).toLocaleDateString()}</span>
              </div>
              
              <span
                className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  subscriber.status === 'subscribed'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {subscriber.status}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <button
            onClick={handleStatusToggle}
            className={`px-3 py-1 text-xs rounded-md transition-colors ${
              subscriber.status === 'subscribed'
                ? 'bg-red-100 text-red-700 hover:bg-red-200'
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            }`}
            title={subscriber.status === 'subscribed' ? 'Unsubscribe' : 'Subscribe'}
          >
            {subscriber.status === 'subscribed' ? 'Unsubscribe' : 'Subscribe'}
          </button>
          
          <button
            onClick={handleDelete}
            className="p-1 text-red-600 hover:text-red-800 transition-colors"
            title="Delete subscriber"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
