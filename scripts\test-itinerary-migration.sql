-- Test Script: Verify Itinerary Migration to Hours
-- Run this after executing the migration script to verify changes

-- =============================================
-- 1. CHECK TABLE STRUCTURES
-- =============================================

-- Check sas_package_itinerary table structure
SELECT 
    'sas_package_itinerary' as table_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sas_package_itinerary' 
ORDER BY ordinal_position;

-- Check sas_mini_package_itinerary table structure  
SELECT 
    'sas_mini_package_itinerary' as table_name,
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'sas_mini_package_itinerary' 
ORDER BY ordinal_position;

-- =============================================
-- 2. VERIFY REMOVED COLUMNS
-- =============================================

-- These queries should return 0 rows if migration was successful
SELECT COUNT(*) as day_number_columns_remaining
FROM information_schema.columns 
WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
AND column_name = 'day_number';

SELECT COUNT(*) as activities_columns_remaining
FROM information_schema.columns 
WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
AND column_name = 'activities';

SELECT COUNT(*) as accommodation_columns_remaining
FROM information_schema.columns 
WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
AND column_name = 'accommodation';

SELECT COUNT(*) as meals_columns_remaining
FROM information_schema.columns 
WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
AND column_name = 'meals';

-- =============================================
-- 3. VERIFY NEW HOUR_NUMBER COLUMN
-- =============================================

-- These queries should return 2 rows if migration was successful
SELECT COUNT(*) as hour_number_columns_added
FROM information_schema.columns 
WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
AND column_name = 'hour_number';

-- =============================================
-- 4. CHECK SAMPLE DATA
-- =============================================

-- Check if data was preserved in package itinerary
SELECT 
    id,
    hour_number,
    title,
    description,
    sort_order,
    created_at
FROM public.sas_package_itinerary 
ORDER BY sort_order 
LIMIT 5;

-- Check if data was preserved in mini package itinerary
SELECT 
    id,
    hour_number,
    title,
    description,
    sort_order,
    created_at
FROM public.sas_mini_package_itinerary 
ORDER BY sort_order 
LIMIT 5;

-- =============================================
-- 5. SUMMARY REPORT
-- =============================================

DO $$
DECLARE
    pkg_count INTEGER;
    mini_pkg_count INTEGER;
    removed_cols INTEGER;
    added_cols INTEGER;
BEGIN
    -- Count existing itinerary records
    SELECT COUNT(*) INTO pkg_count FROM public.sas_package_itinerary;
    SELECT COUNT(*) INTO mini_pkg_count FROM public.sas_mini_package_itinerary;
    
    -- Count removed columns (should be 0)
    SELECT COUNT(*) INTO removed_cols
    FROM information_schema.columns 
    WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
    AND column_name IN ('day_number', 'activities', 'accommodation', 'meals');
    
    -- Count added hour_number columns (should be 2)
    SELECT COUNT(*) INTO added_cols
    FROM information_schema.columns 
    WHERE table_name IN ('sas_package_itinerary', 'sas_mini_package_itinerary')
    AND column_name = 'hour_number';
    
    RAISE NOTICE '=== MIGRATION SUMMARY ===';
    RAISE NOTICE 'Package itinerary records: %', pkg_count;
    RAISE NOTICE 'Mini package itinerary records: %', mini_pkg_count;
    RAISE NOTICE 'Old columns remaining: % (should be 0)', removed_cols;
    RAISE NOTICE 'New hour_number columns: % (should be 2)', added_cols;
    
    IF removed_cols = 0 AND added_cols = 2 THEN
        RAISE NOTICE '✅ Migration appears successful!';
    ELSE
        RAISE NOTICE '❌ Migration may have issues - check results above';
    END IF;
END $$;
