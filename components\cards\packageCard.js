'use client';
import { MapPin } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { getTemporaryFallbackImage } from '@/lib/fallback-images';
import LazyImage from '../common/LazyImage';

export default function TourCard({ tour }) {
  const router = useRouter();

  const handleViewPackage = () => {
    router.push(`/package/${tour.slug}`);
  };

  // Format price from database data
  const formatPrice = (pricing) => {
    if (typeof pricing === 'string') return pricing; // If already formatted
    if (typeof pricing === 'number') return `$${pricing.toLocaleString()}`; // If it's a number

    // Handle pricing object
    if (pricing && typeof pricing === 'object') {
      const prices = [pricing.solo, pricing.honeymoon, pricing.family, pricing.group].filter(p => p && p > 0);
      if (prices.length > 0) {
        const lowestPrice = Math.min(...prices);
        return `$${lowestPrice.toLocaleString()}`;
      }
    }

    // Fallback
    return '$0';
  };

  // Get image URL with fallback handling
  const getImageUrl = () => {
    // Priority order: hero_image_url -> image_url -> fallback
    const primaryImageUrl = tour.hero_image_url || tour.image_url;

    // Check if we have a valid-looking URL
    if (primaryImageUrl && primaryImageUrl.startsWith('https://')) {
      return primaryImageUrl;
    }

    // If no valid URL found, use fallback
    const fallbackImage = getTemporaryFallbackImage(tour.category);
    return fallbackImage.url;
  };

  const imageAlt = tour.hero_image_alt || tour.image_alt || tour.title || 'Tour destination';
  const imageUrl = getImageUrl();

  // Error handler for edge cases
  const handleImageError = (e) => {
    // Set fallback image directly on the img element
    if (!e.target.src.includes('/images/hero/great-migration-serengeti-national-park.webp')) {
      e.target.src = '/images/hero/great-migration-serengeti-national-park.webp';
    }
  };

  return (
    <div className="bg-[var(--secondary-background)] shadow-lg overflow-hidden h-[27rem] text-left flex flex-col">
      {/* Header Image Area */}
      <div className="relative h-56 flex-shrink-0">
        <LazyImage
          key={imageUrl}
          src={imageUrl}
          alt={imageAlt}
          className="w-full h-full object-cover"
          width={600}
          height={224}
          style={{objectFit: 'cover'}}
          onError={handleImageError}
          unoptimized={imageUrl.includes('supabase.co')}
          priority={false}
          skeletonVariant="card"
        />

        {/* Price Badge */}
        <div className="absolute top-4 right-4 bg-[var(--secondary-background)] rounded-lg px-3 py-2 shadow-md">
          <div className="text-xs text-gray-600">From</div>
          <div className="text-base font-bold text-gray-900">{formatPrice(tour.pricing || tour.price)}/Person</div>
        </div>
        
        {/* Location Badge */}
        <div className="absolute bottom-4 left-4">
          <div className="flex items-center gap-1 bg-[var(--secondary-background)] rounded-full px-3 py-1 shadow-sm">
            <MapPin className="w-3 h-3 text-[var(--text)]" />
            <span className="text-xs font-medium text-[var(--light-green)]">{tour.location}</span>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="p-6 flex flex-col flex-grow">
        {/* Title Area */}
        <div className="h-[4rem]">
          <h2 className="text-lg font-bold text-gray-900 leading-tight">
            {tour.title}
          </h2>
        </div>

        {/* Details Row */}
        <div className="flex justify-between items-start">
          <div>
            <div className="text-xs text-gray-600 mb-1">Difficulty</div>
            <div className="text-sm font-semibold text-gray-900">{tour.difficulty}</div>
          </div>
          <div className="text-right">
            <div className="text-xs text-gray-600 mb-1">Tour Category</div>
            <div className="text-sm font-semibold text-gray-900">{tour.category}</div>
          </div>
        </div>

        {/* Book Now Button */}
        <div className="flex justify-center w-full mt-4">
          <button
            onClick={handleViewPackage}
            className="btn w-[169px] text-white text-sm font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
          >
            View Tour Package
          </button>
        </div>
      </div>
    </div>
  );
}