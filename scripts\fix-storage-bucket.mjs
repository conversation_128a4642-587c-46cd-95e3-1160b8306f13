import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('\n📝 To get your service role key:');
  console.error('   1. Go to your Supabase dashboard');
  console.error('   2. Click Project Settings → API');
  console.error('   3. Copy the "service_role" key (not the anon key)');
  console.error('   4. Add it to your .env.local file as SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Use service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixStorageBucket() {
  console.log('🔧 Fixing Storage Bucket Issue\n');

  try {
    // 1. Check current buckets
    console.log('📦 Checking current buckets...');
    const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError.message);
      return;
    }

    console.log(`Found ${existingBuckets?.length || 0} existing buckets`);
    
    // 2. Create the package-images bucket
    console.log('\n🚀 Creating package-images bucket...');
    const { data: newBucket, error: createError } = await supabase.storage.createBucket('package-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    });

    if (createError) {
      if (createError.message.includes('already exists')) {
        console.log('✅ Bucket already exists');
      } else {
        console.error('❌ Error creating bucket:', createError.message);
        return;
      }
    } else {
      console.log('✅ Successfully created package-images bucket');
      console.log('📋 Bucket details:', newBucket);
    }

    // 3. Verify the bucket was created
    console.log('\n🔍 Verifying bucket creation...');
    const { data: buckets, error: verifyError } = await supabase.storage.listBuckets();
    
    if (verifyError) {
      console.error('❌ Error verifying buckets:', verifyError.message);
      return;
    }

    const packageImagesBucket = buckets.find(b => b.name === 'package-images');
    
    if (packageImagesBucket) {
      console.log('✅ package-images bucket confirmed!');
      console.log(`   Name: ${packageImagesBucket.name}`);
      console.log(`   ID: ${packageImagesBucket.id}`);
      console.log(`   Public: ${packageImagesBucket.public}`);
      console.log(`   File size limit: ${packageImagesBucket.file_size_limit} bytes`);
    } else {
      console.log('❌ Bucket not found after creation');
      return;
    }

    // 4. Test bucket access
    console.log('\n🧪 Testing bucket access...');
    const { data: files, error: testError } = await supabase.storage
      .from('package-images')
      .list();
    
    if (testError) {
      console.log(`⚠️  Warning: ${testError.message}`);
      console.log('   This might be normal for an empty bucket');
    } else {
      console.log(`✅ Bucket access confirmed. Files: ${files?.length || 0}`);
    }

    console.log('\n🎉 Storage bucket issue fixed!');
    console.log('\n📝 Next steps:');
    console.log('   1. Try uploading an image in your admin dashboard');
    console.log('   2. The error should be gone now');
    console.log('   3. Images should upload successfully');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

fixStorageBucket().catch(console.error); 