/**
 * Fallback image utilities for Swift Africa Safaris
 * Provides consistent fallback images from Supabase storage
 */

import { supabase } from './supabase';

// Default fallback images stored in Supabase storage
export const FALLBACK_IMAGES = {
  // Package/Safari images
  PACKAGE_DEFAULT: 'fallback/safari-default.webp',
  PACKAGE_WILDLIFE: 'fallback/wildlife-safari.webp', 
  PACKAGE_ADVENTURE: 'fallback/adventure-safari.webp',
  PACKAGE_CULTURAL: 'fallback/cultural-safari.webp',
  PACKAGE_BEACH: 'fallback/beach-safari.webp',
  PACKAGE_NATURE: 'fallback/nature-safari.webp',
  PACKAGE_LUXURY: 'fallback/luxury-safari.webp',
  
  // Blog images
  BLOG_DEFAULT: 'fallback/blog-default.webp',
  
  // User avatars
  USER_AVATAR: 'fallback/user-avatar.webp',
  
  // Product images
  PRODUCT_DEFAULT: 'fallback/product-default.webp',
  
  // Destination images
  DESTINATION_DEFAULT: 'fallback/destination-default.webp'
};

// Default alt texts for fallback images
export const FALLBACK_ALT_TEXTS = {
  PACKAGE_DEFAULT: 'Safari package - Wildlife adventure in Africa',
  PACKAGE_WILDLIFE: 'Wildlife safari - African animals in natural habitat',
  PACKAGE_ADVENTURE: 'Adventure safari - Exciting outdoor activities',
  PACKAGE_CULTURAL: 'Cultural safari - Local communities and traditions',
  PACKAGE_BEACH: 'Beach safari - Coastal wildlife and relaxation',
  PACKAGE_NATURE: 'Nature safari - Pristine landscapes and ecosystems',
  PACKAGE_LUXURY: 'Luxury safari - Premium wildlife experience',
  BLOG_DEFAULT: 'Blog post image - Safari and travel content',
  USER_AVATAR: 'User profile picture',
  PRODUCT_DEFAULT: 'Safari product image',
  DESTINATION_DEFAULT: 'Safari destination image'
};

/**
 * Get Supabase storage URL for a fallback image
 */
export function getFallbackImageUrl(imageKey: keyof typeof FALLBACK_IMAGES, bucket: string = 'sas-package-images'): string {
  const imagePath = FALLBACK_IMAGES[imageKey];
  const { data } = supabase.storage.from(bucket).getPublicUrl(imagePath);
  return data.publicUrl;
}

/**
 * Get fallback alt text for an image
 */
export function getFallbackAltText(imageKey: keyof typeof FALLBACK_ALT_TEXTS): string {
  return FALLBACK_ALT_TEXTS[imageKey];
}

/**
 * Get package fallback image based on category
 */
export function getPackageFallbackImage(category?: string, bucket: string = 'sas-package-images'): {
  url: string;
  alt: string;
} {
  let imageKey: keyof typeof FALLBACK_IMAGES = 'PACKAGE_DEFAULT';
  
  // Map category to specific fallback image
  switch (category?.toLowerCase()) {
    case 'wildlife':
      imageKey = 'PACKAGE_WILDLIFE';
      break;
    case 'adventure':
      imageKey = 'PACKAGE_ADVENTURE';
      break;
    case 'cultural':
      imageKey = 'PACKAGE_CULTURAL';
      break;
    case 'beach':
      imageKey = 'PACKAGE_BEACH';
      break;
    case 'nature':
      imageKey = 'PACKAGE_NATURE';
      break;
    case 'luxury':
      imageKey = 'PACKAGE_LUXURY';
      break;
    default:
      imageKey = 'PACKAGE_DEFAULT';
  }
  
  return {
    url: getFallbackImageUrl(imageKey, bucket),
    alt: getFallbackAltText(imageKey as keyof typeof FALLBACK_ALT_TEXTS)
  };
}

/**
 * Get fallback image from Supabase storage based on category
 * Uses the uploaded fallback images in the sas-package-images bucket
 */
export function getTemporaryFallbackImage(_category?: string): {
  url: string;
  alt: string;
} {
  // Always use local fallback images for reliability since Supabase storage fallbacks don't exist
  return {
    url: '/images/hero/great-migration-serengeti-national-park.webp',
    alt: 'Safari package - Wildlife adventure in Africa'
  };
}
