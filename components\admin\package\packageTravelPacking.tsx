'use client';

import React, { useState, useEffect } from 'react';
import { Plus, X } from 'lucide-react';

interface TravelPackingUploaderProps {
  highlights?: string[];
  packingList?: string[];
  includes?: string[];
  excludes?: string[];
  onSave?: (data: {
    highlights: string[];
    packingList: string[];
    includes: string[];
    excludes: string[];
  }) => void;
}

const TravelPackingUploader: React.FC<TravelPackingUploaderProps> = ({
  highlights: initialHighlights = [''],
  packingList: initialPackingList = [''],
  includes: initialIncludes = [''],
  excludes: initialExcludes = [''],
  onSave
}) => {
  const [highlights, setHighlights] = useState<string[]>(initialHighlights);
  const [packingItems, setPackingItems] = useState<string[]>(initialPackingList);
  const [includes, setIncludes] = useState<string[]>(initialIncludes);
  const [excludes, setExcludes] = useState<string[]>(initialExcludes);

  // Debounced save effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSave) {
        onSave({
          highlights: highlights.filter(h => h.trim() !== ''),
          packingList: packingItems.filter(p => p.trim() !== ''),
          includes: includes.filter(i => i.trim() !== ''),
          excludes: excludes.filter(e => e.trim() !== '')
        });
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [highlights, packingItems, includes, excludes, onSave]);

  const addHighlight = () => {
    setHighlights([...highlights, '']);
  };

  const removeHighlight = (index: number) => {
    if (highlights.length > 1) {
      setHighlights(highlights.filter((_, i) => i !== index));
    }
  };

  const updateHighlight = (index: number, value: string) => {
    const newHighlights = [...highlights];
    newHighlights[index] = value;
    setHighlights(newHighlights);
  };

  const addPackingItem = () => {
    setPackingItems([...packingItems, '']);
  };

  const removePackingItem = (index: number) => {
    if (packingItems.length > 1) {
      setPackingItems(packingItems.filter((_, i) => i !== index));
    }
  };

  const updatePackingItem = (index: number, value: string) => {
    const newItems = [...packingItems];
    newItems[index] = value;
    setPackingItems(newItems);
  };

  // Includes handlers
  const addIncludes = () => {
    setIncludes([...includes, '']);
  };

  const removeIncludes = (index: number) => {
    if (includes.length > 1) {
      setIncludes(includes.filter((_, i) => i !== index));
    }
  };

  const updateIncludes = (index: number, value: string) => {
    const newItems = [...includes];
    newItems[index] = value;
    setIncludes(newItems);
  };

  // Excludes handlers
  const addExcludes = () => {
    setExcludes([...excludes, '']);
  };

  const removeExcludes = (index: number) => {
    if (excludes.length > 1) {
      setExcludes(excludes.filter((_, i) => i !== index));
    }
  };

  const updateExcludes = (index: number, value: string) => {
    const newItems = [...excludes];
    newItems[index] = value;
    setExcludes(newItems);
  };

  return (
    <div className="min-h-auto bg-[var(--background)] p-4 my-4">
      <div className="text-justify">
        <div className="space-y-6">

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Highlights Section */}
            <div className="bg-[var(--white)] p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold text-[var(--text)] mb-4 flex items-center">
                <div className="w-3 h-3 bg-[var(--btn)] rounded-full mr-3"></div>
                Trip Highlights
              </h2>
              <p className="text-[var(--text)] opacity-70 mb-4 text-sm">
                Add the main attractions and experiences for your trip
              </p>

              <div className="space-y-3">
                {highlights.map((highlight, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-[var(--btn)] rounded-full mt-3 flex-shrink-0"></div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={highlight}
                        onChange={(e) => updateHighlight(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--btn)]"
                        placeholder="Enter highlight..."
                      />
                    </div>
                    {highlights.length > 1 && (
                      <button
                        onClick={() => removeHighlight(index)}
                        className="p-2 text-[var(--accent)] hover:text-red-700 hover:bg-[var(--primary-background)] rounded-md transition-colors"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  onClick={addHighlight}
                  className="flex items-center text-[var(--btn)] hover:text-[var(--light-green)] font-medium"
                >
                  <Plus size={16} className="mr-1" />
                  Add Highlight
                </button>
              </div>
            </div>

            {/* Packing Items Section */}
            <div className="bg-[var(--white)] p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold text-[var(--text)] mb-4 flex items-center">
                <div className="w-3 h-3 bg-[var(--light-green)] rounded-full mr-3"></div>
                Packing List
              </h2>
              <p className="text-[var(--text)] opacity-70 mb-4 text-sm">
                Add essential items travelers should pack for this trip
              </p>

              <div className="space-y-3">
                {packingItems.map((item, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-[var(--light-green)] rounded-full mt-3 flex-shrink-0"></div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={item}
                        onChange={(e) => updatePackingItem(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)]"
                        placeholder="Enter packing item..."
                      />
                    </div>
                    {packingItems.length > 1 && (
                      <button
                        onClick={() => removePackingItem(index)}
                        className="p-2 text-[var(--accent)] hover:text-red-700 hover:bg-[var(--primary-background)] rounded-md transition-colors"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  onClick={addPackingItem}
                  className="flex items-center text-[var(--light-green)] hover:text-[var(--btn)] font-medium"
                >
                  <Plus size={16} className="mr-1" />
                  Add Packing Item
                </button>
              </div>
            </div>

            {/* Includes Section */}
            <div className="bg-[var(--white)] p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold text-[var(--text)] mb-4 flex items-center">
                <div className="w-3 h-3 bg-[var(--light-green)] rounded-full mr-3"></div>
                What's Included
              </h2>
              <p className="text-[var(--text)] opacity-70 mb-4 text-sm">
                Add items that are included in this package
              </p>

              <div className="space-y-3">
                {includes.map((item, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-[var(--light-green)] rounded-full mt-3 flex-shrink-0"></div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={item}
                        onChange={(e) => updateIncludes(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)]"
                        placeholder="Enter included item..."
                      />
                    </div>
                    {includes.length > 1 && (
                      <button
                        onClick={() => removeIncludes(index)}
                        className="p-2 text-[var(--accent)] hover:text-red-700 hover:bg-[var(--primary-background)] rounded-md transition-colors"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  onClick={addIncludes}
                  className="flex items-center text-[var(--light-green)] hover:text-[var(--btn)] font-medium"
                >
                  <Plus size={16} className="mr-1" />
                  Add Included Item
                </button>
              </div>
            </div>

            {/* Excludes Section */}
            <div className="bg-[var(--white)] p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold text-[var(--text)] mb-4 flex items-center">
                <div className="w-3 h-3 bg-[var(--accent)] rounded-full mr-3"></div>
                What's Not Included
              </h2>
              <p className="text-[var(--text)] opacity-70 mb-4 text-sm">
                Add items that are not included in this package
              </p>

              <div className="space-y-3">
                {excludes.map((item, index) => (
                  <div key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-[var(--accent)] rounded-full mt-3 flex-shrink-0"></div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={item}
                        onChange={(e) => updateExcludes(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--light-green)]"
                        placeholder="Enter excluded item..."
                      />
                    </div>
                    {excludes.length > 1 && (
                      <button
                        onClick={() => removeExcludes(index)}
                        className="p-2 text-[var(--accent)] hover:text-red-700 hover:bg-[var(--primary-background)] rounded-md transition-colors"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  onClick={addExcludes}
                  className="flex items-center text-[var(--accent)] hover:text-[var(--btn)] font-medium"
                >
                  <Plus size={16} className="mr-1" />
                  Add Excluded Item
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TravelPackingUploader;
