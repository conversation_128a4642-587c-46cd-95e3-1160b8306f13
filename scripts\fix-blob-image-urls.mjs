#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixBlobUrls() {
  console.log('🔧 Fixing packages with blob URLs...\n');

  try {
    // Fetch packages with blob URLs
    const { data: packages, error } = await supabase
      .from('sas_packages')
      .select('id, title, slug, image_url, hero_image_url')
      .or('image_url.like.blob:%,hero_image_url.like.blob:%');

    if (error) {
      console.error('❌ Error fetching packages:', error);
      return;
    }

    if (packages.length === 0) {
      console.log('✅ No packages with blob URLs found!');
      return;
    }

    console.log(`🚨 Found ${packages.length} packages with blob URLs\n`);

    let fixedCount = 0;

    for (const pkg of packages) {
      console.log(`🔧 Fixing: ${pkg.title} (${pkg.slug})`);
      
      const updates = {};
      
      // Fix image_url if it's a blob URL
      if (pkg.image_url && pkg.image_url.startsWith('blob:')) {
        updates.image_url = null;
        console.log(`   - Clearing image_url: ${pkg.image_url}`);
      }
      
      // Fix hero_image_url if it's a blob URL
      if (pkg.hero_image_url && pkg.hero_image_url.startsWith('blob:')) {
        updates.hero_image_url = null;
        console.log(`   - Clearing hero_image_url: ${pkg.hero_image_url}`);
      }

      if (Object.keys(updates).length > 0) {
        const { error: updateError } = await supabase
          .from('sas_packages')
          .update(updates)
          .eq('id', pkg.id);

        if (updateError) {
          console.error(`   ❌ Error updating package ${pkg.id}:`, updateError);
        } else {
          console.log(`   ✅ Updated successfully`);
          fixedCount++;
        }
      }
      
      console.log('');
    }

    console.log(`🎉 Fixed ${fixedCount} packages!`);
    console.log('💡 These packages will now use fallback images until new images are uploaded.');

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Ask for confirmation before running
console.log('⚠️  This script will clear blob URLs from packages and set them to null.');
console.log('   Packages will use fallback images until new images are uploaded.');
console.log('   Continue? (y/N)');

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.on('data', (key) => {
  const input = key.toString().toLowerCase();
  
  if (input === 'y' || input === 'yes\n') {
    console.log('\n🚀 Starting fix...\n');
    fixBlobUrls().then(() => process.exit(0));
  } else {
    console.log('\n❌ Cancelled.');
    process.exit(0);
  }
});
