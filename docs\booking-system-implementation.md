# Swift Africa Safaris - Comprehensive Booking System Implementation

## Table of Contents
1. [Overview](#overview)
2. [Database Schema](#database-schema)
3. [API Endpoints](#api-endpoints)
4. [Admin Dashboard](#admin-dashboard)
5. [Email System](#email-system)
6. [Form Integration](#form-integration)
7. [Testing Guide](#testing-guide)
8. [Deployment Notes](#deployment-notes)

## Overview

This document describes the comprehensive booking system implementation for Swift Africa Safaris. The system handles multiple booking types including tour bookings, apartment rentals, car hire, volunteering applications, and contact form submissions.

### Key Features
- **Multiple Booking Types**: Tour, Apartment, Car, Volunteering, Contact
- **Admin Dashboard**: Complete management interface for all booking types
- **Email Notifications**: Automated email notifications to admin team
- **Status Management**: Track booking status through lifecycle
- **Export Functionality**: CSV export for all booking data
- **Email Configuration**: Manage notification email addresses

## Database Schema

### Tables Created
All tables use the `sas_` prefix and include common fields for tracking and management.

#### 1. sas_tour_bookings
```sql
- id (UUID, Primary Key)
- full_name (VARCHAR(255), NOT NULL)
- email (VARCHAR(255), NOT NULL)
- whatsapp (VARCHAR(50), NOT NULL)
- number_of_people (INTEGER, NOT NULL)
- message (TEXT)
- status (VARCHAR(50), DEFAULT 'pending')
- admin_notes (TEXT)
- email_sent (BOOLEAN, DEFAULT FALSE)
- email_sent_at (TIMESTAMP)
- notification_emails_sent (JSONB)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

#### 2. sas_apartment_bookings
```sql
- id (UUID, Primary Key)
- full_name (VARCHAR(255), NOT NULL)
- email (VARCHAR(255), NOT NULL)
- whatsapp (VARCHAR(50), NOT NULL)
- properties (TEXT, NOT NULL)
- status (VARCHAR(50), DEFAULT 'pending')
- admin_notes (TEXT)
- email_sent (BOOLEAN, DEFAULT FALSE)
- email_sent_at (TIMESTAMP)
- notification_emails_sent (JSONB)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

#### 3. sas_car_bookings
```sql
- id (UUID, Primary Key)
- full_name (VARCHAR(255), NOT NULL)
- email (VARCHAR(255), NOT NULL)
- whatsapp (VARCHAR(50), NOT NULL)
- car_properties (TEXT, NOT NULL)
- status (VARCHAR(50), DEFAULT 'pending')
- admin_notes (TEXT)
- email_sent (BOOLEAN, DEFAULT FALSE)
- email_sent_at (TIMESTAMP)
- notification_emails_sent (JSONB)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

#### 4. sas_volunteering_applications
```sql
- id (UUID, Primary Key)
- name (VARCHAR(255), NOT NULL)
- email (VARCHAR(255), NOT NULL)
- arrival_date (DATE, NOT NULL)
- departure_date (DATE, NOT NULL)
- message (TEXT)
- status (VARCHAR(50), DEFAULT 'pending')
- admin_notes (TEXT)
- email_sent (BOOLEAN, DEFAULT FALSE)
- email_sent_at (TIMESTAMP)
- notification_emails_sent (JSONB)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

#### 5. sas_contact_submissions
```sql
- id (UUID, Primary Key)
- name (VARCHAR(255), NOT NULL)
- whatsapp (VARCHAR(50), NOT NULL)
- email (VARCHAR(255), NOT NULL)
- message (TEXT)
- status (VARCHAR(50), DEFAULT 'new')
- admin_notes (TEXT)
- email_sent (BOOLEAN, DEFAULT FALSE)
- email_sent_at (TIMESTAMP)
- notification_emails_sent (JSONB)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

#### 6. sas_notification_emails
```sql
- id (UUID, Primary Key)
- email_address (VARCHAR(255), NOT NULL, UNIQUE)
- notification_types (JSONB, DEFAULT '["all"]')
- is_active (BOOLEAN, DEFAULT TRUE)
- created_at (TIMESTAMP, DEFAULT NOW())
- updated_at (TIMESTAMP, DEFAULT NOW())
```

### Indexes
Performance indexes are created on:
- Email addresses for all booking tables
- Status fields for filtering
- Created_at fields for sorting
- Date fields for volunteering applications

## API Endpoints

### Tour Bookings
- `GET /api/bookings/tour` - Fetch tour bookings with pagination and filtering
- `POST /api/bookings/tour` - Create new tour booking
- `PATCH /api/bookings/tour` - Update tour booking status/notes

### Apartment Bookings
- `GET /api/bookings/apartment` - Fetch apartment bookings
- `POST /api/bookings/apartment` - Create new apartment booking
- `PATCH /api/bookings/apartment` - Update apartment booking

### Car Bookings
- `GET /api/bookings/car` - Fetch car bookings
- `POST /api/bookings/car` - Create new car booking
- `PATCH /api/bookings/car` - Update car booking

### Volunteering Applications
- `GET /api/bookings/volunteering` - Fetch volunteering applications
- `POST /api/bookings/volunteering` - Create new application
- `PATCH /api/bookings/volunteering` - Update application status

### Contact Submissions
- `GET /api/bookings/contact` - Fetch contact submissions
- `POST /api/bookings/contact` - Create new contact submission
- `PATCH /api/bookings/contact` - Update submission status

### Email Management
- `GET /api/admin/emails` - Fetch notification emails
- `POST /api/admin/emails` - Add new notification email
- `PATCH /api/admin/emails` - Update notification email
- `DELETE /api/admin/emails` - Remove notification email
- `POST /api/admin/emails/test` - Send test email

## Admin Dashboard

### Navigation Structure
The admin sidebar now includes under "Bookings":
- Tour Bookings (`/admin/bookings/tour`)
- Apartment Bookings (`/admin/bookings/apartment`)
- Car Hire Bookings (`/admin/bookings/car`)
- Volunteering Applications (`/admin/bookings/volunteering`)
- Contact Submissions (`/admin/bookings/contact`)
- Email Notifications (`/admin/bookings/emails`)

### Features
Each booking management page includes:
- **List View**: Paginated table with search and filtering
- **Status Management**: Dropdown to update booking status
- **Export**: CSV export functionality
- **Summary Stats**: Dashboard cards showing key metrics
- **Search**: Full-text search across relevant fields
- **Filtering**: Filter by status and other criteria

### Email Management
- Add/edit/delete notification email addresses
- Configure notification types per email
- Test email functionality
- Active/inactive status management

## Email System

### Notification Types
- `all` - Receives all notifications
- `tour` - Tour booking notifications only
- `apartment` - Apartment booking notifications only
- `car` - Car booking notifications only
- `volunteering` - Volunteering application notifications only
- `contact` - Contact form notifications only

### Email Templates
Professional HTML email templates for each booking type including:
- Company branding
- Customer information
- Booking/application details
- Next steps for admin
- Responsive design

### Default Configuration
- Default notification email: `<EMAIL>`
- Fallback email system in case of configuration issues
- Email tracking and delivery status

## Form Integration

### Updated Forms
All existing forms have been updated to connect to the new API endpoints:

1. **Plan Tour Form** (`components/common/planTourForm.js`)
   - Connects to `/api/bookings/tour`
   - Validates required fields
   - Shows success/error feedback

2. **Apartment Booking** (`components/common/apartmentBooking.js`)
   - Connects to `/api/bookings/apartment`
   - Maintains existing validation
   - Proper error handling

3. **Car Booking** (`components/common/carBooking.js`)
   - Connects to `/api/bookings/car`
   - Field validation
   - Success feedback

4. **Volunteering Form** (`components/common/volunteeringOpportunities.js`)
   - Connects to `/api/bookings/volunteering`
   - Date validation
   - Error display

5. **Contact Form** (`components/contact/form.js`)
   - Connects to `/api/bookings/contact`
   - Form element wrapper added
   - Success/error handling

### Form Behavior
- All forms maintain existing UI/UX design
- Proper validation before submission
- Loading states during submission
- Success/error feedback to users
- Form reset after successful submission

## Testing Guide

### 1. Database Testing
```sql
-- Verify tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_name LIKE 'sas_%' AND table_schema = 'public';

-- Test data insertion
INSERT INTO sas_tour_bookings (full_name, email, whatsapp, number_of_people, message)
VALUES ('Test User', '<EMAIL>', '+1234567890', 2, 'Test booking');
```

### 2. API Testing
Use tools like Postman or curl to test endpoints:

```bash
# Test tour booking creation
curl -X POST http://localhost:3000/api/bookings/tour \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "whatsapp": "+1234567890",
    "numberOfPeople": 2,
    "message": "Looking for a safari tour"
  }'

# Test email management
curl -X GET http://localhost:3000/api/admin/emails
```

### 3. Frontend Testing
1. Navigate to each form on the website
2. Fill out and submit forms
3. Verify data appears in admin dashboard
4. Test admin dashboard functionality
5. Verify email notifications are sent

### 4. Email Testing
1. Go to `/admin/bookings/emails`
2. Add a test email address
3. Use the "Send Test" functionality
4. Submit a form and verify notification emails

## Deployment Notes

### Environment Variables
Ensure these are set in your `.env.local`:
```
# Email Configuration
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
SMTP_FROM=your-from-email

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Database Setup
1. Run the database setup script: `scripts/setup-booking-system-database.sql`
2. Verify all tables and indexes are created
3. Check Row Level Security policies are in place

### Post-Deployment Checklist
- [ ] All forms submit successfully
- [ ] Admin dashboard loads and displays data
- [ ] Email notifications are sent
- [ ] CSV export works
- [ ] Search and filtering function properly
- [ ] Status updates work correctly
- [ ] Email management interface functions

### Monitoring
Monitor the following:
- Form submission success rates
- Email delivery rates
- Database performance
- Admin dashboard usage
- Error logs for any issues

## Support and Maintenance

### Regular Tasks
- Monitor email delivery rates
- Review booking data for insights
- Update notification email lists as needed
- Export data for reporting
- Monitor database performance

### Troubleshooting
- Check email configuration if notifications fail
- Verify database connections
- Review API error logs
- Test form submissions manually
- Validate email templates render correctly

This comprehensive booking system provides a robust foundation for managing all customer inquiries and bookings for Swift Africa Safaris.
