"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';

const LoadingContext = createContext();

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export const LoadingProvider = ({ children }) => {
  const [globalLoading, setGlobalLoading] = useState(true);
  const [loadingStates, setLoadingStates] = useState({});

  // Global loading state management
  useEffect(() => {
    const timer = setTimeout(() => {
      setGlobalLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const setLoading = (key, isLoading) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: isLoading
    }));
  };

  const isLoading = (key) => {
    return loadingStates[key] || false;
  };

  const isAnyLoading = () => {
    return Object.values(loadingStates).some(loading => loading);
  };

  const value = {
    globalLoading,
    setGlobalLoading,
    setLoading,
    isLoading,
    isAnyLoading,
    loadingStates
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};
