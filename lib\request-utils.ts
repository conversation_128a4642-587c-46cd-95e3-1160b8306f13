import { NextRequest } from 'next/server';

export const MAX_REQUEST_SIZE = 10 * 1024 * 1024; // 10MB

export interface RequestSizeValidationResult {
  isValid: boolean;
  error?: string;
  size?: number;
}

/**
 * Validates the size of an incoming request
 */
export function validateRequestSize(request: NextRequest): RequestSizeValidationResult {
  const contentLength = request.headers.get('content-length');
  
  if (!contentLength) {
    return { isValid: true };
  }
  
  const sizeInBytes = parseInt(contentLength);
  
  if (isNaN(sizeInBytes)) {
    return { isValid: true };
  }
  
  if (sizeInBytes > MAX_REQUEST_SIZE) {
    const sizeInMB = (sizeInBytes / (1024 * 1024)).toFixed(2);
    const maxSizeInMB = (MAX_REQUEST_SIZE / (1024 * 1024)).toFixed(0);
    
    return {
      isValid: false,
      size: sizeInBytes,
      error: `Request size (${sizeInMB}MB) exceeds the maximum allowed size of ${maxSizeInMB}MB. Please reduce content size, compress images, or split into multiple posts.`
    };
  }
  
  return { isValid: true, size: sizeInBytes };
}

/**
 * Estimates the size of a JSON object in bytes
 */
export function estimateJsonSize(obj: any): number {
  return new Blob([JSON.stringify(obj)]).size;
}

/**
 * Checks if a blog content object is too large
 */
export function validateBlogContentSize(blogData: any): RequestSizeValidationResult {
  const estimatedSize = estimateJsonSize(blogData);
  
  if (estimatedSize > MAX_REQUEST_SIZE) {
    const sizeInMB = (estimatedSize / (1024 * 1024)).toFixed(2);
    const maxSizeInMB = (MAX_REQUEST_SIZE / (1024 * 1024)).toFixed(0);
    
    return {
      isValid: false,
      size: estimatedSize,
      error: `Blog content (${sizeInMB}MB) exceeds the maximum allowed size of ${maxSizeInMB}MB. Please reduce content size, compress images, or split into multiple posts.`
    };
  }
  
  return { isValid: true, size: estimatedSize };
}

/**
 * Provides suggestions for reducing content size
 */
export function getContentSizeReductionTips(): string[] {
  return [
    'Compress images before uploading (use tools like TinyPNG or ImageOptim)',
    'Reduce image dimensions to web-appropriate sizes (max 1920px width)',
    'Split very long articles into multiple parts',
    'Remove unnecessary formatting or empty content blocks',
    'Use external links for large media files instead of embedding',
    'Consider using image galleries instead of inline images for multiple photos'
  ];
}
