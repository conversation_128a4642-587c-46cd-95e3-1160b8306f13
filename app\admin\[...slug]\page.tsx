'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function AdminCatchAll() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to main admin page if no valid route is found
    router.push('/admin');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          Admin Page Not Found
        </h1>
        <p className="text-gray-600 mb-6">
          The admin page you&apos;re looking for doesn&apos;t exist.
        </p>
        <p className="text-sm text-gray-500">
          Redirecting to admin dashboard...
        </p>
      </div>
    </div>
  );
}