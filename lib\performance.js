/**
 * Performance optimization utilities for Core Web Vitals improvements
 */

// Preload critical resources
export const preloadCriticalResources = () => {
  if (typeof window !== 'undefined') {
    // Preload critical fonts
    const fontLink = document.createElement('link');
    fontLink.rel = 'preload';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Jost:wght@300;400;500;600;700&display=swap';
    fontLink.as = 'style';
    fontLink.crossOrigin = 'anonymous';
    document.head.appendChild(fontLink);

    // Preload critical images
    const criticalImages = [
      '/images/hero/great-migration-serengeti-national-park.webp',
      '/images/common/swift-africa-safaris-best-tour-operator-icon.png'
    ];

    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = src;
      link.as = 'image';
      document.head.appendChild(link);
    });
  }
};

// Optimize images for better loading
export const optimizeImageLoading = () => {
  if (typeof window !== 'undefined') {
    // Add loading="lazy" to all images that don't have it
    const images = document.querySelectorAll('img:not([loading])');
    images.forEach(img => {
      if (!img.closest('[data-priority="true"]')) {
        img.loading = 'lazy';
      }
    });
  }
};

// Debounce function for performance
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Throttle function for scroll events
export const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// Measure Core Web Vitals
export const measureWebVitals = () => {
  if (typeof window !== 'undefined') {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS((metric) => {
        console.log('CLS:', metric);
      });
      getFID((metric) => {
        console.log('FID:', metric);
      });
      getFCP((metric) => {
        console.log('FCP:', metric);
      });
      getLCP((metric) => {
        console.log('LCP:', metric);
      });
      getTTFB((metric) => {
        console.log('TTFB:', metric);
      });
    }).catch((error) => {
      console.log('Web Vitals not available:', error);
    });
  }
};

// Optimize bundle loading (use this in React components)
export const createLazyComponent = (importFunc) => {
  return () =>
    importFunc().then(module => ({
      default: module.default || module
    }));
};

// Intersection Observer for lazy loading
export const createIntersectionObserver = (callback, options = {}) => {
  const defaultOptions = {
    root: null,
    rootMargin: '50px 0px',
    threshold: 0.1,
    ...options
  };

  if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
    return new IntersectionObserver(callback, defaultOptions);
  }
  return null;
};

// Performance monitoring
export const performanceMonitor = {
  mark: (name) => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.mark(name);
    }
  },
  
  measure: (name, startMark, endMark) => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.measure(name, startMark, endMark);
      const measure = performance.getEntriesByName(name)[0];
      console.log(`${name}: ${measure.duration}ms`);
      return measure.duration;
    }
    return 0;
  },

  clearMarks: () => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  }
};

// Resource hints
export const addResourceHints = () => {
  if (typeof window !== 'undefined') {
    // DNS prefetch for external domains
    const domains = [
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'mtqdzkhkpjutyvorwzjk.supabase.co'
    ];

    domains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });
  }
};

// Initialize performance optimizations
export const initPerformanceOptimizations = () => {
  if (typeof window !== 'undefined') {
    // Run optimizations after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        preloadCriticalResources();
        optimizeImageLoading();
        addResourceHints();
        measureWebVitals();
      });
    } else {
      preloadCriticalResources();
      optimizeImageLoading();
      addResourceHints();
      measureWebVitals();
    }
  }
};
