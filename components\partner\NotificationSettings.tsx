'use client';

import React from 'react';
import { 
  Bell, 
  Volume2, 
  VolumeX, 
  Clock, 
  Calendar, 
  DollarSign, 
  MessageSquare, 
  Package,
  Settings,
  Save
} from 'lucide-react';
import { useNotifications } from './NotificationProvider';

export default function NotificationSettings() {
  const { settings, updateSettings } = useNotifications();

  const handleToggle = (key: keyof typeof settings, value: boolean) => {
    updateSettings({ [key]: value });
  };

  const handleDurationChange = (duration: number) => {
    updateSettings({ toastDuration: duration });
  };

  const handleQuietHoursChange = (field: 'start' | 'end', value: string) => {
    updateSettings({
      quietHours: {
        ...settings.quietHours,
        [field]: value
      }
    });
  };

  const handleQuietHoursToggle = (enabled: boolean) => {
    updateSettings({
      quietHours: {
        ...settings.quietHours,
        enabled
      }
    });
  };

  const notificationTypes = [
    {
      key: 'showBookingNotifications' as const,
      title: 'Booking Notifications',
      description: 'Get notified about booking confirmations, modifications, and cancellations',
      icon: <Calendar className="h-5 w-5 text-blue-600" />
    },
    {
      key: 'showPaymentNotifications' as const,
      title: 'Payment Notifications',
      description: 'Receive updates about commission payments and transfers',
      icon: <DollarSign className="h-5 w-5 text-green-600" />
    },
    {
      key: 'showMessageNotifications' as const,
      title: 'Message Notifications',
      description: 'Get notified when admins send you messages',
      icon: <MessageSquare className="h-5 w-5 text-purple-600" />
    },
    {
      key: 'showPackageNotifications' as const,
      title: 'Package Notifications',
      description: 'Stay updated about new packages and price changes',
      icon: <Package className="h-5 w-5 text-orange-600" />
    },
    {
      key: 'showSystemNotifications' as const,
      title: 'System Notifications',
      description: 'Receive system updates, maintenance notices, and general information',
      icon: <Settings className="h-5 w-5 text-gray-600" />
    }
  ];

  const durationOptions = [
    { value: 3000, label: '3 seconds' },
    { value: 5000, label: '5 seconds' },
    { value: 8000, label: '8 seconds' },
    { value: 10000, label: '10 seconds' },
    { value: 0, label: 'Manual dismiss' }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2 flex items-center">
          <Bell className="h-5 w-5 mr-2 text-blue-600" />
          Notification Settings
        </h3>
        <p className="text-sm text-gray-600">
          Customize how and when you receive notifications
        </p>
      </div>

      {/* Master Toggle */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Bell className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <h4 className="font-medium text-blue-900">Enable Toast Notifications</h4>
              <p className="text-sm text-blue-700">Show popup notifications in the corner of your screen</p>
            </div>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.enableToasts}
              onChange={(e) => handleToggle('enableToasts', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      {/* Notification Types */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Notification Types</h4>
        <div className="space-y-3">
          {notificationTypes.map((type) => (
            <div key={type.key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-start">
                <div className="mr-3 mt-1">
                  {type.icon}
                </div>
                <div>
                  <h5 className="font-medium text-gray-900">{type.title}</h5>
                  <p className="text-sm text-gray-600">{type.description}</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings[type.key]}
                  onChange={(e) => handleToggle(type.key, e.target.checked)}
                  disabled={!settings.enableToasts}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:opacity-50"></div>
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Toast Duration */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Toast Duration</h4>
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-sm text-gray-600 mb-3">How long should notifications stay visible?</p>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {durationOptions.map((option) => (
              <button
                key={option.value}
                onClick={() => handleDurationChange(option.value)}
                disabled={!settings.enableToasts}
                className={`p-2 rounded-lg text-sm font-medium transition-colors ${
                  settings.toastDuration === option.value
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                } disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Sound Settings */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Sound Settings</h4>
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {settings.enableSounds ? (
                <Volume2 className="h-5 w-5 text-blue-600 mr-3" />
              ) : (
                <VolumeX className="h-5 w-5 text-gray-400 mr-3" />
              )}
              <div>
                <h5 className="font-medium text-gray-900">Notification Sounds</h5>
                <p className="text-sm text-gray-600">Play a sound when notifications appear</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.enableSounds}
                onChange={(e) => handleToggle('enableSounds', e.target.checked)}
                disabled={!settings.enableToasts}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:opacity-50"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Quiet Hours */}
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900">Quiet Hours</h4>
        <div className="bg-gray-50 rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-gray-600 mr-3" />
              <div>
                <h5 className="font-medium text-gray-900">Enable Quiet Hours</h5>
                <p className="text-sm text-gray-600">Disable notifications during specific hours</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.quietHours.enabled}
                onChange={(e) => handleQuietHoursToggle(e.target.checked)}
                disabled={!settings.enableToasts}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:opacity-50"></div>
            </label>
          </div>
          
          {settings.quietHours.enabled && (
            <div className="grid grid-cols-2 gap-4 pt-2">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                <input
                  type="time"
                  value={settings.quietHours.start}
                  onChange={(e) => handleQuietHoursChange('start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                <input
                  type="time"
                  value={settings.quietHours.end}
                  onChange={(e) => handleQuietHoursChange('end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save Notice */}
      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
        <div className="flex items-center">
          <Save className="h-5 w-5 text-green-600 mr-2" />
          <p className="text-sm text-green-800">
            Settings are automatically saved as you make changes
          </p>
        </div>
      </div>
    </div>
  );
}
