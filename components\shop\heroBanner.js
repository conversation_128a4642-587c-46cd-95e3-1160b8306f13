/* eslint-disable @next/next/no-img-element */


import React from 'react';
import { useRouter } from 'next/navigation';
import { useHeroBanner } from '../../hooks/useHeroBanner';
import { ArrowRight, ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import LazyImage from '../common/LazyImage';
import { products } from '../data/productData';

const HeroBanner = () => {
    const router = useRouter();
    const {
        currentProduct,
        totalProducts,
        currentIndex,
        isAnimating,
        nextSlide,
        prevSlide,
        goToSlide
    } = useHeroBanner(products);

    // Return null if no product or if discount is 0
    if (!currentProduct || currentProduct.discount === 0) return null;

    const discountedPrice = (
        currentProduct.price - (currentProduct.price * currentProduct.discount / 100)
    ).toFixed(2);

    return (
        <div className="relative h-[400px] bg-white overflow-hidden">
            {/* Background Mesh Gradient */}
            <div className="absolute inset-0">
                <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_120%,#d5e8ff,#ffffff)]" />
                <div className="absolute inset-0 opacity-30">
                    <div className="h-full w-full bg-[linear-gradient(45deg,#00000010_1px,transparent_1px),linear-gradient(-45deg,#00000010_1px,transparent_1px)]"
                        style={{ backgroundSize: '20px 20px' }} />
                </div>
            </div>

            {/* Navigation Arrows */}
            <div className="absolute inset-x-0 top-1/2 -translate-y-1/2 flex justify-between px-4 z-10">
                <button onClick={prevSlide}
                    className="p-2 rounded-full bg-white/80 backdrop-blur-sm border border-[var(--light-green)] shadow-lg
                                 hover:bg-[var(--btn)] hover:text-white transition-all duration-200 group">
                    <ChevronLeft className="w-5 h-5 group-hover:-translate-x-0.5 transition-transform" />
                </button>
                <button onClick={nextSlide}
                    className="p-2 rounded-full bg-white/80 backdrop-blur-sm border border-[var(--light-green)] shadow-lg
                                 hover:bg-[var(--btn)] hover:text-white transition-all duration-200 group">
                    <ChevronRight className="w-5 h-5 group-hover:translate-x-0.5 transition-transform" />
                </button>
            </div>

            {/* Main Content */}
            <div className="relative h-full flex items-center max-w-6xl mx-auto px-8">
                <div className="grid grid-cols-2 gap-8 items-center w-full">
                    {/* Left Content */}
                    <div className={`space-y-6 transition-all duration-500 
                        ${isAnimating ? 'opacity-0 -translate-x-8' : 'opacity-100 translate-x-0'}`}>
                        {/* Sale Tag */}
                        <div className="inline-flex items-center gap-2 bg-red-50 text-red-600 px-3 py-1 rounded-full text-sm font-medium">
                            <Clock className="w-4 h-4" />
                            <span>Limited Time Offer</span>
                            <span className="px-1.5 py-0.5 bg-red-100 rounded-full text-xs font-bold">
                                -{currentProduct.discount}%
                            </span>
                        </div>

                        <h1 className="text-4xl font-bold text-gray-900 leading-tight">
                            {currentProduct.title}
                        </h1>

                        <p className="text-gray-600 line-clamp-2">
                            {currentProduct.description}
                        </p>

                        <div className="flex items-baseline gap-3">
                            <span className="text-3xl font-bold text-gray-900">
                                ${discountedPrice}
                            </span>
                            <span className="text-lg text-gray-400 line-through">
                                ${currentProduct.price}
                            </span>
                        </div>

                        <button onClick={() => router.push(`/shop/product/${currentProduct.slug}`)}
                            className="group flex items-center gap-2 bg-[var(--btn)] text-white px-6 py-3 rounded-full
                                         hover:bg-[var(--light-green)] transition-all duration-300">
                            <span>Shop Now</span>
                            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                        </button>
                    </div>

                    {/* Right Content - Product Image */}
                    <div className={`relative transition-all duration-700 transform
                        ${isAnimating ? 'opacity-0 translate-x-8' : 'opacity-100 translate-x-0'}`}>
                        <div className="relative aspect-square rounded-2xl overflow-hidden bg-white/80 backdrop-blur-sm">
                            <LazyImage
                                src={currentProduct.images?.[0] || 'https://i.imgur.com/tQomaKM.jpg'}
                                alt={currentProduct.title}
                                className="w-full h-full object-contain p-6"
                                width={400}
                                height={400}
                                priority={true}
                                skeletonVariant="card"
                            />
                        </div>
                    </div>
                </div>
            </div>

            {/* Progress Dots */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-1.5">
                {Array.from({ length: totalProducts }).map((_, idx) => (
                    <button key={idx}
                        onClick={() => goToSlide(idx)}
                        className={`w-1.5 h-1.5 rounded-full transition-all duration-300
                                ${idx === currentIndex ? 'w-4 bg-[var(--btn)]' : 'bg-[var(--light-green)]'}`} />
                ))}
            </div>
        </div>
    );
};

export default HeroBanner;