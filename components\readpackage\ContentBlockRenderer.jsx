'use client';
import React from 'react';

const ContentBlockRenderer = ({ contentBlocks }) => {
  if (!contentBlocks || contentBlocks.length === 0) {
    return (
      <div className="text-gray-500 italic">
        No content available for this package.
      </div>
    );
  }

  const renderBlock = (block) => {
    switch (block.block_type) {
      case 'text':
      case 'paragraph':
        return (
          <div key={block.id} className="mb-6">
            <div
              className="text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: block.content }}
            />
          </div>
        );

      case 'heading':
      case 'heading1':
      case 'heading2':
      case 'heading3':
      case 'heading4':
      case 'heading5':
      case 'heading6':
        // Extract heading level from block type or use default
        let level = 2;
        if (block.block_type.startsWith('heading')) {
          const levelMatch = block.block_type.match(/heading(\d)/);
          if (levelMatch) {
            level = parseInt(levelMatch[1]);
          }
        }
        const HeadingTag = `h${level}`;
        return (
          <div key={block.id} className="mb-4">
            <HeadingTag className="font-bold text-[var(--primary-color)] mb-2">
              {block.content}
            </HeadingTag>
          </div>
        );

      case 'image':
        // Handle both old format (image_url/image_alt) and new format (content_data)
        const imageData = block.content_data || {};
        const imageSrc = imageData.src || block.image_url;
        const imageAlt = imageData.alt || block.image_alt || 'Package image';
        const imageCaption = imageData.caption || block.image_caption;

        if (!imageSrc) {
          return (
            <div key={block.id} className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <p className="text-gray-600 text-center">Image not available</p>
            </div>
          );
        }

        return (
          <div key={block.id} className="mb-6">
            <div className="rounded-lg overflow-hidden">
              <img
                src={imageSrc}
                alt={imageAlt}
                className="w-full h-auto object-cover"
              />
              {imageCaption && (
                <p className="text-sm text-gray-600 mt-2 italic text-center">
                  {imageCaption}
                </p>
              )}
            </div>
          </div>
        );

      case 'quote':
        return (
          <div key={block.id} className="mb-6">
            <blockquote className="border-l-4 border-[var(--accent)] pl-4 py-2 bg-gray-50 rounded-r-lg">
              <p className="text-gray-700 italic text-lg leading-relaxed">
                "{block.content}"
              </p>
            </blockquote>
          </div>
        );

      case 'divider':
        return (
          <div key={block.id} className="mb-6">
            <hr className="border-t-2 border-gray-200" />
          </div>
        );

      case 'list':
        const listItems = block.content_data?.items || [];
        const ListTag = block.content_data?.type === 'ordered' ? 'ol' : 'ul';
        return (
          <div key={block.id} className="mb-6">
            <ListTag className={`text-gray-700 leading-relaxed ${
              block.content_data?.type === 'ordered' ? 'list-decimal' : 'list-disc'
            } list-inside space-y-1`}>
              {listItems.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ListTag>
          </div>
        );

      case 'quote':
        return (
          <div key={block.id} className="mb-6">
            <blockquote className="border-l-4 border-[var(--primary-color)] pl-4 py-2 bg-gray-50 italic text-gray-700">
              {block.content}
            </blockquote>
          </div>
        );

      case 'divider':
        return (
          <div key={block.id} className="mb-6">
            <hr className="border-gray-300" />
          </div>
        );

      default:
        return (
          <div key={block.id} className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800">
              Unknown content block type: {block.block_type}
            </p>
          </div>
        );
    }
  };

  return (
    <div className="content-blocks">
      {contentBlocks
        .sort((a, b) => a.sort_order - b.sort_order)
        .map((block, index) => renderBlock({ ...block, id: block.id || `block-${index}` }))}
    </div>
  );
};

export default ContentBlockRenderer;
