import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch comments for a blog post (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // First, get the blog post ID
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('id')
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();

    if (postError || !post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Fetch comments (only approved comments for public)
    const { data: comments, error: commentsError } = await supabase
      .from('sas_blog_comments')
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        likes_count,
        created_at,
        parent_comment_id
      `)
      .eq('blog_post_id', post.id)
      .eq('status', 'approved')
      .is('deleted_at', null)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (commentsError) {
      console.error('Comments error:', commentsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch comments' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('sas_blog_comments')
      .select('*', { count: 'exact', head: true })
      .eq('blog_post_id', post.id)
      .eq('status', 'approved')
      .is('deleted_at', null);

    // Organize comments into threads (parent comments with their replies)
    const parentComments = comments?.filter(comment => !comment.parent_comment_id) || [];
    const replies = comments?.filter(comment => comment.parent_comment_id) || [];

    const threaded = parentComments.map(parent => ({
      ...parent,
      replies: replies.filter(reply => reply.parent_comment_id === parent.id)
    }));

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        comments: threaded,
        pagination: {
          currentPage: page,
          totalPages,
          totalComments: count || 0,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new comment (public endpoint)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['author_name', 'author_email', 'content'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.author_email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Get blog post ID
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('id')
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();

    if (postError || !post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Validate parent comment if provided
    if (body.parent_comment_id) {
      const { data: parentComment, error: parentError } = await supabase
        .from('sas_blog_comments')
        .select('id')
        .eq('id', body.parent_comment_id)
        .eq('blog_post_id', post.id)
        .is('deleted_at', null)
        .single();

      if (parentError || !parentComment) {
        return NextResponse.json(
          { success: false, error: 'Parent comment not found' },
          { status: 400 }
        );
      }
    }

    // Prepare comment data
    const commentData = {
      blog_post_id: post.id,
      parent_comment_id: body.parent_comment_id || null,
      author_name: body.author_name.trim(),
      author_email: body.author_email.trim().toLowerCase(),
      content: body.content.trim(),
      is_admin_reply: false,
      status: 'approved' // Auto-approve for now, can be changed to 'pending' for moderation
    };

    // Insert comment
    const { data: newComment, error: insertError } = await supabase
      .from('sas_blog_comments')
      .insert(commentData)
      .select(`
        id,
        author_name,
        author_email,
        content,
        is_admin_reply,
        likes_count,
        created_at,
        parent_comment_id
      `)
      .single();

    if (insertError) {
      console.error('Insert error:', insertError);
      return NextResponse.json(
        { success: false, error: 'Failed to create comment' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newComment,
      message: 'Comment created successfully'
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
