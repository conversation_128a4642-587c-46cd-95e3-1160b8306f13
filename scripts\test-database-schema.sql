-- Test script to check if author column has been removed
-- Run this in Supabase SQL Editor to verify the migration

-- Check current table structure
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'sas_blog_posts' 
ORDER BY ordinal_position;

-- This should NOT show an 'author' column if the migration was successful
-- Expected columns should be:
-- id, title, slug, description, hero_image_url, hero_image_alt, category, tags, status, published_at, created_at, updated_at, etc.
