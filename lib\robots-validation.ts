/**
 * Robots.txt Validation Utilities
 * Comprehensive validation for robots.txt implementation and SEO compliance
 */

import { defaultSEO } from './seo'

export interface RobotsValidationResult {
  isValid: boolean
  score: number
  issues: RobotsIssue[]
  recommendations: string[]
  crawlabilityScore: number
  securityScore: number
  performanceScore: number
  timestamp: string
}

export interface RobotsIssue {
  type: 'error' | 'warning' | 'info'
  category: 'syntax' | 'crawlability' | 'security' | 'performance' | 'seo'
  message: string
  details?: any
  fix?: string
  impact: 'high' | 'medium' | 'low'
}

/**
 * Validate robots.txt content and implementation
 */
export async function validateRobotsTxt(baseUrl: string = defaultSEO.siteUrl): Promise<RobotsValidationResult> {
  const issues: RobotsIssue[] = []
  let crawlabilityScore = 100
  let securityScore = 100
  let performanceScore = 100

  try {
    // Fetch robots.txt content
    const response = await fetch(`${baseUrl}/robots.txt`)
    
    if (!response.ok) {
      issues.push({
        type: 'error',
        category: 'crawlability',
        message: 'Robots.txt file not accessible',
        details: { status: response.status, statusText: response.statusText },
        fix: 'Ensure robots.txt is properly configured and accessible at /robots.txt',
        impact: 'high'
      })
      crawlabilityScore -= 50
    } else {
      const content = await response.text()
      
      // Validate content structure and rules
      await validateRobotsContent(content, issues, baseUrl)
      
      // Calculate scores based on issues
      const scores = calculateScores(issues)
      crawlabilityScore = scores.crawlability
      securityScore = scores.security
      performanceScore = scores.performance
    }

  } catch (error) {
    issues.push({
      type: 'error',
      category: 'crawlability',
      message: 'Failed to fetch robots.txt',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
      fix: 'Check network connectivity and robots.txt accessibility',
      impact: 'high'
    })
    crawlabilityScore = 0
  }

  const overallScore = Math.round((crawlabilityScore + securityScore + performanceScore) / 3)

  return {
    isValid: issues.filter(i => i.type === 'error').length === 0,
    score: overallScore,
    issues,
    recommendations: generateRobotsRecommendations(issues),
    crawlabilityScore,
    securityScore,
    performanceScore,
    timestamp: new Date().toISOString()
  }
}

/**
 * Validate robots.txt content structure and rules
 */
async function validateRobotsContent(content: string, issues: RobotsIssue[], baseUrl: string): Promise<void> {
  const lines = content.split('\n').map(line => line.trim())
  
  // Check for basic structure
  const hasUserAgent = lines.some(line => line.toLowerCase().startsWith('user-agent:'))
  if (!hasUserAgent) {
    issues.push({
      type: 'error',
      category: 'syntax',
      message: 'No User-agent directive found',
      fix: 'Add at least one User-agent directive to define crawling rules',
      impact: 'high'
    })
  }

  // Check for sitemap references
  const sitemapLines = lines.filter(line => line.toLowerCase().startsWith('sitemap:'))
  if (sitemapLines.length === 0) {
    issues.push({
      type: 'error',
      category: 'seo',
      message: 'No sitemap references found',
      fix: 'Add Sitemap: directives to help search engines discover your content',
      impact: 'high'
    })
  } else {
    // Validate sitemap URLs
    for (const sitemapLine of sitemapLines) {
      const sitemapUrl = sitemapLine.substring(8).trim() // Remove 'Sitemap:' prefix
      try {
        new URL(sitemapUrl)
        
        // Test sitemap accessibility
        try {
          const sitemapResponse = await fetch(sitemapUrl)
          if (!sitemapResponse.ok) {
            issues.push({
              type: 'warning',
              category: 'seo',
              message: `Sitemap not accessible: ${sitemapUrl}`,
              details: { status: sitemapResponse.status },
              fix: 'Ensure sitemap is accessible and returns valid XML',
              impact: 'medium'
            })
          }
        } catch {
          issues.push({
            type: 'warning',
            category: 'seo',
            message: `Cannot verify sitemap accessibility: ${sitemapUrl}`,
            fix: 'Check sitemap URL and server accessibility',
            impact: 'medium'
          })
        }
      } catch {
        issues.push({
          type: 'error',
          category: 'syntax',
          message: `Invalid sitemap URL: ${sitemapUrl}`,
          fix: 'Use absolute URLs for sitemap references',
          impact: 'medium'
        })
      }
    }
  }

  // Check for security considerations
  const hasAdminBlock = lines.some(line => 
    line.toLowerCase().includes('disallow:') && 
    (line.includes('/admin') || line.includes('/api'))
  )
  if (!hasAdminBlock) {
    issues.push({
      type: 'warning',
      category: 'security',
      message: 'Admin areas not blocked from crawling',
      fix: 'Add Disallow: /admin/ and Disallow: /api/ to prevent crawling of sensitive areas',
      impact: 'medium'
    })
  }

  // Check for crawl delay settings
  const hasCrawlDelay = lines.some(line => line.toLowerCase().startsWith('crawl-delay:'))
  if (!hasCrawlDelay) {
    issues.push({
      type: 'info',
      category: 'performance',
      message: 'No crawl delay specified',
      fix: 'Consider adding Crawl-delay: 1 to be respectful of server resources',
      impact: 'low'
    })
  }

  // Check for host directive
  const hasHost = lines.some(line => line.toLowerCase().startsWith('host:'))
  if (!hasHost) {
    issues.push({
      type: 'info',
      category: 'seo',
      message: 'No host directive specified',
      fix: 'Add Host: directive to specify the preferred domain',
      impact: 'low'
    })
  }

  // Check for important content accessibility
  const allowRules = lines.filter(line => line.toLowerCase().startsWith('allow:'))
  const disallowRules = lines.filter(line => line.toLowerCase().startsWith('disallow:'))
  
  const importantPaths = ['/package/', '/blog/', '/about', '/contact']
  for (const path of importantPaths) {
    const isBlocked = disallowRules.some(rule => {
      const disallowPath = rule.substring(9).trim() // Remove 'Disallow:' prefix
      return path.startsWith(disallowPath) || disallowPath === '/'
    })
    
    const isExplicitlyAllowed = allowRules.some(rule => {
      const allowPath = rule.substring(6).trim() // Remove 'Allow:' prefix
      return path.startsWith(allowPath)
    })
    
    if (isBlocked && !isExplicitlyAllowed) {
      issues.push({
        type: 'warning',
        category: 'crawlability',
        message: `Important content path may be blocked: ${path}`,
        fix: `Add Allow: ${path} to ensure important content is crawlable`,
        impact: 'high'
      })
    }
  }

  // Check for overly restrictive rules
  const hasRootDisallow = disallowRules.some(rule => {
    const disallowPath = rule.substring(9).trim()
    return disallowPath === '/'
  })
  
  if (hasRootDisallow) {
    const hasSpecificAllows = allowRules.length > 0
    if (!hasSpecificAllows) {
      issues.push({
        type: 'error',
        category: 'crawlability',
        message: 'Root path blocked without specific Allow rules',
        fix: 'Add specific Allow: rules for important content when using Disallow: /',
        impact: 'high'
      })
    }
  }

  // Check for syntax errors
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    if (line === '' || line.startsWith('#')) continue // Skip empty lines and comments
    
    const validDirectives = [
      'user-agent:', 'disallow:', 'allow:', 'crawl-delay:', 
      'sitemap:', 'host:', 'request-rate:', 'visit-time:'
    ]
    
    const isValidDirective = validDirectives.some(directive => 
      line.toLowerCase().startsWith(directive)
    )
    
    if (!isValidDirective) {
      issues.push({
        type: 'warning',
        category: 'syntax',
        message: `Unrecognized directive on line ${i + 1}: ${line}`,
        fix: 'Remove or correct unrecognized directives',
        impact: 'low'
      })
    }
  }

  // Check for international SEO considerations
  const hasHreflangSitemap = sitemapLines.some(line => 
    line.includes('hreflang-sitemap.xml')
  )
  if (!hasHreflangSitemap) {
    issues.push({
      type: 'info',
      category: 'seo',
      message: 'No hreflang sitemap reference found',
      fix: 'Add Sitemap: reference to hreflang-sitemap.xml for international SEO',
      impact: 'medium'
    })
  }
}

/**
 * Calculate scores based on issues
 */
function calculateScores(issues: RobotsIssue[]): {
  crawlability: number
  security: number
  performance: number
} {
  let crawlability = 100
  let security = 100
  let performance = 100

  for (const issue of issues) {
    const penalty = issue.impact === 'high' ? 20 : issue.impact === 'medium' ? 10 : 5
    const errorMultiplier = issue.type === 'error' ? 2 : issue.type === 'warning' ? 1 : 0.5

    const actualPenalty = penalty * errorMultiplier

    switch (issue.category) {
      case 'crawlability':
      case 'seo':
        crawlability -= actualPenalty
        break
      case 'security':
        security -= actualPenalty
        break
      case 'performance':
        performance -= actualPenalty
        break
      case 'syntax':
        crawlability -= actualPenalty * 0.5 // Syntax issues affect crawlability
        break
    }
  }

  return {
    crawlability: Math.max(0, crawlability),
    security: Math.max(0, security),
    performance: Math.max(0, performance)
  }
}

/**
 * Generate recommendations based on issues
 */
function generateRobotsRecommendations(issues: RobotsIssue[]): string[] {
  const recommendations: string[] = []
  
  const categories = issues.reduce((acc, issue) => {
    if (!acc[issue.category]) acc[issue.category] = []
    acc[issue.category].push(issue)
    return acc
  }, {} as Record<string, RobotsIssue[]>)

  if (categories.crawlability || categories.seo) {
    recommendations.push('Ensure important content paths are crawlable and include comprehensive sitemap references')
  }

  if (categories.security) {
    recommendations.push('Block sensitive areas like /admin/, /api/, and /private/ from search engine crawling')
  }

  if (categories.performance) {
    recommendations.push('Implement appropriate crawl delays to balance SEO with server performance')
  }

  if (categories.syntax) {
    recommendations.push('Fix syntax errors and use only recognized robots.txt directives')
  }

  // High-impact recommendations
  const highImpactIssues = issues.filter(i => i.impact === 'high')
  if (highImpactIssues.length > 0) {
    recommendations.push('Address high-impact issues immediately to prevent SEO problems')
  }

  // Error-specific recommendations
  const errors = issues.filter(i => i.type === 'error')
  if (errors.length > 0) {
    recommendations.push('Fix critical errors that prevent proper robots.txt functionality')
  }

  if (recommendations.length === 0) {
    recommendations.push('Excellent! Your robots.txt implementation follows SEO best practices')
  }

  return recommendations
}

/**
 * Test robots.txt against specific user agents
 */
export async function testRobotsForUserAgent(
  userAgent: string, 
  testPaths: string[], 
  baseUrl: string = defaultSEO.siteUrl
): Promise<{
  userAgent: string
  results: Array<{
    path: string
    allowed: boolean
    rule?: string
  }>
}> {
  const results = []
  
  try {
    const response = await fetch(`${baseUrl}/robots.txt`)
    const content = await response.text()
    
    // Simple robots.txt parser for testing
    const rules = parseRobotsRules(content, userAgent)
    
    for (const path of testPaths) {
      const allowed = isPathAllowed(path, rules)
      results.push({
        path,
        allowed,
        rule: rules.find(r => path.startsWith(r.pattern))?.directive
      })
    }
  } catch (error) {
    // If robots.txt is not accessible, assume all paths are allowed
    for (const path of testPaths) {
      results.push({
        path,
        allowed: true,
        rule: 'default (robots.txt not accessible)'
      })
    }
  }

  return {
    userAgent,
    results
  }
}

// Helper functions for robots.txt parsing
function parseRobotsRules(content: string, userAgent: string): Array<{
  directive: string
  pattern: string
}> {
  const lines = content.split('\n').map(line => line.trim())
  const rules = []
  let currentUserAgent = ''
  let inRelevantSection = false

  for (const line of lines) {
    if (line.toLowerCase().startsWith('user-agent:')) {
      const agent = line.substring(11).trim()
      currentUserAgent = agent
      inRelevantSection = agent === '*' || agent.toLowerCase() === userAgent.toLowerCase()
    } else if (inRelevantSection && (line.toLowerCase().startsWith('allow:') || line.toLowerCase().startsWith('disallow:'))) {
      const directive = line.toLowerCase().startsWith('allow:') ? 'allow' : 'disallow'
      const pattern = line.substring(directive === 'allow' ? 6 : 9).trim()
      rules.push({ directive, pattern })
    }
  }

  return rules
}

function isPathAllowed(path: string, rules: Array<{ directive: string; pattern: string }>): boolean {
  let allowed = true // Default is to allow
  
  // Process rules in order, with more specific rules taking precedence
  for (const rule of rules) {
    if (path.startsWith(rule.pattern) || rule.pattern === '/') {
      allowed = rule.directive === 'allow'
    }
  }
  
  return allowed
}
