'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Calendar, 
  Users, 
  DollarSign, 
  Eye, 
  Edit, 
  Plus,
  CheckCircle,
  Clock,
  XCircle,
  MapPin
} from 'lucide-react';
import Link from 'next/link';

interface Booking {
  id: string;
  packageName: string;
  packageId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  travelers: number;
  totalAmount: number;
  commission: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  paymentStatus: 'paid' | 'pending' | 'failed';
  bookingDate: string;
  travelDate: string;
  duration: string;
  destination: string;
  specialRequests?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerBookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [paymentFilter, setPaymentFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockBookings: Booking[] = [
      {
        id: 'SA-B2B-001',
        packageName: 'Serengeti & Ngorongoro 5-Day Safari',
        packageId: 'pkg-001',
        customerName: 'Michael Johnson',
        customerEmail: '<EMAIL>',
        customerPhone: '****** 123 4567',
        travelers: 4,
        totalAmount: 4800,
        commission: 720,
        status: 'confirmed',
        paymentStatus: 'paid',
        bookingDate: '2024-12-15',
        travelDate: '2025-01-20',
        duration: '5 Days',
        destination: 'Serengeti & Ngorongoro',
        specialRequests: 'Vegetarian meals for 2 travelers'
      },
      {
        id: 'SA-B2B-002',
        packageName: 'Kilimanjaro Climbing Adventure',
        packageId: 'pkg-002',
        customerName: 'Sarah Williams',
        customerEmail: '<EMAIL>',
        customerPhone: '+44 20 1234 5678',
        travelers: 2,
        totalAmount: 3200,
        commission: 384,
        status: 'pending',
        paymentStatus: 'pending',
        bookingDate: '2024-12-18',
        travelDate: '2025-02-10',
        duration: '7 Days',
        destination: 'Mount Kilimanjaro'
      },
      {
        id: 'SA-B2B-003',
        packageName: 'Tarangire Day Trip',
        packageId: 'pkg-003',
        customerName: 'David Brown',
        customerEmail: '<EMAIL>',
        customerPhone: '****** 987 6543',
        travelers: 6,
        totalAmount: 1200,
        commission: 180,
        status: 'completed',
        paymentStatus: 'paid',
        bookingDate: '2024-12-10',
        travelDate: '2024-12-25',
        duration: '1 Day',
        destination: 'Tarangire National Park'
      },
      {
        id: 'SA-B2B-004',
        packageName: 'Maasai Cultural Experience',
        packageId: 'pkg-004',
        customerName: 'Emma Davis',
        customerEmail: '<EMAIL>',
        customerPhone: '+61 2 9876 5432',
        travelers: 3,
        totalAmount: 900,
        commission: 162,
        status: 'cancelled',
        paymentStatus: 'failed',
        bookingDate: '2024-12-05',
        travelDate: '2024-12-30',
        duration: '2 Days',
        destination: 'Maasai Village',
        specialRequests: 'Photography permission required'
      }
    ];
    setBookings(mockBookings);
  }, []);

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch = booking.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.packageName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.destination.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;
    const matchesPayment = paymentFilter === 'all' || booking.paymentStatus === paymentFilter;
    return matchesSearch && matchesStatus && matchesPayment;
  });

  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const bookingStats = {
    total: bookings.length,
    confirmed: bookings.filter(b => b.status === 'confirmed').length,
    pending: bookings.filter(b => b.status === 'pending').length,
    totalRevenue: bookings.reduce((sum, b) => sum + b.totalAmount, 0),
    totalCommission: bookings.reduce((sum, b) => sum + b.commission, 0)
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">My Bookings</h1>
          <p className="text-gray-600">Manage all your customer bookings and track their status</p>
        </div>
        <Link href="/partner/bookings/create">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <Plus className="h-4 w-4 mr-2" />
            New Booking
          </button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Calendar className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-xl font-bold text-gray-900">{bookingStats.total}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Confirmed</p>
              <p className="text-xl font-bold text-gray-900">{bookingStats.confirmed}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Clock className="h-6 w-6 text-yellow-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-xl font-bold text-gray-900">{bookingStats.pending}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <DollarSign className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(bookingStats.totalRevenue)}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <DollarSign className="h-6 w-6 text-purple-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Commission</p>
              <p className="text-xl font-bold text-gray-900">{formatCurrency(bookingStats.totalCommission)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search bookings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="confirmed">Confirmed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
          </div>
          <div>
            <select
              value={paymentFilter}
              onChange={(e) => setPaymentFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Payments</option>
              <option value="paid">Paid</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          {filteredBookings.length} booking{filteredBookings.length !== 1 ? 's' : ''} found
        </div>
      </div>

      {/* Bookings Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Booking ID</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Package</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Customer</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Amount</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Status</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Travel Date</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentBookings.map((booking) => (
                <tr key={booking.id} className="border-b border-gray-200 hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div>
                      <div className="font-medium text-gray-900">{booking.id}</div>
                      <div className="text-sm text-gray-500">
                        Booked: {new Date(booking.bookingDate).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div>
                      <div className="font-medium text-gray-900 line-clamp-1">{booking.packageName}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {booking.destination}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {booking.duration}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div>
                      <div className="font-medium text-gray-900">{booking.customerName}</div>
                      <div className="text-sm text-gray-500">{booking.customerEmail}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {booking.travelers} traveler{booking.travelers !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div>
                      <div className="font-semibold text-gray-900">{formatCurrency(booking.totalAmount)}</div>
                      <div className="text-sm text-green-600">
                        Commission: {formatCurrency(booking.commission)}
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(booking.paymentStatus)}`}>
                        {booking.paymentStatus}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                      {getStatusIcon(booking.status)}
                      <span className="ml-1 capitalize">{booking.status}</span>
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-gray-900">
                      {new Date(booking.travelDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center space-x-2">
                      <Link href={`/partner/bookings/${booking.id}`}>
                        <button className="p-1 text-blue-600 hover:bg-blue-50 rounded transition-colors">
                          <Eye className="h-4 w-4" />
                        </button>
                      </Link>
                      <Link href={`/partner/bookings/${booking.id}/edit`}>
                        <button className="p-1 text-green-600 hover:bg-green-50 rounded transition-colors">
                          <Edit className="h-4 w-4" />
                        </button>
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredBookings.length)} of {filteredBookings.length} bookings
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-1 border rounded-md ${
                    currentPage === page
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              ))}
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
