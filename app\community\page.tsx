import React from 'react';
import type { Metadata } from 'next';
import Navbar from '@/components/header';
import HeroSection from '@/components/community/HeroSection';
import ChangingLives from '@/components/community/ChangingLives';
import FeaturedCampaigns from '@/components/community/FeaturedCampaigns';
import CommunityProjects from '@/components/community/CommunityProjects';
import FloatingDonateButton from '@/components/community/FloatingDonateButton';
import TourismFooter from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa’s Impact on Local Communities',
  description: 'See how your journey helps uplift African communities. Learn about our sustainable tourism model, conservation efforts & local partnerships.',
  url: '/community',
  keywords: [
    'community impact Africa',
    'What’s the most sustainable way to travel in Africa?',
    'Can I support local Maasai communities through eco-tourism?',
    'What community tourism projects can I visit in Rwanda?',
    'Can I support conservation efforts while visiting Tanzania?',
    'Can I visit Rwanda and support local communities?',
    'conservation projects East Africa',
    'sustainable tourism Rwanda',
    'community development Uganda',
    'wildlife conservation Tanzania',
    'education initiatives Africa',
    'eco-tourism community projects',
    'African conservation efforts',
    'community based tourism',
    'sustainable travel Africa',
    'conservation volunteering',
    'community support programs',
    'environmental protection Africa',
    'local community empowerment',
    'responsible tourism Africa'
  ],
  type: 'website'
});

const CommunityPage = () => {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Community Impact', url: '/community' }
  ]);

  const communityPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Swift Africa Safaris - Community Impact & Conservation Projects',
    description: 'Join our mission to transform communities through sustainable tourism. Support conservation projects, education initiatives, and community development across East Africa.',
    url: `${defaultSEO.siteUrl}/community`,
    keywords: [
      'community impact Africa',
      'conservation projects East Africa',
      'sustainable tourism Rwanda',
      'community development Uganda',
      'wildlife conservation Tanzania',
      'education initiatives Africa',
      'eco-tourism community projects',
      'African conservation efforts',
      'community-based tourism',
      'sustainable travel Africa',
      'conservation volunteering',
      'community support programs',
      'environmental protection Africa',
      'local community empowerment',
      'responsible tourism Africa'
    ],
    mainEntity: {
      '@type': 'Organization',
      name: 'Swift Africa Safaris Community Impact Program',
      description: 'Join our mission to transform communities through sustainable tourism. Support conservation projects, education initiatives, and community development across East Africa.',
      foundingDate: '2014',
      mission: 'To create positive impact in African communities through sustainable tourism and conservation efforts',
      focus: [
        'Wildlife Conservation',
        'Community Education',
        'Sustainable Tourism',
        'Environmental Protection',
        'Local Empowerment'
      ],
      serviceArea: {
        '@type': 'Place',
        name: 'East Africa',
        description: 'Rwanda, Tanzania, Uganda, Kenya, South Africa'
      },
      program: [
        {
          '@type': 'EducationalOrganization',
          name: 'Community Education Initiative',
          description: 'Supporting local schools and educational programs'
        },
        {
          '@type': 'EnvironmentalOrganization',
          name: 'Wildlife Conservation Program',
          description: 'Protecting endangered species and their habitats'
        },
        {
          '@type': 'Organization',
          name: 'Community Development Projects',
          description: 'Empowering local communities through sustainable development'
        }
      ]
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, communityPageSchema]} />
      <Navbar />
      <main>
        <HeroSection />
        <FloatingDonateButton />
        <ChangingLives />
        <FeaturedCampaigns />
        <CommunityProjects />
      </main>
      <TourismFooter />
    </div>
  );
};

export default CommunityPage;