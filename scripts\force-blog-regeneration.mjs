import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function forceBlogRegeneration() {
  console.log('🔄 FORCING BLOG POST REGENERATION')
  console.log('=' .repeat(40))

  try {
    // Get all published blog posts
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, updated_at')
      .eq('status', 'published')
      .is('deleted_at', null)

    if (error) {
      console.error('❌ Error fetching posts:', error.message)
      return
    }

    console.log(`📝 Found ${posts?.length || 0} published posts`)

    if (!posts || posts.length === 0) {
      console.log('❌ No posts to regenerate')
      return
    }

    // Update the updated_at timestamp for all posts to force ISR regeneration
    const currentTime = new Date().toISOString()
    
    console.log('\n🔄 Updating timestamps to force ISR regeneration...')
    
    for (const post of posts) {
      const { error: updateError } = await supabase
        .from('sas_blog_posts')
        .update({
          updated_at: currentTime
        })
        .eq('id', post.id)

      if (updateError) {
        console.log(`❌ Failed to update "${post.title}": ${updateError.message}`)
      } else {
        console.log(`✅ Updated "${post.title}" (${post.slug})`)
      }
    }

    console.log('\n🎯 SPECIAL FOCUS: "volcanoes-national-park"')
    
    // Double-check the problematic post
    const { data: volcanoesPost, error: volcanoesError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', 'volcanoes-national-park')
      .single()

    if (volcanoesError) {
      console.log('❌ Could not fetch volcanoes post:', volcanoesError.message)
    } else {
      console.log('✅ Volcanoes post details:')
      console.log(`   Title: "${volcanoesPost.title}"`)
      console.log(`   Status: ${volcanoesPost.status}`)
      console.log(`   Published: ${volcanoesPost.published_at}`)
      console.log(`   Updated: ${volcanoesPost.updated_at}`)
      console.log(`   Deleted: ${volcanoesPost.deleted_at || 'No'}`)
      console.log(`   Hero Image: ${volcanoesPost.hero_image_url ? 'YES' : 'NO'}`)
      
      // Check content blocks
      const { data: blocks, error: blocksError } = await supabase
        .from('sas_blog_content_blocks')
        .select('count')
        .eq('blog_post_id', volcanoesPost.id)

      if (!blocksError) {
        console.log(`   Content Blocks: ${blocks?.length || 0}`)
      }
    }

    console.log('\n📋 REGENERATION SUMMARY:')
    console.log(`   - Posts updated: ${posts.length}`)
    console.log(`   - Timestamp: ${currentTime}`)
    console.log(`   - ISR will regenerate pages within 5 minutes`)

    console.log('\n💡 NEXT STEPS:')
    console.log('   1. Wait 5-10 minutes for Vercel deployment to complete')
    console.log('   2. Clear browser cache (Ctrl+F5 or Cmd+Shift+R)')
    console.log('   3. Try accessing the blog post again')
    console.log('   4. If still 404, check Vercel deployment logs')

    console.log('\n🔗 TEST URLS:')
    console.log('   Local: http://localhost:3000/blog/volcanoes-national-park')
    console.log('   Production: https://sas-website-app.vercel.app/blog/volcanoes-national-park')

    console.log('\n✅ Force regeneration completed!')

  } catch (error) {
    console.error('❌ Fatal error during regeneration:', error)
  }
}

// Run the regeneration
forceBlogRegeneration()
