/* eslint-disable @typescript-eslint/no-unused-expressions */
import React, { useState } from 'react';

const ColorSelector = ({ colors, selectedColor, onColorChange }) => {
    const defaultColors = [
        { name: 'Pink', value: '#FF6B6B', gradient: 'bg-gradient-to-br from-pink-400 to-red-400' },
        { name: 'Space Gray', value: '#4A5568', gradient: 'bg-gradient-to-br from-gray-600 to-gray-800' },
        { name: 'Green', value: '#48BB78', gradient: 'bg-gradient-to-br from-green-400 to-green-600' },
        { name: 'Silver', value: '#E2E8F0', gradient: 'bg-gradient-to-br from-gray-200 to-gray-400' },
        { name: 'Sky Blue', value: '#4299E1', gradient: 'bg-gradient-to-br from-blue-400 to-blue-600' }
    ];

    const [selected, setSelected] = useState(selectedColor || defaultColors[0].name);
    const colorOptions = colors || defaultColors;

    const handleColorChange = (color) => {
        setSelected(color.name);
        onColorChange && onColorChange(color);
    };

    return (
        <div className="space-y-3">
            <h3 className="text-base font-medium text-gray-900">Choose a Color</h3>
            <div className="flex items-center space-x-3">
                {colorOptions.map((color) => (
                    <button
                        key={color.name}
                        onClick={() => handleColorChange(color)}
                        className={`relative w-8 h-8 rounded-full transition-all duration-200 ${color.gradient} ${
                            selected === color.name
                                ? 'ring-2 ring-offset-2 ring-gray-400 scale-110'
                                : 'hover:scale-105 hover:ring-2 hover:ring-offset-1 hover:ring-gray-300'
                        }`}
                        title={color.name}
                    >
                        {selected === color.name && (
                            <div className="absolute inset-0 flex items-center justify-center">
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                            </div>
                        )}
                    </button>
                ))}
            </div>
        </div>
    );
};

export default ColorSelector;