-- Setup Mini Package Management Database Schema for Swift Africa Safaris
-- This script creates the necessary tables for mini package management system
-- All tables use 'sas_' prefix as required

-- =====================================================
-- MINI PACKAGES TABLE - Main mini package information
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_mini_packages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    difficulty TEXT NOT NULL, -- Accepts custom values (Easy, Moderate, Hard, Challenging)
    category TEXT NOT NULL, -- Accepts custom values (Wildlife, Adventure, Cultural, Beach, Safari, Nature, Luxury)
    location TEXT NOT NULL,
    duration TEXT,
    
    -- Pricing for different package types
    pricing_solo DECIMAL(10,2) DEFAULT 0,
    pricing_honeymoon DECIMAL(10,2) DEFAULT 0,
    pricing_family DECIMAL(10,2) DEFAULT 0,
    pricing_group DECIMAL(10,2) DEFAULT 0,
    
    -- Status and visibility
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    
    -- Main images
    image_url TEXT,
    image_alt TEXT, -- MANDATORY alt text for main image
    hero_image_url TEXT,
    hero_image_alt TEXT, -- MANDATORY alt text for hero image
    
    -- SEO and metadata fields (user-inputted)
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[], -- Array of keywords
    og_title TEXT,
    og_description TEXT,
    og_image_url TEXT,
    canonical_url TEXT,
    robots_index TEXT DEFAULT 'index' CHECK (robots_index IN ('index', 'noindex')),
    robots_follow TEXT DEFAULT 'follow' CHECK (robots_follow IN ('follow', 'nofollow')),
    
    -- Schema.org structured data (JSON)
    schema_data JSONB,
    
    -- Mini package highlights
    highlights TEXT[], -- Array of highlight strings
    
    -- What to pack list
    packing_list TEXT[], -- Array of packing items
    
    -- Includes and excludes
    includes TEXT[], -- Array of included items
    excludes TEXT[], -- Array of excluded items
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- MINI PACKAGE CONTENT BLOCKS - Block-based content editor
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_mini_package_content_blocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
    block_type TEXT NOT NULL CHECK (block_type IN (
        'paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6',
        'list', 'quote', 'code', 'image', 'divider', 'bulleted-list', 'numbered-list'
    )),
    content TEXT, -- For text-based blocks
    content_data JSONB, -- For complex blocks (images, lists, etc.)
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    -- Image-specific fields (when block_type = 'image')
    image_url TEXT,
    image_alt TEXT, -- MANDATORY for all images
    image_caption TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MINI PACKAGE ITINERARY - Hour-by-hour itinerary
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_mini_package_itinerary (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
    hour_number INTEGER NOT NULL, -- Hours instead of days for mini packages
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MINI PACKAGE IMAGES - Additional mini package gallery images
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_mini_package_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_alt TEXT NOT NULL, -- MANDATORY alt text
    caption TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Mini packages indexes
CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_slug ON public.sas_mini_packages(slug);
CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_status ON public.sas_mini_packages(status);
CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_category ON public.sas_mini_packages(category);
CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_location ON public.sas_mini_packages(location);
CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_published_at ON public.sas_mini_packages(published_at);

-- Content blocks indexes
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_content_blocks_mini_package_id ON public.sas_mini_package_content_blocks(mini_package_id);
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_content_blocks_sort_order ON public.sas_mini_package_content_blocks(sort_order);

-- Itinerary indexes
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_itinerary_mini_package_id ON public.sas_mini_package_itinerary(mini_package_id);
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_itinerary_sort_order ON public.sas_mini_package_itinerary(sort_order);
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_itinerary_hour_number ON public.sas_mini_package_itinerary(hour_number);

-- Images indexes
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_images_mini_package_id ON public.sas_mini_package_images(mini_package_id);
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_images_sort_order ON public.sas_mini_package_images(sort_order);
CREATE INDEX IF NOT EXISTS idx_sas_mini_package_images_is_featured ON public.sas_mini_package_images(is_featured);

-- =====================================================
-- TRIGGERS FOR AUTO-UPDATING TIMESTAMPS
-- =====================================================

-- Mini packages updated_at trigger
CREATE OR REPLACE FUNCTION update_sas_mini_packages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_sas_mini_packages_updated_at
    BEFORE UPDATE ON public.sas_mini_packages
    FOR EACH ROW
    EXECUTE FUNCTION update_sas_mini_packages_updated_at();

-- Content blocks updated_at trigger
CREATE OR REPLACE FUNCTION update_sas_mini_package_content_blocks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_sas_mini_package_content_blocks_updated_at
    BEFORE UPDATE ON public.sas_mini_package_content_blocks
    FOR EACH ROW
    EXECUTE FUNCTION update_sas_mini_package_content_blocks_updated_at();

-- Itinerary updated_at trigger
CREATE OR REPLACE FUNCTION update_sas_mini_package_itinerary_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_sas_mini_package_itinerary_updated_at
    BEFORE UPDATE ON public.sas_mini_package_itinerary
    FOR EACH ROW
    EXECUTE FUNCTION update_sas_mini_package_itinerary_updated_at();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.sas_mini_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_mini_package_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_mini_package_itinerary ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_mini_package_images ENABLE ROW LEVEL SECURITY;

-- Public read access for published mini packages
CREATE POLICY "Public can read published mini packages" ON public.sas_mini_packages
    FOR SELECT USING (status = 'published');

-- Public read access for content blocks of published mini packages
CREATE POLICY "Public can read content blocks of published mini packages" ON public.sas_mini_package_content_blocks
    FOR SELECT USING (
        mini_package_id IN (
            SELECT id FROM public.sas_mini_packages WHERE status = 'published'
        )
    );

-- Public read access for itinerary of published mini packages
CREATE POLICY "Public can read itinerary of published mini packages" ON public.sas_mini_package_itinerary
    FOR SELECT USING (
        mini_package_id IN (
            SELECT id FROM public.sas_mini_packages WHERE status = 'published'
        )
    );

-- Public read access for images of published mini packages
CREATE POLICY "Public can read images of published mini packages" ON public.sas_mini_package_images
    FOR SELECT USING (
        mini_package_id IN (
            SELECT id FROM public.sas_mini_packages WHERE status = 'published'
        )
    );

-- Admin full access (authenticated users with admin role)
CREATE POLICY "Admins have full access to mini packages" ON public.sas_mini_packages
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Admins have full access to mini package content blocks" ON public.sas_mini_package_content_blocks
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Admins have full access to mini package itinerary" ON public.sas_mini_package_itinerary
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Admins have full access to mini package images" ON public.sas_mini_package_images
    FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- STORAGE BUCKETS FOR MINI PACKAGE IMAGES
-- =====================================================

-- Create storage bucket for mini package images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'sas-mini-package-images',
    'sas-mini-package-images',
    true,
    52428800, -- 50MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- Create storage bucket for mini package content
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'sas-mini-package-content',
    'sas-mini-package-content',
    true,
    52428800, -- 50MB limit
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
) ON CONFLICT (id) DO NOTHING;

-- Storage policies for mini package images bucket
CREATE POLICY "Public can view mini package images" ON storage.objects
    FOR SELECT USING (bucket_id = 'sas-mini-package-images');

CREATE POLICY "Authenticated users can upload mini package images" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'sas-mini-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update mini package images" ON storage.objects
    FOR UPDATE USING (bucket_id = 'sas-mini-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete mini package images" ON storage.objects
    FOR DELETE USING (bucket_id = 'sas-mini-package-images' AND auth.role() = 'authenticated');

-- Storage policies for mini package content bucket
CREATE POLICY "Public can view mini package content" ON storage.objects
    FOR SELECT USING (bucket_id = 'sas-mini-package-content');

CREATE POLICY "Authenticated users can upload mini package content" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'sas-mini-package-content' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update mini package content" ON storage.objects
    FOR UPDATE USING (bucket_id = 'sas-mini-package-content' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete mini package content" ON storage.objects
    FOR DELETE USING (bucket_id = 'sas-mini-package-content' AND auth.role() = 'authenticated');

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample mini package data
INSERT INTO public.sas_mini_packages (
    title, slug, difficulty, category, location, duration,
    pricing_solo, pricing_honeymoon, pricing_family, pricing_group,
    status, image_url, image_alt, hero_image_url, hero_image_alt,
    seo_title, seo_description, seo_keywords,
    highlights, packing_list, includes, excludes
) VALUES (
    'The Last Login - Digital Detox Safari Experience',
    'the-last-login',
    'Easy',
    'Cultural',
    'Uganda',
    '6 Hours',
    150.00, 280.00, 120.00, 100.00,
    'published',
    'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5',
    'Digital detox safari experience in Uganda',
    'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5',
    'Digital detox safari experience - disconnect to reconnect',
    'The Last Login - Digital Detox Safari | Swift Africa Safaris',
    'Experience a transformative 6-hour digital detox safari in Uganda. Disconnect from technology and reconnect with nature and yourself.',
    ARRAY['digital detox', 'safari', 'uganda', 'wellness', 'nature', 'mindfulness'],
    ARRAY[
        'Complete digital disconnection experience',
        'Guided nature meditation sessions',
        'Wildlife observation without distractions',
        'Traditional storytelling by local guides',
        'Organic lunch prepared with local ingredients',
        'Certificate of digital detox completion'
    ],
    ARRAY[
        'Comfortable walking shoes',
        'Hat and sunglasses',
        'Water bottle (provided)',
        'Light jacket for evening',
        'Notebook and pen (analog only!)',
        'Open mind and willingness to disconnect'
    ],
    ARRAY[
        'Professional guide',
        'All meals and refreshments',
        'Transportation within the park',
        'Digital detox certificate',
        'Nature meditation sessions',
        'Traditional storytelling session'
    ],
    ARRAY[
        'Personal electronic devices (intentionally excluded)',
        'International flights',
        'Accommodation (day trip only)',
        'Personal shopping',
        'Tips and gratuities'
    ]
) ON CONFLICT (slug) DO NOTHING;

-- Insert sample itinerary for the mini package
INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    1,
    'Digital Surrender & Welcome (Hour 1)',
    'Begin your transformative journey by surrendering all digital devices in our secure digital vault. Meet your guide and fellow participants, receive your analog journal, and set intentions for your digital detox experience.',
    1
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    2,
    'Nature Immersion Walk (Hour 2)',
    'Embark on a guided nature walk through pristine wilderness. Learn to observe wildlife and natural phenomena without the urge to photograph or share. Practice mindful walking and deep breathing exercises.',
    2
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    3,
    'Meditation & Reflection (Hour 3)',
    'Participate in guided meditation sessions in a serene natural setting. Learn techniques for managing digital addiction and finding peace in silence. Journal your thoughts and feelings using pen and paper.',
    3
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    4,
    'Organic Lunch & Storytelling (Hour 4)',
    'Enjoy a delicious organic lunch prepared with locally sourced ingredients. Listen to traditional stories and folklore from local community members, experiencing the ancient art of oral tradition.',
    4
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    5,
    'Wildlife Observation & Sketching (Hour 5)',
    'Engage in patient wildlife observation using only your eyes and analog sketching materials. Learn to appreciate the present moment without the need to capture and share every experience digitally.',
    5
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

INSERT INTO public.sas_mini_package_itinerary (mini_package_id, hour_number, title, description, sort_order)
SELECT
    p.id,
    6,
    'Integration & Digital Re-entry (Hour 6)',
    'Reflect on your digital detox experience and create a personal plan for healthier technology use. Receive your digital detox certificate and gradually reintegrate with your devices using mindful practices.',
    6
FROM public.sas_mini_packages p
WHERE p.slug = 'the-last-login'
ON CONFLICT DO NOTHING;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify tables were created
SELECT
    schemaname,
    tablename,
    tableowner
FROM pg_tables
WHERE tablename LIKE 'sas_mini_%'
ORDER BY tablename;

-- Verify indexes were created
SELECT
    indexname,
    tablename,
    indexdef
FROM pg_indexes
WHERE tablename LIKE 'sas_mini_%'
ORDER BY tablename, indexname;

-- Verify storage buckets were created
SELECT
    id,
    name,
    public,
    file_size_limit,
    allowed_mime_types,
    created_at
FROM storage.buckets
WHERE name LIKE 'sas-mini-%'
ORDER BY name;

-- Verify sample data was inserted
SELECT
    id,
    title,
    slug,
    status,
    category,
    location,
    pricing_solo,
    created_at
FROM public.sas_mini_packages
ORDER BY created_at DESC
LIMIT 5;

-- Verify sample itinerary was inserted
SELECT
    i.id,
    i.hour_number,
    i.title,
    i.description,
    i.sort_order,
    p.title as mini_package_title
FROM public.sas_mini_package_itinerary i
JOIN public.sas_mini_packages p ON i.mini_package_id = p.id
ORDER BY p.title, i.sort_order
LIMIT 10;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ Mini Package Management Database Setup Complete!';
    RAISE NOTICE '📋 Tables created: sas_mini_packages, sas_mini_package_content_blocks, sas_mini_package_itinerary, sas_mini_package_images';
    RAISE NOTICE '🔒 RLS policies configured for security';
    RAISE NOTICE '📁 Storage buckets created: sas-mini-package-images, sas-mini-package-content';
    RAISE NOTICE '⚡ Indexes created for performance optimization';
    RAISE NOTICE '🔄 Triggers configured for auto-updates';
    RAISE NOTICE '📝 Sample data inserted for testing (The Last Login mini package)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Ready for Mini Package API development!';
    RAISE NOTICE '⏰ Mini packages use HOUR_NUMBER instead of day_number for itineraries';
    RAISE NOTICE '🔗 Test the sample mini package at: /mini-package/the-last-login';
END $$;
