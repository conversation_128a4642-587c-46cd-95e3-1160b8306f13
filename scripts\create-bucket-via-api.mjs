import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createBucketViaAPI() {
  console.log('🚀 Creating package-images bucket via API...\n');

  try {
    // Method 1: Try direct bucket creation (might work with anon key)
    console.log('📦 Attempting to create bucket directly...');
    const { data: newBucket, error: createError } = await supabase.storage.createBucket('package-images', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    });

    if (createError) {
      console.log(`❌ Direct creation failed: ${createError.message}`);
      
      // Method 2: Try using the REST API directly
      console.log('\n🌐 Trying REST API method...');
      const response = await fetch(`${supabaseUrl}/rest/v1/storage/buckets`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'apikey': supabaseAnonKey
        },
        body: JSON.stringify({
          id: 'package-images',
          name: 'package-images',
          public: true,
          file_size_limit: 5242880,
          allowed_mime_types: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
        })
      });

      if (response.ok) {
        const bucket = await response.json();
        console.log('✅ Bucket created via REST API!');
        console.log('📋 Bucket details:', bucket);
      } else {
        const errorText = await response.text();
        console.log(`❌ REST API failed: ${response.status} - ${errorText}`);
        
        // Method 3: Provide manual SQL solution
        console.log('\n🔧 Manual SQL Solution Required:');
        console.log('Since automatic creation failed, you need to run this SQL manually:');
        console.log(`
-- Complete SQL to create package-images bucket
-- Run this in your Supabase SQL Editor

-- 1. Create the bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'package-images',
  'package-images',
  true,
  5242880,
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. Create policies
DROP POLICY IF EXISTS "Public read access for package-images" ON storage.objects;
CREATE POLICY "Public read access for package-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'package-images');

DROP POLICY IF EXISTS "Authenticated users can upload to package-images" ON storage.objects;
CREATE POLICY "Authenticated users can upload to package-images"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'package-images' AND auth.role() = 'authenticated');

DROP POLICY IF EXISTS "Users can update their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can update their uploads in package-images"
ON storage.objects FOR UPDATE
USING (bucket_id = 'package-images');

DROP POLICY IF EXISTS "Users can delete their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can delete their uploads in package-images"
ON storage.objects FOR DELETE
USING (bucket_id = 'package-images');

-- 4. Verify creation
SELECT * FROM storage.buckets WHERE name = 'package-images';
        `);
      }
    } else {
      console.log('✅ Bucket created successfully!');
      console.log('📋 Bucket details:', newBucket);
    }

    // Verify the bucket was created
    console.log('\n🔍 Verifying bucket creation...');
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.log(`❌ Verification failed: ${listError.message}`);
    } else {
      const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
      if (packageImagesBucket) {
        console.log('✅ package-images bucket is now accessible!');
        console.log('🎉 You can now upload images in your admin dashboard.');
      } else {
        console.log('❌ Bucket still not found after creation attempt');
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
    console.log('\n🔧 Manual SQL Solution:');
    console.log('Please run this SQL in your Supabase dashboard:');
    console.log(`
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('package-images', 'package-images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;
    `);
  }
}

createBucketViaAPI().catch(console.error); 