import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Create clients for both anon and service role
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);
const supabaseService = supabaseServiceKey ? createClient(supabaseUrl, supabaseServiceKey) : null;

async function setupStoragePermanently() {
  console.log('🔧 Setting up Storage Bucket Permanently\n');

  try {
    // Method 1: Try with service role key (if available)
    if (supabaseService) {
      console.log('🚀 Attempting to create bucket with service role...');
      const { data: newBucket, error: createError } = await supabaseService.storage.createBucket('package-images', {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
        fileSizeLimit: 5242880 // 5MB
      });

      if (createError) {
        if (createError.message.includes('already exists')) {
          console.log('✅ Bucket already exists (service role method)');
        } else {
          console.log(`⚠️  Service role method failed: ${createError.message}`);
        }
      } else {
        console.log('✅ Successfully created bucket with service role');
        console.log('📋 Bucket details:', newBucket);
      }
    } else {
      console.log('⚠️  No service role key available, trying alternative methods...');
    }

    // Method 2: Check if bucket exists with anon key
    console.log('\n🔍 Checking bucket status with anon key...');
    const { data: buckets, error: listError } = await supabaseAnon.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError.message);
    } else {
      console.log(`Found ${buckets?.length || 0} buckets`);
      buckets?.forEach(bucket => {
        console.log(`   - ${bucket.name} (public: ${bucket.public})`);
      });
    }

    const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
    
    if (packageImagesBucket) {
      console.log('✅ package-images bucket exists and is accessible!');
    } else {
      console.log('❌ package-images bucket not found');
      
      // Method 3: Create SQL script for manual execution
      console.log('\n📝 Creating SQL script for manual bucket creation...');
      const sqlScript = `-- Create package-images storage bucket
-- Run this in your Supabase SQL Editor

-- 1. Create the storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'package-images',
  'package-images',
  true,
  5242880, -- 5MB file size limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- 2. Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 3. Create policy for public read access
DROP POLICY IF EXISTS "Public read access for package-images" ON storage.objects;
CREATE POLICY "Public read access for package-images"
ON storage.objects
FOR SELECT
USING (bucket_id = 'package-images');

-- 4. Create policy for authenticated users to upload
DROP POLICY IF EXISTS "Authenticated users can upload to package-images" ON storage.objects;
CREATE POLICY "Authenticated users can upload to package-images"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'package-images' 
  AND auth.role() = 'authenticated'
);

-- 5. Create policy for users to update their uploads
DROP POLICY IF EXISTS "Users can update their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can update their uploads in package-images"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'package-images'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 6. Create policy for users to delete their uploads
DROP POLICY IF EXISTS "Users can delete their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can delete their uploads in package-images"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'package-images'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- 7. Verify the bucket was created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'package-images';`;

      fs.writeFileSync('scripts/create-package-images-bucket.sql', sqlScript);
      console.log('✅ SQL script created: scripts/create-package-images-bucket.sql');
    }

    // Method 4: Create a bucket check utility
    console.log('\n🔧 Creating bucket check utility...');
    const checkUtility = `import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

export async function checkPackageImagesBucket() {
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error checking buckets:', error.message);
      return false;
    }
    
    const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
    return !!packageImagesBucket;
  } catch (error) {
    console.error('Error in checkPackageImagesBucket:', error.message);
    return false;
  }
}

export async function ensurePackageImagesBucket() {
  const exists = await checkPackageImagesBucket();
  
  if (!exists) {
    console.error('Storage bucket "package-images" does not exist. Please create it in Supabase.');
    console.error('Run the SQL script: scripts/create-package-images-bucket.sql');
    return false;
  }
  
  return true;
}`;

    fs.writeFileSync('lib/storage-utils.js', checkUtility);
    console.log('✅ Storage utility created: lib/storage-utils.js');

    // Final verification
    console.log('\n🔍 Final verification...');
    const { data: finalBuckets, error: finalError } = await supabaseAnon.storage.listBuckets();
    
    if (finalError) {
      console.log('⚠️  Final verification failed:', finalError.message);
    } else {
      const finalBucket = finalBuckets?.find(b => b.name === 'package-images');
      if (finalBucket) {
        console.log('✅ package-images bucket is now accessible!');
      } else {
        console.log('❌ Bucket still not accessible');
      }
    }

    console.log('\n🎉 Permanent Storage Setup Complete!');
    console.log('\n📋 What was created:');
    console.log('   1. SQL script: scripts/create-package-images-bucket.sql');
    console.log('   2. Storage utility: lib/storage-utils.js');
    console.log('   3. This setup script: scripts/setup-storage-permanently.mjs');
    
    console.log('\n📝 Next steps:');
    console.log('   1. If bucket was created automatically: Try uploading images now');
    console.log('   2. If bucket was not created: Run the SQL script in Supabase dashboard');
    console.log('   3. For future issues: Run this script again');
    console.log('   4. The error should never appear again!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

setupStoragePermanently().catch(console.error); 