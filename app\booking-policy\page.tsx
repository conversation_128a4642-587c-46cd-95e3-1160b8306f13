import type { Metadata } from 'next';
import Navbar from '@/components/header';
import Footer from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Booking & Payment Policy',
  description: 'Learn about Swift Africa Safaris booking terms, payment methods, cancellation policy, and refund procedures. Fair and transparent policies for your African safari adventure.',
  keywords: [
    'booking policy',
    'payment terms',
    'safari booking',
    'cancellation policy',
    'refund policy',
    'Swift Africa Safaris booking',
    'safari payment methods',
    'travel insurance',
    'booking confirmation',
    'safari deposit',
    'tour cancellation',
    'African safari booking terms'
  ],
  url: '/booking-policy'
});

export default function BookingPolicy() {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Booking & Payment Policy', url: '/booking-policy' }
  ]);

  const bookingPolicySchema = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Booking & Payment Policy - Swift Africa Safaris',
    description: 'Comprehensive booking and payment policy for Swift Africa Safaris tours and services.',
    url: `${defaultSEO.siteUrl}/booking-policy`,
    mainEntity: {
      '@type': 'Policy',
      name: 'Swift Africa Safaris Booking & Payment Policy',
      description: 'Fair, transparent, and guest-centered booking policy for African safari experiences.',
      dateModified: new Date().toISOString(),
      publisher: {
        '@type': 'Organization',
        name: 'Swift Africa Safaris',
        url: defaultSEO.siteUrl
      }
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, bookingPolicySchema]} />
      <Navbar />
      
      <main className="min-h-screen">
        {/* Hero Section */}
        <div className="bg-[var(--primary-background)] py-16">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[var(--text)] mb-6">
              Booking & Payment Policy
            </h1>
            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
            <p className="text-lg text-[var(--text)] leading-relaxed max-w-3xl mx-auto">
              At Swift Africa Safaris, we believe that travel is more than just visiting a destination, it's about creating lasting memories, discovering new perspectives, and connecting with nature and cultures in meaningful ways. That's why we've crafted a fair, transparent, and guest centered booking policy that gives you peace of mind while also supporting responsible tourism and our dedicated teams on the ground.
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="bg-white rounded-lg shadow-lg p-8 md:p-12 space-y-12">
            
            {/* Section 1 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                1. Booking Confirmation
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                To secure your journey with us:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6 mb-4">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    A <strong>50% deposit</strong> of the total tour cost is required upon confirmation of your safari.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    This deposit ensures we can reserve accommodations, expert guides, park permits, and local experiences often in high demand and limited in availability.
                  </div>
                </li>
              </ul>
              <p className="text-[var(--text)] leading-relaxed">
                Your booking will be officially confirmed once the deposit is received and a written confirmation is sent to you.
              </p>
            </section>

            {/* Section 2 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                2. Balance Payment
              </h2>
              <ul className="space-y-3 text-[var(--text)] ml-6 mb-4">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    The remaining <strong>50% balance</strong> is due 55 days before the start date of your tour.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    If booking is made within 55 days of the safari start date, <strong>full payment</strong> is required at the time of booking.
                  </div>
                </li>
              </ul>
              <p className="text-[var(--text)] leading-relaxed italic bg-[var(--card-bg)] p-4 rounded-lg">
                We understand that life can be unpredictable. If you're facing challenges meeting the timeline, please talk to us. We're human and we care.
              </p>
            </section>

            {/* Section 3 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                3. Payment Methods
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                We accept secure payments via:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6 mb-4">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Bank transfer (USD)</strong>
                  </div>
                </li>
              </ul>
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> All transfer fees and bank charges are the responsibility of the client to ensure full receipt of the payment on our end.
                </p>
              </div>
            </section>

            {/* Section 4 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                4. Cancellations & Refunds
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                Because your safari arrangements involve multiple third-party services and limited park permits:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6 mb-6">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Cancellations made more than 60 days before departure:</strong> 80% refund of the deposit
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Cancellations between 30–59 days:</strong> 50% refund of the total paid
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Cancellations less than 30 days before start:</strong> No refund (we will do our best to reschedule or provide credit if possible)
                  </div>
                </li>
              </ul>
              <div className="bg-[var(--card-bg)] p-6 rounded-lg">
                <h4 className="font-semibold text-[var(--text)] mb-3">Why these matters:</h4>
                <p className="text-[var(--text)] leading-relaxed mb-3">
                  When you book with us, we immediately begin securing services that are often nonrefundable. These include accommodations, national park fees, and expert guides.
                </p>
                <p className="text-[var(--text)] leading-relaxed">
                  Your commitment allows us to offer consistently high standards and meaningful employment to local communities.
                </p>
              </div>
            </section>

            {/* Section 5 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                5. Date Changes & Flexibility
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                We're proud to offer flexibility:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>One free reschedule</strong> (up to 12 months ahead) is allowed if requested at least 30 days before your trip.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    Additional changes may incur small fees depending on partner policies.
                  </div>
                </li>
              </ul>
            </section>

            {/* Section 6 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                6. Travel Insurance
              </h2>
              <p className="text-[var(--text)] leading-relaxed">
                We strongly recommend travel insurance that covers trip cancellations, medical expenses, and lost luggage. It's a small investment that protects your big adventure.
              </p>
            </section>

            {/* Section 7 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                7. Our Promise
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                By booking with Swift Africa Safaris, you're not just reserving a tour, you're becoming part of a community that believes in purpose driven travel, authentic experiences, and responsibility to people and planet.
              </p>
              <div className="bg-[var(--secondary-background)] p-6 rounded-lg">
                <h4 className="font-semibold text-[var(--text)] mb-3">We are committed to:</h4>
                <ul className="space-y-2 text-[var(--text)] ml-6">
                  <li className="flex items-start">
                    <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                    <div>Transparent communication</div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                    <div>Fair business practices</div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                    <div>Exceptional service from first email to final farewell</div>
                  </li>
                </ul>
              </div>
              <div className="mt-6 text-center bg-[var(--accent)] text-white p-6 rounded-lg">
                <p className="text-lg font-semibold mb-2">
                  Ready to confirm your safari? We're here for every question, concern, and dream.
                </p>
                <p className="text-base">
                  Thank you for choosing us. Your journey begins the moment you say yes.
                </p>
              </div>
            </section>

            {/* Last Updated */}
            <div className="border-t border-gray-200 pt-8 text-center">
              <p className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleDateString('en-US', { 
                  year: 'numeric', 
                  month: 'long', 
                  day: 'numeric' 
                })}
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
