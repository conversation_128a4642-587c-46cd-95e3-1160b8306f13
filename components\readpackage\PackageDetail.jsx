
import { useParams } from 'react-router-dom';
import { packageDetails } from '../data/packageData';
import PackageHero from './PackageHero';
import PackageOverview from './PackageOverview';
import PackageHighlights from './PackageHighlights';
import PackageItinerary from './PackageItinerary';
import PackageIncludes from './PackageIncludes';
import SafariBookingForm from './BookingForm/booking-Form';

export default function PackageDetail() {
  const { slug } = useParams();
  const packageData = packageDetails[slug];

  if (!packageData) {
    return <div>Package not found</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 text-justify">
      <PackageHero image={packageData.heroImage} title={packageData.title} />
      
      <div className="container mx-auto px-4 lg:px-8 py-8 flex flex-col lg:flex-row gap-8">
        <div className="lg:w-2/3 text-justify">
          <PackageOverview
            contentBlocks={packageData.contentBlocks || []}
            difficulty={packageData.difficulty}
          />
          <PackageHighlights 
            highlights={packageData.highlights}
            whatToPack={packageData.whatToPack}
          />
          <PackageItinerary itinerary={packageData.itinerary} />
          <PackageIncludes 
            includes={packageData.includes}
            excludes={packageData.excludes}
          />
        </div>
        
        <div className="lg:w-1/3">
          <div className="sticky top-8">
            <SafariBookingForm
              pricing={{
                solo: { price: 1355, text: 'Per 1 Person' },
                honeymoon: { price: 1353, text: 'Per 1 Person' },
                family: { price: 1351, text: 'Per 1 Person' },
                group: { price: 1348, text: 'Per 1 Person' }
              }}
              packageTitle={packageData.title}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
