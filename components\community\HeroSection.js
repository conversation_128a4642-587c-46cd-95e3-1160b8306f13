"use client";
/* eslint-disable react/no-unescaped-entities */

import React, { useEffect, useState } from 'react';
import DonateForm from './donate';

const HeroSection = () => {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    // Hydration safe: Check if window and document are defined
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      // Add animation classes after component mounts
      const elements = document.querySelectorAll('.animate-fade-up, .animate-fade-left');
      elements.forEach(el => {
        el.classList.add('opacity-100');
      });
    }
  }, []);

  // Update the modal effect to handle scroll better
  useEffect(() => {
    // Hydration safe: Check if window and document are defined
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      const body = document.body;
      if (showModal) {
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        body.style.overflow = 'hidden';
        body.style.paddingRight = `${scrollbarWidth}px`;
      } else {
        body.style.overflow = '';
        body.style.paddingRight = '';
      }
      return () => {
        body.style.overflow = '';
        body.style.paddingRight = '';
      };
    }
  }, [showModal]);

  return (
    <>
      <section className="bg-[var(--primary-background)] py-24 relative min-h-[80vh] lg:px-16">
        {/* Mobile background */}
        <div className="lg:hidden absolute inset-0 bg-black/40">
          {/* Background image */}
          <div
            className="absolute inset w-full h-full"
            style={{
              backgroundImage: `url(/images/community/africa-community-impactful-travel.webp)`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.6,
              
            }}
          />
        </div>

        <div className="container mx-auto px-4 flex flex-col lg:flex-row items-center justify-center h-full relative z-10">
          <div className="text-center max-w-[600px] mx-auto lg:text-left mb-10 lg:mb-0">
            <h1 className="text-4xl lg:text-5xl font-bold mb-4 transform transition-all duration-700 translate-y-0 animate-fade-up lg:text-black text-white">
              Where Journey Heal Lands and Lift Lives
            </h1>
            <p className="text-lg mb-8 transform transition-all duration-700 delay-200 translate-y-0 animate-fade-up lg:text-black text-white/90">
              Travel That Protect Nature, Empowers Communities and Transform The Heart of Africa
            </p>
            <button
              onClick={() => setShowModal(true)}
              className="btn text-white px-12 py-4 rounded-full font-semibold transition duration-300 transform translate-y-0 animate-fade-up"
            >
              Donate Now
            </button>
          </div>

          {/* Desktop Africa shape */}
          <div className="hidden lg:block relative w-full lg:w-1/2 transform transition-all duration-700 translate-x-0 animate-fade-left">
            <div
              className="africa-shape w-full h-[500px] bg-no-repeat bg-contain bg-center"
              style={{
                backgroundImage: `url(${'/images/community/africa-community-impactful-travel.webp'})`,
                backgroundSize: 'contain',
              }}
            />
            {/* Animated dots */}
            <div className="absolute top-[15%] left-[75%] w-3 h-3 rounded-full bg-[#d35400] animate-pulse"></div>
            <div className="absolute top-[35%] right-[5%] w-3 h-3 rounded-full bg-[#d35400] animate-pulse delay-200"></div>
            <div className="absolute bottom-[25%] left-[15%] w-3 h-3 rounded-full bg-[#317100] animate-pulse delay-300"></div>
            <div className="absolute bottom-[45%] left-[10%] w-3 h-3 rounded-full bg-[#03a9f4] animate-pulse delay-400"></div>
            <div className="absolute bottom-[10%] right-[20%] w-3 h-3 rounded-full bg-[#03a9f4] animate-pulse delay-500"></div>
          </div>
        </div>
      </section>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-h-[90vh] overflow-y-auto relative">
            <button
              onClick={() => setShowModal(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700 z-10"
            >
              ✕
            </button>
            <DonateForm />
          </div>
        </div>
      )}
    </>
  );
};

export default HeroSection;
