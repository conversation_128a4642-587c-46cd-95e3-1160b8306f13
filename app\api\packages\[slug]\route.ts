import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch single package by slug (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Fetch package with all related data (only if active)
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select(`
        *,
        sas_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_package_itinerary (
          id,
          day_number,
          title,
          description,
          activities,
          accommodation,
          meals,
          sort_order
        ),
        sas_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', slug)
      .eq('status', 'active') // Only return active packages
      .single();

    if (packageError) {
      if (packageError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Package not found or not available' },
          { status: 404 }
        );
      }
      console.error('Supabase error:', packageError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch package' },
        { status: 500 }
      );
    }

    // Debug: Log the raw package data from database
    console.log('Single Package API - Raw data from DB:', {
      id: packageData.id,
      title: packageData.title,
      slug: packageData.slug,
      image_url: packageData.image_url,
      hero_image_url: packageData.hero_image_url,
      image_alt: packageData.image_alt,
      hero_image_alt: packageData.hero_image_alt
    });

    // Calculate lowest price for display
    const prices = [
      packageData.pricing_solo,
      packageData.pricing_honeymoon,
      packageData.pricing_family,
      packageData.pricing_group
    ];
    const lowestPrice = Math.min(...prices);

    // Transform data to match frontend expectations
    const transformedPackage = {
      id: packageData.id,
      title: packageData.title,
      slug: packageData.slug,
      description: packageData.description,
      overview: packageData.overview,
      difficulty: packageData.difficulty,
      category: packageData.category,
      location: packageData.location,
      duration: packageData.duration,
      
      // Pricing information
      pricing: {
        solo: { price: packageData.pricing_solo, text: 'Per Person' },
        honeymoon: { price: packageData.pricing_honeymoon, text: 'Per Person' },
        family: { price: packageData.pricing_family, text: 'Per Person' },
        group: { price: packageData.pricing_group, text: 'Per Person' }
      },
      lowestPrice: lowestPrice,
      
      // Images
      heroImage: packageData.hero_image_url || packageData.image_url,
      heroImageUrl: packageData.hero_image_url || packageData.image_url,
      heroImageAlt: packageData.hero_image_alt || packageData.image_alt,
      imageUrl: packageData.image_url,
      imageAlt: packageData.image_alt,
      image: packageData.image_url, // For card compatibility
      image_url: packageData.image_url, // For card compatibility
      
      // Content arrays
      highlights: packageData.highlights || [],
      whatToPack: packageData.packing_list || [],
      packingList: packageData.packing_list || [], // For backward compatibility
      includes: packageData.includes || [],
      excludes: packageData.excludes || [],
      
      // SEO data (for metadata generation)
      seo: packageData.seo_title ? {
        title: packageData.seo_title,
        description: packageData.seo_description,
        keywords: packageData.seo_keywords || [],
        image: packageData.og_image_url || packageData.hero_image_url || packageData.image_url,
        author: 'Swift Africa Safaris',
        publishedTime: packageData.published_at,
        modifiedTime: packageData.updated_at,
        category: packageData.category,
        tags: [packageData.location, packageData.category, packageData.difficulty]
      } : null,
      
      // Schema.org structured data
      schema: packageData.schema_data || {
        '@type': 'TouristTrip',
        name: packageData.title,
        description: packageData.overview || packageData.description,
        image: packageData.hero_image_url || packageData.image_url,
        touristType: 'Safari Enthusiast',
        duration: packageData.duration,
        location: {
          '@type': 'Place',
          name: packageData.location,
          address: {
            '@type': 'PostalAddress',
            addressCountry: getCountryCode(packageData.location)
          }
        },
        offers: {
          '@type': 'Offer',
          price: lowestPrice.toString(),
          priceCurrency: 'USD',
          availability: 'https://schema.org/InStock',
          seller: {
            '@type': 'TravelAgency',
            name: 'Swift Africa Safaris'
          }
        },
        provider: {
          '@type': 'TravelAgency',
          name: 'Swift Africa Safaris',
          url: 'https://swiftafricasafaris.com'
        }
      },
      
      // Related content
      contentBlocks: packageData.sas_package_content_blocks?.sort((a, b) => a.sort_order - b.sort_order) || [],
      itinerary: packageData.sas_package_itinerary?.sort((a, b) => a.sort_order - b.sort_order) || [],
      gallery: packageData.sas_package_images?.sort((a, b) => a.sort_order - b.sort_order) || [],
      
      // Metadata
      status: packageData.status,
      createdAt: packageData.created_at,
      updatedAt: packageData.updated_at,
      publishedAt: packageData.published_at
    };

    return NextResponse.json({
      success: true,
      data: transformedPackage
    });

  } catch (error) {
    console.error('Error fetching package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch package' },
      { status: 500 }
    );
  }
}

// Helper function to get country code from location
function getCountryCode(location: string): string {
  const countryMap: { [key: string]: string } = {
    'Uganda': 'UG',
    'Rwanda': 'RW',
    'Tanzania': 'TZ',
    'Kenya': 'KE',
    'South Africa': 'ZA',
    'Botswana': 'BW',
    'Namibia': 'NA',
    'Zambia': 'ZM',
    'Zimbabwe': 'ZW',
    'Malawi': 'MW',
    'Mozambique': 'MZ',
    'Madagascar': 'MG',
    'Seychelles': 'SC',
    'Mauritius': 'MU'
  };
  
  return countryMap[location] || 'AF'; // Default to Africa continent code
}
