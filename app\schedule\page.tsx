//schedule page.tsx
import type { Metadata } from 'next';
import React from 'react';
import { generateMetadata } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - Schedule Your African Safari Adventure',
  description: 'Plan and schedule your perfect African safari adventure with Swift Africa Safaris. View available dates, seasonal highlights, and book your dream safari.',
  url: '/schedule',
  keywords: [
    'safari schedule',
    'African safari booking',
    'safari availability',
    'safari planning calendar',
    'best time safari Africa',
    'seasonal safari tours',
    'safari booking dates',
    'African travel schedule',
    'wildlife viewing seasons',
    'safari tour calendar'
  ],
  type: 'website'
});

const Schedule = () => {
  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-4">Schedule</h1>
      <p>This is the Schedule page content.</p>
    </div>
  );
};

export default Schedule; 