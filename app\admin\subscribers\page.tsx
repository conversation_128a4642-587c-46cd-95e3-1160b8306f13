/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { useState, useEffect } from 'react';
import { Search, Mail, Calendar, Download, Trash2, UserPlus } from 'lucide-react';

interface Subscriber {
  id: number;
  email: string;
  subscribedAt: string;
  status: 'active' | 'unsubscribed';
  source: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function Subscribers() {
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [filteredSubscribers, setFilteredSubscribers] = useState<Subscriber[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Sample data
  const sampleSubscribers: Subscriber[] = [
    {
      id: 1,
      email: '<EMAIL>',
      subscribedAt: '2024-01-15T10:30:00Z',
      status: 'active',
      source: 'Website Footer'
    },
    {
      id: 2,
      email: '<EMAIL>',
      subscribedAt: '2024-02-20T14:22:00Z',
      status: 'active',
      source: 'Blog Signup'
    },
    {
      id: 3,
      email: '<EMAIL>',
      subscribedAt: '2024-03-10T09:15:00Z',
      status: 'unsubscribed',
      source: 'Newsletter Popup'
    },
    {
      id: 4,
      email: '<EMAIL>',
      subscribedAt: '2024-03-25T16:45:00Z',
      status: 'active',
      source: 'Contact Form'
    },
    {
      id: 5,
      email: '<EMAIL>',
      subscribedAt: '2024-04-05T11:20:00Z',
      status: 'active',
      source: 'Website Footer'
    }
  ];

  useEffect(() => {
    setSubscribers(sampleSubscribers);
    setFilteredSubscribers(sampleSubscribers);
  }, [sampleSubscribers]);

  // Filter subscribers
  useEffect(() => {
    let filtered = subscribers;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(subscriber => subscriber.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(subscriber =>
        subscriber.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        subscriber.source.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSubscribers(filtered);
  }, [subscribers, searchTerm, statusFilter]);

  const handleUnsubscribe = (id: number) => {
    setSubscribers(subscribers.map(subscriber =>
      subscriber.id === id ? { ...subscriber, status: 'unsubscribed' as const } : subscriber
    ));
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this subscriber?')) {
      setSubscribers(subscribers.filter(subscriber => subscriber.id !== id));
    }
  };

  const exportSubscribers = () => {
    const activeSubscribers = subscribers.filter(sub => sub.status === 'active');
    const csvContent = 'Email,Subscribed At,Source\n' +
      activeSubscribers.map(sub => 
        `${sub.email},${new Date(sub.subscribedAt).toLocaleDateString()},${sub.source}`
      ).join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'subscribers.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string): string => {
    return status === 'active' ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100';
  };

  return (
    <main className={`ml-16 pt-16 min-h-[calc(10vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out`}>
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className='mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col'>
        <div className="p-6 border-b border-gray-300">
          <h1 className="text-2xl font-bold text-gray-900">Subscribers Management</h1>
        </div>
        <div className="flex-1 overflow-y-auto p-6" 
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#6B7280 transparent'
          }}
        >
          <div className="bg-white rounded-lg shadow-sm">
            {/* Header Controls */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search subscribers..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                    />
                  </div>

                  {/* Status Filter */}
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Subscribers</option>
                    <option value="active">Active</option>
                    <option value="unsubscribed">Unsubscribed</option>
                  </select>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <button
                    onClick={exportSubscribers}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Export CSV
                  </button>
                </div>
              </div>

              {/* Stats */}
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <UserPlus className="w-8 h-8 text-blue-600 mr-3" />
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{subscribers.length}</div>
                      <div className="text-sm text-gray-600">Total Subscribers</div>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Mail className="w-8 h-8 text-green-600 mr-3" />
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {subscribers.filter(s => s.status === 'active').length}
                      </div>
                      <div className="text-sm text-gray-600">Active Subscribers</div>
                    </div>
                  </div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <Trash2 className="w-8 h-8 text-red-600 mr-3" />
                    <div>
                      <div className="text-2xl font-bold text-red-600">
                        {subscribers.filter(s => s.status === 'unsubscribed').length}
                      </div>
                      <div className="text-sm text-gray-600">Unsubscribed</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Subscribers Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscribed Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Source</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSubscribers.map((subscriber) => (
                    <tr key={subscriber.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Mail className="w-4 h-4 text-gray-400 mr-2" />
                          <span className="text-sm font-medium text-gray-900">{subscriber.email}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                          {new Date(subscriber.subscribedAt).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {subscriber.source}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(subscriber.status)}`}>
                          {subscriber.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          {subscriber.status === 'active' && (
                            <button
                              onClick={() => handleUnsubscribe(subscriber.id)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Unsubscribe"
                            >
                              Unsubscribe
                            </button>
                          )}
                          <button
                            onClick={() => handleDelete(subscriber.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredSubscribers.length === 0 && (
              <div className="p-12 text-center text-gray-500">
                <Mail className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No subscribers found matching your criteria.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}
