
/* eslint-disable react/no-unescaped-entities */
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Package, Save, Upload, Trash2 } from 'lucide-react';
import PackageCard, { validatePackageFormData } from '@/components/admin/package/packageCard';
import PackageEditor from '@/components/admin/package/packageEditor';
import PackageSeo from '@/components/admin/package/packageSeo';
import PackageTravelItineraryCreator from '@/components/admin/package/packageTravelItineraryCreator';
import PackageTravelPacking from '@/components/admin/package/packageTravelPacking';

interface PackageFormData {
  id: string;
  title: string;
  description: string;
  overview: string;
  content: unknown[];
  duration: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  difficulty: string;
  category: string;
  location: string;
  status: 'published' | 'draft' | 'archived';
  imageUrl: string;
  imageAlt: string;
  heroImageUrl: string;
  heroImageAlt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  highlights: string[];
  packingList: string[];
  includes: string[];
  excludes: string[];
  itinerary: unknown[];
  images: unknown[];
}

const styles = `
  ::-webkit-scrollbar {
    width: 8px;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function EditPackagePage() {
  const params = useParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [packageLoading, setPackageLoading] = useState(true);
  const [formData, setFormData] = useState<PackageFormData | null>(null);

  const tabs = [
    { id: 'basic', label: 'Basic Info', component: 'PackageCard' },
    { id: 'content', label: 'Content', component: 'PackageEditor' },
    { id: 'itinerary', label: 'Itinerary', component: 'PackageTravelItineraryCreator' },
    { id: 'packing', label: 'Packing List', component: 'PackageTravelPacking' },
    { id: 'seo', label: 'SEO', component: 'PackageSeo' }
  ];

  // Load package data
  useEffect(() => {
    const loadPackage = async () => {
      try {
        const response = await fetch(`/api/admin/packages/${params.slug}`);
        const result = await response.json();

        if (result.success) {
          setFormData(result.data);
        } else {
          console.error('Failed to load package:', result.error);
        }
      } catch (error) {
        console.error('Error loading package:', error);
      } finally {
        setPackageLoading(false);
      }
    };

    if (params.slug) {
      loadPackage();
    }
  }, [params.slug]);

  const handleFormDataChange = (updates: Partial<PackageFormData>) => {
    if (formData) {
      setFormData({ ...formData, ...updates });
    }
  };

  const handleSave = async (status?: 'draft' | 'active' | 'inactive') => {
    if (!formData) return;

    // Validate form data before submission
    const validation = validatePackageFormData(formData);
    if (!validation.isValid) {
      alert('Please fix the following errors:\n' + validation.errors.join('\n'));
      return;
    }

    setLoading(true);
    try {
      const packageData = {
        ...formData,
        ...(status && { status }),
        updatedAt: new Date().toISOString()
      };

      console.log('Sending package data:', JSON.stringify(packageData, null, 2));

      const response = await fetch(`/api/admin/packages/${params.slug}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(packageData)
      });

      const result = await response.json();
      console.log('Received response:', JSON.stringify(result, null, 2));

      if (result.success) {
        // Update local state with the returned data
        setFormData(result.data);
        console.log('Updated form data:', JSON.stringify(result.data, null, 2));
        // Show success message
        alert('Package updated successfully');
      } else {
        console.error('Failed to update package:', result.error);
        alert('Failed to update package: ' + result.error);
      }
    } catch (err) {
      console.error('Error updating package:', err);
      alert('Failed to update package. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!formData) return;

    const confirmed = window.confirm('Are you sure you want to delete this package? This action cannot be undone.');
    if (!confirmed) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/packages/${params.slug}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        // Show success message
        alert('Package deleted successfully');
        // Redirect to packages list
        router.push('/admin/packages');
      } else {
        console.error('Failed to delete package:', result.error);
        alert('Failed to delete package: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting package:', error);
      alert('Failed to delete package. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePublish = () => {
    handleSave('active');
  };

  const handleUnpublish = () => {
    handleSave('inactive');
  };

  const renderTabContent = () => {
    if (!formData) return null;

    switch (activeTab) {
      case 'basic':
        return (
          <PackageCard
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case 'content':
        return (
          <PackageEditor
            initialContent={formData.content}
            onSave={(content) => handleFormDataChange({ content })}
          />
        );
      case 'itinerary':
        return (
          <PackageTravelItineraryCreator
            initialItinerary={formData.itinerary}
            onSave={(itinerary) => handleFormDataChange({ itinerary })}
          />
        );
      case 'packing':
        return (
          <PackageTravelPacking
            highlights={formData.highlights}
            packingList={formData.packingList}
            includes={formData.includes}
            excludes={formData.excludes}
            onSave={(data) => handleFormDataChange(data)}
          />
        );
      case 'seo':
        return (
          <PackageSeo
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      default:
        return (
          <PackageCard
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
    }
  };

  if (packageLoading) {
    return (
      <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
        <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading package...</p>
          </div>
        </div>
      </main>
    );
  }

  if (!formData) {
    return (
      <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
        <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex items-center justify-center">
          <div className="text-center">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Package not found</h3>
            <p className="mt-1 text-sm text-gray-500">The package you're looking for doesn't exist.</p>
            <div className="mt-6">
              <button
                onClick={() => router.push('/admin/packages')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Packages
              </button>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300 bg-white rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-gray-600" />
              </button>
              <Package className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Edit Package</h1>
                <p className="text-gray-600">Update package: {formData.title}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleDelete}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
              <button
                onClick={() => handleSave()}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
              {formData.status === 'published' ? (
                <button
                  onClick={handleUnpublish}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
                >
                  Unpublish
                </button>
              ) : (
                <button
                  onClick={handlePublish}
                  disabled={loading}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  <Upload className="w-4 h-4" />
                  {loading ? 'Publishing...' : 'Publish'}
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white border-b border-gray-200">
          <div className="px-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto" style={{ scrollbarWidth: 'thin', scrollbarColor: '#6B7280 transparent' }}>
          {renderTabContent()}
        </div>
      </div>
    </main>
  );
}
