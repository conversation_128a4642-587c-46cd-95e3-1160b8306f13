import React from 'react';
import SkeletonBase from './SkeletonBase';

/**
 * Skeleton Text Component for titles, paragraphs, and text content
 * Provides various text skeleton patterns
 */
const SkeletonText = ({ 
  variant = 'paragraph', // 'title', 'subtitle', 'paragraph', 'line', 'button'
  lines = 3,
  className = '',
  width = 'w-full'
}) => {
  const getTextHeight = () => {
    switch (variant) {
      case 'title':
        return 'h-8';
      case 'subtitle':
        return 'h-6';
      case 'button':
        return 'h-10';
      case 'line':
        return 'h-4';
      default:
        return 'h-4';
    }
  };

  const getTextWidth = (index) => {
    if (variant === 'title') return index === 0 ? 'w-3/4' : 'w-1/2';
    if (variant === 'subtitle') return 'w-2/3';
    if (variant === 'button') return 'w-full';
    
    // For paragraphs, vary the width of lines
    const widths = ['w-full', 'w-full', 'w-3/4', 'w-5/6', 'w-2/3'];
    return widths[index % widths.length];
  };

  if (variant === 'line' || variant === 'title' || variant === 'subtitle' || variant === 'button') {
    return (
      <SkeletonBase 
        width={width}
        height={getTextHeight()}
        className={className}
      />
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonBase
          key={index}
          width={getTextWidth(index)}
          height={getTextHeight()}
        />
      ))}
    </div>
  );
};

export default SkeletonText;
