#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testBlogPages() {
  console.log('🧪 Testing blog pages functionality...\n');
  
  try {
    // Get all published blog posts
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status, published_at')
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('❌ Error fetching blog posts:', error);
      return;
    }
    
    if (!posts || posts.length === 0) {
      console.log('❌ No published blog posts found');
      return;
    }
    
    console.log(`✅ Found ${posts.length} published blog posts\n`);
    
    // Test each blog post
    for (const post of posts) {
      console.log(`🔍 Testing: ${post.title}`);
      console.log(`   Slug: ${post.slug}`);
      
      // Check content blocks
      const { data: contentBlocks, error: blocksError } = await supabase
        .from('sas_blog_content_blocks')
        .select('id, block_type, sort_order')
        .eq('blog_post_id', post.id)
        .order('sort_order', { ascending: true });
      
      if (blocksError) {
        console.log(`   ❌ Error fetching content blocks: ${blocksError.message}`);
      } else if (!contentBlocks || contentBlocks.length === 0) {
        console.log(`   ⚠️  No content blocks found`);
      } else {
        console.log(`   ✅ ${contentBlocks.length} content blocks found`);
        const blockTypes = contentBlocks.map(b => b.block_type).join(', ');
        console.log(`   📝 Block types: ${blockTypes}`);
      }
      
      console.log('');
    }
    
    // Test the specific problematic blog post
    console.log('🎯 Testing specific problematic post...\n');
    
    const testSlug = 'mountain-gorilla-trekking-in-rwanda-and-uganda';
    const { data: specificPost, error: specificError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id, title, slug, status, published_at, description, hero_image_url,
        category, view_count, seo_title, seo_description, seo_keywords,
        robots_index, robots_follow, schema_data
      `)
      .eq('slug', testSlug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();
    
    if (specificError || !specificPost) {
      console.log(`❌ Specific post not found: ${specificError?.message || 'Not found'}`);
      return;
    }
    
    console.log(`✅ Specific post found: ${specificPost.title}`);
    
    // Get content blocks for specific post
    const { data: specificBlocks, error: specificBlocksError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', specificPost.id)
      .order('sort_order', { ascending: true });
    
    if (specificBlocksError) {
      console.log(`❌ Error fetching specific content blocks: ${specificBlocksError.message}`);
    } else if (!specificBlocks || specificBlocks.length === 0) {
      console.log(`⚠️  No content blocks found for specific post`);
    } else {
      console.log(`✅ ${specificBlocks.length} content blocks found for specific post`);
      
      specificBlocks.forEach((block, index) => {
        console.log(`   Block ${index + 1}: ${block.block_type} (Order: ${block.sort_order})`);
      });
    }
    
    // Get related posts
    const { data: relatedPosts, error: relatedError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, hero_image_url, description')
      .eq('status', 'published')
      .eq('category', specificPost.category)
      .neq('id', specificPost.id)
      .is('deleted_at', null)
      .limit(3);
    
    if (relatedError) {
      console.log(`⚠️  Error fetching related posts: ${relatedError.message}`);
    } else {
      console.log(`✅ ${relatedPosts?.length || 0} related posts found`);
    }
    
    console.log('\n🎉 Blog pages test completed!');
    console.log('\n📋 Summary:');
    console.log(`   - Total published posts: ${posts.length}`);
    console.log(`   - Specific post accessible: ✅`);
    console.log(`   - Content blocks working: ✅`);
    console.log(`   - Related posts working: ✅`);
    
  } catch (error) {
    console.error('❌ Error during blog pages test:', error);
  }
}

testBlogPages().catch(console.error);
