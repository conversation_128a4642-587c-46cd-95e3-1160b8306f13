'use client';

import React from 'react';
import ExistingReviews from '@/components/admin/reviews/reviewsCards';

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function Reviews() {
  return (
    <main
      className={`ml-16 pt-16 min-h-[calc(10vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out`}
    >
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className='mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col'>
        <div className="p-6 border-b border-gray-300">
          <h1 className="text-2xl font-bold text-gray-900">Reviews Management</h1>
        </div>
        <div className="flex-1 overflow-y-auto p-6" 
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#6B7280 transparent'
          }}
        >
          <div className="bg-white rounded-lg shadow-sm">
            <ExistingReviews />
          </div>
        </div>
      </div>
    </main>
  );
}
