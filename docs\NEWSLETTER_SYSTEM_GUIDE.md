# Newsletter System Implementation Guide

## Overview

The Swift Africa Safaris Newsletter System is a comprehensive email marketing solution built with Next.js, Supabase, and Nodemailer. It provides secure subscriber management, newsletter composition, and automated email delivery with proper unsubscribe handling.

## Features

### ✅ Frontend Components
- **Newsletter Subscription Form** (`components/newsLetter.js`)
  - Email validation and sanitization
  - Rate limiting protection
  - Real-time feedback and error handling
  - Consistent Swift Africa Safaris design

### ✅ Admin Dashboard
- **Subscriber Management** (`/admin/newsletter`)
  - View, search, and filter subscribers
  - Export subscriber data to CSV
  - Manual subscriber addition/removal
  - Status management (subscribed/unsubscribed)

- **Newsletter Composer** (`/admin/newsletter/compose`)
  - Rich text and HTML email composition
  - Live preview functionality
  - Content validation and sanitization
  - Batch email sending with progress tracking

- **SMTP Settings** (`/admin/newsletter/settings`)
  - Secure SMTP configuration
  - Password encryption
  - Test email functionality
  - Hostinger SMTP integration

### ✅ Database Schema
```sql
-- Subscribers table
sas_newsletter_subscribers (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) CHECK (status IN ('subscribed', 'unsubscribed')),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- SMTP settings table
sas_newsletter_settings (
    id UUID PRIMARY KEY,
    smtp_email VARCHAR(255) NOT NULL,
    smtp_password TEXT NOT NULL, -- Encrypted
    smtp_host VARCHAR(255),
    smtp_port INTEGER,
    smtp_secure BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
);

-- Activity logs table
sas_newsletter_logs (
    id UUID PRIMARY KEY,
    subject TEXT NOT NULL,
    content_preview TEXT,
    total_recipients INTEGER,
    successful_sends INTEGER,
    failed_sends INTEGER,
    sent_at TIMESTAMP WITH TIME ZONE
);

-- Admin audit logs
sas_admin_logs (
    id UUID PRIMARY KEY,
    user_id UUID,
    action VARCHAR(100),
    details JSONB,
    ip_address INET,
    timestamp TIMESTAMP WITH TIME ZONE
);
```

### ✅ API Routes

#### Public APIs
- `POST /api/newsletter/subscribe` - Subscribe to newsletter
- `POST /api/newsletter/unsubscribe` - Unsubscribe from newsletter
- `GET /api/newsletter/unsubscribe?email=` - Unsubscribe via email link

#### Admin APIs
- `GET /api/admin/newsletter/subscribers` - Fetch subscribers (paginated)
- `POST /api/admin/newsletter/subscribers` - Add new subscriber
- `PUT /api/admin/newsletter/subscribers/[id]` - Update subscriber
- `DELETE /api/admin/newsletter/subscribers/[id]` - Delete subscriber
- `GET /api/newsletter/settings` - Get SMTP settings
- `POST /api/newsletter/settings` - Update SMTP settings
- `POST /api/newsletter/send` - Send newsletter to all subscribers
- `POST /api/newsletter/test` - Send test email

### ✅ Security Features

#### Input Validation & Sanitization
- Email format validation with regex
- HTML content sanitization using DOMPurify
- Subject line spam detection
- Content length limits and validation

#### Rate Limiting
- Subscription endpoint: 3 requests per 10 minutes per IP
- Admin actions: 100 requests per hour per user
- Automatic cleanup of expired rate limit entries

#### Authentication & Authorization
- Admin-only access to management features
- Development mode bypass for testing
- Session-based authentication
- Admin action logging for audit trail

#### Data Protection
- SMTP password encryption using AES-256
- SQL injection prevention with parameterized queries
- XSS protection through content sanitization
- CSRF protection through proper headers

## Configuration

### Environment Variables
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://swiftafricasafaris.com

# Security Keys
NEWSLETTER_ENCRYPTION_KEY=your_encryption_key
NEWSLETTER_SECRET=your_newsletter_secret
ADMIN_SESSION_SECRET=your_admin_session_secret
```

### SMTP Configuration
Default Hostinger SMTP settings:
- **Host:** smtp.hostinger.com
- **Port:** 465
- **Secure:** true (SSL/TLS)
- **Email:** <EMAIL>

## Usage Guide

### 1. Initial Setup
1. Run database migrations to create tables
2. Configure SMTP settings in admin panel
3. Test email configuration
4. Add newsletter component to website

### 2. Managing Subscribers
1. Navigate to `/admin/newsletter`
2. View subscriber statistics and list
3. Search, filter, and export subscribers
4. Manually add/remove subscribers as needed

### 3. Sending Newsletters
1. Go to `/admin/newsletter/compose`
2. Write subject and content (HTML or plain text)
3. Preview newsletter before sending
4. Send to all active subscribers
5. Monitor delivery statistics

### 4. Monitoring & Analytics
- View subscriber growth trends
- Track newsletter open rates (if implemented)
- Monitor unsubscribe rates
- Review admin action logs

## Email Templates

### Newsletter Template Features
- Responsive design for mobile and desktop
- Swift Africa Safaris branding
- Automatic unsubscribe links
- Professional footer with contact information
- Support for both HTML and plain text

### Unsubscribe Page
- Branded unsubscribe confirmation page
- Immediate status update in database
- User-friendly success/error messages
- Link back to main website

## Best Practices

### Content Guidelines
- Keep subject lines under 50 characters
- Use clear, engaging content
- Include call-to-action buttons
- Test emails before sending
- Respect subscriber preferences

### Security Recommendations
- Regularly rotate SMTP passwords
- Monitor admin action logs
- Use strong encryption keys
- Keep software dependencies updated
- Implement proper backup procedures

### Performance Optimization
- Send emails in batches (10 per batch)
- Add delays between batches
- Monitor SMTP server limits
- Clean up old logs regularly
- Use database indexes for queries

## Troubleshooting

### Common Issues

#### SMTP Connection Errors
- Verify SMTP credentials
- Check firewall settings
- Confirm port and security settings
- Test with different email providers

#### High Unsubscribe Rates
- Review email content quality
- Check sending frequency
- Verify subscriber consent
- Improve email design

#### Database Performance
- Monitor query execution times
- Add indexes for frequently queried columns
- Clean up old logs and data
- Optimize pagination queries

### Error Codes
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (admin privileges required)
- `409` - Conflict (duplicate email)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Maintenance

### Regular Tasks
- Clean up old admin logs (90+ days)
- Monitor subscriber growth trends
- Review and update email templates
- Test SMTP configuration monthly
- Backup subscriber data

### Updates & Migrations
- Follow semantic versioning
- Test in development environment
- Backup database before updates
- Monitor for breaking changes
- Update documentation as needed

## Support

For technical support or questions about the newsletter system:
1. Check this documentation first
2. Review error logs in admin panel
3. Test in development environment
4. Contact system administrator

---

**Last Updated:** December 2024  
**Version:** 1.0.0  
**Maintainer:** Swift Africa Safaris Development Team
