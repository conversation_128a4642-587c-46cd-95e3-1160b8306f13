'use client'

import React from 'react'
import Link from 'next/link'
import TravelCard from '../cards/blogCard'
import { RelatedPost } from '@/lib/blog-service'

interface RelatedPostsProps {
  posts: RelatedPost[]
  currentCategory: string
}

const RelatedPosts: React.FC<RelatedPostsProps> = ({ posts, currentCategory }) => {
  if (!posts || posts.length === 0) {
    return null
  }

  return (
    <section className="bg-gray-50 py-16">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              More from {currentCategory}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover more amazing stories and insights about African safaris and travel experiences.
            </p>
          </div>

          {/* Related Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {posts.map((post) => (
              <TravelCard
                key={post.id}
                image={post.hero_image_url}
                title={post.title}
                description={post.description}
                slug={post.slug}
              />
            ))}
          </div>

          {/* View All Posts Link */}
          <div className="text-center mt-12">
            <Link
              href="/blog"
              className="inline-flex items-center gap-2 bg-accent text-white px-8 py-3 rounded-lg font-semibold transition-colors hover:brightness-90"
              style={{
                backgroundColor: 'var(--accent)',
              }}
            >
              <span>View All Blog Posts</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RelatedPosts
