import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixOversizedBlog() {
  console.log('🔧 Fixing oversized blog post: mountain-gorilla-trekking-in-rwanda-and-uganda\n');
  
  try {
    // Get the current blog post
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, hero_image_url, og_image_url')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (postError || !post) {
      console.error('❌ Error fetching blog post:', postError);
      return;
    }
    
    console.log(`✅ Found blog post: ${post.title}`);
    console.log(`📊 Current hero_image_url size: ${(post.hero_image_url.length / 1024).toFixed(2)} KB`);
    
    // Replace the base64 image with a proper URL
    // Using a suitable gorilla trekking image from Unsplash
    const newHeroImageUrl = 'https://images.unsplash.com/photo-1551632811-561732d1e306?auto=format&fit=crop&w=1200&q=80';
    const newOgImageUrl = 'https://images.unsplash.com/photo-1551632811-561732d1e306?auto=format&fit=crop&w=1200&q=80';
    
    console.log(`\n🔄 Updating blog post with proper image URLs...`);
    console.log(`   New hero image: ${newHeroImageUrl}`);
    console.log(`   New OG image: ${newOgImageUrl}`);
    
    // Update the blog post
    const { data: updatedPost, error: updateError } = await supabase
      .from('sas_blog_posts')
      .update({
        hero_image_url: newHeroImageUrl,
        og_image_url: newOgImageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', post.id)
      .select('id, title, hero_image_url, og_image_url');
    
    if (updateError) {
      console.error('❌ Error updating blog post:', updateError);
      return;
    }

    console.log(`\n✅ Successfully updated blog post!`);
    if (updatedPost && updatedPost.length > 0) {
      console.log(`📊 New hero_image_url size: ${(updatedPost[0].hero_image_url.length / 1024).toFixed(2)} KB`);
      console.log(`📊 Size reduction: ${((post.hero_image_url.length - updatedPost[0].hero_image_url.length) / 1024 / 1024).toFixed(2)} MB`);
    } else {
      console.log(`📊 New hero_image_url size: ${(newHeroImageUrl.length / 1024).toFixed(2)} KB`);
      console.log(`📊 Size reduction: ${((post.hero_image_url.length - newHeroImageUrl.length) / 1024 / 1024).toFixed(2)} MB`);
    }
    
    // Verify the fix
    console.log(`\n🔍 Verifying the fix...`);
    
    const { data: verifyPost, error: verifyError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (verifyError || !verifyPost) {
      console.error('❌ Error verifying fix:', verifyError);
      return;
    }
    
    const totalSize = JSON.stringify(verifyPost).length;
    console.log(`📊 New total post size: ${(totalSize / 1024).toFixed(2)} KB (${(totalSize / 1024 / 1024).toFixed(2)} MB)`);
    
    if (totalSize < 1024 * 1024) { // Less than 1MB
      console.log(`✅ Post size is now within acceptable limits!`);
    } else {
      console.log(`⚠️  Post size is still large but should be under Vercel's limit`);
    }
    
    console.log(`\n🎉 Fix completed! The blog post should now deploy successfully.`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fixOversizedBlog();
