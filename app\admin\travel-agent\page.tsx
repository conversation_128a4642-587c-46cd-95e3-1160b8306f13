'use client';

import React, { useState, useEffect } from 'react';
import { Briefcase, Plus, Search, Eye, Edit, Trash2, Users, DollarSign, Calendar, Link as LinkIcon, UserCheck, UserX } from 'lucide-react';
import Link from 'next/link';

interface TravelAgent {
  id: number;
  partnerName: string;
  companyName: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  totalBookings: number;
  monthlyRevenue: number;
  commissionRate: number;
  joinDate: string;
  lastActivity: string;
  apiKey: string;
  country: string;
  website?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function TravelAgentManagementPage() {
  const [partners, setPartners] = useState<TravelAgent[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockPartners: TravelAgent[] = [
      {
        id: 1,
        partnerName: 'John Safari Tours',
        companyName: 'Safari Adventures Ltd',
        email: '<EMAIL>',
        phone: '+44 20 1234 5678',
        status: 'active',
        totalBookings: 45,
        monthlyRevenue: 12500,
        commissionRate: 15,
        joinDate: '2024-01-15',
        lastActivity: '2024-12-20',
        apiKey: 'sa_live_abc123def456',
        country: 'United Kingdom',
        website: 'https://safariadventures.com'
      },
      {
        id: 2,
        partnerName: 'African Dreams Travel',
        companyName: 'Dreams Travel Agency',
        email: '<EMAIL>',
        phone: '****** 123 4567',
        status: 'active',
        totalBookings: 32,
        monthlyRevenue: 8900,
        commissionRate: 12,
        joinDate: '2024-02-20',
        lastActivity: '2024-12-19',
        apiKey: 'sa_live_xyz789ghi012',
        country: 'United States',
        website: 'https://africandreams.com'
      },
      {
        id: 3,
        partnerName: 'Wild Kenya Safaris',
        companyName: 'Wild Kenya Ltd',
        email: '<EMAIL>',
        phone: '+254 20 123 4567',
        status: 'pending',
        totalBookings: 0,
        monthlyRevenue: 0,
        commissionRate: 10,
        joinDate: '2024-12-15',
        lastActivity: '2024-12-15',
        apiKey: 'sa_test_pending123',
        country: 'Kenya'
      }
    ];
    setPartners(mockPartners);
  }, []);

  const filteredPartners = partners.filter(partner => {
    const matchesSearch = partner.partnerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         partner.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         partner.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || partner.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredPartners.length / itemsPerPage);
  const currentPartners = filteredPartners.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalStats = {
    totalPartners: partners.length,
    activeBookings: partners.reduce((sum, p) => sum + p.totalBookings, 0),
    monthlyRevenue: partners.reduce((sum, p) => sum + p.monthlyRevenue, 0),
    pendingApprovals: partners.filter(p => p.status === 'pending').length
  };

  const generatePartnerLink = (partnerId: number) => {
    return `${window.location.origin}/partner/auth/login?partner=${partnerId}`;
  };

  const handleStatusChange = (partnerId: number, newStatus: TravelAgent['status']) => {
    setPartners(prev => prev.map(partner => 
      partner.id === partnerId ? { ...partner, status: newStatus } : partner
    ));
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Briefcase className="mr-3 text-blue-600" />
                Travel Agent B2B Management
              </h1>
              <p className="text-gray-600 mt-1">Manage travel agent partners and their bookings</p>
            </div>
            <div className="flex items-center space-x-3">
              <Link href="/admin/travel-agent/bookings">
                <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
                  <Calendar size={16} className="mr-2" />
                  View Bookings
                </button>
              </Link>
              <Link href="/admin/travel-agent/messages">
                <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
                  <Users size={16} className="mr-2" />
                  Messages
                </button>
              </Link>
              <button className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center">
                <Plus size={16} className="mr-2" />
                Add Partner
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-blue-600">Total Partners</p>
                  <p className="text-2xl font-bold text-blue-900">{totalStats.totalPartners}</p>
                </div>
              </div>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-green-600">Active Bookings</p>
                  <p className="text-2xl font-bold text-green-900">{totalStats.activeBookings}</p>
                </div>
              </div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-purple-600">Monthly Revenue</p>
                  <p className="text-2xl font-bold text-purple-900">${totalStats.monthlyRevenue.toLocaleString()}</p>
                </div>
              </div>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-yellow-600">Pending Approvals</p>
                  <p className="text-2xl font-bold text-yellow-900">{totalStats.pendingApprovals}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search partners..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
            <div className="text-sm text-gray-600">
              {filteredPartners.length} partner{filteredPartners.length !== 1 ? 's' : ''} found
            </div>
          </div>
        </div>

        {/* Partners Table */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Partner</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Status</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Bookings</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Revenue</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Commission</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Last Activity</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentPartners.map((partner) => (
                  <tr key={partner.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{partner.partnerName}</div>
                        <div className="text-sm text-gray-500">{partner.companyName}</div>
                        <div className="text-sm text-gray-500">{partner.email}</div>
                        <div className="text-sm text-gray-500">{partner.country}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(partner.status)}`}>
                        {partner.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-semibold text-gray-900">{partner.totalBookings}</div>
                      <div className="text-sm text-gray-500">total bookings</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-semibold text-green-600">${partner.monthlyRevenue.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">this month</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-semibold text-purple-600">{partner.commissionRate}%</div>
                      <div className="text-sm text-gray-500">commission rate</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-gray-900">{new Date(partner.lastActivity).toLocaleDateString()}</div>
                      <div className="text-sm text-gray-500">
                        Joined {new Date(partner.joinDate).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <button 
                          className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                          title="View Details"
                        >
                          <Eye size={16} />
                        </button>
                        <button 
                          className="p-1 text-green-600 hover:bg-green-50 rounded"
                          title="Edit Partner"
                        >
                          <Edit size={16} />
                        </button>
                        <button 
                          className="p-1 text-purple-600 hover:bg-purple-50 rounded"
                          title="Generate Partner Link"
                          onClick={() => {
                            const link = generatePartnerLink(partner.id);
                            navigator.clipboard.writeText(link);
                            alert('Partner link copied to clipboard!');
                          }}
                        >
                          <LinkIcon size={16} />
                        </button>
                        {partner.status === 'active' ? (
                          <button 
                            className="p-1 text-red-600 hover:bg-red-50 rounded"
                            title="Suspend Partner"
                            onClick={() => handleStatusChange(partner.id, 'suspended')}
                          >
                            <UserX size={16} />
                          </button>
                        ) : partner.status === 'pending' ? (
                          <button 
                            className="p-1 text-green-600 hover:bg-green-50 rounded"
                            title="Approve Partner"
                            onClick={() => handleStatusChange(partner.id, 'active')}
                          >
                            <UserCheck size={16} />
                          </button>
                        ) : (
                          <button 
                            className="p-1 text-green-600 hover:bg-green-50 rounded"
                            title="Activate Partner"
                            onClick={() => handleStatusChange(partner.id, 'active')}
                          >
                            <UserCheck size={16} />
                          </button>
                        )}
                        <button 
                          className="p-1 text-red-600 hover:bg-red-50 rounded"
                          title="Delete Partner"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredPartners.length)} of {filteredPartners.length} partners
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-1 border rounded-md ${
                      currentPage === page
                        ? 'bg-[var(--accent)] text-white border-[var(--accent)]'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
