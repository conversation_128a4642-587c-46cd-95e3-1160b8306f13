import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get('url') || '/'
    
    // Perform comprehensive SEO health check
    const healthCheck = await performSEOHealthCheck(url)
    
    return NextResponse.json(healthCheck)

  } catch (error) {
    console.error('SEO Health Check error:', error)
    return NextResponse.json(
      { error: 'Failed to perform SEO health check' },
      { status: 500 }
    )
  }
}

async function performSEOHealthCheck(url: string) {
  const baseUrl = 'https://swiftafricasafaris.com'
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`
  
  const results = {
    url: fullUrl,
    timestamp: new Date().toISOString(),
    score: 0,
    checks: {
      technical: await checkTechnicalSEO(fullUrl),
      content: await checkContentSEO(url),
      performance: await checkPerformanceSEO(url),
      indexing: await checkIndexingSEO(url)
    }
  }

  // Calculate overall score
  const allChecks = [
    ...results.checks.technical,
    ...results.checks.content,
    ...results.checks.performance,
    ...results.checks.indexing
  ]
  
  const passedChecks = allChecks.filter(check => check.status === 'pass').length
  results.score = Math.round((passedChecks / allChecks.length) * 100)

  return results
}

async function checkTechnicalSEO(fullUrl: string) {
  const checks = []

  try {
    // Check if URL is accessible
    const response = await fetch(fullUrl, { 
      method: 'HEAD',
      headers: {
        'User-Agent': 'SEO-Health-Checker/1.0'
      }
    })

    checks.push({
      name: 'URL Accessibility',
      status: response.ok ? 'pass' : 'fail',
      message: response.ok ? 'URL is accessible' : `HTTP ${response.status}: ${response.statusText}`,
      impact: 'high'
    })

    // Check response headers
    const headers = response.headers

    checks.push({
      name: 'Content-Type Header',
      status: headers.get('content-type')?.includes('text/html') ? 'pass' : 'fail',
      message: headers.get('content-type') || 'No content-type header',
      impact: 'medium'
    })

    checks.push({
      name: 'X-Robots-Tag Header',
      status: headers.get('x-robots-tag') ? 'pass' : 'warning',
      message: headers.get('x-robots-tag') || 'No X-Robots-Tag header found',
      impact: 'low'
    })

    checks.push({
      name: 'Cache-Control Header',
      status: headers.get('cache-control') ? 'pass' : 'warning',
      message: headers.get('cache-control') || 'No Cache-Control header found',
      impact: 'medium'
    })

    checks.push({
      name: 'Security Headers',
      status: headers.get('x-content-type-options') && headers.get('x-frame-options') ? 'pass' : 'warning',
      message: 'Security headers present',
      impact: 'medium'
    })

  } catch (error) {
    checks.push({
      name: 'URL Accessibility',
      status: 'fail',
      message: `Failed to fetch URL: ${error}`,
      impact: 'high'
    })
  }

  return checks
}

async function checkContentSEO(url: string) {
  const checks = []

  try {
    // Check if it's a dynamic route that should have content
    if (url.startsWith('/blog/')) {
      const slug = url.replace('/blog/', '')
      const { data: blogPost } = await supabase
        .from('blog_posts')
        .select('title, seo_title, seo_description, seo_keywords, status')
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (blogPost) {
        checks.push({
          name: 'Content Exists',
          status: 'pass',
          message: 'Blog post found and published',
          impact: 'high'
        })

        checks.push({
          name: 'SEO Title',
          status: blogPost.seo_title ? 'pass' : 'warning',
          message: blogPost.seo_title ? `Title: ${blogPost.seo_title}` : 'No custom SEO title set',
          impact: 'high'
        })

        checks.push({
          name: 'SEO Description',
          status: blogPost.seo_description ? 'pass' : 'warning',
          message: blogPost.seo_description ? 'Meta description set' : 'No meta description set',
          impact: 'high'
        })

        checks.push({
          name: 'SEO Keywords',
          status: blogPost.seo_keywords && blogPost.seo_keywords.length > 0 ? 'pass' : 'warning',
          message: blogPost.seo_keywords ? `${blogPost.seo_keywords.length} keywords set` : 'No keywords set',
          impact: 'medium'
        })
      } else {
        checks.push({
          name: 'Content Exists',
          status: 'fail',
          message: 'Blog post not found or not published',
          impact: 'high'
        })
      }
    } else if (url.startsWith('/package/')) {
      const slug = url.replace('/package/', '')
      const { data: packageData } = await supabase
        .from('packages')
        .select('title, seoTitle, seoDescription, seoKeywords, status')
        .eq('slug', slug)
        .eq('status', 'published')
        .single()

      if (packageData) {
        checks.push({
          name: 'Content Exists',
          status: 'pass',
          message: 'Package found and published',
          impact: 'high'
        })

        checks.push({
          name: 'SEO Title',
          status: packageData.seoTitle ? 'pass' : 'warning',
          message: packageData.seoTitle ? `Title: ${packageData.seoTitle}` : 'No custom SEO title set',
          impact: 'high'
        })

        checks.push({
          name: 'SEO Description',
          status: packageData.seoDescription ? 'pass' : 'warning',
          message: packageData.seoDescription ? 'Meta description set' : 'No meta description set',
          impact: 'high'
        })
      } else {
        checks.push({
          name: 'Content Exists',
          status: 'fail',
          message: 'Package not found or not published',
          impact: 'high'
        })
      }
    } else {
      // Static page - assume content exists
      checks.push({
        name: 'Content Exists',
        status: 'pass',
        message: 'Static page should have content',
        impact: 'high'
      })
    }

  } catch (error) {
    checks.push({
      name: 'Content Check',
      status: 'fail',
      message: `Failed to check content: ${error}`,
      impact: 'high'
    })
  }

  return checks
}

async function checkPerformanceSEO(url: string) {
  const checks = []

  try {
    // Get recent performance metrics for this URL
    const { data: metrics } = await supabase
      .from('performance_metrics')
      .select('metric_name, metric_value')
      .eq('url', url)
      .gte('timestamp', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
      .order('timestamp', { ascending: false })
      .limit(100)

    if (metrics && metrics.length > 0) {
      // Check Core Web Vitals
      const lcpValues = metrics.filter(m => m.metric_name === 'LCP').map(m => parseFloat(m.metric_value))
      const fidValues = metrics.filter(m => m.metric_name === 'FID').map(m => parseFloat(m.metric_value))
      const clsValues = metrics.filter(m => m.metric_name === 'CLS').map(m => parseFloat(m.metric_value))

      if (lcpValues.length > 0) {
        const avgLCP = lcpValues.reduce((sum, val) => sum + val, 0) / lcpValues.length
        checks.push({
          name: 'Largest Contentful Paint (LCP)',
          status: avgLCP <= 2500 ? 'pass' : avgLCP <= 4000 ? 'warning' : 'fail',
          message: `Average LCP: ${Math.round(avgLCP)}ms`,
          impact: 'high'
        })
      }

      if (fidValues.length > 0) {
        const avgFID = fidValues.reduce((sum, val) => sum + val, 0) / fidValues.length
        checks.push({
          name: 'First Input Delay (FID)',
          status: avgFID <= 100 ? 'pass' : avgFID <= 300 ? 'warning' : 'fail',
          message: `Average FID: ${Math.round(avgFID)}ms`,
          impact: 'high'
        })
      }

      if (clsValues.length > 0) {
        const avgCLS = clsValues.reduce((sum, val) => sum + val, 0) / clsValues.length
        checks.push({
          name: 'Cumulative Layout Shift (CLS)',
          status: avgCLS <= 0.1 ? 'pass' : avgCLS <= 0.25 ? 'warning' : 'fail',
          message: `Average CLS: ${avgCLS.toFixed(3)}`,
          impact: 'high'
        })
      }
    } else {
      checks.push({
        name: 'Performance Data',
        status: 'warning',
        message: 'No recent performance data available',
        impact: 'medium'
      })
    }

  } catch (error) {
    checks.push({
      name: 'Performance Check',
      status: 'fail',
      message: `Failed to check performance: ${error}`,
      impact: 'medium'
    })
  }

  return checks
}

async function checkIndexingSEO(url: string) {
  const checks = []

  try {
    // Check recent crawl activity
    const { data: crawlEvents } = await supabase
      .from('seo_crawl_events')
      .select('user_agent, timestamp, status_code, bot_type')
      .eq('url', url)
      .gte('timestamp', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
      .order('timestamp', { ascending: false })
      .limit(50)

    if (crawlEvents && crawlEvents.length > 0) {
      const searchEngineCrawls = crawlEvents.filter(e => e.bot_type === 'search_engine')
      const recentCrawls = crawlEvents.filter(e => 
        new Date(e.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      )

      checks.push({
        name: 'Search Engine Crawling',
        status: searchEngineCrawls.length > 0 ? 'pass' : 'warning',
        message: `${searchEngineCrawls.length} search engine crawls in last 7 days`,
        impact: 'high'
      })

      checks.push({
        name: 'Recent Crawl Activity',
        status: recentCrawls.length > 0 ? 'pass' : 'warning',
        message: `${recentCrawls.length} crawls in last 24 hours`,
        impact: 'medium'
      })

      const errorCrawls = crawlEvents.filter(e => e.status_code >= 400)
      checks.push({
        name: 'Crawl Errors',
        status: errorCrawls.length === 0 ? 'pass' : 'fail',
        message: errorCrawls.length === 0 ? 'No crawl errors' : `${errorCrawls.length} crawl errors found`,
        impact: 'high'
      })
    } else {
      checks.push({
        name: 'Crawl Activity',
        status: 'warning',
        message: 'No recent crawl activity detected',
        impact: 'medium'
      })
    }

  } catch (error) {
    checks.push({
      name: 'Indexing Check',
      status: 'fail',
      message: `Failed to check indexing: ${error}`,
      impact: 'medium'
    })
  }

  return checks
}
