'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import FormattingToolbar from './FormattingToolbar';
import { tinyMCEConfig } from '@/lib/config';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  height?: number;
  disabled?: boolean;
  className?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder = "Start typing...",
  height = 200,
  disabled = false,
  className = ""
}) => {
  const editorRef = useRef<any>(null);
  const [showToolbar, setShowToolbar] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 0, y: 0 });

  // Validate TinyMCE API key
  useEffect(() => {
    if (!tinyMCEConfig.apiKey || tinyMCEConfig.apiKey === "no-api-key") {
      console.warn('TinyMCE API key is not configured. Please set NEXT_PUBLIC_TINYMCE_API_KEY in your environment variables.');
      console.warn('Current API key value:', tinyMCEConfig.apiKey);
    } else {
      console.log('TinyMCE API key configured successfully');
    }
  }, []);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!editorRef.current) return;

      const editor = editorRef.current;
      const isCtrl = e.ctrlKey || e.metaKey;

      if (isCtrl) {
        switch (e.key.toLowerCase()) {
          case 'b':
            e.preventDefault();
            editor.execCommand('Bold');
            break;
          case 'i':
            e.preventDefault();
            editor.execCommand('Italic');
            break;
          case 'u':
            e.preventDefault();
            editor.execCommand('Underline');
            break;
          case 'k':
            e.preventDefault();
            editor.execCommand('mceLink');
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleEditorChange = (content: string) => {
    onChange(content);
  };

  // Handle text selection for floating toolbar
  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().length > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();

      setToolbarPosition({
        x: rect.left + rect.width / 2,
        y: rect.top + window.scrollY
      });
      setShowToolbar(true);
    } else {
      setShowToolbar(false);
    }
  };

  // Handle formatting commands from toolbar
  const handleFormat = (command: string, value?: string) => {
    if (editorRef.current) {
      if (command === 'insertHTML' && value) {
        editorRef.current.execCommand('insertHTML', false, value);
      } else {
        editorRef.current.execCommand(command);
      }
    }
  };

  const editorConfig = {
    height,
    menubar: false,
    placeholder,
    plugins: [
      'advlist', 'autolink', 'lists', 'link', 'charmap',
      'searchreplace', 'visualblocks', 'code', 'fullscreen',
      'insertdatetime', 'table', 'help', 'wordcount'
    ],
    toolbar: 'bold italic underline | link unlink | bullist numlist | undo redo | removeformat',
    content_style: `
      body { 
        font-family: 'Jost', sans-serif; 
        font-size: 14px; 
        line-height: 1.6;
        color: #374151;
        padding: 8px;
      }
      p { margin: 0 0 8px 0; }
      a { color: #2563eb; text-decoration: underline; }
      a:hover { color: #1d4ed8; }
      ul, ol { margin: 8px 0; padding-left: 20px; }
      li { margin: 2px 0; }
    `,
    skin: 'oxide',
    content_css: false,
    branding: false,
    resize: false,
    statusbar: false,
    valid_elements: 'p,br,strong,em,u,a[href],ul,ol,li',
    valid_styles: {},
    forced_root_block: 'p',
    force_br_newlines: false,
    force_p_newlines: true,
    remove_trailing_brs: true,
    convert_urls: false,
    relative_urls: false,
    setup: (editor: any) => {
      editorRef.current = editor;
      
      // Prevent drag events from bubbling up to parent containers
      editor.on('init', () => {
        const editorBody = editor.getBody();
        if (editorBody) {
          editorBody.addEventListener('dragstart', (e: DragEvent) => {
            e.stopPropagation();
          });
          editorBody.addEventListener('drag', (e: DragEvent) => {
            e.stopPropagation();
          });
          editorBody.addEventListener('dragend', (e: DragEvent) => {
            e.stopPropagation();
          });

          // Add text selection event listeners for floating toolbar
          editorBody.addEventListener('mouseup', handleTextSelection);
          editorBody.addEventListener('keyup', handleTextSelection);
        }
      });

      // Add keyboard shortcut handlers within editor
      editor.addShortcut('ctrl+b', 'Bold text', () => {
        editor.execCommand('Bold');
      });

      editor.addShortcut('ctrl+i', 'Italic text', () => {
        editor.execCommand('Italic');
      });

      editor.addShortcut('ctrl+u', 'Underline text', () => {
        editor.execCommand('Underline');
      });

      editor.addShortcut('ctrl+k', 'Insert link', () => {
        const selectedText = editor.selection.getContent({ format: 'text' });
        const url = prompt('Enter URL:', 'https://');

        if (url && url.trim()) {
          if (selectedText) {
            editor.insertContent(`<a href="${url.trim()}">${selectedText}</a>`);
          } else {
            const linkText = prompt('Enter link text:', url.trim());
            if (linkText) {
              editor.insertContent(`<a href="${url.trim()}">${linkText}</a>`);
            }
          }
        }
      });

      // Handle link insertion
      editor.ui.registry.addButton('link', {
        icon: 'link',
        tooltip: 'Insert/edit link (Ctrl+K)',
        onAction: () => {
          const selectedText = editor.selection.getContent({ format: 'text' });
          const url = prompt('Enter URL:', 'https://');

          if (url && url.trim()) {
            if (selectedText) {
              editor.insertContent(`<a href="${url.trim()}">${selectedText}</a>`);
            } else {
              const linkText = prompt('Enter link text:', url.trim());
              if (linkText) {
                editor.insertContent(`<a href="${url.trim()}">${linkText}</a>`);
              }
            }
          }
        }
      });

      // Custom formatting buttons with keyboard shortcut tooltips
      editor.ui.registry.addButton('bold', {
        icon: 'bold',
        tooltip: 'Bold (Ctrl+B)',
        onAction: () => editor.execCommand('Bold')
      });

      editor.ui.registry.addButton('italic', {
        icon: 'italic',
        tooltip: 'Italic (Ctrl+I)',
        onAction: () => editor.execCommand('Italic')
      });

      editor.ui.registry.addButton('underline', {
        icon: 'underline',
        tooltip: 'Underline (Ctrl+U)',
        onAction: () => editor.execCommand('Underline')
      });
    }
  };

  return (
    <div className={`rich-text-editor ${className}`}>
      <Editor
        apiKey={tinyMCEConfig.apiKey || "no-api-key"}
        value={value}
        onEditorChange={handleEditorChange}
        init={editorConfig}
        disabled={disabled}
      />

      {/* Floating formatting toolbar */}
      <FormattingToolbar
        onFormat={handleFormat}
        isVisible={showToolbar}
        position={toolbarPosition}
      />
    </div>
  );
};

export default RichTextEditor;
