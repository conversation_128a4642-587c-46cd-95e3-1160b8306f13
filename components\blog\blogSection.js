"use client";

import React, { useState, useEffect } from 'react';
import TravelCard from '../cards/blogCard';
import { SkeletonGrid } from '../skeleton';
import { Search, Filter, X } from 'lucide-react';

const BlogSection = () => {
    const [posts, setPosts] = useState([]);
    const [filteredPosts, setFilteredPosts] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [error, setError] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [categories, setCategories] = useState([]);
    const [pagination, setPagination] = useState({
        currentPage: 1,
        totalPages: 1,
        totalPosts: 0,
        hasNextPage: false
    });

    // Fetch blog posts from API
    const fetchPosts = async (page = 1, search = '', category = '') => {
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: '6',
                ...(search && { search }),
                ...(category && { category })
            });

            const response = await fetch(`/api/blog?${params}`);
            const data = await response.json();

            if (data.success) {
                if (page === 1) {
                    setPosts(data.data.posts);
                    setFilteredPosts(data.data.posts);
                } else {
                    setPosts(prev => [...prev, ...data.data.posts]);
                    setFilteredPosts(prev => [...prev, ...data.data.posts]);
                }
                setPagination(data.data.pagination);
            } else {
                setError(data.error || 'Failed to fetch blog posts');
            }
        } catch (err) {
            console.error('Error fetching posts:', err);
            setError('Failed to fetch blog posts');
        }
    };

    // Fetch categories for filter
    const fetchCategories = async () => {
        try {
            const response = await fetch('/api/blog', { method: 'OPTIONS' });
            const data = await response.json();

            if (data.success) {
                setCategories(data.data.categories);
            }
        } catch (err) {
            console.error('Error fetching categories:', err);
        }
    };

    useEffect(() => {
        const loadInitialData = async () => {
            setIsLoading(true);
            await Promise.all([
                fetchPosts(1, searchTerm, selectedCategory),
                fetchCategories()
            ]);
            setIsLoading(false);
        };

        loadInitialData();
    }, []);

    // Handle search and filter changes
    useEffect(() => {
        const delayedSearch = setTimeout(() => {
            if (searchTerm !== '' || selectedCategory !== '') {
                setIsLoading(true);
                fetchPosts(1, searchTerm, selectedCategory).finally(() => {
                    setIsLoading(false);
                });
            } else if (searchTerm === '' && selectedCategory === '') {
                setIsLoading(true);
                fetchPosts(1).finally(() => {
                    setIsLoading(false);
                });
            }
        }, 500);

        return () => clearTimeout(delayedSearch);
    }, [searchTerm, selectedCategory]);

    const handleLoadMore = async () => {
        if (!pagination.hasNextPage || isLoadingMore) return;

        setIsLoadingMore(true);
        await fetchPosts(pagination.currentPage + 1, searchTerm, selectedCategory);
        setIsLoadingMore(false);
    };

    const clearFilters = () => {
        setSearchTerm('');
        setSelectedCategory('');
    };

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="text-center text-red-600">
                    <p>Error loading blog posts: {error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="btn mt-4 py-2 px-4 rounded"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/* Search and Filter Section */}
            <div className="mb-8 space-y-4">
                <div className="flex flex-col md:flex-row gap-4">
                    {/* Search Input */}
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <input
                            type="text"
                            placeholder="Search blog posts..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            style={{ fontFamily: 'Jost, sans-serif' }}
                        />
                    </div>

                    {/* Category Filter */}
                    <div className="relative">
                        <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                        <select
                            value={selectedCategory}
                            onChange={(e) => setSelectedCategory(e.target.value)}
                            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white min-w-[200px]"
                            style={{ fontFamily: 'Jost, sans-serif' }}
                        >
                            <option value="">All Categories</option>
                            {categories.map((category) => (
                                <option key={category} value={category}>
                                    {category}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Clear Filters */}
                    {(searchTerm || selectedCategory) && (
                        <button
                            onClick={clearFilters}
                            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
                            style={{ fontFamily: 'Jost, sans-serif' }}
                        >
                            <X className="w-4 h-4" />
                            Clear
                        </button>
                    )}
                </div>

                {/* Results Info */}
                {!isLoading && (
                    <div className="text-sm text-gray-600" style={{ fontFamily: 'Jost, sans-serif' }}>
                        {pagination.totalPosts > 0 ? (
                            <>
                                Showing {filteredPosts.length} of {pagination.totalPosts} posts
                                {(searchTerm || selectedCategory) && (
                                    <span>
                                        {searchTerm && ` for "${searchTerm}"`}
                                        {selectedCategory && ` in "${selectedCategory}"`}
                                    </span>
                                )}
                            </>
                        ) : (
                            'No posts found'
                        )}
                    </div>
                )}
            </div>

            {/* Loading State */}
            {isLoading ? (
                <SkeletonGrid
                    variant="blog"
                    count={6}
                    cardProps={{ showButton: true }}
                />
            ) : (
                <>
                    {/* Blog Posts Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredPosts.map((post) => (
                            <TravelCard
                                key={post.slug}
                                image={post.hero_image_url}
                                title={post.title}
                                description={post.description}
                                slug={post.slug}
                            />
                        ))}
                    </div>

                    {/* No Results */}
                    {filteredPosts.length === 0 && !isLoading && (
                        <div className="text-center py-12">
                            <p className="text-gray-600 text-lg" style={{ fontFamily: 'Jost, sans-serif' }}>
                                No blog posts found.
                            </p>
                            {(searchTerm || selectedCategory) && (
                                <button
                                    onClick={clearFilters}
                                    className="btn mt-4 py-2 px-4 rounded"
                                >
                                    Clear Filters
                                </button>
                            )}
                        </div>
                    )}

                    {/* Load More Button */}
                    {pagination.hasNextPage && (
                        <div className="text-center mt-8">
                            {isLoadingMore ? (
                                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                            ) : (
                                <button
                                    onClick={handleLoadMore}
                                    className="btn text-white font-normal py-2 px-4 rounded"
                                    data-testid="load-more-button"
                                    style={{ fontFamily: 'Jost, sans-serif' }}
                                >
                                    Load More ({pagination.totalPosts - filteredPosts.length} remaining)
                                </button>
                            )}
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default BlogSection;
