'use client';

import React, { useState, useEffect } from 'react';
import { Search, Globe, Share2, Eye, AlertCircle, CheckCircle } from 'lucide-react';

interface BlogFormData {
  title: string;
  description: string;
  category: string;
  tags: string[];
  slug: string;
  seo_title: string;
  seo_description: string;
  seo_keywords: string[];
  og_title: string;
  og_description: string;
  og_image_url: string;
  canonical_url: string;
  robots_index: string;
  robots_follow: string;
  schema_data: any;
}

interface BlogSEOProps {
  formData: BlogFormData;
  onFormDataChange: (data: Partial<BlogFormData>) => void;
}

const BlogSEO: React.FC<BlogSEOProps> = ({ formData, onFormDataChange }) => {
  const [activeTab, setActiveTab] = useState('basic');
  const [keywordInput, setKeywordInput] = useState('');
  const [seoScore, setSeoScore] = useState(0);
  const [seoIssues, setSeoIssues] = useState<string[]>([]);

  // Calculate SEO score and issues
  useEffect(() => {
    const issues: string[] = [];
    let score = 0;

    // Title checks
    if (formData.seo_title) {
      if (formData.seo_title.length >= 30 && formData.seo_title.length <= 60) {
        score += 20;
      } else {
        issues.push('SEO title should be between 30-60 characters');
      }
    } else {
      issues.push('SEO title is required');
    }

    // Description checks
    if (formData.seo_description) {
      if (formData.seo_description.length >= 120 && formData.seo_description.length <= 160) {
        score += 20;
      } else {
        issues.push('SEO description should be between 120-160 characters');
      }
    } else {
      issues.push('SEO description is required');
    }

    // Keywords checks
    if (formData.seo_keywords && formData.seo_keywords.length > 0) {
      score += 15;
      if (formData.seo_keywords.length > 10) {
        issues.push('Too many keywords (max 10 recommended)');
      }
    } else {
      issues.push('At least one SEO keyword is recommended');
    }

    // Open Graph checks
    if (formData.og_title && formData.og_description) {
      score += 15;
    } else {
      issues.push('Open Graph title and description improve social sharing');
    }

    // Canonical URL check
    if (formData.canonical_url) {
      score += 10;
    }

    // Slug check
    if (formData.slug && formData.slug.length > 0) {
      score += 10;
      if (formData.slug.includes(' ') || formData.slug.includes('_')) {
        issues.push('Slug should use hyphens instead of spaces or underscores');
      }
    } else {
      issues.push('URL slug is required');
    }

    // Schema data check
    if (formData.schema_data && Object.keys(formData.schema_data).length > 0) {
      score += 10;
    }

    setSeoScore(score);
    setSeoIssues(issues);
  }, [formData]);

  const handleKeywordAdd = () => {
    if (keywordInput.trim() && !formData.seo_keywords?.includes(keywordInput.trim())) {
      const newKeywords = [...(formData.seo_keywords || []), keywordInput.trim()];
      onFormDataChange({ seo_keywords: newKeywords });
      setKeywordInput('');
    }
  };

  const handleKeywordRemove = (keyword: string) => {
    const newKeywords = formData.seo_keywords?.filter(k => k !== keyword) || [];
    onFormDataChange({ seo_keywords: newKeywords });
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const generateSEOTitle = () => {
    if (formData.title) {
      const seoTitle = `${formData.title} - Swift Africa Safaris Blog`;
      onFormDataChange({ seo_title: seoTitle });
    }
  };

  const generateSEODescription = () => {
    if (formData.description) {
      const seoDescription = formData.description.length > 160 
        ? formData.description.substring(0, 157) + '...'
        : formData.description;
      onFormDataChange({ seo_description: seoDescription });
    }
  };

  const generateCanonicalUrl = () => {
    if (formData.slug) {
      const canonicalUrl = `https://swiftafricasafaris.com/blog/${formData.slug}`;
      onFormDataChange({ canonical_url: canonicalUrl });
    }
  };

  const generateSchemaData = () => {
    const schema = {
      '@context': 'https://schema.org',
      '@type': 'BlogPosting',
      headline: formData.seo_title || formData.title,
      description: formData.seo_description || formData.description,
      author: {
        '@type': 'Organization',
        name: 'Swift Africa Safaris'
      },
      publisher: {
        '@type': 'Organization',
        name: 'Swift Africa Safaris',
        logo: {
          '@type': 'ImageObject',
          url: 'https://swiftafricasafaris.com/logo.png'
        }
      },
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': formData.canonical_url || `https://swiftafricasafaris.com/blog/${formData.slug}`
      },
      keywords: formData.seo_keywords?.join(', ') || '',
      articleSection: formData.category || 'Travel',
      about: {
        '@type': 'Thing',
        name: formData.category || 'African Safari Travel'
      }
    };
    onFormDataChange({ schema_data: schema });
  };

  const tabs = [
    { id: 'basic', label: 'Basic SEO', icon: <Search size={16} /> },
    { id: 'social', label: 'Social Media', icon: <Share2 size={16} /> },
    { id: 'advanced', label: 'Advanced', icon: <Globe size={16} /> },
    { id: 'preview', label: 'Preview', icon: <Eye size={16} /> }
  ];

  const getSEOScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* SEO Score Card */}
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">SEO Optimization</h2>
          <div className={`px-3 py-1 rounded-full text-sm font-medium ${getSEOScoreColor(seoScore)}`}>
            SEO Score: {seoScore}/100
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              seoScore >= 80 ? 'bg-green-500' : seoScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
            }`}
            style={{ width: `${seoScore}%` }}
          ></div>
        </div>

        {/* SEO Issues */}
        {seoIssues.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <AlertCircle size={16} className="text-orange-500" />
              SEO Recommendations
            </h3>
            <ul className="space-y-1">
              {seoIssues.map((issue, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                  <span className="text-orange-500 mt-1">•</span>
                  {issue}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {/* Basic SEO Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              {/* SEO Title */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    SEO Title *
                  </label>
                  <button
                    onClick={generateSEOTitle}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Auto-generate
                  </button>
                </div>
                <input
                  type="text"
                  value={formData.seo_title || ''}
                  onChange={(e) => onFormDataChange({ seo_title: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter SEO title (30-60 characters)"
                  maxLength={60}
                />
                <div className="flex justify-between mt-1">
                  <span className={`text-xs ${
                    (formData.seo_title?.length || 0) >= 30 && (formData.seo_title?.length || 0) <= 60
                      ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formData.seo_title?.length || 0}/60 characters
                  </span>
                  <span className="text-xs text-gray-500">
                    Optimal: 30-60 characters
                  </span>
                </div>
              </div>

              {/* SEO Description */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    SEO Description *
                  </label>
                  <button
                    onClick={generateSEODescription}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Auto-generate
                  </button>
                </div>
                <textarea
                  value={formData.seo_description || ''}
                  onChange={(e) => onFormDataChange({ seo_description: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter SEO description (120-160 characters)"
                  rows={3}
                  maxLength={160}
                />
                <div className="flex justify-between mt-1">
                  <span className={`text-xs ${
                    (formData.seo_description?.length || 0) >= 120 && (formData.seo_description?.length || 0) <= 160
                      ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formData.seo_description?.length || 0}/160 characters
                  </span>
                  <span className="text-xs text-gray-500">
                    Optimal: 120-160 characters
                  </span>
                </div>
              </div>

              {/* URL Slug */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    URL Slug *
                  </label>
                  <button
                    onClick={() => onFormDataChange({ slug: generateSlug(formData.title) })}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Generate from title
                  </button>
                </div>
                <div className="flex">
                  <span className="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm rounded-l-lg">
                    /blog/
                  </span>
                  <input
                    type="text"
                    value={formData.slug || ''}
                    onChange={(e) => onFormDataChange({ slug: e.target.value })}
                    className="flex-1 p-2 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="url-slug"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Use lowercase letters, numbers, and hyphens only
                </p>
              </div>

              {/* Keywords */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SEO Keywords
                </label>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleKeywordAdd())}
                    className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Add keyword and press Enter"
                  />
                  <button
                    onClick={handleKeywordAdd}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.seo_keywords?.map((keyword, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                    >
                      {keyword}
                      <button
                        onClick={() => handleKeywordRemove(keyword)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Add relevant keywords (max 10 recommended)
                </p>
              </div>
            </div>
          )}

          {/* Social Media Tab */}
          {activeTab === 'social' && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Open Graph Settings</h3>
                <p className="text-sm text-blue-700">
                  These settings control how your blog post appears when shared on social media platforms like Facebook, Twitter, and LinkedIn.
                </p>
              </div>

              {/* OG Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Social Media Title
                </label>
                <input
                  type="text"
                  value={formData.og_title || ''}
                  onChange={(e) => onFormDataChange({ og_title: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Title for social media sharing"
                />
                <p className="text-xs text-gray-500 mt-1">
                  If empty, will use SEO title
                </p>
              </div>

              {/* OG Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Social Media Description
                </label>
                <textarea
                  value={formData.og_description || ''}
                  onChange={(e) => onFormDataChange({ og_description: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Description for social media sharing"
                  rows={3}
                />
                <p className="text-xs text-gray-500 mt-1">
                  If empty, will use SEO description
                </p>
              </div>

              {/* OG Image */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Social Media Image URL
                </label>
                <input
                  type="url"
                  value={formData.og_image_url || ''}
                  onChange={(e) => onFormDataChange({ og_image_url: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Recommended size: 1200x630px. If empty, will use hero image.
                </p>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && (
            <div className="space-y-6">
              {/* Canonical URL */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Canonical URL
                  </label>
                  <button
                    onClick={generateCanonicalUrl}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Auto-generate
                  </button>
                </div>
                <input
                  type="url"
                  value={formData.canonical_url || ''}
                  onChange={(e) => onFormDataChange({ canonical_url: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://swiftafricasafaris.com/blog/post-slug"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Helps prevent duplicate content issues
                </p>
              </div>

              {/* Robots Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Robots Index
                  </label>
                  <select
                    value={formData.robots_index || 'index'}
                    onChange={(e) => onFormDataChange({ robots_index: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="index">Index (Allow search engines)</option>
                    <option value="noindex">No Index (Block search engines)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Robots Follow
                  </label>
                  <select
                    value={formData.robots_follow || 'follow'}
                    onChange={(e) => onFormDataChange({ robots_follow: e.target.value })}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="follow">Follow (Allow link following)</option>
                    <option value="nofollow">No Follow (Block link following)</option>
                  </select>
                </div>
              </div>

              {/* Schema Data */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Schema.org Structured Data
                  </label>
                  <button
                    onClick={generateSchemaData}
                    className="text-xs text-blue-600 hover:text-blue-800"
                  >
                    Auto-generate
                  </button>
                </div>
                <textarea
                  value={formData.schema_data ? JSON.stringify(formData.schema_data, null, 2) : ''}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      onFormDataChange({ schema_data: parsed });
                    } catch (error) {
                      // Invalid JSON, don't update
                    }
                  }}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                  placeholder="JSON-LD structured data"
                  rows={8}
                />
                <p className="text-xs text-gray-500 mt-1">
                  JSON-LD format for rich snippets in search results
                </p>
              </div>
            </div>
          )}

          {/* Preview Tab */}
          {activeTab === 'preview' && (
            <div className="space-y-6">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-4">Search Engine Preview</h3>
                <div className="bg-white p-4 rounded border">
                  <div className="text-blue-600 text-lg hover:underline cursor-pointer">
                    {formData.seo_title || formData.title || 'Blog Post Title'}
                  </div>
                  <div className="text-green-700 text-sm mt-1">
                    https://swiftafricasafaris.com/blog/{formData.slug || 'post-slug'}
                  </div>
                  <div className="text-gray-600 text-sm mt-2">
                    {formData.seo_description || formData.description || 'Blog post description will appear here...'}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-4">Social Media Preview</h3>
                <div className="bg-white border rounded-lg overflow-hidden max-w-md">
                  {formData.og_image_url && (
                    <img 
                      src={formData.og_image_url} 
                      alt="Social preview" 
                      className="w-full h-40 object-cover"
                    />
                  )}
                  <div className="p-4">
                    <div className="font-medium text-gray-900 mb-1">
                      {formData.og_title || formData.seo_title || formData.title || 'Blog Post Title'}
                    </div>
                    <div className="text-gray-600 text-sm mb-2">
                      {formData.og_description || formData.seo_description || formData.description || 'Blog post description...'}
                    </div>
                    <div className="text-gray-500 text-xs">
                      swiftafricasafaris.com
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BlogSEO;
