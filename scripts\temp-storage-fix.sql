-- Temporary Storage Fix - Allows uploads without authentication for development
-- Run this in your Supabase SQL Editor

-- Drop the existing upload policy that requires authentication
DROP POLICY IF EXISTS "Authenticated users can upload package images" ON storage.objects;

-- Create a temporary policy that allows anyone to upload (for development only)
CREATE POLICY "Anyone can upload package images (temp)" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-package-images');

-- Also allow anyone to update and delete for development
DROP POLICY IF EXISTS "Authenticated users can update package images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete package images" ON storage.objects;

CREATE POLICY "Anyone can update package images (temp)" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-package-images');

CREATE POLICY "Anyone can delete package images (temp)" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-package-images');
