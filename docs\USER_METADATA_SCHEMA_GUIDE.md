# User Metadata and Schema Input Guide

This guide explains how users can input custom metadata and schema information for reading pages (blog posts, packages, iconic destinations, and products).

## Overview

All reading pages now support user-inputted metadata and schema through data files. If user metadata/schema is provided, it will be used; otherwise, the system falls back to auto-generated metadata.

## Data Structure

### Blog Posts (`components/data/blogData.js`)

```javascript
export const blogContents = {
  'blog-slug': {
    title: 'Blog Post Title',
    heroImage: 'image-url.jpg',
    
    // User Input: SEO Metadata
    seo: {
      title: 'Custom SEO Title | Swift Africa Safaris',
      description: 'Custom meta description for search engines',
      keywords: [
        'keyword1',
        'keyword2',
        'keyword3'
      ],
      image: 'seo-image.jpg',
      author: 'Author Name',
      publishedTime: '2024-01-15T10:00:00Z',
      modifiedTime: '2024-01-20T14:00:00Z',
      category: 'Blog Category',
      tags: ['tag1', 'tag2', 'tag3']
    },
    
    // User Input: Schema.org Structured Data
    schema: {
      "@type": "Article",
      headline: "Article Headline",
      description: "Article description",
      image: "article-image.jpg",
      author: {
        "@type": "Organization",
        name: "Swift Africa Safaris"
      },
      publisher: {
        "@type": "Organization",
        name: "Swift Africa Safaris",
        logo: {
          "@type": "ImageObject",
          url: "logo-url.png"
        }
      },
      datePublished: "2024-01-15T10:00:00Z",
      dateModified: "2024-01-20T14:00:00Z",
      about: {
        "@type": "Place",
        name: "Location Name",
        description: "Location description"
      }
    },
    
    sections: [
      // Blog content sections...
    ]
  }
}
```

### Safari Packages (`components/data/packageData.js`)

```javascript
export const packageDetails = {
  "package-slug": {
    title: "Package Title",
    heroImage: "package-image.jpg",
    overview: "Package overview...",
    
    // User Input: SEO Metadata
    seo: {
      title: 'Custom Package Title | Swift Africa Safaris',
      description: 'Custom package description with pricing and highlights',
      keywords: [
        'safari package',
        'destination name',
        'wildlife tour'
      ],
      image: 'package-seo-image.jpg',
      author: 'Swift Africa Safaris',
      publishedTime: '2024-01-05T09:00:00Z',
      modifiedTime: '2024-01-20T14:00:00Z',
      category: 'Safari Packages',
      tags: ['Safari', 'Wildlife', 'Adventure']
    },
    
    // User Input: Schema.org Structured Data
    schema: {
      "@type": "TouristTrip",
      name: "Package Name",
      description: "Package description",
      image: "package-image.jpg",
      touristType: "Safari Enthusiast",
      duration: "4 Days",
      location: {
        "@type": "Place",
        name: "Destination",
        address: {
          "@type": "PostalAddress",
          addressCountry: "Country Code"
        }
      },
      offers: {
        "@type": "Offer",
        price: "1348",
        priceCurrency: "USD",
        availability: "https://schema.org/InStock",
        seller: {
          "@type": "TravelAgency",
          name: "Swift Africa Safaris"
        }
      },
      provider: {
        "@type": "TravelAgency",
        name: "Swift Africa Safaris",
        url: "https://swiftafricasafaris.com"
      }
    },
    
    // Other package data...
    pricing: { /* pricing data */ },
    itinerary: [ /* itinerary data */ ]
  }
}
```

### Iconic Destinations (`components/data/iconicData.js`)

```javascript
export const iconicData = {
  destinations: [
    {
      name: 'Tanzania',
      countryTitle: "Tanzania - The Land of Endless Plains",
      
      // User Input: SEO Metadata
      seo: {
        title: "Tanzania Safari Destination | Swift Africa Safaris",
        description: "Discover Tanzania's wildlife and attractions",
        keywords: [
          "Tanzania safari",
          "Serengeti National Park",
          "wildlife tours"
        ],
        image: "tanzania-seo-image.jpg",
        author: "Swift Africa Safaris",
        publishedTime: "2024-01-15T10:00:00Z",
        modifiedTime: "2024-01-15T10:00:00Z"
      },
      
      // User Input: Schema.org Structured Data
      schema: {
        "@type": "TouristDestination",
        name: "Tanzania - The Land of Endless Plains",
        description: "Experience the majestic Serengeti...",
        image: "tanzania-image.jpg",
        touristType: "Safari Enthusiast",
        includesAttraction: [
          {
            "@type": "TouristAttraction",
            name: "Serengeti National Park",
            description: "Home to the Great Migration"
          }
        ],
        geo: {
          "@type": "Country",
          name: "Tanzania"
        }
      },
      
      // Other destination data...
      hero: { /* hero data */ },
      overview: { /* overview data */ }
    }
  ]
}
```

### Products (`components/data/productData.js`)

```javascript
export const products = [
  {
    slug: 'product-slug',
    title: 'Product Title',
    price: 499.00,
    discount: 20,
    description: 'Product description',
    
    // User Input: SEO Metadata
    seo: {
      title: 'Product Title - Safari Gear | Swift Africa Safaris Shop',
      description: 'Product description with pricing and features',
      keywords: [
        'safari gear',
        'product category',
        'adventure equipment'
      ],
      image: 'product-seo-image.jpg',
      author: 'Swift Africa Safaris',
      publishedTime: '2024-01-15T10:00:00Z',
      modifiedTime: '2024-01-15T10:00:00Z',
      category: 'Safari Gear',
      tags: ['Gear', 'Safari', 'Adventure']
    },
    
    // User Input: Schema.org Structured Data
    schema: {
      "@type": "Product",
      name: "Product Name",
      description: "Product description",
      image: "product-image.jpg",
      brand: {
        "@type": "Brand",
        name: "Swift Africa Safaris"
      },
      category: "Product Category",
      offers: {
        "@type": "Offer",
        price: "399.20",
        priceCurrency: "USD",
        availability: "https://schema.org/InStock",
        seller: {
          "@type": "Organization",
          name: "Swift Africa Safaris"
        },
        priceValidUntil: "2024-12-31"
      },
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: "4.8",
        reviewCount: "24",
        bestRating: "5",
        worstRating: "1"
      }
    },
    
    // Other product data...
    stock: 15,
    images: [ /* image array */ ]
  }
]
```

## Key Benefits

1. **User Control**: Users can input custom metadata and schema for better SEO
2. **Fallback System**: Auto-generated metadata if user input is not provided
3. **SEO Optimization**: Rich snippets and better search engine visibility
4. **Consistency**: Standardized structure across all content types
5. **Flexibility**: Easy to update and maintain

## Implementation Notes

- All reading pages check for user-inputted `seo` and `schema` objects
- If present, user data is used; otherwise, fallback generation occurs
- Schema includes `mainEntity` properties for better search engine understanding
- Metadata is dynamically generated at build time for optimal performance

## Usage Instructions

1. Add `seo` object with custom metadata fields
2. Add `schema` object with Schema.org structured data
3. Deploy changes - pages will automatically use the new metadata
4. Test with Google's Rich Results Test tool
