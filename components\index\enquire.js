"use client";

import React, { useState } from 'react';
import SafariBookingModal from '@/components/common/planTourForm';
import PdfViewer from '@/components/common/PdfViewer';

const JourneyCards = () => {
  const [showPlanTourForm, setShowPlanTourForm] = useState(false);
  const [showPdfViewer, setShowPdfViewer] = useState(false);

  const handleDownloadInfoKit = () => {
    setShowPdfViewer(true);
  };

  const handlePlanTour = () => {
    setShowPlanTourForm(true);
  };

  return (
    <div className="bg-[var(--secondary-background)] flex flex-col lg:flex-row gap-6 max-w-full px-auto p-4">
      {/* Begin Your Journey Card */}
      <div className="flex-1 bg-[var(--primary-background)] overflow-hidden">
        {/* Green top border */}
        <div className="h-1 bg-green-700"
          style={{ backgroundColor: 'rgb(3, 108, 3)' }}
        ></div>

        <div className="p-6 text-left flex flex-col min-h-[250px]">
          {/* Title with orange underline */}
          <div className="mb-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Get to know more about us</h2>
            <div className="w-1/2 h-[2px] bg-orange-500"></div>
          </div>

          {/* Description */}
          <p className="text-gray-700 text-base mb-8 leading-relaxed flex-grow">
            Step into our story to discover the passion, purpose, and people behind everything we do.
          </p>

          {/* READ Button */}
          <div className="mt-auto">
            <button
              onClick={handleDownloadInfoKit}
              className="btn text-white font-bold py-3 px-6 rounded transition-colors duration-200 text-xs tracking-wide"
            >
              READ OUR INFOKIT
            </button>
          </div>
        </div>
      </div>

      {/* Order a Brochure Card */}
      <div className="flex-1 bg-[var(--primary-background)] overflow-hidden">
        {/* Green top border */}
        <div className="h-1 bg-green-700"
          style={{ backgroundColor: 'rgb(3, 108, 3)' }}
        ></div>

        <div className="p-6 text-left flex flex-col min-h-[250px]">
          {/* Title with orange underline */}
          <div className="mb-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Are you ready to begin your journey?</h2>
            <div className="w-1/2 h-[2px] bg-orange-500"></div>
          </div>

          {/* Description with adjusted height */}
          <p className="text-gray-700 text-base mb-8 leading-relaxed flex-grow">
            Take the first step toward discovering your purpose, unlocking your potential, and transforming your future through powerful learning and growth.
          </p>

          {/* Plan Tour Button */}
          <div className="mt-auto">
            <button
              onClick={handlePlanTour}
              className="btn text-white font-bold py-3 px-6 rounded transition-colors duration-200 text-xs tracking-wide"
            >
              PLAN YOUR TOUR
            </button>
          </div>
        </div>
      </div>

      {/* Plan Tour Form Modal */}
      {showPlanTourForm && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowPlanTourForm(false)}></div>
          <div className="absolute inset-0 flex items-center justify-center p-4">
            <div className="relative">
              <SafariBookingModal onClose={() => setShowPlanTourForm(false)} />
            </div>
          </div>
        </div>
      )}

      {/* PDF Viewer Modal */}
      <PdfViewer
        isOpen={showPdfViewer}
        onClose={() => setShowPdfViewer(false)}
        pdfUrl="/doc/Swift-Africa-Safaris-Info-Kit.pdf"
        title="Swift Africa Safaris Info Kit"
      />
    </div>
  );
};

export default JourneyCards;