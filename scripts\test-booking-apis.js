/**
 * Test script for booking system APIs
 * Run with: node scripts/test-booking-apis.js
 */

const BASE_URL = 'http://localhost:3000';

// Test data for different booking types
const testData = {
  tour: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    whatsapp: '+1234567890',
    numberOfPeople: 2,
    message: 'Looking for a 5-day safari tour in Tanzania'
  },
  apartment: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    whatsapp: '+1987654321',
    properties: '2-bedroom apartment near city center, available for 1 week'
  },
  car: {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    whatsapp: '+1122334455',
    carProperties: '4WD vehicle for safari, automatic transmission, 7 days rental'
  },
  volunteering: {
    name: '<PERSON>',
    email: '<EMAIL>',
    arrivalDate: '2024-03-15',
    departureDate: '2024-04-15',
    message: 'Interested in wildlife conservation volunteering'
  },
  contact: {
    name: '<PERSON>',
    whatsapp: '+1555666777',
    email: '<EMAIL>',
    message: 'General inquiry about safari packages'
  },
  email: {
    emailAddress: '<EMAIL>',
    notificationTypes: ['all'],
    isActive: true
  }
};

// Helper function to make API requests
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    console.log(`\n${method} ${endpoint}`);
    console.log(`Status: ${response.status}`);
    console.log(`Response:`, JSON.stringify(result, null, 2));
    
    return { success: response.ok, data: result };
  } catch (error) {
    console.error(`\nError ${method} ${endpoint}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Test functions for each booking type
async function testTourBookings() {
  console.log('\n=== TESTING TOUR BOOKINGS ===');
  
  // Create tour booking
  const createResult = await makeRequest('/api/bookings/tour', 'POST', testData.tour);
  
  if (createResult.success) {
    // Fetch tour bookings
    await makeRequest('/api/bookings/tour', 'GET');
    
    // Update booking status
    const bookingId = createResult.data.data.id;
    await makeRequest('/api/bookings/tour', 'PATCH', {
      id: bookingId,
      status: 'confirmed',
      adminNotes: 'Test booking confirmed'
    });
  }
}

async function testApartmentBookings() {
  console.log('\n=== TESTING APARTMENT BOOKINGS ===');
  
  // Create apartment booking
  const createResult = await makeRequest('/api/bookings/apartment', 'POST', testData.apartment);
  
  if (createResult.success) {
    // Fetch apartment bookings
    await makeRequest('/api/bookings/apartment', 'GET');
    
    // Update booking status
    const bookingId = createResult.data.data.id;
    await makeRequest('/api/bookings/apartment', 'PATCH', {
      id: bookingId,
      status: 'confirmed'
    });
  }
}

async function testCarBookings() {
  console.log('\n=== TESTING CAR BOOKINGS ===');
  
  // Create car booking
  const createResult = await makeRequest('/api/bookings/car', 'POST', testData.car);
  
  if (createResult.success) {
    // Fetch car bookings
    await makeRequest('/api/bookings/car', 'GET');
    
    // Update booking status
    const bookingId = createResult.data.data.id;
    await makeRequest('/api/bookings/car', 'PATCH', {
      id: bookingId,
      status: 'confirmed'
    });
  }
}

async function testVolunteeringApplications() {
  console.log('\n=== TESTING VOLUNTEERING APPLICATIONS ===');
  
  // Create volunteering application
  const createResult = await makeRequest('/api/bookings/volunteering', 'POST', testData.volunteering);
  
  if (createResult.success) {
    // Fetch volunteering applications
    await makeRequest('/api/bookings/volunteering', 'GET');
    
    // Update application status
    const applicationId = createResult.data.data.id;
    await makeRequest('/api/bookings/volunteering', 'PATCH', {
      id: applicationId,
      status: 'approved'
    });
  }
}

async function testContactSubmissions() {
  console.log('\n=== TESTING CONTACT SUBMISSIONS ===');
  
  // Create contact submission
  const createResult = await makeRequest('/api/bookings/contact', 'POST', testData.contact);
  
  if (createResult.success) {
    // Fetch contact submissions
    await makeRequest('/api/bookings/contact', 'GET');
    
    // Update submission status
    const submissionId = createResult.data.data.id;
    await makeRequest('/api/bookings/contact', 'PATCH', {
      id: submissionId,
      status: 'responded'
    });
  }
}

async function testEmailManagement() {
  console.log('\n=== TESTING EMAIL MANAGEMENT ===');
  
  // Fetch existing emails
  await makeRequest('/api/admin/emails', 'GET');
  
  // Add new email
  const createResult = await makeRequest('/api/admin/emails', 'POST', testData.email);
  
  if (createResult.success) {
    const emailId = createResult.data.data.id;
    
    // Update email
    await makeRequest('/api/admin/emails', 'PATCH', {
      id: emailId,
      notificationTypes: ['tour', 'apartment'],
      isActive: false
    });
    
    // Send test email
    await makeRequest('/api/admin/emails/test', 'POST', {
      emailAddress: testData.email.emailAddress
    });
    
    // Delete email
    await makeRequest(`/api/admin/emails?id=${emailId}`, 'DELETE');
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Booking System API Tests...');
  console.log(`Base URL: ${BASE_URL}`);
  
  try {
    await testTourBookings();
    await testApartmentBookings();
    await testCarBookings();
    await testVolunteeringApplications();
    await testContactSubmissions();
    await testEmailManagement();
    
    console.log('\n✅ All tests completed!');
    console.log('\nNext steps:');
    console.log('1. Check your admin dashboard at /admin/bookings/*');
    console.log('2. Verify email notifications were sent');
    console.log('3. Test the frontend forms manually');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  testTourBookings,
  testApartmentBookings,
  testCarBookings,
  testVolunteeringApplications,
  testContactSubmissions,
  testEmailManagement
};
