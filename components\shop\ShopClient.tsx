'use client';
import React, { useState, useCallback } from 'react';
import { CartProvider } from '@/components/shop/CartContext';
import { ShopProvider } from '@/components/shop/ShopContext';
import SearchBar from '@/components/shop/search';
import ShoppingCart from '@/components/shop/shoppingCart';
import HeroBanner from '@/components/shop/heroBanner';
import FilterSection from '@/components/shop/filterSection';
import ProductGrid from '@/components/shop/ProductGrid';
import StructuredData from '@/components/common/StructuredData';
import { generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

interface Product {
  id: number;
  slug: string;
  title: string;
  price: number;
  discount: number;
  description: string;
  stock?: number;
  monthlyPrice?: number;
  images?: string[];
  category: string;
  createdAt?: string;
  coverImage?: string;
}

const ShopClient = () => {
  const [isCartOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [noResultsMessage, setNoResultsMessage] = useState('');

  const handleFilterChange = useCallback(() => {
    // Handle filter change
  }, []);

  const handleSearch = useCallback(({ term, results }: { term: string; results: Product[] }) => {
    setSearchTerm(term);
    if (results.length === 0) {
      setNoResultsMessage(`No products found for "${term}"`);
    } else {
      setNoResultsMessage('');
    }
  }, []);

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Shop', url: '/shop' }
  ]);

  const shopPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Swift Africa Safaris - African Safari Gear & Travel Essentials Shop',
    description: 'Shop premium safari gear, travel essentials, and African inspired products. Everything you need for your African adventure, from clothing to equipment.',
    url: `${defaultSEO.siteUrl}/shop`,
    keywords: [
      'safari gear shop',
      'African travel essentials',
      'safari clothing',
      'wildlife photography equipment',
      'safari accessories',
      'travel gear Africa',
      'safari equipment store',
      'African souvenirs',
      'outdoor gear Africa',
      'safari supplies',
      'travel accessories',
      'safari apparel',
      'adventure gear shop',
      'African travel products',
      'safari merchandise'
    ],
    mainEntity: {
      '@type': 'Store',
      name: 'Swift Africa Safaris Shop',
      description: 'Shop premium safari gear, travel essentials, and African inspired products. Everything you need for your African adventure, from clothing to equipment.',
      url: `${defaultSEO.siteUrl}/shop`,
      brand: {
        '@type': 'Brand',
        name: defaultSEO.siteName
      },
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Safari Gear & Travel Essentials',
        itemListElement: [
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Product',
              name: 'Safari Clothing & Apparel',
              category: 'Clothing'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Product',
              name: 'Photography Equipment',
              category: 'Equipment'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Product',
              name: 'Travel Accessories',
              category: 'Accessories'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Product',
              name: 'African Souvenirs',
              category: 'Souvenirs'
            }
          }
        ]
      }
    }
  };

  return (
    <CartProvider>
      <ShopProvider>
        <div className="min-h-screen bg-gray-50">
          <StructuredData data={[breadcrumbSchema, shopPageSchema]} />
          {/* Shopping Cart Sidebar */}
          <ShoppingCart />

          {/* Main Content */}
          <main className={`max-w-7xl mx-auto px-4 py-8 transition-all duration-300 ${isCartOpen ? 'ml-80' : ''}`}>
            {/* Hero Banner */}
            <HeroBanner />

            {/* Search Bar */}
            <div className="my-8 flex justify-center">
              <SearchBar onSearch={handleSearch} />
            </div>

            {/* Filter Section */}
            <FilterSection onFilterChange={handleFilterChange} searchTerm={searchTerm} />

            {/* Add no results message */}
            {noResultsMessage && (
              <div className="text-center py-8">
                <p className="text-lg text-gray-600">{noResultsMessage}</p>
                <button 
                  onClick={() => {
                    setSearchTerm('');
                    setNoResultsMessage('');
                  }}
                  className="mt-4 text-blue-600 hover:text-blue-800"
                >
                  Clear search
                </button>
              </div>
            )}

            {/* Products Section */}
            <ProductGrid />
          </main>
        </div>
      </ShopProvider>
    </CartProvider>
  );
};

export default ShopClient;
