import type { Metadata } from 'next';
import React from 'react';
import HeroSection from '@/components/iconic/HeroSection';
import BannerSection from '@/components/iconic/BannerSection';
import ContentSection from '@/components/iconic/ContentSection';
import DestinationGrid from '@/components/iconic/DestinationGrid';
import { iconicData } from '@/components/data/iconicData';
import TourismFooter from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - Iconic African Destinations & Wildlife Experiences',
  description: 'Explore Africa\'s most iconic destinations and wildlife experiences. Discover breathtaking landscapes, diverse cultures, and unforgettable adventures across East Africa.',
  url: '/iconic',
  keywords: [
    'iconic African destinations',
    'East Africa wildlife',
    'African safari destinations',
    'Rwanda gorilla trekking',
    'Tanzania Serengeti',
    'Uganda wildlife parks',
    'Kenya safari tours',
    'South Africa Big Five',
    'African cultural experiences',
    'wildlife photography Africa',
    'African adventure destinations',
    'safari destination guide',
    'East Africa travel',
    'African wildlife tours',
    'iconic safari experiences'
  ],
  type: 'website'
});

const IconicOverview = () => {

    const breadcrumbSchema = generateBreadcrumbSchema([
        { name: 'Home', url: '/' },
        { name: 'Iconic Destinations', url: '/iconic' }
    ]);

    const iconicPageSchema = {
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        name: 'Swift Africa Safaris - Iconic African Destinations & Wildlife Experiences',
        description: 'Explore Africa\'s most iconic destinations and wildlife experiences. Discover breathtaking landscapes, diverse cultures, and unforgettable adventures across East Africa.',
        url: `${defaultSEO.siteUrl}/iconic`,
        keywords: [
            'iconic African destinations',
            'East Africa wildlife',
            'African safari destinations',
            'Rwanda gorilla trekking',
            'Tanzania Serengeti',
            'Uganda wildlife parks',
            'Kenya safari tours',
            'South Africa Big Five',
            'African cultural experiences',
            'wildlife photography Africa',
            'African adventure destinations',
            'safari destination guide',
            'East Africa travel',
            'African wildlife tours',
            'iconic safari experiences'
        ],
        mainEntity: {
            '@type': 'ItemList',
            name: 'Iconic African Safari Destinations',
            description: 'Discover Africa\'s most breathtaking destinations and wildlife experiences',
            numberOfItems: iconicData.destinations?.length || 5,
            itemListElement: iconicData.destinations?.map((destination, index) => ({
                '@type': 'TouristDestination',
                name: destination.name,
                description: `Explore the iconic ${destination.name} with its unique wildlife and cultural experiences`,
                image: destination.image,
                url: `${defaultSEO.siteUrl}/iconic/${destination.name.toLowerCase()}`,
                position: index + 1,
                geo: {
                    '@type': 'GeoCoordinates',
                    addressCountry: 'East Africa'
                },
                touristType: 'Safari Enthusiast'
            })) || [
                {
                    '@type': 'TouristDestination',
                    name: 'Rwanda',
                    description: 'Land of a Thousand Hills with mountain gorillas',
                    position: 1
                },
                {
                    '@type': 'TouristDestination',
                    name: 'Tanzania',
                    description: 'Home to Serengeti and Mount Kilimanjaro',
                    position: 2
                },
                {
                    '@type': 'TouristDestination',
                    name: 'Uganda',
                    description: 'Pearl of Africa with diverse wildlife',
                    position: 3
                },
                {
                    '@type': 'TouristDestination',
                    name: 'Kenya',
                    description: 'Famous for the Great Migration',
                    position: 4
                },
                {
                    '@type': 'TouristDestination',
                    name: 'South Africa',
                    description: 'Big Five safari destination',
                    position: 5
                }
            ]
        }
    };

    return (
        <div>
            <StructuredData data={[breadcrumbSchema, iconicPageSchema]} />
            <div className="bg-[#efe9e0]">
                {/* General iconic content */}
                <HeroSection {...iconicData.hero} />
                <BannerSection {...iconicData.banner} />
                {iconicData.contentSections.map((section, index) => (
                    <ContentSection key={index} {...section} />
                ))}

                {/* Destinations Grid */}
                <div className="w-full py-8 px-4">
                    <div className="mb-6 text-center">
                        <h2 className="text-3xl font-bold mb-2">Choose Your Destination</h2>
                        <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                    </div>
                    <div className="max-w-7xl mx-auto">
                        <DestinationGrid destinations={iconicData.destinations} />
                    </div>
                </div>
            </div>
            <TourismFooter />
        </div>
    );
};

export default IconicOverview;
