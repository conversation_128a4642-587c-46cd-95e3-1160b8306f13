import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkBlogPostSize() {
  console.log('🔍 Checking blog post size for: mountain-gorilla-trekking-in-rwanda-and-uganda\n');
  
  try {
    // Get the blog post with all data
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (postError || !post) {
      console.error('❌ Error fetching blog post:', postError);
      return;
    }
    
    console.log(`✅ Found blog post: ${post.title}`);
    
    // Calculate size of main post data
    const postDataSize = JSON.stringify(post).length;
    console.log(`📊 Main post data size: ${(postDataSize / 1024).toFixed(2)} KB`);
    
    // Get content blocks
    const { data: contentBlocks, error: blocksError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true });
    
    if (blocksError) {
      console.error('❌ Error fetching content blocks:', blocksError);
      return;
    }
    
    console.log(`\n📝 Found ${contentBlocks?.length || 0} content blocks:`);
    
    let totalContentSize = 0;
    
    if (contentBlocks) {
      contentBlocks.forEach((block, index) => {
        const blockSize = JSON.stringify(block).length;
        totalContentSize += blockSize;
        
        console.log(`\n📝 Block ${index + 1}:`);
        console.log(`   Type: ${block.block_type}`);
        console.log(`   Size: ${(blockSize / 1024).toFixed(2)} KB`);
        
        if (block.block_type === 'image' && block.content) {
          try {
            const imageContent = typeof block.content === 'string' 
              ? JSON.parse(block.content) 
              : block.content;
            console.log(`   Image URL: ${imageContent.src?.substring(0, 80)}...`);
            console.log(`   Alt text: ${imageContent.alt}`);
          } catch (e) {
            console.log(`   Content preview: ${JSON.stringify(block.content).substring(0, 100)}...`);
          }
        } else if (block.content) {
          const contentPreview = typeof block.content === 'string' 
            ? block.content.substring(0, 200)
            : JSON.stringify(block.content).substring(0, 200);
          console.log(`   Content preview: ${contentPreview}...`);
        }
      });
    }
    
    console.log(`\n📊 TOTAL SIZES:`);
    console.log(`   Main post data: ${(postDataSize / 1024).toFixed(2)} KB`);
    console.log(`   Content blocks: ${(totalContentSize / 1024).toFixed(2)} KB`);
    console.log(`   Combined total: ${((postDataSize + totalContentSize) / 1024).toFixed(2)} KB`);
    console.log(`   Combined total: ${((postDataSize + totalContentSize) / 1024 / 1024).toFixed(2)} MB`);
    
    // Check if there's any unusually large content
    if (contentBlocks) {
      const largeBlocks = contentBlocks.filter(block => 
        JSON.stringify(block).length > 100000 // 100KB
      );
      
      if (largeBlocks.length > 0) {
        console.log(`\n⚠️  Found ${largeBlocks.length} large content blocks (>100KB):`);
        largeBlocks.forEach((block, index) => {
          const blockSize = JSON.stringify(block).length;
          console.log(`   Block ${block.sort_order}: ${block.block_type} - ${(blockSize / 1024).toFixed(2)} KB`);
        });
      }
    }
    
    // Check for potential issues
    console.log(`\n🔍 POTENTIAL ISSUES:`);
    
    if ((postDataSize + totalContentSize) > 1024 * 1024) { // 1MB
      console.log(`   ⚠️  Total content size (${((postDataSize + totalContentSize) / 1024 / 1024).toFixed(2)} MB) is quite large`);
    }
    
    if (post.hero_image_url && post.hero_image_url.includes('blob:')) {
      console.log(`   ⚠️  Hero image uses blob URL: ${post.hero_image_url}`);
    }
    
    if (contentBlocks) {
      const blobImages = contentBlocks.filter(block => 
        block.block_type === 'image' && 
        block.content && 
        JSON.stringify(block.content).includes('blob:')
      );
      
      if (blobImages.length > 0) {
        console.log(`   ⚠️  Found ${blobImages.length} content blocks with blob URLs`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkBlogPostSize();
