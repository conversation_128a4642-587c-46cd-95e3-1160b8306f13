"use client"
import React, { useState, useCallback, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { ChevronDown, Camera, ChevronRight, ArrowLeft } from 'lucide-react';
import { slide as Menu } from 'react-burger-menu';
const webLogo = '/images/logo/swift-africa-safaris-best-tour-operator.png';
import SafariBookingModal from '@/components/common/planTourForm';
import '@/styles/burger-menu.css';

const Navbar = () => {
  const [activeMenu, setActiveMenu] = useState(null);
  const [hoveredPackage, setHoveredPackage] = useState(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [mobileView, setMobileView] = useState('main'); // 'main' | 'submenu'
  const [currentSubmenu, setCurrentSubmenu] = useState(null);
  const [showPlanTourForm, setShowPlanTourForm] = useState(false);
  const [megaMenuItems, setMegaMenuItems] = useState({});
  const menuRefs = useRef({});
  const location = usePathname();

  // Define menu order
  const menuOrder = ['tanzania', 'rwanda', 'south africa', 'uganda', 'luxury'];

  // Fetch packages from API
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await fetch('/api/packages?limit=100');
        const result = await response.json();

        if (result.success) {
          // Transform packages into megaMenuItems format
          const menuItems = result.data.reduce((acc, pkg) => {
            const location = pkg.location.toLowerCase();
            if (!acc[location]) {
              acc[location] = {
                title: pkg.location,
                packages: []
              };
            }

            // Calculate lowest price from pricing options
            const prices = [
              pkg.pricing_solo,
              pkg.pricing_honeymoon,
              pkg.pricing_family,
              pkg.pricing_group
            ].filter(price => price && price > 0);

            const lowestPrice = prices.length > 0 ? Math.min(...prices) : 0;

            acc[location].packages.push({
              name: pkg.title,
              duration: pkg.duration || pkg.category,
              price: lowestPrice > 0 ? `$${lowestPrice.toLocaleString()}` : 'Contact for price',
              image: pkg.image_url || pkg.hero_image_url || "https://images.unsplash.com/photo-1566073771259-6a8506099945",
              description: pkg.difficulty,
              slug: pkg.slug
            });

            return acc;
          }, {});

          setMegaMenuItems(menuItems);
        }
      } catch (error) {
        console.error('Header: Error fetching packages:', error);
      }
    };

    fetchPackages();
  }, []);

  const handleMenuEnter = useCallback((menu) => {
    setActiveMenu(menu);
  }, []);

  const handleMenuLeave = useCallback((menu) => {
    // Only close if we're not hovering the mega menu
    if (!menuRefs.current[menu]?.contains(document.activeElement)) {
      setActiveMenu(null);
    }
  }, []);

  const handleMegaMenuEnter = useCallback(() => {
    setActiveMenu((prev) => prev);
  }, []);

  const handleMegaMenuLeave = useCallback(() => {
    setActiveMenu(null);
  }, []);

  const renderMegaMenu = (menuKey) => {
    const menu = megaMenuItems[menuKey];
    if (!menu) return null;

    return (
      <div
        ref={el => menuRefs.current[menuKey] = el}
        onMouseEnter={handleMegaMenuEnter}
        onMouseLeave={() => handleMegaMenuLeave()}
        className="absolute top-full left-0 bg-[var(--secondary-background)] shadow-2xl border-t border-gray-100 z-50"
      >
        <div className="flex">
          {/* Packages List */}
          <div className="w-[17rem] p-4 border-r border-gray-100 ">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
              {menu.title} Tour Packages
            </h3>
            <div className="space-y-2">
              {menu.packages.map((pkg, index) => (
                <Link
                  key={index}
                  href={`/package/${pkg.slug}`}
                  className={`block p-3 cursor-pointer transition-all duration-200  ${
                    hoveredPackage === `${menuKey}-${index}`
                      ? 'bg-[var(--btn)] border-l-4 border-[var(--accent)] text-white'
                      : 'hover:bg-[var(--btn)] hover:text-white'
                  }`}
                  onMouseEnter={() => setHoveredPackage(`${menuKey}-${index}`)}
                  onMouseLeave={() => setHoveredPackage(null)}
                >
                  <div className="flex justify-between items-start text-left">
                    <div className="flex-1">
                      <h4 className={`font-medium text-sm leading-tight ${
                        hoveredPackage === `${menuKey}-${index}`
                        ? 'text-white'
                        : 'text-[var(--text)]'
                      }`}>
                        {pkg.name}
                      </h4>
                      <div className="flex items-center justify-between mt-1">
                        <span className={`text-xs ${
                          hoveredPackage === `${menuKey}-${index}`
                          ? 'text-white'
                          : 'text-gray-500'
                        }`}>
                          {pkg.duration}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Image Display */}
          <div className="w-[16rem] p-4">
            {hoveredPackage ? (
              <div className="space-y-4">
                {(() => {
                  const [menuKey, index] = hoveredPackage.split('-');
                  const pkg = megaMenuItems[menuKey].packages[index];
                  return (
                    <>
                      <div className="relative overflow-hidden">
                        <Image
                          src={pkg.image}
                          alt={pkg.name}
                          className="w-full h-48 object-cover transition-transform duration-300 hover:scale-105"
                          width={400}
                          height={300}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                        <div className="absolute bottom-3 left-3 text-white">
                          <span className="text-xs bg-black/50 px-2 py-1 rounded">
                            {pkg.duration}
                          </span>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 text-lg mb-2">
                          {pkg.name}
                        </h4>
                        <p className="text-gray-600 text-sm mb-3">
                          {pkg.description}
                        </p>
                      </div>
                    </>
                  );
                })()}
              </div>
            ) : (
              <div className="flex items-center justify-center h-48 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <Camera className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-500 text-sm">Hover over a package to see details</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Handle mobile menu state with scroll lock
  const handleStateChange = (state) => {
    setIsMenuOpen(state.isOpen);
    if (typeof window !== 'undefined' && typeof document !== 'undefined') {
      if (state.isOpen) {
        document.body.style.overflow = 'hidden';
      } else {
        document.body.style.overflow = 'unset';
      }
    }
  };

  // Cleanup scroll lock when component unmounts
  useEffect(() => {
    return () => {
      if (typeof window !== 'undefined' && typeof document !== 'undefined') {
        document.body.style.overflow = 'unset';
      }
    };
  }, []);

  // Handle submenu navigation
  const showSubmenu = (menuKey) => {
    setCurrentSubmenu(menuKey);
    setMobileView('submenu');
  };

  const goBack = () => {
    setMobileView('main');
    setCurrentSubmenu(null);
  };

  const renderMobileSubmenu = (menuKey) => {
    const menu = megaMenuItems[menuKey];
    if (!menu) return null;

    return (
      <div className="mobile-submenu  text-left items-start">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
          {menu.title} Tour Packages
        </h3>
        <div className="space-y-2">
          {menu.packages.map((pkg, index) => (
            <Link
              key={index}
              href={`/package/${pkg.slug}`}
              className="block p-3 hover:bg-gray-50 rounded-lg"
              onClick={() => setIsMenuOpen(false)}
            >
              <h4 className="font-medium text-gray-900">{pkg.name}</h4>
              <div className="flex items-center text-sm text-gray-500">
                <span>{pkg.duration}</span>
                <span className="mx-2">•</span>
                <span>{pkg.price}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    );
  };

  // Add this function to handle showing the form
  const handlePlanTourClick = () => {
    setShowPlanTourForm(true);
    setIsMenuOpen(false); // Close mobile menu if open
  };

  return (
    <>
      <nav className="bg-[var(--secondary-background)] shadow-lg sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex-shrink-0 flex items-center">
              <Link href="/">
                <Image
                  src={webLogo}
                  alt="Swift Africa Safaris"
                  className="h-8 w-auto"
                  width={120}
                  height={32}
                  priority
                />
              </Link>
            </div>

            {/* Navigation Links - Update breakpoint from lg to xl */}
            <div className="hidden xl:block text-left items-start">
              <div className="ml-10 flex  text-left items-start space-x-0">
                {/* Explore */}
                <Link
                  href="/"
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200 
                    ${location === '/'
                      ? 'text-[var(--accent)]'
                      : 'text-[var(--text)] hover:text-[var(--accent)]'}
                    after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                    after:left-0 after:bottom-0 after:rounded-full
                    after:transform after:scale-x-0 after:transition-transform after:duration-800
                    ${location === '/' ? 'after:scale-x-100' : 'hover:after:scale-x-100'}`}
                >
                  Explore
                </Link>

                {/* Countries Mega Menu */}
                {menuOrder.map((menuKey) => (
                  <div
                    key={menuKey}
                    className="relative group"
                    onMouseEnter={() => handleMenuEnter(menuKey)}
                    onMouseLeave={() => handleMenuLeave(menuKey)}
                  >
                    <button
                      className={`relative flex items-center space-x-1 px-3 py-2 text-sm font-medium transition-all duration-200 capitalize
                        ${location.includes(menuKey)
                          ? 'text-[var(--accent)]'
                          : 'text-[var(--text)] hover:text-[var(--accent)]'}
                        after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                        after:left-0 after:bottom-0 after:rounded-full
                        after:transform after:scale-x-0 after:transition-all after:duration-200
                        ${activeMenu === menuKey || location.includes(menuKey) ? 'after:scale-x-100' : 'group-hover:after:scale-x-100'}`}
                    >
                      <span>{menuKey === 'south africa' ? 'South Africa' : menuKey}</span>
                      <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${activeMenu === menuKey ? 'transform rotate-180' : ''}`} />
                    </button>
                    {activeMenu === menuKey && (
                      <div className="absolute top-full left-0 w-full">
                        {renderMegaMenu(menuKey)}
                      </div>
                    )}
                  </div>
                ))}

                {/* blog */}
                <Link
                  href="/blog"
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200
                    ${location === '/blog'
                      ? 'text-[var(--accent)]'
                      : 'text-[var(--text)] hover:text-[var(--accent)]'}
                    after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                    after:left-0 after:bottom-0 after:rounded-full
                    after:transform after:scale-x-0 after:transition-transform after:duration-300
                    ${location === '/blog' ? 'after:scale-x-100' : 'hover:after:scale-x-100'}`}
                >
                  blog
                </Link>
                
                {/* Community */}
                <Link
                  href="/community"
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200
                    ${location === '/community'
                      ? 'text-[var(--accent)]'
                      : 'text-[var(--text)] hover:text-[var(--accent)]'}
                    after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                    after:left-0 after:bottom-0 after:rounded-full
                    after:transform after:scale-x-0 after:transition-transform after:duration-300
                    ${location === '/community' ? 'after:scale-x-100' : 'hover:after:scale-x-100'}`}
                >
                  Community
                </Link>

                {/* Contact */}
                <Link
                  href="/contact"
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200
                    ${location === '/contact'
                      ? 'text-[var(--accent)]'
                      : 'text-[var(--text)] hover:text-[var(--accent)]'}
                    after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                    after:left-0 after:bottom-0 after:rounded-full
                    after:transform after:scale-x-0 after:transition-transform after:duration-300
                    ${location === '/contact' ? 'after:scale-x-100' : 'hover:after:scale-x-100'}`}
                >
                  Contact Us
                </Link>

                {/* About */}
                <Link
                  href="/about"
                  className={`relative px-3 py-2 text-sm font-medium transition-colors duration-200
                    ${location === '/about'
                      ? 'text-[var(--accent)]'
                      : 'text-[var(--text)] hover:text-[var(--accent)]'}
                    after:content-[''] after:absolute after:w-full after:h-[2px] after:bg-[var(--accent)] 
                    after:left-0 after:bottom-0 after:rounded-full
                    after:transform after:scale-x-0 after:transition-transform after:duration-300
                    ${location === '/about' ? 'after:scale-x-100' : 'hover:after:scale-x-100'}`}
                >
                  About Us
                </Link>
              </div>
            </div>

            {/* Action Buttons - Update breakpoint from lg to xl */}
            <div className="hidden xl:flex items-center space-x-2">
              <button 
                className="btn text-[var(--white)] px-6 py-2 rounded-lg text-sm font-medium transition-colors duration-200 shadow-md hover:shadow-lg"
                onClick={handlePlanTourClick}
              >
                Plan Your Tour
              </button>
            </div>

            {/* Mobile menu button - Update breakpoint from lg to xl */}
            <div className="xl:hidden">
              <Menu
                right
                isOpen={isMenuOpen}
                onStateChange={handleStateChange}
                customBurgerIcon={
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                }
                styles={{
                  bmMenu: {
                    background: 'var(--secondary-background)',
                    padding: '2.5rem 1.5rem',
                    width: '100%',
                    maxWidth: '600px', // Increased maxWidth for tablet
                    '@media (minWidth: 600px)': {
                      width: '70%' // Adjust width for tablet
                    }
                  },
                  bmOverlay: {
                    background: 'rgba(0, 0, 0, 0.7)' // Darker overlay
                  },
                  bmBurgerButton: {
                    width: '24px',
                    height: '24px'
                  },
                  bmMenuWrap: {
                    width: '100%',
                    top: '0',
                    left: '0',
                    position: 'fixed',
                    '@media (minWidth: 640px)': {
                      width: '80%' // Match menu width for tablet
                    }
                  }
                }}
              >
                {mobileView === 'main' ? (
                  <div className="flex flex-col w-full">
                    <div className="border-b border-gray-700 pb-4 mb-4">
                      <Link href="/" onClick={() => setIsMenuOpen(false)}>
                        <Image src={webLogo} alt="Swift Africa Safaris" className="h-12 w-auto" width={180} height={48} priority />
                      </Link>
                    </div>
                    <div className="space-y-6  text-left items-start font-semibold text-gray-900">
                      <Link href="/" className="py-2 block text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)]" onClick={() => setIsMenuOpen(false)}>
                        Explore
                      </Link>
                      {menuOrder.map((menuKey) => (
                        <button
                          key={menuKey}
                          onClick={() => showSubmenu(menuKey)}
                          className="py-2 flex items-center justify-between text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)] w-full"
                        >
                          <span className="capitalize">{menuKey === 'south africa' ? 'South Africa' : menuKey}</span>
                          <ChevronRight className="w-5 h-5" />
                        </button>
                      ))}
                      <Link href="/blog" className="py-2 block text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)]" onClick={() => setIsMenuOpen(false)}>
                        Blog
                      </Link>
                      <Link href="/community" className="py-2 block text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)]" onClick={() => setIsMenuOpen(false)}>
                        Community
                      </Link>
                      <Link href="/contact" className="py-2 block text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)]" onClick={() => setIsMenuOpen(false)}>
                        Contact Us
                      </Link>
                      <Link href="/about" className="py-2 block text-[var(--text)] hover:text-[var(--accent)] border-b border-[var(--primary-background)]" onClick={() => setIsMenuOpen(false)}>
                        About Us
                      </Link>
                    </div>
                    <div className="mt-8">
                      <button 
                        className="w-full btn text-white px-4 py-3 rounded-lg text-sm font-medium"
                        onClick={handlePlanTourClick}
                      >
                        Plan Your Tour
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="text-white">
                    <button
                      onClick={goBack}
                      className="flex items-center text-[var(--text)] mb-6 hover:text-[var(--accent)]"
                    >
                      <ArrowLeft className="w-5 h-5 mr-2" />
                      Back to Menu
                    </button>
                    {renderMobileSubmenu(currentSubmenu)}
                  </div>
                )}
              </Menu>
            </div>
          </div>
        </div>
      </nav>

      {/* Add the form modal */}
      {showPlanTourForm && (
        <div className="fixed inset-0 z-50">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setShowPlanTourForm(false)}></div>
          <div className="absolute inset-0 flex items-center justify-center p-4">
            <div className="relative">
              <SafariBookingModal onClose={() => setShowPlanTourForm(false)} />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Navbar;