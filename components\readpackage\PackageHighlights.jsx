import React from 'react';
import { FaCircle } from 'react-icons/fa6';

const PackageHighlights = ({ highlights = [], whatToPack = [] }) => {
  const circleClass = "text-orange-500 mt-1.5 mr-2 text-[6px] shrink-0";

  return (
    <section className="grid md:grid-cols-2 gap-8 mb-12 text-justify">
      <div className="bg-[var(--secondary-background)] p-6 shadow-md">
        <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-6">Highlights</h2>
        {highlights.length > 0 ? (
          <ul className="space-y-3 text-justify">
            {highlights.map((highlight, index) => (
              <li key={`highlight-${index}`} className="flex items-start">
                <FaCircle className={circleClass} />
                <span className="text-gray-700">{highlight}</span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-600">No highlights available for this package.</p>
        )}
      </div>

      <div className="bg-[var(--secondary-background)] p-6 shadow-md">
        <h2 className="text-2xl font-bold text-[var(--primary-color)] mb-6">What to Pack</h2>
        {whatToPack.length > 0 ? (
          <ul className="space-y-3 text-justify">
            {whatToPack.map((item, index) => (
              <li key={`pack-${index}`} className="flex items-start">
                <FaCircle className={circleClass} />
                <span className="text-gray-700">{item}</span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-600">No packing list available for this package.</p>
        )}
      </div>
    </section>
  );
};

export default PackageHighlights;
