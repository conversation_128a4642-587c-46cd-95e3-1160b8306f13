import React from 'react';
import { FaCheck, FaTimes } from 'react-icons/fa';

const PackageIncludes = ({ includes = [], excludes = [] }) => {
  return (
    <section className="py-12 bg-[var(--secondary-background)]">
      <h2 className="text-3xl font-bold text-center mb-10 text-[var(--primary-color)]">
        Your Safari Essentials Package
      </h2>

      <div className="max-w-6xl mx-auto px-4 grid md:grid-cols-2 gap-8">
        {/* Included Items */}
        <div className="bg-[var(--primary-background)] p-6 shadow-md text-justify">
          <div className="flex items-center gap-2 mb-6">
            <FaCheck className="text-green-600 text-2xl" />
            <h3 className="text-xl font-bold text-[var(--primary-color)]">Included</h3>
          </div>
          {includes.length > 0 ? (
            <ul className="space-y-3">
              {includes.map((item, index) => (
                <li key={`included-${index}`} className="flex items-center gap-2">
                  <FaCheck className="text-green-600 text-sm" />
                  <span className="text-gray-700">{item}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-600">No included items specified for this package.</p>
          )}
        </div>

        {/* Excluded Items */}
        <div className="bg-[var(--primary-background)] p-6 shadow-md text-justify">
          <div className="flex items-center gap-2 mb-6">
            <FaTimes className="text-orange-500 text-2xl" />
            <h3 className="text-xl font-bold text-[var(--primary-color)]">Not Included</h3>
          </div>
          {excludes.length > 0 ? (
            <ul className="space-y-3">
              {excludes.map((item, index) => (
                <li key={`excluded-${index}`} className="flex items-center gap-2">
                  <FaTimes className="text-orange-500 text-sm" />
                  <span className="text-gray-700">{item}</span>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-600">No excluded items specified for this package.</p>
          )}
        </div>
      </div>
    </section>
  );
};

export default PackageIncludes;
