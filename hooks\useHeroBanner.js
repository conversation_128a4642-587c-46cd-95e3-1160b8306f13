import { useState, useEffect, useCallback } from 'react';

export const useHeroBanner = (products, interval = 5000) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const discountedProducts = products.filter(p => p.discount > 0)
    .sort((a, b) => b.discount - a.discount);

  const goToSlide = useCallback((index) => {
    setIsAnimating(true);
    setCurrentIndex(index);
    setTimeout(() => setIsAnimating(false), 500);
  }, []);

  const nextSlide = useCallback(() => {
    goToSlide((currentIndex + 1) % discountedProducts.length);
  }, [currentIndex, discountedProducts.length, goToSlide]);

  const prevSlide = useCallback(() => {
    goToSlide(currentIndex === 0 ? discountedProducts.length - 1 : currentIndex - 1);
  }, [currentIndex, discountedProducts.length, goToSlide]);

  useEffect(() => {
    const timer = setInterval(nextSlide, interval);
    return () => clearInterval(timer);
  }, [nextSlide, interval]);

  return {
    currentProduct: discountedProducts[currentIndex],
    totalProducts: discountedProducts.length,
    currentIndex,
    isAnimating,
    nextSlide,
    prevSlide,
    goToSlide
  };
};
