import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createBucketDashboard() {
  console.log('🎯 CREATING BUCKET VIA SUPABASE DASHBOARD\n');

  try {
    // First check if bucket already exists
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log('❌ Cannot access storage:', error.message);
    } else {
      const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
      
      if (packageImagesBucket) {
        console.log('✅ package-images bucket already exists!');
        console.log('🎉 Your storage is working correctly.');
        console.log('📤 You can now upload images in your admin dashboard.');
        return;
      }
    }

    console.log('❌ package-images bucket not found');
    console.log('\n🔧 FOLLOW THESE EXACT STEPS TO CREATE THE BUCKET:\n');

    console.log('📋 STEP-BY-STEP INSTRUCTIONS:');
    console.log('');
    console.log('1️⃣  Open your Supabase Dashboard');
    console.log('   • Go to: https://supabase.com/dashboard');
    console.log('   • Sign in to your account');
    console.log('   • Select your project');
    console.log('');
    console.log('2️⃣  Navigate to Storage');
    console.log('   • Click "Storage" in the left sidebar');
    console.log('   • You should see a list of buckets (probably empty)');
    console.log('');
    console.log('3️⃣  Create New Bucket');
    console.log('   • Click "Create a new bucket" button');
    console.log('   • Or look for a "+" or "New bucket" button');
    console.log('');
    console.log('4️⃣  Fill in the Bucket Details');
    console.log('   • Name: package-images');
    console.log('   • Public bucket: ✅ CHECK THIS BOX (very important!)');
    console.log('   • File size limit: 5242880 (or 5MB)');
    console.log('   • Allowed MIME types: image/jpeg, image/png, image/webp, image/gif');
    console.log('');
    console.log('5️⃣  Create the Bucket');
    console.log('   • Click "Create bucket" or "Save"');
    console.log('   • Wait for the confirmation');
    console.log('');
    console.log('6️⃣  Verify Creation');
    console.log('   • You should see "package-images" in your bucket list');
    console.log('   • The bucket should show as "Public"');
    console.log('');
    console.log('7️⃣  Test in Your App');
    console.log('   • Go back to your admin dashboard');
    console.log('   • Try uploading an image');
    console.log('   • The error should be gone!');
    console.log('');

    console.log('🔍 After creating the bucket, run this to verify:');
    console.log('   node scripts/test-public-bucket.mjs');
    console.log('');

    console.log('⚠️  IMPORTANT NOTES:');
    console.log('• Make sure to check "Public bucket" - this is crucial!');
    console.log('• The bucket name must be exactly "package-images"');
    console.log('• If you see any permission errors, try refreshing the page');
    console.log('• This method bypasses SQL permission issues');
    console.log('');

    console.log('🎯 This dashboard method will work 100%!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createBucketDashboard().catch(console.error); 