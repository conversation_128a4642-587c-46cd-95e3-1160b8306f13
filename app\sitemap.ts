import { MetadataRoute } from 'next'
import { defaultSEO } from '@/lib/seo'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client for sitemap generation
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = defaultSEO.siteUrl

  // Static pages with proper priorities and change frequencies
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/package`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/mini-package`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/faqs`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/journey`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/iconic`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/community`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/shop`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/schedule`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${baseUrl}/booking-policy`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ]

  try {
    // Fetch dynamic content from Supabase
    const [packagesResult, miniPackagesResult, blogPostsResult] = await Promise.allSettled([
      // Fetch published packages
      supabase
        .from('packages')
        .select('slug, updated_at, created_at')
        .eq('status', 'published')
        .order('updated_at', { ascending: false }),

      // Fetch published mini packages
      supabase
        .from('mini_packages')
        .select('slug, updated_at, created_at')
        .eq('status', 'published')
        .order('updated_at', { ascending: false }),

      // Fetch published blog posts
      supabase
        .from('blog_posts')
        .select('slug, updated_at, published_at')
        .eq('status', 'published')
        .order('updated_at', { ascending: false })
    ])

    // Process packages
    const packagePages: MetadataRoute.Sitemap = []
    if (packagesResult.status === 'fulfilled' && packagesResult.value.data) {
      packagePages.push(...packagesResult.value.data.map((pkg) => ({
        url: `${baseUrl}/package/${pkg.slug}`,
        lastModified: new Date(pkg.updated_at || pkg.created_at),
        changeFrequency: 'weekly' as const,
        priority: 0.8,
      })))
    }

    // Process mini packages
    const miniPackagePages: MetadataRoute.Sitemap = []
    if (miniPackagesResult.status === 'fulfilled' && miniPackagesResult.value.data) {
      miniPackagePages.push(...miniPackagesResult.value.data.map((pkg) => ({
        url: `${baseUrl}/mini-package/${pkg.slug}`,
        lastModified: new Date(pkg.updated_at || pkg.created_at),
        changeFrequency: 'weekly' as const,
        priority: 0.7,
      })))
    }

    // Process blog posts
    const blogPages: MetadataRoute.Sitemap = []
    if (blogPostsResult.status === 'fulfilled' && blogPostsResult.value.data) {
      blogPages.push(...blogPostsResult.value.data.map((post) => ({
        url: `${baseUrl}/blog/${post.slug}`,
        lastModified: new Date(post.updated_at || post.published_at),
        changeFrequency: 'monthly' as const,
        priority: 0.7,
      })))
    }

    console.log(`✅ Sitemap generated: ${staticPages.length} static, ${packagePages.length} packages, ${miniPackagePages.length} mini-packages, ${blogPages.length} blog posts`)

    return [...staticPages, ...packagePages, ...miniPackagePages, ...blogPages]

  } catch (error) {
    console.error('❌ Error generating dynamic sitemap:', error)

    // Fallback to static pages only if database fails
    return staticPages
  }
}

// Enable ISR for sitemap - regenerate every hour
export const revalidate = 3600
