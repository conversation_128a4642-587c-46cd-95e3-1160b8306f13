/* eslint-disable react/no-unescaped-entities */

import React from 'react';
import { Mail, Phone, MapPin } from 'lucide-react';
import SafariContactForm from './form';

const SafariContactComponent = () => {
    return (
        <div className="bg-[primary-background] p-4 pb-8 sm:p-6 md:p-8 lg:p-12">
            <div className="max-w-6xl mx-auto flex flex-col lg:flex-row gap-6 md:gap-8 lg:gap-12">
                {/* Left Side - Contact Info */}
                <div className="w-full lg:w-1/2 space-y-6 md:space-y-8 pt-4 md:pt-[20px] order-2 lg:order-1">
                    <div className="space-y-3 md:space-y-4 text-left">
                        <p className="text-base sm:text-lg md:text-xl leading-relaxed">
                            Your adventure start here! Ready to explore the wild beauty of
                            <span className="hidden sm:inline"><br /></span> Africa?
                            Contact us today and let&apos;s craft your wonderful safari experience.
                        </p>
                    </div>

                    <div className="space-y-4 sm:space-y-5 md:space-y-6 mt-4 sm:my-5 md:my-6">
                        {/* Email */}
                        <div className="flex items-center space-x-3 md:space-x-4 mb-4 mx-3 md:mx-3">
                            <a href="mailto:<EMAIL>">
                                <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center hover:bg-opacity-90 cursor-pointer"
                                    style={{ backgroundColor: 'var(--btn)' }}
                                >
                                    <Mail className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                                </div>
                            </a>
                            <a
                                href="mailto:<EMAIL>"
                                className="text-gray-800 hover:text-[--btn] text-sm sm:text-base md:text-lg break-all sm:break-normal ml-3"
                            >
                                <EMAIL>
                            </a>
                        </div>

                        {/* Phone */}
                        <div className="flex items-center space-x-3 md:space-x-4 mb-4 mx-3 md:mx-3">
                            <a href="tel:+250784250281">
                                <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center hover:bg-opacity-90 cursor-pointer"
                                    style={{ backgroundColor: 'var(--btn)' }}
                                >
                                    <Phone className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                                </div>
                            </a>
                            <a
                                href="tel:+250784250281"
                                className="text-gray-800 hover:text-[--btn] text-sm sm:text-base md:text-lg ml-3"
                            >
                                +250 784 250 281
                            </a>
                        </div>

                        {/* Location */}
                        <div className="flex items-center space-x-3 md:space-x-4 mb-4 mx-3 md:mx-3">
                            <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center"
                                style={{ backgroundColor: 'var(--btn)' }}
                            >
                                <MapPin className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                            </div>
                            <span className="text-gray-800 text-sm sm:text-base md:text-lg ml-3">
                                Musanze, Rwanda
                            </span>
                        </div>
                    </div>
                </div>

                {/* Right Side - Form */}
                <div className="w-full lg:w-1/2 mt-6 lg:mt-0 order-1 lg:order-2">
                    <SafariContactForm />
                </div>
            </div>
        </div>
    );
};

export default SafariContactComponent;