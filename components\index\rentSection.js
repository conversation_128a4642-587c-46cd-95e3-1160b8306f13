/* eslint-disable @next/next/no-img-element */
"use client";
import React, { useState, useEffect } from 'react';
import CarBookingForm from '../common/carBooking';
import ApartmentBookingForm from '../common/apartmentBooking';

const SafariServices = () => {
    const [showCarModal, setShowCarModal] = useState(false);
    const [showApartmentModal, setShowApartmentModal] = useState(false);
    const [currentCarImage, setCurrentCarImage] = useState(0);
    const [currentApartmentImage, setCurrentApartmentImage] = useState(0);

    // Car rental images
    const carImages = [
        {
            src: "images/rent/car-hire-in-rwanda.webp",
            alt: "a car for rent packed"
            
        },
        {
            src: "images/rent/car-hire-in-rwanda-eco-friendly.webp",
            alt: "the car for rent seats present"
            
        },
        {
            src: "images/rent/car-hire-in-rwanda-cargo.webp",
            alt: "cargo place in car for rent"
            
        }
    ];

    // Apartment rental images
    const apartmentImages = [
        {
            src: "images/rent/rent-apartment-in-rwanda.webp",
            alt: "Modern luxury apartment building"
           
        },
        {
            src: "images/rent/apartment-in-rwanda-for-vacation.webp",
            alt: "Elegant apartment interior with modern furnishing"
            
        },
        {
            src: "images/rent/vacation-stay-in-rwanda.webp",
            alt: "bathroom interior"
           
        }
    ];

    // Auto-slide functionality
    useEffect(() => {
        const carInterval = setInterval(() => {
            setCurrentCarImage((prev) => (prev + 1) % carImages.length);
        }, 4000);

        const apartmentInterval = setInterval(() => {
            setCurrentApartmentImage((prev) => (prev + 1) % apartmentImages.length);
        }, 4500);

        return () => {
            clearInterval(carInterval);
            clearInterval(apartmentInterval);
        };
    }, [carImages.length, apartmentImages.length]);

    const nextCarImage = () => {
        setCurrentCarImage((prev) => (prev + 1) % carImages.length);
    };

    const prevCarImage = () => {
        setCurrentCarImage((prev) => (prev - 1 + carImages.length) % carImages.length);
    };

    const nextApartmentImage = () => {
        setCurrentApartmentImage((prev) => (prev + 1) % apartmentImages.length);
    };

    const prevApartmentImage = () => {
        setCurrentApartmentImage((prev) => (prev - 1 + apartmentImages.length) % apartmentImages.length);
    };

    return (
        <>
            {/* Modal for Car Booking */}
            {showCarModal && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                    <div className="relative w-full max-w-4xl">
                        <button 
                            onClick={() => setShowCarModal(false)}
                            className="absolute right-4 top-4 z-50 bg-white rounded-full p-2 hover:bg-gray-100"
                        >
                            ✕
                        </button>
                        <CarBookingForm />
                    </div>
                </div>
            )}

            {/* Modal for Apartment Booking */}
            {showApartmentModal && (
                <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
                    <div className="relative w-full max-w-4xl">
                        <button 
                            onClick={() => setShowApartmentModal(false)}
                            className="absolute right-4 top-4 z-50 bg-white rounded-full p-2 hover:bg-gray-100"
                        >
                            ✕
                        </button>
                        <ApartmentBookingForm />
                    </div>
                </div>
            )}

            <div className="w-full bg-[var(--primary-background)] py-6 px-4">
                <div className="mb-12 text-center">
                    <h2 className="text-3xl font-bold mb-2">Car And Apartment Rentals</h2>
                    <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                </div>
                <div className="max-w-7xl mx-auto">
                    {/* First Section - Car Hire */}
                    <div className="mb-12">
                        <div className="flex flex-col lg:flex-row gap-6 items-stretch">
                            {/* Image Carousel */}
                            <div className="lg:w-1/2 order-1 lg:order-1">
                                <div className="relative h-48 lg:h-64 overflow-hidden">
                                    {/* Images */}
                                    <div className="relative w-full h-full">
                                        {carImages.map((image, index) => (
                                            <div
                                                key={index}
                                                className={`absolute inset-0 transition-opacity duration-500 ${
                                                    index === currentCarImage ? 'opacity-100' : 'opacity-0'
                                                }`}
                                            >
                                                <img
                                                    src={image.src}
                                                    alt={image.alt}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        ))}
                                    </div>

                                    {/* Navigation Arrows */}
                                    <button
                                        onClick={prevCarImage}
                                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                    >
                                        ←
                                    </button>
                                    <button
                                        onClick={nextCarImage}
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                    >
                                        →
                                    </button>

                                    {/* Dots Indicator */}
                                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                                        {carImages.map((_, index) => (
                                            <button
                                                key={index}
                                                onClick={() => setCurrentCarImage(index)}
                                                className={`w-2 h-2 rounded-full transition-colors ${
                                                    index === currentCarImage ? 'bg-white' : 'bg-white/50'
                                                }`}
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Content */}
                            <div className=" lg:w-1/2 order-2 lg:order-2 flex flex-col justify-center">

                                <div className="border-r-4 border-orange-500 pl-4 mb-4">
                                    <h2 className="text-2xl lg:text-3xl font-bold text-gray-900">
                                        Hire Car
                                    </h2>
                                </div>
                                <p className="text-gray-700 text-base leading-relaxed mb-6">
                                    Looking for the perfect, smooth, and comfortable vehicle for your journey and adventures? At Swift Africa Safaris, we offer reliable car rental services for travelers looking the freedom of a self-drive experience around Rwanda. Enjoy the flexibility and convenience of exploring the thousand hills country at your own pace.
                                </p>
                                <div>
                                    <button 
                                        className="btn text-white px-6 py-2 rounded text-sm font-medium transition-colors duration-200"
                                        onClick={() => setShowCarModal(true)}
                                    >
                                        HIRE CAR
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Second Section - Check Availability */}
                    <div>
                        <div className="flex flex-col lg:flex-row gap-8 items-stretch">
                            {/* Content */}
                            <div className="lg:w-1/2 order-2 lg:order-1 flex flex-col justify-center">
                                <div className="border-l-4 border-orange-500 pl-4 mb-4">
                                    <h2 className="text-2xl lg:text-3xl font-bold text-gray-900">
                                        Check Apartment
                                    </h2>
                                </div>
                                <p className="text-gray-700 text-base leading-relaxed mb-6">
                                    Looking for a luxurious and private retreat during your stay in Rwanda? At Swift Africa Safaris, we offer exclusive apartment services for travelers looking a fancy and private accommodation experience. Whether you&apos;re in town for a short getaway or planning a long period of time stay, our apartments provide the perfect comfort.
                                </p>
                                <div>

                                    <button 
                                        className="btn text-white px-6 py-2 rounded text-sm font-medium transition-colors duration-200"
                                        onClick={() => setShowApartmentModal(true)}
                                    >
                                        CHECK AVAILABILITY
                                    </button>
                                </div>
                            </div>

                            {/* Image Carousel */}
                            <div className="lg:w-1/2 order-1 lg:order-2">
                                <div className="relative h-64 lg:h-80 overflow-hidden">
                                    {/* Images */}
                                    <div className="relative w-full h-full">
                                        {apartmentImages.map((image, index) => (
                                            <div
                                                key={index}
                                                className={`absolute inset-0 transition-opacity duration-500 ${
                                                    index === currentApartmentImage ? 'opacity-100' : 'opacity-0'
                                                }`}
                                            >
                                                <img
                                                    src={image.src}
                                                    alt={image.alt}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                        ))}
                                    </div>

                                    {/* Navigation Arrows */}
                                    <button
                                        onClick={prevApartmentImage}
                                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                    >
                                        ←
                                    </button>
                                    <button
                                        onClick={nextApartmentImage}
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                                    >
                                        →
                                    </button>

                                    {/* Dots Indicator */}
                                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                                        {apartmentImages.map((_, index) => (
                                            <button
                                                key={index}
                                                onClick={() => setCurrentApartmentImage(index)}
                                                className={`w-2 h-2 rounded-full transition-colors ${
                                                    index === currentApartmentImage ? 'bg-white' : 'bg-white/50'
                                                }`}
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default SafariServices;