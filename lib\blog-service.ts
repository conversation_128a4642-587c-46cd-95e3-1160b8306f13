/**
 * New Blog Service - Complete rewrite for blog data management
 * Handles all blog-related data fetching and processing
 */

import { supabase } from './supabase'

// Types for the new blog system
export interface BlogPost {
  id: string
  title: string
  slug: string
  description: string
  hero_image_url: string
  hero_image_alt: string
  category: string
  tags: string[]
  status: string
  published_at: string
  created_at: string
  updated_at: string
  view_count: number
  seo_title: string
  seo_description: string
  seo_keywords: string[]
  og_title: string
  og_description: string
  og_image_url: string
  canonical_url: string
  robots_index: string
  robots_follow: string
  schema_data: any
  content_blocks?: ContentBlock[]
}

export interface ContentBlock {
  id: string
  blog_post_id: string
  block_type: 'paragraph' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'image' | 'video' | 'listing' | 'quote' | 'divider'
  content: any
  sort_order: number
  created_at: string
  updated_at: string
}

export interface RelatedPost {
  id: string
  title: string
  slug: string
  description: string
  hero_image_url: string
  category: string
  published_at: string
}

/**
 * Fetch a single blog post by slug with all related data
 */
export async function fetchBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    console.log(`🔍 Fetching blog post: ${slug}`)
    
    // Fetch the main blog post
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count,
        seo_title,
        seo_description,
        seo_keywords,
        og_title,
        og_description,
        og_image_url,
        canonical_url,
        robots_index,
        robots_follow,
        schema_data
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single()

    if (postError || !post) {
      console.log(`❌ Blog post not found: ${slug}`)
      return null
    }

    console.log(`✅ Blog post found: ${post.title}`)

    // Fetch content blocks
    const { data: contentBlocks, error: blocksError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true })

    if (blocksError) {
      console.error(`❌ Error fetching content blocks: ${blocksError.message}`)
      // Continue without content blocks rather than failing completely
    }

    // Attach content blocks to the post
    const blogPost: BlogPost = {
      ...post,
      content_blocks: contentBlocks || []
    }

    console.log(`✅ Blog post loaded with ${contentBlocks?.length || 0} content blocks`)
    return blogPost

  } catch (error) {
    console.error(`❌ Error in fetchBlogPostBySlug: ${error}`)
    return null
  }
}

/**
 * Fetch related blog posts by category
 */
export async function fetchRelatedPosts(category: string, excludeId: string, limit: number = 3): Promise<RelatedPost[]> {
  try {
    console.log(`🔍 Fetching related posts for category: ${category}`)
    
    const { data: relatedPosts, error } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        category,
        published_at
      `)
      .eq('status', 'published')
      .eq('category', category)
      .neq('id', excludeId)
      .is('deleted_at', null)
      .order('published_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error(`❌ Error fetching related posts: ${error.message}`)
      return []
    }

    console.log(`✅ Found ${relatedPosts?.length || 0} related posts`)
    return relatedPosts || []

  } catch (error) {
    console.error(`❌ Error in fetchRelatedPosts: ${error}`)
    return []
  }
}

/**
 * Generate static params for all published blog posts
 */
export async function generateBlogStaticParams(): Promise<{ slug: string }[]> {
  try {
    console.log('🔍 Generating static params for blog posts')
    
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select('slug')
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('created_at', { ascending: false })

    if (error) {
      console.error(`❌ Error generating static params: ${error.message}`)
      return []
    }

    const params = posts?.map(post => ({ slug: post.slug })) || []
    console.log(`✅ Generated ${params.length} static params`)
    
    return params

  } catch (error) {
    console.error(`❌ Error in generateBlogStaticParams: ${error}`)
    return []
  }
}

/**
 * Increment view count for a blog post
 */
export async function incrementViewCount(postId: string): Promise<void> {
  try {
    // Validate postId
    if (!postId || typeof postId !== 'string') {
      console.error('❌ Invalid postId provided to incrementViewCount');
      return;
    }

    // First get the current view count
    const { data: currentPost, error: fetchError } = await supabase
      .from('sas_blog_posts')
      .select('view_count')
      .eq('id', postId)
      .single();

    if (fetchError) {
      // Don't log error if post doesn't exist (404 is expected for invalid slugs)
      if (fetchError.code !== 'PGRST116') {
        console.error(`❌ Error fetching current view count: ${fetchError.message}`);
      }
      return;
    }

    // Increment the view count
    const newViewCount = (currentPost?.view_count || 0) + 1;

    const { error } = await supabase
      .from('sas_blog_posts')
      .update({
        view_count: newViewCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', postId);

    if (error) {
      console.error(`❌ Error incrementing view count: ${error.message}`);
    }
  } catch (error) {
    console.error(`❌ Error in incrementViewCount: ${error}`);
  }
}

/**
 * Get all published blog posts for static generation
 */
export async function getAllPublishedPosts(): Promise<BlogPost[]> {
  try {
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count
      `)
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('published_at', { ascending: false })

    if (error) {
      console.error(`❌ Error fetching all posts: ${error.message}`)
      return []
    }

    return posts || []
  } catch (error) {
    console.error(`❌ Error in getAllPublishedPosts: ${error}`)
    return []
  }
}
