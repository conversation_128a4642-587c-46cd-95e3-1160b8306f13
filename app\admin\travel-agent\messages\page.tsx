'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { ArrowLeft, MessageSquare, Send, Search, Package, Clock } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

interface Message {
  id: number;
  conversationId: number;
  sender: 'admin' | 'partner';
  senderName: string;
  content: string;
  timestamp: string;
  read: boolean;
}

interface Conversation {
  id: number;
  partnerName: string;
  partnerCompany: string;
  bookingReference?: string;
  packageName?: string;
  subject: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  status: 'open' | 'resolved' | 'pending';
  priority: 'low' | 'medium' | 'high';
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

function TravelAgentMessagesContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const bookingId = searchParams.get('booking');
  
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<number | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockConversations: Conversation[] = [
      {
        id: 1,
        partnerName: 'John Safari Tours',
        partnerCompany: 'Safari Adventures Ltd',
        bookingReference: 'SA-B2B-001',
        packageName: 'Serengeti & Ngorongoro 5-Day Safari',
        subject: 'Booking Modification Request',
        lastMessage: 'Can we change the travel date to January 25th?',
        lastMessageTime: '2024-12-20T10:30:00Z',
        unreadCount: 2,
        status: 'open',
        priority: 'medium'
      },
      {
        id: 2,
        partnerName: 'African Dreams Travel',
        partnerCompany: 'Dreams Travel Agency',
        bookingReference: 'SA-B2B-002',
        packageName: 'Kilimanjaro Climbing Adventure',
        subject: 'Payment Confirmation',
        lastMessage: 'Payment has been processed successfully.',
        lastMessageTime: '2024-12-19T15:45:00Z',
        unreadCount: 0,
        status: 'resolved',
        priority: 'low'
      },
      {
        id: 3,
        partnerName: 'Wild Kenya Safaris',
        partnerCompany: 'Wild Kenya Ltd',
        subject: 'Partnership Application',
        lastMessage: 'Thank you for your application. We are reviewing your documents.',
        lastMessageTime: '2024-12-18T09:15:00Z',
        unreadCount: 1,
        status: 'pending',
        priority: 'high'
      }
    ];

    const mockMessages: Message[] = [
      {
        id: 1,
        conversationId: 1,
        sender: 'partner',
        senderName: 'John Safari Tours',
        content: 'Hello, I need to modify the booking SA-B2B-001. The client wants to change the travel date.',
        timestamp: '2024-12-20T09:00:00Z',
        read: true
      },
      {
        id: 2,
        conversationId: 1,
        sender: 'admin',
        senderName: 'Swift Africa Admin',
        content: 'Hi John, I can help you with that. What is the new preferred travel date?',
        timestamp: '2024-12-20T09:15:00Z',
        read: true
      },
      {
        id: 3,
        conversationId: 1,
        sender: 'partner',
        senderName: 'John Safari Tours',
        content: 'Can we change the travel date to January 25th? The client had a schedule conflict.',
        timestamp: '2024-12-20T10:30:00Z',
        read: false
      }
    ];

    setConversations(mockConversations);
    setMessages(mockMessages);

    // Auto-select conversation if coming from booking
    if (bookingId) {
      const bookingConversation = mockConversations.find(c => c.bookingReference?.includes(bookingId));
      if (bookingConversation) {
        setSelectedConversation(bookingConversation.id);
      }
    }
  }, [bookingId]);

  const filteredConversations = conversations.filter(conv => {
    const matchesSearch = conv.partnerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         conv.bookingReference?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || conv.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const selectedConversationData = conversations.find(c => c.id === selectedConversation);
  const conversationMessages = messages.filter(m => m.conversationId === selectedConversation);

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedConversation) {
      const message: Message = {
        id: messages.length + 1,
        conversationId: selectedConversation,
        sender: 'admin',
        senderName: 'Swift Africa Admin',
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        read: true
      };
      setMessages(prev => [...prev, message]);
      setNewMessage('');

      // Update conversation last message
      setConversations(prev => prev.map(conv => 
        conv.id === selectedConversation 
          ? { ...conv, lastMessage: newMessage.trim(), lastMessageTime: new Date().toISOString() }
          : conv
      ));
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <MessageSquare className="mr-3 text-purple-600" />
                  Partner Messages
                </h1>
                <p className="text-gray-600 mt-1">Communicate with travel agent partners</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Conversations List */}
          <div className="w-1/3 border-r border-gray-300 bg-white flex flex-col">
            {/* Search and Filters */}
            <div className="p-4 border-b border-gray-200">
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search conversations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                  />
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                >
                  <option value="all">All Status</option>
                  <option value="open">Open</option>
                  <option value="pending">Pending</option>
                  <option value="resolved">Resolved</option>
                </select>
              </div>
            </div>

            {/* Conversations */}
            <div className="flex-1 overflow-y-auto">
              {filteredConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => setSelectedConversation(conversation.id)}
                  className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                    selectedConversation === conversation.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{conversation.partnerName}</div>
                      <div className="text-sm text-gray-500">{conversation.partnerCompany}</div>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(conversation.status)}`}>
                        {conversation.status}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(conversation.priority)}`}>
                        {conversation.priority}
                      </span>
                    </div>
                  </div>
                  
                  {conversation.bookingReference && (
                    <div className="flex items-center text-sm text-blue-600 mb-2">
                      <Package size={12} className="mr-1" />
                      {conversation.bookingReference}
                    </div>
                  )}
                  
                  <div className="font-medium text-sm text-gray-900 mb-1">{conversation.subject}</div>
                  <div className="text-sm text-gray-600 line-clamp-2 mb-2">{conversation.lastMessage}</div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500 flex items-center">
                      <Clock size={12} className="mr-1" />
                      {formatTime(conversation.lastMessageTime)}
                    </div>
                    {conversation.unreadCount > 0 && (
                      <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                        {conversation.unreadCount}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Message Area */}
          <div className="flex-1 flex flex-col bg-white">
            {selectedConversationData ? (
              <>
                {/* Conversation Header */}
                <div className="p-4 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{selectedConversationData.partnerName}</div>
                      <div className="text-sm text-gray-500">{selectedConversationData.subject}</div>
                      {selectedConversationData.bookingReference && (
                        <div className="text-sm text-blue-600 flex items-center mt-1">
                          <Package size={12} className="mr-1" />
                          {selectedConversationData.bookingReference} - {selectedConversationData.packageName}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedConversationData.status)}`}>
                        {selectedConversationData.status}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(selectedConversationData.priority)}`}>
                        {selectedConversationData.priority} priority
                      </span>
                    </div>
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {conversationMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'admin' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender === 'admin'
                          ? 'bg-[var(--accent)] text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <div className="text-sm">{message.content}</div>
                        <div className={`text-xs mt-1 ${
                          message.sender === 'admin' ? 'text-blue-100' : 'text-gray-500'
                        }`}>
                          {message.senderName} • {formatTime(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Message Input */}
                <div className="p-4 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type your message..."
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    />
                    <button
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                      className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      <Send size={16} className="mr-2" />
                      Send
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <MessageSquare size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">Select a conversation</p>
                  <p className="text-sm">Choose a conversation from the list to start messaging</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}

export default function TravelAgentMessagesPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TravelAgentMessagesContent />
    </Suspense>
  );
}
