'use client'

import { useEffect } from 'react'
import { analytics, trackSEOMetrics } from '@/lib/analytics'

interface PerformanceMonitorProps {
  enableSEOTracking?: boolean
  enableErrorTracking?: boolean
  enableInteractionTracking?: boolean
  customThresholds?: {
    LCP?: { warning: number; critical: number }
    FID?: { warning: number; critical: number }
    CLS?: { warning: number; critical: number }
    TTFB?: { warning: number; critical: number }
  }
}

export default function PerformanceMonitor({
  enableSEOTracking = true,
  enableErrorTracking = true,
  enableInteractionTracking = true,
  customThresholds
}: PerformanceMonitorProps) {
  useEffect(() => {
    // Set custom thresholds if provided
    if (customThresholds) {
      Object.entries(customThresholds).forEach(([metric, thresholds]) => {
        analytics.setAlertThreshold(metric, thresholds.warning, thresholds.critical)
      })
    }

    // Enable SEO-specific tracking
    if (enableSEOTracking) {
      trackSEOMetrics()
    }

    // Enable error tracking
    if (enableErrorTracking) {
      setupErrorTracking()
    }

    // Enable interaction tracking
    if (enableInteractionTracking) {
      setupInteractionTracking()
    }

    // Track page view
    analytics.trackPageView()

    // Setup scroll depth tracking
    setupScrollTracking()

    // Setup form tracking
    setupFormTracking()

    // Setup click tracking for important elements
    setupClickTracking()

    // Setup visibility change tracking
    setupVisibilityTracking()

    // Setup connection change tracking
    setupConnectionTracking()

  }, [enableSEOTracking, enableErrorTracking, enableInteractionTracking, customThresholds])

  return null // This component doesn't render anything
}

function setupErrorTracking() {
  // Global error handler
  window.addEventListener('error', (event) => {
    analytics.trackError(`${event.error?.name}: ${event.error?.message}`, window.location.pathname)
  })

  // Unhandled promise rejection handler
  window.addEventListener('unhandledrejection', (event) => {
    analytics.trackError(`Unhandled Promise Rejection: ${event.reason}`, window.location.pathname)
  })

  // Console error tracking (optional - be careful not to create loops)
  const originalConsoleError = console.error
  console.error = (...args) => {
    analytics.trackError(`Console Error: ${args.join(' ')}`, window.location.pathname)
    originalConsoleError.apply(console, args)
  }
}

function setupInteractionTracking() {
  // Track clicks on important elements
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    
    // Track button clicks
    if (target.tagName === 'BUTTON' || target.closest('button')) {
      const button = target.tagName === 'BUTTON' ? target : target.closest('button')
      const buttonText = button?.textContent?.trim() || 'Unknown Button'
      analytics.trackClick(`button:${buttonText}`)
    }
    
    // Track link clicks
    if (target.tagName === 'A' || target.closest('a')) {
      const link = target.tagName === 'A' ? target : target.closest('a')
      const href = (link as HTMLAnchorElement)?.href || 'Unknown Link'
      analytics.trackClick(`link:${href}`)
    }
    
    // Track navigation clicks
    if (target.closest('nav')) {
      analytics.trackClick('navigation')
    }
    
    // Track CTA clicks
    if (target.classList.contains('cta') || target.closest('.cta')) {
      analytics.trackClick('cta')
    }
  })

  // Track form interactions
  document.addEventListener('focus', (event) => {
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.tagName === 'SELECT') {
      analytics.trackInteraction('form_field_focus', target.name || target.id || 'unknown')
    }
  }, true)
}

function setupScrollTracking() {
  let maxScroll = 0
  let scrollTimeout: NodeJS.Timeout

  const trackScroll = () => {
    const scrollPercent = Math.round(
      (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
    )
    
    if (scrollPercent > maxScroll) {
      maxScroll = scrollPercent
      
      // Track milestone scrolls
      if (maxScroll >= 25 && maxScroll < 50) {
        analytics.trackScroll(25)
      } else if (maxScroll >= 50 && maxScroll < 75) {
        analytics.trackScroll(50)
      } else if (maxScroll >= 75 && maxScroll < 90) {
        analytics.trackScroll(75)
      } else if (maxScroll >= 90) {
        analytics.trackScroll(90)
      }
    }
  }

  window.addEventListener('scroll', () => {
    clearTimeout(scrollTimeout)
    scrollTimeout = setTimeout(trackScroll, 100)
  })

  // Track final scroll depth on page unload
  window.addEventListener('beforeunload', () => {
    analytics.trackScroll(maxScroll)
  })
}

function setupFormTracking() {
  // Track form submissions
  document.addEventListener('submit', (event) => {
    const form = event.target as HTMLFormElement
    const formName = form.name || form.id || form.className || 'unknown'
    
    // Track form submission attempt
    analytics.trackFormSubmission(formName, true)
    
    // Track form fields
    const formData = new FormData(form)
    const fieldCount = Array.from(formData.keys()).length
    analytics.trackMetric('form_fields', fieldCount)
  })

  // Track form validation errors
  document.addEventListener('invalid', (event) => {
    const target = event.target as HTMLInputElement
    const form = target.closest('form')
    const formName = form?.name || form?.id || 'unknown'
    
    analytics.trackFormSubmission(formName, false)
    analytics.trackInteraction('form_validation_error', target.name || target.id || 'unknown')
  }, true)
}

function setupClickTracking() {
  // Track specific element clicks with data attributes
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    const trackingData = target.dataset.track || target.closest('[data-track]')?.getAttribute('data-track')
    
    if (trackingData) {
      analytics.trackInteraction('tracked_click', trackingData)
    }
    
    // Track package/tour clicks
    if (target.closest('[data-package]')) {
      const packageId = target.closest('[data-package]')?.getAttribute('data-package')
      analytics.trackInteraction('package_click', packageId || 'unknown')
    }
    
    // Track booking button clicks
    if (target.textContent?.toLowerCase().includes('book') || 
        target.classList.contains('book-now') ||
        target.closest('.book-now')) {
      analytics.trackConversion('booking_intent')
    }
    
    // Track contact button clicks
    if (target.textContent?.toLowerCase().includes('contact') ||
        target.classList.contains('contact-btn') ||
        target.closest('.contact-btn')) {
      analytics.trackConversion('contact_intent')
    }
  })
}

function setupVisibilityTracking() {
  let visibilityStartTime = Date.now()
  
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      const visibilityDuration = Date.now() - visibilityStartTime
      analytics.trackMetric('page_visibility_duration', visibilityDuration)
    } else {
      visibilityStartTime = Date.now()
    }
  })
}

function setupConnectionTracking() {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    
    // Track initial connection info
    analytics.trackMetric('connection_downlink', connection.downlink || 0)
    analytics.trackInteraction('connection_type', connection.effectiveType || 'unknown')
    
    // Track connection changes
    connection.addEventListener('change', () => {
      analytics.trackMetric('connection_downlink', connection.downlink || 0)
      analytics.trackInteraction('connection_change', connection.effectiveType || 'unknown')
    })
  }
}

// Export utility functions for manual tracking
export const trackCustomEvent = (eventName: string, value?: number, metadata?: Record<string, any>) => {
  analytics.trackCustomEvent(eventName, value, metadata)
}

export const trackConversion = (type: string, value?: number) => {
  analytics.trackConversion(type, value)
}

export const trackSearchQuery = (query: string, resultsCount: number) => {
  analytics.trackSearchQuery(query, resultsCount)
}

export const trackPackageView = (packageId: string, packageName: string) => {
  analytics.trackCustomEvent('package_view', 1, { packageId, packageName })
}

export const trackBookingStep = (step: string, packageId?: string) => {
  analytics.trackCustomEvent('booking_step', 1, { step, packageId })
}

export const trackNewsletterSignup = (source: string) => {
  analytics.trackConversion('newsletter_signup', 1)
  analytics.trackCustomEvent('newsletter_source', 1, { source })
}

export const trackSocialShare = (platform: string, url: string) => {
  analytics.trackCustomEvent('social_share', 1, { platform, url })
}

export const trackDownload = (fileName: string, fileType: string) => {
  analytics.trackCustomEvent('file_download', 1, { fileName, fileType })
}

export const trackVideoPlay = (videoId: string, videoTitle: string) => {
  analytics.trackCustomEvent('video_play', 1, { videoId, videoTitle })
}

export const trackImageView = (imageUrl: string, context: string) => {
  analytics.trackCustomEvent('image_view', 1, { imageUrl, context })
}
