import React from 'react';
import LazyImage from '../common/LazyImage';

const BannerSection = ({ title, description, image, alt }) => {
  return (
    <section className="relative h-[500px] overflow-hidden shadow-lg">
      <LazyImage
        src={image}
        alt={alt}
        className="w-full h-full object-cover brightness-75 animate-kenburns"
        width={1920}
        height={500}
        priority={true}
        skeletonVariant="hero"
      />
      <div className="absolute inset-0 bg-black/50 flex flex-col justify-center items-center p-5 text-center text-white">
        <h2 className="text-5xl font-bold mb-6">{title}</h2>
        <p className="text-xl max-w-3xl mx-auto mb-8 opacity-90">{description}</p>
        <button className="px-8 py-3 bg-[#163201] text-white font-bold rounded-full hover:bg-[#317100] transition-colors duration-300">
          Get Started
        </button>
      </div>
    </section>
  );
};

export default BannerSection;
