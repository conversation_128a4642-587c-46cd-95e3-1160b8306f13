import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function diagnoseBlogDatabase() {
  console.log('🔍 COMPREHENSIVE BLOG DATABASE DIAGNOSIS')
  console.log('=' .repeat(50))

  try {
    // 1. Check all blog posts in database
    console.log('\n📊 1. ALL BLOG POSTS IN DATABASE:')
    const { data: allPosts, error: allError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status, published_at, deleted_at')
      .order('created_at', { ascending: false })

    if (allError) {
      console.error('❌ Error fetching all posts:', allError.message)
      return
    }

    console.log(`   Total posts in database: ${allPosts?.length || 0}`)
    
    if (allPosts && allPosts.length > 0) {
      allPosts.forEach((post, index) => {
        const statusIcon = post.status === 'published' ? '✅' : '⏸️'
        const deletedIcon = post.deleted_at ? '🗑️' : ''
        console.log(`   ${index + 1}. ${statusIcon}${deletedIcon} "${post.title}"`)
        console.log(`      Slug: "${post.slug}"`)
        console.log(`      Status: ${post.status}`)
        console.log(`      Published: ${post.published_at || 'Not published'}`)
        console.log(`      Deleted: ${post.deleted_at || 'No'}`)
        console.log('')
      })
    }

    // 2. Check published posts only
    console.log('\n📝 2. PUBLISHED POSTS ONLY:')
    const { data: publishedPosts, error: publishedError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, published_at')
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('published_at', { ascending: false })

    if (publishedError) {
      console.error('❌ Error fetching published posts:', publishedError.message)
      return
    }

    console.log(`   Published posts: ${publishedPosts?.length || 0}`)
    
    if (publishedPosts && publishedPosts.length > 0) {
      publishedPosts.forEach((post, index) => {
        console.log(`   ${index + 1}. "${post.title}"`)
        console.log(`      Slug: "${post.slug}"`)
        console.log(`      Published: ${post.published_at}`)
        console.log('')
      })
    }

    // 3. Check specific problematic slug
    console.log('\n🎯 3. CHECKING SPECIFIC SLUG: "volcanoes-national-park"')
    const { data: specificPost, error: specificError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', 'volcanoes-national-park')
      .single()

    if (specificError) {
      console.log('❌ Post with slug "volcanoes-national-park" NOT FOUND')
      console.log('   Error:', specificError.message)
      
      // Check for similar slugs
      console.log('\n🔍 Looking for similar slugs containing "volcanoes":')
      const { data: similarPosts, error: similarError } = await supabase
        .from('sas_blog_posts')
        .select('title, slug, status')
        .ilike('slug', '%volcanoes%')

      if (!similarError && similarPosts && similarPosts.length > 0) {
        similarPosts.forEach(post => {
          console.log(`   - "${post.title}" (${post.slug}) - Status: ${post.status}`)
        })
      } else {
        console.log('   No similar slugs found')
      }
    } else {
      console.log('✅ Post found!')
      console.log(`   Title: "${specificPost.title}"`)
      console.log(`   Status: ${specificPost.status}`)
      console.log(`   Published: ${specificPost.published_at}`)
      console.log(`   Deleted: ${specificPost.deleted_at || 'No'}`)
    }

    // 4. Check content blocks for published posts
    console.log('\n🧩 4. CONTENT BLOCKS CHECK:')
    if (publishedPosts && publishedPosts.length > 0) {
      for (const post of publishedPosts.slice(0, 3)) { // Check first 3 posts
        const { data: blocks, error: blocksError } = await supabase
          .from('sas_blog_content_blocks')
          .select('id, block_type, sort_order')
          .eq('blog_post_id', post.id)
          .order('sort_order', { ascending: true })

        if (blocksError) {
          console.log(`   ❌ Error fetching blocks for "${post.title}": ${blocksError.message}`)
        } else {
          console.log(`   📄 "${post.title}" (${post.slug}): ${blocks?.length || 0} blocks`)
          if (blocks && blocks.length > 0) {
            const blockTypes = blocks.map(b => b.block_type).join(', ')
            console.log(`      Block types: ${blockTypes}`)
          }
        }
      }
    }

    // 5. Test database connection
    console.log('\n🔗 5. DATABASE CONNECTION TEST:')
    const { data: connectionTest, error: connectionError } = await supabase
      .from('sas_blog_posts')
      .select('count')
      .limit(1)

    if (connectionError) {
      console.log('❌ Database connection failed:', connectionError.message)
    } else {
      console.log('✅ Database connection successful')
    }

    // 6. Summary
    console.log('\n📋 6. SUMMARY:')
    console.log(`   - Total posts in database: ${allPosts?.length || 0}`)
    console.log(`   - Published posts: ${publishedPosts?.length || 0}`)
    console.log(`   - "volcanoes-national-park" exists: ${specificPost ? 'YES' : 'NO'}`)
    console.log(`   - Database connection: ${connectionError ? 'FAILED' : 'OK'}`)

    // 7. Recommendations
    console.log('\n💡 7. RECOMMENDATIONS:')
    if (!specificPost) {
      console.log('   ❗ The slug "volcanoes-national-park" does not exist in the database')
      console.log('   ❗ Check if the correct slug should be used or if the post needs to be created')
    }
    
    if (publishedPosts && publishedPosts.length === 0) {
      console.log('   ❗ No published posts found - check post statuses')
    }

    console.log('\n✅ Diagnosis complete!')

  } catch (error) {
    console.error('❌ Fatal error during diagnosis:', error)
  }
}

// Run the diagnosis
diagnoseBlogDatabase()
