"use client";
import React from 'react';
import { useRouter } from 'next/navigation';


const CampaignCard = ({ image, alt, title, description, onClick }) => {
  return (
    <div 
      className="bg-[var(--secondary-background)] shadow-lg transition-transform duration-300 hover:-translate-y-2 cursor-pointer"
      onClick={onClick}
    >
      <img src={image} alt={alt} className="w-full h-[200px] object-cover" />
      <div className="p-6">
        <h3 className="text-xl font-bold mb-4">{title}</h3>
        <p className="text-gray-700">{description}</p>
      </div>
    </div>
  );
};

const FeaturedCampaigns = () => {
  const router = useRouter();
  const campaigns = [
    {
      image: "/images/community/eco-hive-guadians-community-project.webp",
      title: "Eco-Hive Guardian Initiative",
      description: "Supporting sustainable beekeeping practices in Rwanda. This project aims to empower local communities through eco-friendly beekeeping, promoting biodiversity and providing a sustainable source of income.",
      link: "/community/hive-guardian",
      alt: "Supporting sustainable beekeeping practices in Rwanda. This project aims to empower local communities through eco-friendly beekeeping, promoting biodiversity and providing a sustainable source of income."
    },
    // Add other campaigns
  ];

  return (
    <section className="py-16 bg-[var(--primary-background)]">
      <div className="container mx-auto px-4">
        <p className="text-[#d35400] text-sm uppercase tracking-wider text-center mb-4">Our Impact</p>
        <h2 className="text-4xl font-bold text-center mb-6">Community Projects</h2>
        <p className="text-center max-w-3xl mx-auto mb-12">
          We are committed to making a real difference through sustainable initiatives that protect wildlife, preserve the environment, and empower local communities.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {campaigns.map((campaign, index) => (
            <CampaignCard key={index} {...campaign} onClick={() => router.push(campaign.link)} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedCampaigns;
