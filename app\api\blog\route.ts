import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// GET - Fetch published blog posts (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category') || '';
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';
    const sortBy = searchParams.get('sortBy') || 'date'; // 'date' or 'views'
    const exclude = searchParams.get('exclude') || ''; // slug to exclude

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build base query for published posts only
    let query = supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        published_at,
        view_count,
        seo_title,
        seo_description
      `)
      .eq('status', 'published')
      .is('deleted_at', null);

    // Apply filters
    if (category) {
      query = query.eq('category', category);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query = query.overlaps('tags', tagArray);
    }

    // Exclude specific post by slug
    if (exclude) {
      query = query.neq('slug', exclude);
    }

    // Apply sorting
    if (sortBy === 'views') {
      query = query.order('view_count', { ascending: false });
    } else {
      query = query.order('published_at', { ascending: false });
    }

    // Get total count for pagination
    const { count } = await supabase
      .from('sas_blog_posts')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published')
      .is('deleted_at', null);

    // Apply pagination
    const { data: posts, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch blog posts' },
        { status: 500 }
      );
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        posts: posts || [],
        pagination: {
          currentPage: page,
          totalPages,
          totalPosts: count || 0,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Get categories and tags for filtering (public endpoint)
export async function OPTIONS(request: NextRequest) {
  try {
    // Get unique categories
    const { data: categoryData, error: categoryError } = await supabase
      .from('sas_blog_posts')
      .select('category')
      .eq('status', 'published')
      .is('deleted_at', null);

    if (categoryError) {
      throw categoryError;
    }

    const categories = [...new Set(categoryData?.map(item => item.category) || [])];

    // Get all tags
    const { data: tagData, error: tagError } = await supabase
      .from('sas_blog_posts')
      .select('tags')
      .eq('status', 'published')
      .is('deleted_at', null);

    if (tagError) {
      throw tagError;
    }

    const allTags = tagData?.reduce((acc: string[], item) => {
      if (item.tags && Array.isArray(item.tags)) {
        acc.push(...item.tags);
      }
      return acc;
    }, []) || [];

    const uniqueTags = [...new Set(allTags)];

    return NextResponse.json({
      success: true,
      data: {
        categories,
        tags: uniqueTags
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories and tags' },
      { status: 500 }
    );
  }
}
