import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { 
  generateMetadata, 
  generateHreflangTags, 
  generateOrganizationSchema,
  generateWebsiteSchema,
  generateArticleSchema,
  generateProductSchema,
  generateLocalBusinessSchema,
  defaultSEO,
  hreflangConfigs
} from '@/lib/seo'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const testType = searchParams.get('test') || 'all'
    const url = searchParams.get('url') || '/'

    const testResults = {
      timestamp: new Date().toISOString(),
      test_type: testType,
      url,
      results: {} as any
    }

    switch (testType) {
      case 'metadata':
        testResults.results = await testMetadataGeneration()
        break
      
      case 'hreflang':
        testResults.results = await testHreflangImplementation()
        break
      
      case 'structured-data':
        testResults.results = await testStructuredData()
        break
      
      case 'sitemap':
        testResults.results = await testSitemapGeneration()
        break
      
      case 'performance':
        testResults.results = await testPerformanceHeaders()
        break
      
      case 'indexing':
        testResults.results = await testIndexingStatus()
        break
      
      case 'validation':
        testResults.results = await runValidationTests()
        break
      
      case 'all':
        testResults.results = await runComprehensiveTests()
        break
      
      default:
        return NextResponse.json({ error: 'Invalid test type' }, { status: 400 })
    }

    return NextResponse.json(testResults)

  } catch (error) {
    console.error('SEO Test API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

async function testMetadataGeneration() {
  const tests = []

  // Test 1: Basic metadata generation
  try {
    const metadata = generateMetadata({
      title: 'Test Page',
      description: 'Test description for SEO validation',
      keywords: ['test', 'seo', 'validation'],
      url: '/test-page'
    })

    tests.push({
      name: 'Basic Metadata Generation',
      status: 'pass',
      details: {
        title: metadata.title,
        description: metadata.description,
        canonical: metadata.alternates?.canonical,
        has_og: !!metadata.openGraph,
        has_twitter: !!metadata.twitter,
        has_robots: !!metadata.robots
      }
    })
  } catch (error) {
    tests.push({
      name: 'Basic Metadata Generation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Article metadata
  try {
    const articleMetadata = generateMetadata({
      title: 'Test Article',
      description: 'Test article description',
      type: 'article',
      author: 'Test Author',
      publishedTime: '2024-01-01T00:00:00Z',
      url: '/blog/test-article'
    })

    tests.push({
      name: 'Article Metadata Generation',
      status: 'pass',
      details: {
        type: articleMetadata.openGraph?.type,
        has_author: !!articleMetadata.authors,
        has_published_time: !!articleMetadata.openGraph
      }
    })
  } catch (error) {
    tests.push({
      name: 'Article Metadata Generation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Product metadata
  try {
    const productMetadata = generateMetadata({
      title: 'Test Safari Package',
      description: 'Test safari package description',
      type: 'product',
      price: 1500,
      currency: 'USD',
      availability: 'InStock',
      url: '/package/test-safari'
    })

    tests.push({
      name: 'Product Metadata Generation',
      status: 'pass',
      details: {
        has_price: !!productMetadata,
        has_availability: !!productMetadata
      }
    })
  } catch (error) {
    tests.push({
      name: 'Product Metadata Generation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function testHreflangImplementation() {
  const tests = []

  // Test 1: Basic hreflang generation
  try {
    const hreflangTags = generateHreflangTags('/')
    
    tests.push({
      name: 'Basic Hreflang Generation',
      status: hreflangTags.length > 0 ? 'pass' : 'fail',
      details: {
        total_regions: hreflangTags.length,
        has_x_default: hreflangTags.some(tag => tag.hrefLang === 'x-default'),
        sample_tags: hreflangTags.slice(0, 3)
      }
    })
  } catch (error) {
    tests.push({
      name: 'Basic Hreflang Generation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Priority regions only
  try {
    const priorityTags = generateHreflangTags('/', { priorityRegionsOnly: true })
    
    tests.push({
      name: 'Priority Regions Hreflang',
      status: priorityTags.length > 0 && priorityTags.length < Object.keys(hreflangConfigs).length ? 'pass' : 'fail',
      details: {
        priority_regions: priorityTags.length,
        total_regions: Object.keys(hreflangConfigs).length
      }
    })
  } catch (error) {
    tests.push({
      name: 'Priority Regions Hreflang',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Hreflang configuration validation
  try {
    const configErrors = []
    Object.entries(hreflangConfigs).forEach(([code, config]) => {
      if (!config.currency || !config.country || !config.language) {
        configErrors.push(`${code}: Missing required fields`)
      }
      if (config.priority < 1 || config.priority > 10) {
        configErrors.push(`${code}: Invalid priority ${config.priority}`)
      }
    })

    tests.push({
      name: 'Hreflang Configuration Validation',
      status: configErrors.length === 0 ? 'pass' : 'fail',
      details: {
        total_configs: Object.keys(hreflangConfigs).length,
        errors: configErrors
      }
    })
  } catch (error) {
    tests.push({
      name: 'Hreflang Configuration Validation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function testStructuredData() {
  const tests = []

  // Test 1: Organization schema
  try {
    const orgSchema = generateOrganizationSchema()
    const requiredFields = ['@context', '@type', 'name', 'url', 'logo']
    const hasRequired = requiredFields.every(field => orgSchema[field])

    tests.push({
      name: 'Organization Schema',
      status: hasRequired ? 'pass' : 'fail',
      details: {
        type: orgSchema['@type'],
        has_required_fields: hasRequired,
        fields: Object.keys(orgSchema)
      }
    })
  } catch (error) {
    tests.push({
      name: 'Organization Schema',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Website schema
  try {
    const websiteSchema = generateWebsiteSchema()
    const requiredFields = ['@context', '@type', 'name', 'url']
    const hasRequired = requiredFields.every(field => websiteSchema[field])

    tests.push({
      name: 'Website Schema',
      status: hasRequired ? 'pass' : 'fail',
      details: {
        type: websiteSchema['@type'],
        has_search_action: !!websiteSchema.potentialAction
      }
    })
  } catch (error) {
    tests.push({
      name: 'Website Schema',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Article schema
  try {
    const articleSchema = generateArticleSchema({
      title: 'Test Article',
      description: 'Test description',
      author: 'Test Author',
      publishedTime: '2024-01-01T00:00:00Z',
      image: '/test-image.jpg',
      url: '/blog/test'
    })

    const requiredFields = ['@context', '@type', 'headline', 'author', 'publisher']
    const hasRequired = requiredFields.every(field => articleSchema[field])

    tests.push({
      name: 'Article Schema',
      status: hasRequired ? 'pass' : 'fail',
      details: {
        type: articleSchema['@type'],
        has_author: !!articleSchema.author,
        has_publisher: !!articleSchema.publisher
      }
    })
  } catch (error) {
    tests.push({
      name: 'Article Schema',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 4: Product schema
  try {
    const productSchema = generateProductSchema({
      name: 'Test Safari',
      description: 'Test safari description',
      image: '/test-safari.jpg',
      price: 1500,
      currency: 'USD',
      availability: 'InStock',
      category: 'Safari Tours',
      url: '/package/test-safari'
    })

    const requiredFields = ['@context', '@type', 'name', 'offers']
    const hasRequired = requiredFields.every(field => productSchema[field])

    tests.push({
      name: 'Product Schema',
      status: hasRequired ? 'pass' : 'fail',
      details: {
        type: productSchema['@type'],
        has_offers: !!productSchema.offers,
        has_brand: !!productSchema.brand
      }
    })
  } catch (error) {
    tests.push({
      name: 'Product Schema',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 5: Local Business schema
  try {
    const businessSchema = generateLocalBusinessSchema()
    const requiredFields = ['@context', '@type', 'name', 'address']
    const hasRequired = requiredFields.every(field => businessSchema[field])

    tests.push({
      name: 'Local Business Schema',
      status: hasRequired ? 'pass' : 'fail',
      details: {
        type: businessSchema['@type'],
        has_address: !!businessSchema.address,
        has_geo: !!businessSchema.geo,
        has_opening_hours: !!businessSchema.openingHours
      }
    })
  } catch (error) {
    tests.push({
      name: 'Local Business Schema',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function testSitemapGeneration() {
  const tests = []

  // Test 1: Main sitemap accessibility
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/sitemap.xml`)
    const isAccessible = response.ok
    const contentType = response.headers.get('content-type')

    tests.push({
      name: 'Main Sitemap Accessibility',
      status: isAccessible ? 'pass' : 'fail',
      details: {
        status_code: response.status,
        content_type: contentType,
        is_xml: contentType?.includes('xml')
      }
    })
  } catch (error) {
    tests.push({
      name: 'Main Sitemap Accessibility',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Hreflang sitemap accessibility
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/hreflang-sitemap.xml`)
    const isAccessible = response.ok

    tests.push({
      name: 'Hreflang Sitemap Accessibility',
      status: isAccessible ? 'pass' : 'fail',
      details: {
        status_code: response.status,
        content_type: response.headers.get('content-type')
      }
    })
  } catch (error) {
    tests.push({
      name: 'Hreflang Sitemap Accessibility',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Dynamic content in sitemap
  try {
    const { data: packages } = await supabase
      .from('packages')
      .select('slug')
      .eq('status', 'published')
      .limit(5)

    const { data: blogPosts } = await supabase
      .from('blog_posts')
      .select('slug')
      .eq('status', 'published')
      .limit(5)

    tests.push({
      name: 'Dynamic Content Availability',
      status: (packages && packages.length > 0) || (blogPosts && blogPosts.length > 0) ? 'pass' : 'fail',
      details: {
        packages_count: packages?.length || 0,
        blog_posts_count: blogPosts?.length || 0
      }
    })
  } catch (error) {
    tests.push({
      name: 'Dynamic Content Availability',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function testPerformanceHeaders() {
  const tests = []

  // Test 1: SEO headers on homepage
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/`)
    const headers = {
      'x-robots-tag': response.headers.get('x-robots-tag'),
      'cache-control': response.headers.get('cache-control'),
      'x-content-type-options': response.headers.get('x-content-type-options'),
      'x-frame-options': response.headers.get('x-frame-options'),
      'link': response.headers.get('link')
    }

    const hasRequiredHeaders = !!(headers['x-content-type-options'] && headers['cache-control'])

    tests.push({
      name: 'SEO Headers on Homepage',
      status: hasRequiredHeaders ? 'pass' : 'fail',
      details: {
        headers,
        has_robots_tag: !!headers['x-robots-tag'],
        has_hreflang_links: headers.link?.includes('hreflang') || false
      }
    })
  } catch (error) {
    tests.push({
      name: 'SEO Headers on Homepage',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Performance monitoring endpoints
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/api/analytics/performance`)
    const isAccessible = response.ok

    tests.push({
      name: 'Performance Monitoring API',
      status: isAccessible ? 'pass' : 'fail',
      details: {
        status_code: response.status,
        endpoint_accessible: isAccessible
      }
    })
  } catch (error) {
    tests.push({
      name: 'Performance Monitoring API',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function testIndexingStatus() {
  const tests = []

  // Test 1: Robots.txt accessibility
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/robots.txt`)
    const content = await response.text()
    const hasUserAgent = content.includes('User-agent')
    const hasSitemap = content.includes('Sitemap:')

    tests.push({
      name: 'Robots.txt Configuration',
      status: response.ok && hasUserAgent && hasSitemap ? 'pass' : 'fail',
      details: {
        status_code: response.status,
        has_user_agent: hasUserAgent,
        has_sitemap: hasSitemap,
        content_length: content.length
      }
    })
  } catch (error) {
    tests.push({
      name: 'Robots.txt Configuration',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Meta robots tags
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/`)
    const html = await response.text()
    const hasMetaRobots = html.includes('name="robots"')
    const hasCanonical = html.includes('rel="canonical"')
    const hasViewport = html.includes('name="viewport"')

    tests.push({
      name: 'Meta Tags Validation',
      status: hasCanonical && hasViewport ? 'pass' : 'warning',
      details: {
        has_meta_robots: hasMetaRobots,
        has_canonical: hasCanonical,
        has_viewport: hasViewport,
        status_code: response.status
      }
    })
  } catch (error) {
    tests.push({
      name: 'Meta Tags Validation',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Structured data validation
  try {
    const response = await fetch(`${defaultSEO.siteUrl}/`)
    const html = await response.text()
    const hasJsonLd = html.includes('application/ld+json')
    const structuredDataCount = (html.match(/application\/ld\+json/g) || []).length

    tests.push({
      name: 'Structured Data Presence',
      status: hasJsonLd && structuredDataCount >= 2 ? 'pass' : 'warning',
      details: {
        has_structured_data: hasJsonLd,
        structured_data_blocks: structuredDataCount
      }
    })
  } catch (error) {
    tests.push({
      name: 'Structured Data Presence',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function runValidationTests() {
  const tests = []

  // Test 1: Database connectivity
  try {
    const { data, error } = await supabase
      .from('packages')
      .select('count')
      .limit(1)

    tests.push({
      name: 'Database Connectivity',
      status: !error ? 'pass' : 'fail',
      details: {
        connection_successful: !error,
        error_message: error?.message
      }
    })
  } catch (error) {
    tests.push({
      name: 'Database Connectivity',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 2: Performance monitoring tables
  try {
    const { data: metricsData, error: metricsError } = await supabase
      .from('performance_metrics')
      .select('count')
      .limit(1)

    const { data: alertsData, error: alertsError } = await supabase
      .from('performance_alerts')
      .select('count')
      .limit(1)

    tests.push({
      name: 'Performance Monitoring Tables',
      status: !metricsError && !alertsError ? 'pass' : 'fail',
      details: {
        metrics_table_accessible: !metricsError,
        alerts_table_accessible: !alertsError,
        metrics_error: metricsError?.message,
        alerts_error: alertsError?.message
      }
    })
  } catch (error) {
    tests.push({
      name: 'Performance Monitoring Tables',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 3: Content availability
  try {
    const { data: packages } = await supabase
      .from('packages')
      .select('id, title, slug, status')
      .eq('status', 'published')
      .limit(5)

    const { data: blogPosts } = await supabase
      .from('blog_posts')
      .select('id, title, slug, status')
      .eq('status', 'published')
      .limit(5)

    const hasContent = (packages && packages.length > 0) || (blogPosts && blogPosts.length > 0)

    tests.push({
      name: 'Published Content Availability',
      status: hasContent ? 'pass' : 'warning',
      details: {
        published_packages: packages?.length || 0,
        published_blog_posts: blogPosts?.length || 0,
        total_content_items: (packages?.length || 0) + (blogPosts?.length || 0)
      }
    })
  } catch (error) {
    tests.push({
      name: 'Published Content Availability',
      status: 'fail',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }

  // Test 4: API endpoints accessibility
  const apiEndpoints = [
    '/api/analytics/performance',
    '/api/analytics/dashboard',
    '/api/hreflang-test',
    '/api/seo-health'
  ]

  for (const endpoint of apiEndpoints) {
    try {
      const response = await fetch(`${defaultSEO.siteUrl}${endpoint}`)
      tests.push({
        name: `API Endpoint: ${endpoint}`,
        status: response.ok ? 'pass' : 'fail',
        details: {
          status_code: response.status,
          endpoint_accessible: response.ok
        }
      })
    } catch (error) {
      tests.push({
        name: `API Endpoint: ${endpoint}`,
        status: 'fail',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  return {
    total_tests: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    tests
  }
}

async function runComprehensiveTests() {
  const startTime = Date.now()

  // Run all test suites
  const [
    metadataResults,
    hreflangResults,
    structuredDataResults,
    sitemapResults,
    performanceResults,
    indexingResults,
    validationResults
  ] = await Promise.all([
    testMetadataGeneration(),
    testHreflangImplementation(),
    testStructuredData(),
    testSitemapGeneration(),
    testPerformanceHeaders(),
    testIndexingStatus(),
    runValidationTests()
  ])

  const allResults = {
    metadata: metadataResults,
    hreflang: hreflangResults,
    structured_data: structuredDataResults,
    sitemap: sitemapResults,
    performance: performanceResults,
    indexing: indexingResults,
    validation: validationResults
  }

  // Calculate overall statistics
  const totalTests = Object.values(allResults).reduce((sum, result) => sum + result.total_tests, 0)
  const totalPassed = Object.values(allResults).reduce((sum, result) => sum + result.passed, 0)
  const totalFailed = Object.values(allResults).reduce((sum, result) => sum + result.failed, 0)
  const overallScore = Math.round((totalPassed / totalTests) * 100)

  // Generate recommendations
  const recommendations = generateSEORecommendations(allResults)

  // Calculate health status
  const healthStatus = getHealthStatus(overallScore)

  const executionTime = Date.now() - startTime

  return {
    summary: {
      total_tests: totalTests,
      passed: totalPassed,
      failed: totalFailed,
      score: overallScore,
      health_status: healthStatus,
      execution_time_ms: executionTime
    },
    test_suites: allResults,
    recommendations,
    timestamp: new Date().toISOString()
  }
}

function generateSEORecommendations(results: any): string[] {
  const recommendations: string[] = []

  // Metadata recommendations
  if (results.metadata.failed > 0) {
    recommendations.push('Fix metadata generation issues to ensure proper title, description, and Open Graph tags')
  }

  // Hreflang recommendations
  if (results.hreflang.failed > 0) {
    recommendations.push('Resolve hreflang implementation issues for better international SEO targeting')
  }

  // Structured data recommendations
  if (results.structured_data.failed > 0) {
    recommendations.push('Fix structured data schemas to improve search engine understanding of your content')
  }

  // Sitemap recommendations
  if (results.sitemap.failed > 0) {
    recommendations.push('Ensure sitemaps are accessible and properly formatted for search engine crawling')
  }

  // Performance recommendations
  if (results.performance.failed > 0) {
    recommendations.push('Optimize performance headers and monitoring setup for better SEO signals')
  }

  // Indexing recommendations
  if (results.indexing.failed > 0) {
    recommendations.push('Fix robots.txt and meta robots configuration for proper search engine indexing')
  }

  // Validation recommendations
  if (results.validation.failed > 0) {
    recommendations.push('Resolve database connectivity and API endpoint issues for proper SEO functionality')
  }

  // General recommendations based on overall score
  const overallScore = Math.round((Object.values(results).reduce((sum: number, result: any) => sum + result.passed, 0) /
                                  Object.values(results).reduce((sum: number, result: any) => sum + result.total_tests, 0)) * 100)

  if (overallScore < 70) {
    recommendations.push('Consider a comprehensive SEO audit to address multiple failing tests')
  } else if (overallScore < 90) {
    recommendations.push('Focus on fixing the remaining SEO issues to achieve excellent SEO health')
  }

  if (recommendations.length === 0) {
    recommendations.push('Excellent! All SEO implementations are working correctly. Continue monitoring performance.')
  }

  return recommendations
}

function getHealthStatus(score: number): string {
  if (score >= 95) return 'excellent'
  if (score >= 85) return 'good'
  if (score >= 70) return 'fair'
  if (score >= 50) return 'poor'
  return 'critical'
}
