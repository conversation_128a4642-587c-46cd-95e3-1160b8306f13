/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Mountain, Save, Plus, X, Upload, Star } from 'lucide-react';

interface DestinationForm {
  id: number;
  name: string;
  location: string;
  description: string;
  highlights: string[];
  featuredImage: string;
  gallery: string[];
  status: 'active' | 'inactive';
  featured: boolean;
  bestTimeToVisit: string;
  activities: string[];
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function EditIconicDestinationPage() {
  const params = useParams();
  const router = useRouter();
  const [formData, setFormData] = useState<DestinationForm | null>(null);
  const [newHighlight, setNewHighlight] = useState('');
  const [newActivity, setNewActivity] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockDestination: DestinationForm = {
      id: parseInt(params.id as string),
      name: 'Serengeti National Park',
      location: 'Northern Tanzania',
      description: 'Home to the Great Migration and endless plains teeming with wildlife. The Serengeti is one of Africa\'s most famous safari destinations, offering incredible wildlife viewing opportunities year-round.',
      highlights: ['Great Migration', 'Big Five', 'Endless Plains', 'Hot Air Balloon Safaris'],
      featuredImage: '/images/destinations/serengeti.jpg',
      gallery: ['/images/destinations/serengeti-1.jpg', '/images/destinations/serengeti-2.jpg'],
      status: 'active',
      featured: true,
      bestTimeToVisit: 'June - October',
      activities: ['Game Drives', 'Hot Air Balloon', 'Walking Safaris', 'Photography'],
      seoTitle: 'Serengeti National Park - Iconic Safari Destination | Swift Africa Safaris',
      seoDescription: 'Discover the Serengeti National Park in Northern Tanzania. Experience the Great Migration, Big Five wildlife, and endless plains on your African safari adventure.',
      seoKeywords: 'Serengeti National Park, Tanzania safari, Great Migration, Big Five, wildlife safari'
    };
    setFormData(mockDestination);
  }, [params.id]);

  const handleInputChange = (field: keyof DestinationForm, value: string | boolean | string[]) => {
    if (formData) {
      setFormData(prev => prev ? { ...prev, [field]: value } : null);
    }
  };

  const addHighlight = () => {
    if (newHighlight.trim() && formData) {
      setFormData(prev => prev ? {
        ...prev,
        highlights: [...prev.highlights, newHighlight.trim()]
      } : null);
      setNewHighlight('');
    }
  };

  const removeHighlight = (index: number) => {
    if (formData) {
      setFormData(prev => prev ? {
        ...prev,
        highlights: prev.highlights.filter((_, i) => i !== index)
      } : null);
    }
  };

  const addActivity = () => {
    if (newActivity.trim() && formData) {
      setFormData(prev => prev ? {
        ...prev,
        activities: [...prev.activities, newActivity.trim()]
      } : null);
      setNewActivity('');
    }
  };

  const removeActivity = (index: number) => {
    if (formData) {
      setFormData(prev => prev ? {
        ...prev,
        activities: prev.activities.filter((_, i) => i !== index)
      } : null);
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, type: 'featured' | 'gallery') => {
    const file = e.target.files?.[0];
    if (file && formData) {
      // In a real app, you would upload to a server
      const imageUrl = URL.createObjectURL(file);
      if (type === 'featured') {
        setFormData(prev => prev ? { ...prev, featuredImage: imageUrl } : null);
      } else {
        setFormData(prev => prev ? { ...prev, gallery: [...prev.gallery, imageUrl] } : null);
      }
    }
  };

  const removeGalleryImage = (index: number) => {
    if (formData) {
      setFormData(prev => prev ? {
        ...prev,
        gallery: prev.gallery.filter((_, i) => i !== index)
      } : null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData) return;

    setIsSubmitting(true);

    try {
      // Here you would make an API call to update the destination
      console.log('Updating destination:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Iconic destination updated successfully!');
      router.push('/admin/iconic-destinations');
    } catch (error) {
      console.error('Error updating destination:', error);
      alert('Error updating destination. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!formData) {
    return (
      <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
        <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg text-gray-600">Loading destination details...</div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <Mountain className="mr-3 text-green-600" />
                  Edit Iconic Destination
                </h1>
                <p className="text-gray-600 mt-1">Destination ID: #{formData.id}</p>
              </div>
            </div>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-[var(--accent)] text-white px-6 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save size={16} className="mr-2" />
              {isSubmitting ? 'Updating...' : 'Update Destination'}
            </button>
          </div>
        </div>

        {/* Form */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Destination Name *</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Enter destination name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location *</label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="e.g., Northern Tanzania"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Best Time to Visit</label>
                  <input
                    type="text"
                    value={formData.bestTimeToVisit}
                    onChange={(e) => handleInputChange('bestTimeToVisit', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="e.g., June - October"
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Describe the destination"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="h-4 w-4 text-[var(--accent)] focus:ring-[var(--accent)] border-gray-300 rounded"
                  />
                  <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                    <Star size={16} className="inline mr-1" />
                    Featured Destination
                  </label>
                </div>
              </div>
            </div>

            {/* Highlights */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Highlights</h2>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newHighlight}
                    onChange={(e) => setNewHighlight(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Add a highlight"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addHighlight())}
                  />
                  <button
                    type="button"
                    onClick={addHighlight}
                    className="px-3 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--btn)] transition-colors"
                  >
                    <Plus size={16} />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.highlights.map((highlight, index) => (
                    <span key={index} className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      {highlight}
                      <button
                        type="button"
                        onClick={() => removeHighlight(index)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Activities */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Activities</h2>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newActivity}
                    onChange={(e) => setNewActivity(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Add an activity"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addActivity())}
                  />
                  <button
                    type="button"
                    onClick={addActivity}
                    className="px-3 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--btn)] transition-colors"
                  >
                    <Plus size={16} />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.activities.map((activity, index) => (
                    <span key={index} className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                      {activity}
                      <button
                        type="button"
                        onClick={() => removeActivity(index)}
                        className="ml-2 text-green-600 hover:text-green-800"
                      >
                        <X size={14} />
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Images */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Images</h2>
              <div className="space-y-6">
                {/* Featured Image */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Featured Image</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-[var(--accent)] transition-colors">
                    {formData.featuredImage ? (
                      <div className="space-y-4">
                        <img
                          src={formData.featuredImage}
                          alt="Featured preview"
                          className="mx-auto h-48 w-auto rounded-lg object-cover"
                        />
                        <div className="flex items-center justify-center space-x-4">
                          <label className="cursor-pointer text-[var(--accent)] hover:text-[var(--btn)] font-medium">
                            Change Image
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleImageUpload(e, 'featured')}
                              className="hidden"
                            />
                          </label>
                          <button
                            type="button"
                            onClick={() => handleInputChange('featuredImage', '')}
                            className="text-red-600 hover:text-red-800 font-medium"
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload size={48} className="mx-auto text-gray-400" />
                        <div>
                          <label className="cursor-pointer">
                            <span className="text-[var(--accent)] hover:text-[var(--btn)] font-medium">
                              Click to upload
                            </span>
                            <span className="text-gray-600"> featured image</span>
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleImageUpload(e, 'featured')}
                              className="hidden"
                            />
                          </label>
                        </div>
                        <p className="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Gallery */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Gallery Images</label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    {formData.gallery.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={image}
                          alt={`Gallery ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeGalleryImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                  </div>
                  <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <Upload size={16} className="mr-2" />
                    Add Gallery Image
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => handleImageUpload(e, 'gallery')}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
            </div>

            {/* SEO Settings */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">SEO Settings</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">SEO Title</label>
                  <input
                    type="text"
                    value={formData.seoTitle}
                    onChange={(e) => handleInputChange('seoTitle', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="SEO title for search engines"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">SEO Description</label>
                  <textarea
                    value={formData.seoDescription}
                    onChange={(e) => handleInputChange('seoDescription', e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="SEO description for search engines"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">SEO Keywords</label>
                  <input
                    type="text"
                    value={formData.seoKeywords}
                    onChange={(e) => handleInputChange('seoKeywords', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    placeholder="Enter keywords separated by commas"
                  />
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
}
