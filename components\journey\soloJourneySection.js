import React from 'react';
import ImageSlider from '../journey/imageSlider';
import Button from './Button';


const SoloJourney = () => {
  const soloImages = [
    {
      src: '/images/journey/Travel-Solo-Safari-Adventure.webp',
      alt: 'A solo travel girl enjoying adventure and nature with Swift Africa Safaris'
    },
    {
      src: '/images/journey/Canopy-walk-travel-Solo-Safari.webp',
      alt: 'A solo traveler doing canopy walk in Nyungwe National Park'
    }
  ];

  return (
    <section className="flex flex-col md:flex-row items-center bg-[var(--secondary-background)] px-6 py-16 md:px-16">
      <div className="flex-1 px-0 md:px-10 order-1 md:order-none  lg:text-left md:text-left lg:pr-16">
        <h2 className="text-3xl md:text-4xl font-medium mb-5 text-gray-900 leading-tight">
          Solo Journey: Discover Africa on Your Own terms
        </h2>
        <div className="text-base leading-relaxed text-gray-700 mb-8 space-y-4 text-justify md:text-left max-w-2xl lg:text-left">
          <p>
            There&apos;s a rare kind of magic in traveling alone where each moment belongs
            entirely to you. Africa welcomes the solo explorer with open arms, offering a journey rich in freedom,
            reflection, and memorable discovery.
          </p>
          <p>
            With every new landscape,
            experience, and culture, you&apos;ll find yourself growing in ways that only solitude and exploration can
            inspire.
          </p>
          <p>
            Designed for mindful adventurers, our solo itineraries prioritize your safety, comfort, and desire for
            authenticity. Whether it&apos;s learning from local communities, trekking through breathtaking terrains, or
            simply pausing to watch the sunset in silence.
          </p>
          <p>
            Go alone not to be alone, but to meet the world as you are.
          </p>
        </div>


        {/* Button for desktop */}
        <Button variant="desktop">Learn More</Button>
      </div>


      <div className="flex-1 order-2 md:order-none mt-8 md:mt-0 w-full">
        <ImageSlider images={soloImages} aspectRatio="portrait" />

        {/* Button for mobile and tablet */}
        <Button variant="mobile">Learn More</Button>
      </div>
    </section>
  );
};

export default SoloJourney;