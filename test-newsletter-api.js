// Simple test script for newsletter API
const testNewsletterAPI = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Newsletter API...\n');
  
  // Test 1: Subscribe to newsletter
  console.log('1. Testing newsletter subscription...');
  try {
    const subscribeResponse = await fetch(`${baseUrl}/api/newsletter/subscribe`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>' })
    });
    
    const subscribeData = await subscribeResponse.json();
    console.log('Subscribe Response:', subscribeData);
  } catch (error) {
    console.error('Subscribe Error:', error.message);
  }
  
  // Test 2: Get newsletter settings
  console.log('\n2. Testing newsletter settings...');
  try {
    const settingsResponse = await fetch(`${baseUrl}/api/newsletter/settings`);
    const settingsData = await settingsResponse.json();
    console.log('Settings Response:', settingsData);
  } catch (error) {
    console.error('Settings Error:', error.message);
  }
  
  // Test 3: Test simple send API
  console.log('\n3. Testing simple send API...');
  try {
    const sendResponse = await fetch(`${baseUrl}/api/newsletter/send-simple`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        subject: 'Test Newsletter',
        content: '<h1>Test Content</h1><p>This is a test newsletter.</p>',
        contentType: 'html'
      })
    });
    
    const sendData = await sendResponse.json();
    console.log('Send Response:', sendData);
  } catch (error) {
    console.error('Send Error:', error.message);
  }
  
  console.log('\nTest completed!');
};

// Run the test
testNewsletterAPI().catch(console.error);
