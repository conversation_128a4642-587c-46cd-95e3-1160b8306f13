import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

interface ContentBlock {
  id?: string;
  package_id: string;
  block_type: string;
  content?: string;
  content_data?: any;
  image_url?: string;
  image_alt?: string;
  image_caption?: string;
  sort_order: number;
}

// GET - Fetch content blocks for a package
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // First get the package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Fetch content blocks
    const { data: contentBlocks, error } = await supabase
      .from('sas_package_content_blocks')
      .select('*')
      .eq('package_id', packageData.id)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch content blocks' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: contentBlocks || []
    });

  } catch (error) {
    console.error('Error fetching content blocks:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch content blocks' },
      { status: 500 }
    );
  }
}

// POST - Create new content block
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!body.block_type) {
      return NextResponse.json(
        { success: false, error: 'Block type is required' },
        { status: 400 }
      );
    }

    // Validate image alt text for image blocks
    if (body.block_type === 'image' && (!body.image_alt || body.image_alt.trim() === '')) {
      return NextResponse.json(
        { success: false, error: 'Alt text is required for image blocks' },
        { status: 400 }
      );
    }

    // Get next sort order
    const { data: lastBlock } = await supabase
      .from('sas_package_content_blocks')
      .select('sort_order')
      .eq('package_id', packageData.id)
      .order('sort_order', { ascending: false })
      .limit(1)
      .single();

    const nextSortOrder = lastBlock ? lastBlock.sort_order + 1 : 0;

    // Prepare content block data
    const contentBlockData: ContentBlock = {
      package_id: packageData.id,
      block_type: body.block_type,
      content: body.content || '',
      content_data: body.content_data || null,
      image_url: body.image_url || '',
      image_alt: body.image_alt || '',
      image_caption: body.image_caption || '',
      sort_order: body.sort_order !== undefined ? body.sort_order : nextSortOrder
    };

    // Insert content block
    const { data: newBlock, error } = await supabase
      .from('sas_package_content_blocks')
      .insert([contentBlockData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create content block' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newBlock,
      message: 'Content block created successfully'
    });

  } catch (error) {
    console.error('Error creating content block:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create content block' },
      { status: 500 }
    );
  }
}

// PUT - Update content blocks (bulk update for reordering)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    if (!Array.isArray(body.blocks)) {
      return NextResponse.json(
        { success: false, error: 'Invalid blocks data' },
        { status: 400 }
      );
    }

    // Update each block
    const updatePromises = body.blocks.map(async (block: any) => {
      // Validate image alt text for image blocks
      if (block.block_type === 'image' && (!block.image_alt || block.image_alt.trim() === '')) {
        throw new Error('Alt text is required for image blocks');
      }

      return supabase
        .from('sas_package_content_blocks')
        .update({
          block_type: block.block_type,
          content: block.content || '',
          content_data: block.content_data || null,
          image_url: block.image_url || '',
          image_alt: block.image_alt || '',
          image_caption: block.image_caption || '',
          sort_order: block.sort_order
        })
        .eq('id', block.id)
        .eq('package_id', packageData.id);
    });

    const results = await Promise.all(updatePromises);
    
    // Check for errors
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('Supabase errors:', errors);
      return NextResponse.json(
        { success: false, error: 'Failed to update some content blocks' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Content blocks updated successfully'
    });

  } catch (error) {
    console.error('Error updating content blocks:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to update content blocks' },
      { status: 500 }
    );
  }
}

// DELETE - Delete content block
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const blockId = searchParams.get('blockId');

    if (!blockId) {
      return NextResponse.json(
        { success: false, error: 'Block ID is required' },
        { status: 400 }
      );
    }

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Delete content block
    const { error } = await supabase
      .from('sas_package_content_blocks')
      .delete()
      .eq('id', blockId)
      .eq('package_id', packageData.id);

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete content block' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Content block deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting content block:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete content block' },
      { status: 500 }
    );
  }
}
