import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createTables() {
  console.log('📋 Creating database tables...');

  // Since we can't use exec_sql, let's create a simple test table first
  // and then manually create the tables through Supabase dashboard

  console.log('⚠️  Note: Tables need to be created manually in Supabase dashboard');
  console.log('   Please run the SQL from scripts/setup-packages-database.sql');
  console.log('   in your Supabase SQL Editor');

  return true;

  // Create other tables...
  const { error: contentError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_package_content_blocks (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
        block_type TEXT NOT NULL CHECK (block_type IN (
          'paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6',
          'list', 'quote', 'code', 'image', 'divider', 'bulleted-list', 'numbered-list'
        )),
        content TEXT,
        content_data JSONB,
        image_url TEXT,
        image_alt TEXT,
        image_caption TEXT,
        sort_order INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (contentError) {
    console.error('❌ Error creating sas_package_content_blocks table:', contentError.message);
  } else {
    console.log('✅ sas_package_content_blocks table created');
  }

  // Create itinerary table
  const { error: itineraryError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_package_itinerary (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
        day_number INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        activities TEXT[],
        accommodation TEXT,
        meals TEXT[],
        sort_order INTEGER NOT NULL DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (itineraryError) {
    console.error('❌ Error creating sas_package_itinerary table:', itineraryError.message);
  } else {
    console.log('✅ sas_package_itinerary table created');
  }

  // Create bookings table
  const { error: bookingsError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_bookings (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        booking_reference TEXT UNIQUE NOT NULL,
        package_id UUID REFERENCES public.sas_packages(id),
        package_title TEXT NOT NULL,
        package_type TEXT NOT NULL CHECK (package_type IN ('solo', 'honeymoon', 'family', 'group')),
        full_name TEXT NOT NULL,
        email TEXT NOT NULL,
        phone TEXT NOT NULL,
        number_of_people INTEGER NOT NULL CHECK (number_of_people > 0),
        check_in_date DATE NOT NULL,
        check_out_date DATE,
        special_requests TEXT,
        amount DECIMAL(10,2) NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
        payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'refunded')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (bookingsError) {
    console.error('❌ Error creating sas_bookings table:', bookingsError.message);
  } else {
    console.log('✅ sas_bookings table created');
  }

  // Create package images table
  const { error: imagesError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_package_images (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
        image_url TEXT NOT NULL,
        image_alt TEXT NOT NULL,
        caption TEXT,
        sort_order INTEGER NOT NULL DEFAULT 0,
        is_featured BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (imagesError) {
    console.error('❌ Error creating sas_package_images table:', imagesError.message);
  } else {
    console.log('✅ sas_package_images table created');
  }
}

async function createPolicies() {
  console.log('🔒 Creating RLS policies...');

  // Enable RLS
  const tables = ['sas_packages', 'sas_package_content_blocks', 'sas_package_itinerary', 'sas_bookings', 'sas_package_images'];
  
  for (const table of tables) {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE public.${table} ENABLE ROW LEVEL SECURITY;`
    });
    
    if (error) {
      console.error(`❌ Error enabling RLS for ${table}:`, error.message);
    } else {
      console.log(`✅ RLS enabled for ${table}`);
    }
  }

  // Create policies for packages (public read access for active packages)
  const { error: policyError } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE POLICY IF NOT EXISTS "Anyone can view active packages" ON public.sas_packages
        FOR SELECT USING (status = 'active');
      
      CREATE POLICY IF NOT EXISTS "Authenticated users can manage packages" ON public.sas_packages
        FOR ALL USING (auth.role() = 'authenticated');
    `
  });

  if (policyError) {
    console.error('❌ Error creating policies:', policyError.message);
  } else {
    console.log('✅ Policies created');
  }
}

async function createIndexes() {
  console.log('⚡ Creating indexes...');

  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_sas_packages_slug ON public.sas_packages(slug);',
    'CREATE INDEX IF NOT EXISTS idx_sas_packages_status ON public.sas_packages(status);',
    'CREATE INDEX IF NOT EXISTS idx_sas_packages_category ON public.sas_packages(category);',
    'CREATE INDEX IF NOT EXISTS idx_sas_packages_featured ON public.sas_packages(featured);',
    'CREATE INDEX IF NOT EXISTS idx_sas_package_content_blocks_package_id ON public.sas_package_content_blocks(package_id);',
    'CREATE INDEX IF NOT EXISTS idx_sas_package_itinerary_package_id ON public.sas_package_itinerary(package_id);',
    'CREATE INDEX IF NOT EXISTS idx_sas_bookings_status ON public.sas_bookings(status);',
    'CREATE INDEX IF NOT EXISTS idx_sas_package_images_package_id ON public.sas_package_images(package_id);'
  ];

  for (const indexSql of indexes) {
    const { error } = await supabase.rpc('exec_sql', { sql: indexSql });
    if (error) {
      console.error('❌ Error creating index:', error.message);
    }
  }

  console.log('✅ Indexes created');
}

async function main() {
  try {
    console.log('🚀 Setting up database...');
    
    await createTables();
    await createPolicies();
    await createIndexes();
    
    console.log('\n✅ Database setup complete!');
    
    // Verify setup
    console.log('\n🔍 Verifying setup...');
    const { data, error } = await supabase.from('sas_packages').select('*').limit(1);
    
    if (error) {
      console.error('❌ Verification failed:', error.message);
    } else {
      console.log('✅ Database is working correctly!');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main();
