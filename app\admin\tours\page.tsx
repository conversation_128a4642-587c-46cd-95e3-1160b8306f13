/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
"use client"

import { useEffect, useState } from "react"
import { Plus, Edit, Trash2, Search, Eye, X, Calendar, MapPin, Users, DollarSign, Save, Upload } from "lucide-react"
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'
const supabase = createClient(supabaseUrl, supabaseKey)

interface Tour {
  id: string
  title: string
  slug: string
  description: string
  short_description: string
  duration_days: number
  duration_nights: number
  price_usd: number
  price_eur: number
  difficulty_level: string
  group_size_min: number
  group_size_max: number
  featured_image: string
  gallery_images: string[]
  highlights: string[]
  includes: string[]
  excludes: string[]
  itinerary: any[]
  accommodation_details: any
  transportation_details: any
  guide_details: any
  requirements: string[]
  what_to_bring: string[]
  cancellation_policy: string
  terms_conditions: string
  status: string
  featured: boolean
  seo_title: string
  seo_description: string
  seo_keywords: string[]
  views_count: number
  bookings_count: number
  rating_average: number
  rating_count: number
  category_id: string
  destination_id: string
  created_at: string
  updated_at: string
}

interface TourFormData {
  title: string
  slug: string
  description: string
  short_description: string
  duration_days: string
  duration_nights: string
  price_usd: string
  price_eur: string
  difficulty_level: string
  group_size_min: string
  group_size_max: string
  featured_image: string
  gallery_images: string[]
  highlights: string
  includes: string
  excludes: string
  itinerary: { day: number; title: string; description: string }[]
  accommodation_details: string
  transportation_details: string
  guide_details: string
  requirements: string
  what_to_bring: string
  cancellation_policy: string
  terms_conditions: string
  status: string
  featured: boolean
  seo_title: string
  seo_description: string
  seo_keywords: string
  category_id: string
  destination_id: string
}

const initialFormData: TourFormData = {
  title: "",
  slug: "",
  description: "",
  short_description: "",
  duration_days: "",
  duration_nights: "",
  price_usd: "",
  price_eur: "",
  difficulty_level: "easy",
  group_size_min: "",
  group_size_max: "",
  featured_image: "",
  gallery_images: [],
  highlights: "",
  includes: "",
  excludes: "",
  itinerary: [],
  accommodation_details: "",
  transportation_details: "",
  guide_details: "",
  requirements: "",
  what_to_bring: "",
  cancellation_policy: "",
  terms_conditions: "",
  status: "draft",
  featured: false,
  seo_title: "",
  seo_description: "",
  seo_keywords: "",
  category_id: "",
  destination_id: ""
}

const TouristManagerPage = () => {
  const [tours, setTours] = useState<Tour[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [, setDestinations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingTour, setEditingTour] = useState<Tour | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [formData, setFormData] = useState<TourFormData>(initialFormData)
  const [showDetails, setShowDetails] = useState(false)
  const [selectedTour, setSelectedTour] = useState<Tour | null>(null)
  const [, setFormStep] = useState(1)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchTours();
    fetchCategories();
    fetchDestinations();
  }, []);

  const fetchTours = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('packages')
        .select(`
          *,
          package_categories(name),
          destinations(name)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tours:', error);
        setError('Failed to fetch tours');
        return;
      }

      setTours(data || []);
    } catch (error) {
      console.error('Error fetching tours:', error);
      setError('Failed to fetch tours');
    } finally {
      setLoading(false);
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('package_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }

  const fetchDestinations = async () => {
    try {
      const { data, error } = await supabase
        .from('destinations')
        .select('*')
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching destinations:', error);
        return;
      }

      setDestinations(data || []);
    } catch (error) {
      console.error('Error fetching destinations:', error);
    }
  }

  const handleDeleteTour = async (tourId: string) => {
    if (!confirm('Are you sure you want to delete this tour?')) return

    try {
      const { error } = await supabase
        .from('packages')
        .delete()
        .eq('id', tourId);

      if (error) {
        console.error('Error deleting tour:', error);
        setError('Failed to delete tour');
        return;
      }

      setTours(prevTours => prevTours.filter(t => t.id !== tourId));
      
      if (selectedTour?.id === tourId) {
        setShowDetails(false);
        setSelectedTour(null);
      }

      setSuccess('Tour deleted successfully');
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error in handleDeleteTour:', error);
      setError('Failed to delete tour. Please try again.');
    }
  }

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value
      }));
    }
  }

  // Robust image upload with error handling
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setUploading(true);
    setError(null);
    try {
      // Try to upload directly without checking bucket existence first
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 8)}.${fileExt}`;
      
      const { error: uploadError } = await supabase.storage.from('tours').upload(fileName, file, { upsert: true });
      
      if (uploadError) {
        // If upload fails, then check if bucket exists
        const { data: bucketList, error: bucketError } = await supabase.storage.listBuckets();
        
        if (bucketError) {
          throw new Error('Could not access storage: ' + bucketError.message);
        }
        
        const bucketExists = bucketList?.find((b: any) => b.id === 'tours');
        
        if (!bucketExists) {
          setError(
            'Storage bucket "tours" does not exist. ' +
            'Please create a bucket named "tours" in your Supabase dashboard.'
          );
          setUploading(false);
          return;
        }
        
        throw new Error('Upload failed: ' + uploadError.message);
      }
      
      const { data: urlData } = supabase.storage.from('tours').getPublicUrl(fileName);
      if (!urlData?.publicUrl) {
        throw new Error('Could not get public URL for uploaded image');
      }
      
      setFormData((prev) => ({ ...prev, featured_image: urlData.publicUrl }));
      setSuccess('Image uploaded successfully');
      setTimeout(() => setSuccess(null), 3000);
    } catch (err: any) {
      setError('Error uploading image: ' + (err?.message || 'Unknown error'));
    } finally {
      setUploading(false);
    }
  };

  // Gallery upload with error handling





  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setUploading(true);
    setError(null);
    try {
      if (!formData.slug) {
        formData.slug = generateSlug(formData.title);
      }
      const tourData = {
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        short_description: formData.short_description,
        duration_days: parseInt(formData.duration_days) || 0,
        duration_nights: parseInt(formData.duration_nights) || 0,
        price_usd: parseFloat(formData.price_usd) || 0,
        price_eur: parseFloat(formData.price_eur) || 0,
        difficulty_level: formData.difficulty_level,
        group_size_min: parseInt(formData.group_size_min) || 1,
        group_size_max: parseInt(formData.group_size_max) || 10,
        featured_image: formData.featured_image,
        gallery_images: formData.gallery_images,
        highlights: formData.highlights.split(',').map(h => h.trim()).filter(h => h),
        includes: formData.includes.split(',').map(i => i.trim()).filter(i => i),
        excludes: formData.excludes.split(',').map(e => e.trim()).filter(e => e),
        itinerary: formData.itinerary,
        accommodation_details: formData.accommodation_details,
        transportation_details: formData.transportation_details,
        guide_details: formData.guide_details,
        requirements: formData.requirements.split(',').map(r => r.trim()).filter(r => r),
        what_to_bring: formData.what_to_bring.split(',').map(w => w.trim()).filter(w => w),
        cancellation_policy: formData.cancellation_policy,
        terms_conditions: formData.terms_conditions,
        status: formData.status,
        featured: formData.featured,
        seo_title: formData.seo_title,
        seo_description: formData.seo_description,
        seo_keywords: formData.seo_keywords.split(',').map(k => k.trim()).filter(k => k),
        category_id: formData.category_id || null,
        destination_id: formData.destination_id || null
      };
      let result;
      if (editingTour) {
        const { data, error } = await supabase.from('packages').update(tourData).eq('id', editingTour.id).select();
        if (error) throw error;
        result = data;
      } else {
        const { data, error } = await supabase.from('packages').insert([tourData]).select();
        if (error) throw error;
        result = data;
      }
      await fetchTours();
      setFormData(initialFormData);
      setShowForm(false);
      setEditingTour(null);
      setFormStep(1);
      setSuccess(editingTour ? 'Tour updated successfully!' : 'Tour created successfully!');
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: any) {
      setError('Failed to save tour. ' + (error?.message || 'Please try again.'));
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = (tour: Tour) => {
    setEditingTour(tour);
    setFormData({
      title: tour.title,
      slug: tour.slug,
      description: tour.description,
      short_description: tour.short_description,
      duration_days: tour.duration_days?.toString() || '',
      duration_nights: tour.duration_nights?.toString() || '',
      price_usd: tour.price_usd?.toString() || '',
      price_eur: tour.price_eur?.toString() || '',
      difficulty_level: tour.difficulty_level,
      group_size_min: tour.group_size_min?.toString() || '',
      group_size_max: tour.group_size_max?.toString() || '',
      featured_image: tour.featured_image,
      gallery_images: tour.gallery_images || [],
      highlights: tour.highlights?.join(', ') || '',
      includes: tour.includes?.join(', ') || '',
      excludes: tour.excludes?.join(', ') || '',
      itinerary: tour.itinerary || [],
      accommodation_details: tour.accommodation_details || '',
      transportation_details: tour.transportation_details || '',
      guide_details: tour.guide_details || '',
      requirements: tour.requirements?.join(', ') || '',
      what_to_bring: tour.what_to_bring?.join(', ') || '',
      cancellation_policy: tour.cancellation_policy || '',
      terms_conditions: tour.terms_conditions || '',
      status: tour.status,
      featured: tour.featured,
      seo_title: tour.seo_title || '',
      seo_description: tour.seo_description || '',
      seo_keywords: tour.seo_keywords?.join(', ') || '',
      category_id: tour.category_id || '',
      destination_id: tour.destination_id || ''
    });
    setShowForm(true);
    setFormStep(1);
  };

  const filteredTours = tours.filter(tour =>
    tour.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    tour.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="ml-16 pt-16 min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
        <div className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="ml-16 pt-16 min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      <div className="p-6">
        {/* Header */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div className="mb-6 lg:mb-0">
              <div className="flex items-center mb-4">
                <div className="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white mr-4">
                  <MapPin className="w-8 h-8" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent">
                    Tour Management
                  </h1>
                  <p className="text-gray-600 mt-2 text-lg">
                    Create, edit, and manage your tour packages
                  </p>
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                setShowForm(true);
                setEditingTour(null);
                setFormData(initialFormData);
                setFormStep(1);
              }}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium flex items-center shadow-lg hover:shadow-xl"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add New Tour
            </button>
          </div>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
            {success}
          </div>
        )}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* Search and Filters */}
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search tours..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex gap-2">
              <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.name}</option>
                ))}
              </select>
              <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="draft">Draft</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Tours Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTours.map((tour) => (
            <div key={tour.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={tour.featured_image || '/images/placeholder.jpg'}
                  alt={tour.title}
                  className="w-full h-full object-cover"
                />
                {tour.featured && (
                  <div className="absolute top-4 left-4 bg-amber-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                    Featured
                  </div>
                )}
                <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                  ${tour.price_usd}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                  {tour.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {tour.short_description || tour.description}
                </p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {tour.duration_days} days
                  </div>
                  <div className="flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    Max {tour.group_size_max}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {tour.difficulty_level}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    tour.status === 'active' ? 'bg-green-100 text-green-800' :
                    tour.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {tour.status}
                  </span>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedTour(tour);
                        setShowDetails(true);
                      }}
                      className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                      title="View details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleEdit(tour)}
                      className="p-2 text-gray-400 hover:text-emerald-600 hover:bg-emerald-50 rounded-lg transition-colors duration-200"
                      title="Edit tour"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteTour(tour.id)}
                      className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                      title="Delete tour"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredTours.length === 0 && !loading && (
          <div className="text-center py-12">
            <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tours found</h3>
            <p className="text-gray-500 mb-6">Create your first tour package to get started.</p>
            <button
              onClick={() => {
                setShowForm(true);
                setEditingTour(null);
                setFormData(initialFormData);
                setFormStep(1);
              }}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium"
            >
              <Plus className="w-5 h-5 mr-2 inline" />
              Create Your First Tour
            </button>
          </div>
        )}
      </div>

      {/* Tour Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">
                  {editingTour ? 'Edit Tour' : 'Create New Tour'}
                </h2>
                <button
                  onClick={() => {
                    setShowForm(false);
                    setEditingTour(null);
                    setFormData(initialFormData);
                    setFormStep(1);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {/* Form content will be added here - this is a simplified version */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tour Title *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter tour title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter tour description"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration (Days)
                    </label>
                    <input
                      type="number"
                      name="duration_days"
                      value={formData.duration_days}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price (USD)
                    </label>
                    <input
                      type="number"
                      name="price_usd"
                      value={formData.price_usd}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category
                    </label>
                    <select
                      name="category_id"
                      value={formData.category_id}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Select Category</option>
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>{category.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Featured Image
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="featured-image"
                    />
                    <label
                      htmlFor="featured-image"
                      className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 cursor-pointer flex items-center"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Image
                    </label>
                    {formData.featured_image && (
                      <img
                        src={formData.featured_image}
                        alt="Featured"
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      name="featured"
                      checked={formData.featured}
                      onChange={handleInputChange}
                      className="mr-2"
                    />
                    <span className="text-sm font-medium text-gray-700">Featured Tour</span>
                  </label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="draft">Draft</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowForm(false);
                    setEditingTour(null);
                    setFormData(initialFormData);
                    setFormStep(1);
                  }}
                  className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={uploading}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 font-medium flex items-center disabled:opacity-50"
                >
                  {uploading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  {editingTour ? 'Update Tour' : 'Create Tour'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Tour Details Modal */}
      {showDetails && selectedTour && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">Tour Details</h2>
                <button
                  onClick={() => {
                    setShowDetails(false);
                    setSelectedTour(null);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <img
                    src={selectedTour.featured_image || '/images/placeholder.jpg'}
                    alt={selectedTour.title}
                    className="w-full h-64 object-cover rounded-lg"
                  />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{selectedTour.title}</h3>
                  <p className="text-gray-600 mb-6">{selectedTour.description}</p>
                  
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <Calendar className="w-5 h-5 text-blue-500 mr-3" />
                      <span className="text-gray-700">{selectedTour.duration_days} days, {selectedTour.duration_nights} nights</span>
                    </div>
                    <div className="flex items-center">
                      <DollarSign className="w-5 h-5 text-green-500 mr-3" />
                      <span className="text-gray-700">${selectedTour.price_usd} USD</span>
                    </div>
                    <div className="flex items-center">
                      <Users className="w-5 h-5 text-purple-500 mr-3" />
                      <span className="text-gray-700">Max {selectedTour.group_size_max} people</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-5 h-5 text-red-500 mr-3" />
                      <span className="text-gray-700">{selectedTour.difficulty_level} difficulty</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TouristManagerPage; 