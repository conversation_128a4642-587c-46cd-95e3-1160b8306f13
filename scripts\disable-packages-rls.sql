-- Disable Row Level Security (RLS) on Package Tables
-- This script disables RLS on all package-related tables to allow unrestricted access
-- Run this script in your Supabase SQL editor

-- =====================================================
-- DISABLE RLS ON PACKAGE TABLES
-- =====================================================

-- Disable RLS on main packages table
ALTER TABLE public.sas_packages DISABLE ROW LEVEL SECURITY;

-- Disable RLS on package content blocks table
ALTER TABLE public.sas_package_content_blocks DISABLE ROW LEVEL SECURITY;

-- Disable RLS on package itinerary table
ALTER TABLE public.sas_package_itinerary DISABLE ROW LEVEL SECURITY;

-- Disable RLS on bookings table
ALTER TABLE public.sas_bookings DISABLE ROW LEVEL SECURITY;

-- Disable RLS on package images table
ALTER TABLE public.sas_package_images DISABLE ROW LEVEL SECURITY;

-- =====================================================
-- DROP ALL EXISTING RLS POLICIES (OPTIONAL)
-- =====================================================
-- Uncomment the following lines if you want to completely remove the policies

-- Drop policies for sas_packages
-- DROP POLICY IF EXISTS "Anyone can view active packages" ON public.sas_packages;
-- DROP POLICY IF EXISTS "Authenticated users can manage packages" ON public.sas_packages;

-- Drop policies for sas_package_content_blocks
-- DROP POLICY IF EXISTS "Anyone can view content blocks for active packages" ON public.sas_package_content_blocks;
-- DROP POLICY IF EXISTS "Authenticated users can manage content blocks" ON public.sas_package_content_blocks;

-- Drop policies for sas_package_itinerary
-- DROP POLICY IF EXISTS "Anyone can view itinerary for active packages" ON public.sas_package_itinerary;
-- DROP POLICY IF EXISTS "Authenticated users can manage itinerary" ON public.sas_package_itinerary;

-- Drop policies for sas_bookings
-- DROP POLICY IF EXISTS "Anyone can create bookings" ON public.sas_bookings;
-- DROP POLICY IF EXISTS "Authenticated users can manage bookings" ON public.sas_bookings;

-- Drop policies for sas_package_images
-- DROP POLICY IF EXISTS "Anyone can view images for active packages" ON public.sas_package_images;
-- DROP POLICY IF EXISTS "Authenticated users can manage package images" ON public.sas_package_images;

-- =====================================================
-- VERIFICATION
-- =====================================================
-- Check which tables still have RLS enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename LIKE 'sas_%' 
ORDER BY tablename;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '✅ RLS Disabled on Package Tables!';
    RAISE NOTICE '📋 Tables affected: sas_packages, sas_package_content_blocks, sas_package_itinerary, sas_bookings, sas_package_images';
    RAISE NOTICE '⚠️  WARNING: All tables now have unrestricted access';
    RAISE NOTICE '🔓 Anyone can now read, insert, update, and delete data in these tables';
    RAISE NOTICE '';
    RAISE NOTICE '💡 To re-enable RLS later, run the setup-packages-database.sql script';
END $$;
