import React, { useState } from 'react';
import { Minus, Plus } from 'lucide-react';

const QuantitySelector = ({ initialQuantity = 1, min = 1, max = 99, onChange }) => {
    const [quantity, setQuantity] = useState(initialQuantity);

    const handleDecrease = () => {
        if (quantity > min) {
            const newQuantity = quantity - 1;
            setQuantity(newQuantity);
            if (onChange) {
                onChange(newQuantity);
            }
        }
    };

    const handleIncrease = () => {
        if (quantity < max) {
            const newQuantity = quantity + 1;
            setQuantity(newQuantity);
            if (onChange) {
                onChange(newQuantity);
            }
        }
    };

    const handleInputChange = (e) => {
        const value = parseInt(e.target.value) || min;
        const clampedValue = Math.max(min, Math.min(max, value));
        setQuantity(clampedValue);
        if (onChange) {
            onChange(clampedValue);
        }
    };

    return (
        <div className="flex items-center space-x-3">
            <button
                onClick={handleDecrease}
                disabled={quantity <= min}
                className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
                <Minus className="w-4 h-4 text-gray-600" />
            </button>

            <input
                type="number"
                value={quantity}
                onChange={handleInputChange}
                min={min}
                max={max}
                className="w-16 h-8 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />

            <button
                onClick={handleIncrease}
                disabled={quantity >= max}
                className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
                <Plus className="w-4 h-4 text-gray-600" />
            </button>
        </div>
    );
};

export default QuantitySelector;