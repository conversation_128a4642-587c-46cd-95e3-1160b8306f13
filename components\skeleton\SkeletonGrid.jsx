import React from 'react';
import SkeletonCard from './SkeletonCard';

/**
 * Skeleton Grid Component for displaying multiple skeleton cards
 * Handles responsive grid layouts for different content types
 */
const SkeletonGrid = ({ 
  variant = 'blog', // 'blog', 'package', 'product', 'mini-package'
  count = 6,
  className = '',
  cardProps = {}
}) => {
  const getGridClasses = () => {
    switch (variant) {
      case 'blog':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10';
      case 'package':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';
      case 'mini-package':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6';
      case 'product':
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
      default:
        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';
    }
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard
          key={index}
          variant={variant}
          {...cardProps}
        />
      ))}
    </div>
  );
};

export default SkeletonGrid;
