import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { readFileSync } from 'fs';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createMiniPackagesTable() {
  console.log('📋 Creating sas_mini_packages table...');

  // Check if table already exists
  const { data: existingTable } = await supabase
    .from('information_schema.tables')
    .select('table_name')
    .eq('table_schema', 'public')
    .eq('table_name', 'sas_mini_packages')
    .single();

  if (existingTable) {
    console.log('✅ sas_mini_packages table already exists');
    return true;
  }

  // Since we can't use exec_sql, we'll create the table using a different approach
  // Let's try to insert a dummy record to trigger table creation via Supabase
  try {
    const { error } = await supabase
      .from('sas_mini_packages')
      .select('id')
      .limit(1);

    if (error && error.message.includes('does not exist')) {
      console.log('❌ Table does not exist and cannot be created via API');
      console.log('📋 Please run the SQL script manually in Supabase SQL Editor:');
      console.log('   scripts/setup-mini-packages-database.sql');
      return false;
    }

    console.log('✅ sas_mini_packages table exists');
    return true;
  } catch (err) {
    console.error('❌ Error checking table:', err.message);
    return false;
  }
}

async function createMiniPackageContentBlocksTable() {
  console.log('📋 Creating sas_mini_package_content_blocks table...');
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_mini_package_content_blocks (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
        block_type TEXT NOT NULL CHECK (block_type IN (
          'paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6',
          'list', 'quote', 'code', 'image', 'divider', 'bulleted-list', 'numbered-list'
        )),
        content TEXT,
        content_data JSONB,
        sort_order INTEGER NOT NULL DEFAULT 0,
        
        image_url TEXT,
        image_alt TEXT,
        image_caption TEXT,
        
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (error) {
    console.error('❌ Error creating sas_mini_package_content_blocks table:', error.message);
    return false;
  } else {
    console.log('✅ sas_mini_package_content_blocks table created');
    return true;
  }
}

async function createMiniPackageItineraryTable() {
  console.log('📋 Creating sas_mini_package_itinerary table...');
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_mini_package_itinerary (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
        hour_number INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        sort_order INTEGER NOT NULL DEFAULT 0,
        
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (error) {
    console.error('❌ Error creating sas_mini_package_itinerary table:', error.message);
    return false;
  } else {
    console.log('✅ sas_mini_package_itinerary table created');
    return true;
  }
}

async function createMiniPackageImagesTable() {
  console.log('📋 Creating sas_mini_package_images table...');
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS public.sas_mini_package_images (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        mini_package_id UUID REFERENCES public.sas_mini_packages(id) ON DELETE CASCADE,
        image_url TEXT NOT NULL,
        image_alt TEXT NOT NULL,
        caption TEXT,
        sort_order INTEGER NOT NULL DEFAULT 0,
        is_featured BOOLEAN DEFAULT false,
        
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `
  });

  if (error) {
    console.error('❌ Error creating sas_mini_package_images table:', error.message);
    return false;
  } else {
    console.log('✅ sas_mini_package_images table created');
    return true;
  }
}

async function createIndexes() {
  console.log('⚡ Creating indexes...');
  
  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_slug ON public.sas_mini_packages(slug);',
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_packages_status ON public.sas_mini_packages(status);',
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_package_content_blocks_mini_package_id ON public.sas_mini_package_content_blocks(mini_package_id);',
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_package_itinerary_mini_package_id ON public.sas_mini_package_itinerary(mini_package_id);',
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_package_itinerary_hour_number ON public.sas_mini_package_itinerary(hour_number);',
    'CREATE INDEX IF NOT EXISTS idx_sas_mini_package_images_mini_package_id ON public.sas_mini_package_images(mini_package_id);'
  ];

  for (const indexSql of indexes) {
    const { error } = await supabase.rpc('exec_sql', { sql: indexSql });
    if (error) {
      console.error('❌ Error creating index:', error.message);
      return false;
    }
  }
  
  console.log('✅ Indexes created');
  return true;
}

async function insertSampleData() {
  console.log('📝 Inserting sample mini package data...');
  
  // Insert sample mini package
  const { data: miniPackage, error: packageError } = await supabase
    .from('sas_mini_packages')
    .insert([{
      title: 'The Last Login - Digital Detox Safari Experience',
      slug: 'the-last-login',
      difficulty: 'Easy',
      category: 'Cultural',
      location: 'Uganda',
      duration: '6 Hours',
      pricing_solo: 150.00,
      pricing_honeymoon: 280.00,
      pricing_family: 120.00,
      pricing_group: 100.00,
      status: 'published',
      image_url: 'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5',
      image_alt: 'Digital detox safari experience in Uganda',
      hero_image_url: 'https://images.unsplash.com/photo-1516026672322-bc52d61a55d5',
      hero_image_alt: 'Digital detox safari experience - disconnect to reconnect',
      seo_title: 'The Last Login - Digital Detox Safari | Swift Africa Safaris',
      seo_description: 'Experience a transformative 6-hour digital detox safari in Uganda. Disconnect from technology and reconnect with nature and yourself.',
      seo_keywords: ['digital detox', 'safari', 'uganda', 'wellness', 'nature', 'mindfulness'],
      highlights: [
        'Complete digital disconnection experience',
        'Guided nature meditation sessions',
        'Wildlife observation without distractions',
        'Traditional storytelling by local guides',
        'Organic lunch prepared with local ingredients',
        'Certificate of digital detox completion'
      ],
      includes: [
        'Professional guide',
        'All meals and refreshments',
        'Transportation within the park',
        'Digital detox certificate',
        'Nature meditation sessions',
        'Traditional storytelling session'
      ],
      excludes: [
        'Personal electronic devices (intentionally excluded)',
        'International flights',
        'Accommodation (day trip only)',
        'Personal shopping',
        'Tips and gratuities'
      ]
    }])
    .select()
    .single();

  if (packageError) {
    console.error('❌ Error inserting sample mini package:', packageError.message);
    return false;
  }

  // Insert sample itinerary
  const itineraryData = [
    {
      mini_package_id: miniPackage.id,
      hour_number: 1,
      title: 'Digital Surrender & Welcome (Hour 1)',
      description: 'Begin your transformative journey by surrendering all digital devices in our secure digital vault. Meet your guide and fellow participants, receive your analog journal, and set intentions for your digital detox experience.',
      sort_order: 1
    },
    {
      mini_package_id: miniPackage.id,
      hour_number: 2,
      title: 'Nature Immersion Walk (Hour 2)',
      description: 'Embark on a guided nature walk through pristine wilderness. Learn to observe wildlife and natural phenomena without the urge to photograph or share. Practice mindful walking and deep breathing exercises.',
      sort_order: 2
    },
    {
      mini_package_id: miniPackage.id,
      hour_number: 3,
      title: 'Meditation & Reflection (Hour 3)',
      description: 'Participate in guided meditation sessions in a serene natural setting. Learn techniques for managing digital addiction and finding peace in silence. Journal your thoughts and feelings using pen and paper.',
      sort_order: 3
    },
    {
      mini_package_id: miniPackage.id,
      hour_number: 4,
      title: 'Organic Lunch & Storytelling (Hour 4)',
      description: 'Enjoy a delicious organic lunch prepared with locally sourced ingredients. Listen to traditional stories and folklore from local community members, experiencing the ancient art of oral tradition.',
      sort_order: 4
    },
    {
      mini_package_id: miniPackage.id,
      hour_number: 5,
      title: 'Wildlife Observation & Sketching (Hour 5)',
      description: 'Engage in patient wildlife observation using only your eyes and analog sketching materials. Learn to appreciate the present moment without the need to capture and share every experience digitally.',
      sort_order: 5
    },
    {
      mini_package_id: miniPackage.id,
      hour_number: 6,
      title: 'Integration & Digital Re-entry (Hour 6)',
      description: 'Reflect on your digital detox experience and create a personal plan for healthier technology use. Receive your digital detox certificate and gradually reintegrate with your devices using mindful practices.',
      sort_order: 6
    }
  ];

  const { error: itineraryError } = await supabase
    .from('sas_mini_package_itinerary')
    .insert(itineraryData);

  if (itineraryError) {
    console.error('❌ Error inserting sample itinerary:', itineraryError.message);
    return false;
  }

  console.log('✅ Sample data inserted successfully');
  return true;
}

async function main() {
  try {
    console.log('🚀 Setting up mini packages database...');
    
    const success = await createMiniPackagesTable() &&
                   await createMiniPackageContentBlocksTable() &&
                   await createMiniPackageItineraryTable() &&
                   await createMiniPackageImagesTable() &&
                   await createIndexes() &&
                   await insertSampleData();

    if (success) {
      console.log('\n✅ Mini packages database setup completed successfully!');
      console.log('📋 Created tables: sas_mini_packages, sas_mini_package_content_blocks, sas_mini_package_itinerary, sas_mini_package_images');
      console.log('⏰ Mini packages use HOUR_NUMBER instead of day_number for itineraries');
      console.log('🔗 Test mini package available at: /mini-package/the-last-login');
    } else {
      console.log('\n❌ Setup failed. Please check the errors above.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

main();
