import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import nodemailer from 'nodemailer';

// Simple newsletter send API for testing
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { subject, content, contentType = 'html' } = body;

    // Basic validation
    if (!subject || !content) {
      return NextResponse.json(
        { success: false, error: 'Subject and content are required' },
        { status: 400 }
      );
    }

    // Create service role client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get newsletter settings
    const { data: settings, error: settingsError } = await supabaseAdmin
      .from('sas_newsletter_settings')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (settingsError || !settings) {
      return NextResponse.json(
        { success: false, error: 'Newsletter settings not configured' },
        { status: 500 }
      );
    }

    // Get subscribers
    const { data: subscribers, error: subscribersError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .select('email')
      .eq('status', 'subscribed');

    if (subscribersError) {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch subscribers' },
        { status: 500 }
      );
    }

    if (!subscribers || subscribers.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No active subscribers found' },
        { status: 404 }
      );
    }

    // For testing, let's just return the count without actually sending
    return NextResponse.json({
      success: true,
      message: `Would send newsletter to ${subscribers.length} subscribers`,
      data: {
        totalSubscribers: subscribers.length,
        subject: subject,
        contentPreview: content.substring(0, 100) + '...',
        settingsConfigured: !!settings.smtp_email
      }
    });

  } catch (error) {
    console.error('Newsletter send error:', error);
    return NextResponse.json(
      { success: false, error: `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}
