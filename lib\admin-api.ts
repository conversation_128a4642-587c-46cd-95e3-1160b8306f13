import { supabase } from './supabase';
import type {
  Booking,
  Package,
  User,
  Review,
  DashboardStats
} from './supabase';

// Dashboard Statistics
export const getDashboardStats = async (): Promise<DashboardStats> => {
  try {
    const [
      { count: bookingsCount },
      { count: packagesCount },
      { count: reviewsCount },
      { count: subscribersCount },
      { count: usersCount },
      { count: donationsCount },
      { data: revenueData }
    ] = await Promise.all([
      supabase.from('bookings').select('*', { count: 'exact', head: true }),
      supabase.from('packages').select('*', { count: 'exact', head: true }),
      supabase.from('reviews').select('*', { count: 'exact', head: true }),
      supabase.from('subscribers').select('*', { count: 'exact', head: true }),
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('donations').select('*', { count: 'exact', head: true }),
      supabase.from('bookings').select('amount').eq('status', 'confirmed')
    ]);

    const totalRevenue = revenueData?.reduce((sum, booking) => sum + (booking.amount || 0), 0) || 0;

    return {
      totalBookings: bookingsCount || 0,
      totalPackages: packagesCount || 0,
      totalRevenue,
      totalReviews: reviewsCount || 0,
      totalSubscribers: subscribersCount || 0,
      totalUsers: usersCount || 0,
      totalDonations: donationsCount || 0
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return {
      totalBookings: 0,
      totalPackages: 0,
      totalRevenue: 0,
      totalReviews: 0,
      totalSubscribers: 0,
      totalUsers: 0,
      totalDonations: 0
    };
  }
};

// Bookings
export const getBookings = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return { data: [], error };
  }
};

export const createBooking = async (booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .insert([booking])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating booking:', error);
    return { data: null, error };
  }
};

export const updateBooking = async (id: string, updates: Partial<Booking>) => {
  try {
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating booking:', error);
    return { data: null, error };
  }
};

export const deleteBooking = async (id: string) => {
  try {
    const { error } = await supabase
      .from('bookings')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting booking:', error);
    return { error };
  }
};

// Packages
export const getPackages = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('packages')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching packages:', error);
    return { data: [], error };
  }
};

export const createPackage = async (packageData: Omit<Package, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const { data, error } = await supabase
      .from('packages')
      .insert([packageData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating package:', error);
    return { data: null, error };
  }
};

export const updatePackage = async (id: string, updates: Partial<Package>) => {
  try {
    const { data, error } = await supabase
      .from('packages')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating package:', error);
    return { data: null, error };
  }
};

export const deletePackage = async (id: string) => {
  try {
    const { error } = await supabase
      .from('packages')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting package:', error);
    return { error };
  }
};

// Users
export const getUsers = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { data: [], error };
  }
};

export const createUser = async (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error creating user:', error);
    return { data: null, error };
  }
};

export const updateUser = async (id: string, updates: Partial<User>) => {
  try {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating user:', error);
    return { data: null, error };
  }
};

export const deleteUser = async (id: string) => {
  try {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting user:', error);
    return { error };
  }
};

// Reviews
export const getReviews = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('reviews')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return { data: [], error };
  }
};

export const updateReview = async (id: string, updates: Partial<Review>) => {
  try {
    const { data, error } = await supabase
      .from('reviews')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    console.error('Error updating review:', error);
    return { data: null, error };
  }
};

export const deleteReview = async (id: string) => {
  try {
    const { error } = await supabase
      .from('reviews')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return { error: null };
  } catch (error) {
    console.error('Error deleting review:', error);
    return { error };
  }
};



// Donations
export const getDonations = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('donations')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching donations:', error);
    return { data: [], error };
  }
};

// Subscribers
export const getSubscribers = async (limit = 10, offset = 0) => {
  try {
    const { data, error } = await supabase
      .from('subscribers')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;
    return { data: data || [], error: null };
  } catch (error) {
    console.error('Error fetching subscribers:', error);
    return { data: [], error };
  }
}; 