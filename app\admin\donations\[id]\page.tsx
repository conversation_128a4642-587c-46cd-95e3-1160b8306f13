'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, DollarSign, User, Mail, Phone, Calendar, CreditCard, FileText, Download, Edit, Save, X } from 'lucide-react';

interface Donation {
  id: number;
  donorName: string;
  donorEmail: string;
  donorPhone: string;
  amount: number;
  purpose: string;
  status: 'completed' | 'pending' | 'failed';
  paymentMethod: string;
  date: string;
  receiptUrl?: string;
  notes?: string;
  transactionId?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function DonationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [donation, setDonation] = useState<Donation | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<Donation>>({});

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockDonation: Donation = {
      id: parseInt(params.id as string),
      donorName: 'John Smith',
      donorEmail: '<EMAIL>',
      donorPhone: '******-0123',
      amount: 500,
      purpose: 'Wildlife Conservation',
      status: 'completed',
      paymentMethod: 'Credit Card',
      date: '2024-12-15',
      receiptUrl: '/receipts/receipt-001.pdf',
      notes: 'Regular donor, very supportive of our wildlife conservation efforts.',
      transactionId: 'TXN-2024-001234'
    };
    setDonation(mockDonation);
    setEditForm(mockDonation);
  }, [params.id]);

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (donation && editForm) {
      setDonation({ ...donation, ...editForm });
      setIsEditing(false);
      // Here you would make an API call to save the changes
      console.log('Saving donation:', editForm);
    }
  };

  const handleCancel = () => {
    setEditForm(donation || {});
    setIsEditing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (!donation) {
    return (
      <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
        <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg text-gray-600">Loading donation details...</div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="mr-4 p-2 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <DollarSign className="mr-3 text-green-600" />
                  Donation Details
                </h1>
                <p className="text-gray-600 mt-1">Donation ID: #{donation.id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {!isEditing ? (
                <button
                  onClick={handleEdit}
                  className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
                >
                  <Edit size={16} className="mr-2" />
                  Edit
                </button>
              ) : (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleSave}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center"
                  >
                    <Save size={16} className="mr-2" />
                    Save
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center"
                  >
                    <X size={16} className="mr-2" />
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Donor Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <User className="mr-2 text-blue-600" />
                Donor Information
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.donorName || ''}
                      onChange={(e) => setEditForm({ ...editForm, donorName: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  ) : (
                    <div className="flex items-center text-gray-900">
                      <User size={16} className="mr-2 text-gray-400" />
                      {donation.donorName}
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  {isEditing ? (
                    <input
                      type="email"
                      value={editForm.donorEmail || ''}
                      onChange={(e) => setEditForm({ ...editForm, donorEmail: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  ) : (
                    <div className="flex items-center text-gray-900">
                      <Mail size={16} className="mr-2 text-gray-400" />
                      {donation.donorEmail}
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                  {isEditing ? (
                    <input
                      type="tel"
                      value={editForm.donorPhone || ''}
                      onChange={(e) => setEditForm({ ...editForm, donorPhone: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  ) : (
                    <div className="flex items-center text-gray-900">
                      <Phone size={16} className="mr-2 text-gray-400" />
                      {donation.donorPhone}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Donation Details */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <DollarSign className="mr-2 text-green-600" />
                Donation Details
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                  {isEditing ? (
                    <input
                      type="number"
                      value={editForm.amount || ''}
                      onChange={(e) => setEditForm({ ...editForm, amount: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  ) : (
                    <div className="text-2xl font-bold text-green-600">${donation.amount.toLocaleString()}</div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Purpose</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={editForm.purpose || ''}
                      onChange={(e) => setEditForm({ ...editForm, purpose: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  ) : (
                    <div className="text-gray-900">{donation.purpose}</div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  {isEditing ? (
                    <select
                      value={editForm.status || ''}
                      onChange={(e) => setEditForm({ ...editForm, status: e.target.value as 'completed' | 'pending' | 'failed' })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    >
                      <option value="completed">Completed</option>
                      <option value="pending">Pending</option>
                      <option value="failed">Failed</option>
                    </select>
                  ) : (
                    <span className={`px-3 py-1 rounded-full text-sm font-medium capitalize ${getStatusColor(donation.status)}`}>
                      {donation.status}
                    </span>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                  <div className="flex items-center text-gray-900">
                    <CreditCard size={16} className="mr-2 text-gray-400" />
                    {donation.paymentMethod}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <div className="flex items-center text-gray-900">
                    <Calendar size={16} className="mr-2 text-gray-400" />
                    {new Date(donation.date).toLocaleDateString()}
                  </div>
                </div>
                {donation.transactionId && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Transaction ID</label>
                    <div className="text-gray-900 font-mono text-sm">{donation.transactionId}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes and Receipt */}
            <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="mr-2 text-purple-600" />
                Additional Information
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                  {isEditing ? (
                    <textarea
                      value={editForm.notes || ''}
                      onChange={(e) => setEditForm({ ...editForm, notes: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                      placeholder="Add any notes about this donation..."
                    />
                  ) : (
                    <div className="text-gray-900 bg-gray-50 p-3 rounded-lg">
                      {donation.notes || 'No notes available'}
                    </div>
                  )}
                </div>
                {donation.receiptUrl && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Receipt</label>
                    <button className="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                      <Download size={16} className="mr-2" />
                      Download Receipt
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
