/* eslint-disable react/no-unescaped-entities */
/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  MapPin, 
  Clock, 
  Users, 
  Star, 
  ShoppingCart,
  Heart,
  Share2,
  Camera,
  Mountain,
  Compass,
  Check,
  X,
  Package
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';

interface PackageDetails {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  destination: string;
  duration: string;
  difficulty: 'Easy' | 'Moderate' | 'Challenging';
  category: 'Safari' | 'Cultural' | 'Climbing' | 'Day Trip';
  images: string[];
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  commission: number;
  rating: number;
  reviewCount: number;
  highlights: string[];
  included: string[];
  excluded: string[];
  itinerary: Array<{
    day: number;
    title: string;
    description: string;
    activities: string[];
  }>;
  maxTravelers: number;
  minAge: number;
  availability: 'Available' | 'Limited' | 'Sold Out';
  bestTime: string;
  physicalRequirements: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PackageDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const packageId = params.id as string;
  
  const [packageDetails, setPackageDetails] = useState<PackageDetails | null>(null);
  const [selectedPricing, setSelectedPricing] = useState<'solo' | 'honeymoon' | 'family' | 'group'>('solo');
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockPackage: PackageDetails = {
      id: packageId,
      name: 'Serengeti & Ngorongoro 5-Day Safari',
      description: 'Experience the best of Tanzania with this comprehensive safari covering Serengeti National Park and Ngorongoro Crater.',
      longDescription: 'Embark on an unforgettable journey through Tanzania\'s most iconic wildlife destinations. This carefully crafted 5-day safari takes you through the vast plains of Serengeti National Park, where you\'ll witness the incredible diversity of African wildlife, and the breathtaking Ngorongoro Crater, often called the "Eighth Wonder of the World." Our experienced guides will ensure you have the best wildlife viewing opportunities while learning about conservation efforts and local culture.',
      destination: 'Serengeti & Ngorongoro',
      duration: '5 Days',
      difficulty: 'Easy',
      category: 'Safari',
      images: [
        '/images/serengeti-1.jpg',
        '/images/serengeti-2.jpg',
        '/images/serengeti-3.jpg',
        '/images/ngorongoro-1.jpg'
      ],
      pricing: {
        solo: 2400,
        honeymoon: 2200,
        family: 1800,
        group: 1600
      },
      commission: 15,
      rating: 4.8,
      reviewCount: 124,
      highlights: [
        'Big Five Wildlife Viewing',
        'Great Migration (seasonal)',
        'Ngorongoro Crater Descent',
        'Professional Safari Guide',
        'Luxury Safari Vehicles',
        'Cultural Village Visit'
      ],
      included: [
        'Accommodation in safari lodges',
        'All meals during safari',
        'Professional safari guide',
        'Game drives in 4x4 vehicles',
        'Park entrance fees',
        'Airport transfers',
        'Bottled water during game drives'
      ],
      excluded: [
        'International flights',
        'Travel insurance',
        'Personal expenses',
        'Alcoholic beverages',
        'Tips for guide and staff',
        'Optional activities'
      ],
      itinerary: [
        {
          day: 1,
          title: 'Arrival in Arusha',
          description: 'Arrive at Kilimanjaro Airport and transfer to Arusha for briefing and overnight stay.',
          activities: ['Airport pickup', 'Safari briefing', 'Equipment check', 'Welcome dinner']
        },
        {
          day: 2,
          title: 'Arusha to Serengeti',
          description: 'Drive to Serengeti National Park with game viewing en route.',
          activities: ['Morning departure', 'Game drive to Serengeti', 'Lunch at lodge', 'Afternoon game drive']
        },
        {
          day: 3,
          title: 'Full Day Serengeti',
          description: 'Full day exploring the vast plains of Serengeti National Park.',
          activities: ['Early morning game drive', 'Picnic lunch in the park', 'Afternoon wildlife viewing', 'Sunset at kopjes']
        },
        {
          day: 4,
          title: 'Serengeti to Ngorongoro',
          description: 'Morning game drive then transfer to Ngorongoro Conservation Area.',
          activities: ['Final Serengeti game drive', 'Transfer to Ngorongoro', 'Crater rim viewing', 'Cultural village visit']
        },
        {
          day: 5,
          title: 'Ngorongoro Crater & Departure',
          description: 'Descend into Ngorongoro Crater for game viewing then return to Arusha.',
          activities: ['Crater descent', 'Game viewing on crater floor', 'Picnic lunch', 'Return to Arusha', 'Airport transfer']
        }
      ],
      maxTravelers: 8,
      minAge: 5,
      availability: 'Available',
      bestTime: 'June to October (Dry season) and December to March',
      physicalRequirements: 'Low - suitable for most fitness levels. Some walking required.'
    };
    
    setPackageDetails(mockPackage);
  }, [packageId]);

  if (!packageDetails) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Safari': return <Camera className="h-4 w-4" />;
      case 'Climbing': return <Mountain className="h-4 w-4" />;
      case 'Cultural': return <Users className="h-4 w-4" />;
      case 'Day Trip': return <Compass className="h-4 w-4" />;
      default: return <MapPin className="h-4 w-4" />;
    }
  };

  const selectedPrice = packageDetails.pricing[selectedPricing];
  const commissionAmount = selectedPrice * packageDetails.commission / 100;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Back Button */}
      <button
        onClick={() => router.back()}
        className="flex items-center text-gray-600 hover:text-gray-900 mb-6 transition-colors"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Packages
      </button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Image Gallery */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="relative h-96 bg-gray-200">
              {/* Main Image */}
              <div className="absolute inset-0 bg-gray-300 flex items-center justify-center">
                <Camera className="h-16 w-16 text-gray-400" />
              </div>
              
              {/* Image Navigation */}
              {packageDetails.images.length > 1 && (
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {packageDetails.images.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-3 h-3 rounded-full ${
                        currentImageIndex === index ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Package Info */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(packageDetails.difficulty)}`}>
                    {getCategoryIcon(packageDetails.category)}
                    <span className="ml-1">{packageDetails.category}</span>
                  </span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    {packageDetails.commission}% commission
                  </span>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{packageDetails.name}</h1>
                <div className="flex items-center text-gray-600 space-x-4 mb-4">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{packageDetails.destination}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{packageDetails.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    <span>Max {packageDetails.maxTravelers} travelers</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Heart className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center mb-6">
              <div className="flex items-center">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <span className="ml-1 text-lg font-medium text-gray-900">{packageDetails.rating}</span>
              </div>
              <span className="ml-2 text-gray-500">({packageDetails.reviewCount} reviews)</span>
            </div>

            {/* Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">About This Package</h3>
              <p className="text-gray-600 leading-relaxed">{packageDetails.longDescription}</p>
            </div>

            {/* Highlights */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Highlights</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {packageDetails.highlights.map((highlight, index) => (
                  <div key={index} className="flex items-center">
                    <Check className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" />
                    <span className="text-gray-700">{highlight}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Itinerary */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Day by Day Itinerary</h3>
            <div className="space-y-4">
              {packageDetails.itinerary.map((day, index) => (
                <div key={index} className="border-l-2 border-blue-200 pl-4 pb-4">
                  <div className="flex items-center mb-2">
                    <div className="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium -ml-7 mr-3">
                      {day.day}
                    </div>
                    <h4 className="font-medium text-gray-900">{day.title}</h4>
                  </div>
                  <p className="text-gray-600 mb-2">{day.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {day.activities.map((activity, actIndex) => (
                      <span key={actIndex} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm">
                        {activity}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Included/Excluded */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Included</h3>
              <div className="space-y-2">
                {packageDetails.included.map((item, index) => (
                  <div key={index} className="flex items-start">
                    <Check className="h-4 w-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What's Not Included</h3>
              <div className="space-y-2">
                {packageDetails.excluded.map((item, index) => (
                  <div key={index} className="flex items-start">
                    <X className="h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Booking Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing & Booking</h3>
              
              {/* Pricing Options */}
              <div className="space-y-3 mb-4">
                {Object.entries(packageDetails.pricing).map(([type, price]) => (
                  <button
                    key={type}
                    onClick={() => setSelectedPricing(type as any)}
                    className={`w-full p-3 rounded-lg border-2 transition-colors ${
                      selectedPricing === type
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="text-left">
                        <div className="font-medium text-gray-900 capitalize">{type}</div>
                        <div className="text-sm text-gray-500">
                          {type === 'solo' && 'Single traveler'}
                          {type === 'honeymoon' && '2 travelers (couple)'}
                          {type === 'family' && '3-4 travelers'}
                          {type === 'group' && '5+ travelers'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">{formatCurrency(price)}</div>
                        <div className="text-sm text-green-600">
                          {formatCurrency(price * packageDetails.commission / 100)} commission
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>

              {/* Selected Price Summary */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-600">Package Price</span>
                  <span className="font-medium text-gray-900">{formatCurrency(selectedPrice)}</span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-600">Your Commission ({packageDetails.commission}%)</span>
                  <span className="font-medium text-green-600">{formatCurrency(commissionAmount)}</span>
                </div>
                <div className="border-t border-gray-200 pt-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">Customer Pays</span>
                    <span className="font-bold text-gray-900">{formatCurrency(selectedPrice)}</span>
                  </div>
                </div>
              </div>

              {/* Booking Buttons */}
              <div className="space-y-3">
                <Link href={`/partner/bookings/create?package=${packageDetails.id}&pricing=${selectedPricing}`} className="block">
                  <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Book This Package
                  </button>
                </Link>
                <Link href="/partner/bookings/custom" className="block">
                  <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                    <Package className="h-4 w-4 mr-2" />
                    Request Custom Package
                  </button>
                </Link>
              </div>
            </div>

            {/* Package Details */}
            <div className="border-t border-gray-200 pt-6">
              <h4 className="font-medium text-gray-900 mb-3">Package Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Difficulty</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(packageDetails.difficulty)}`}>
                    {packageDetails.difficulty}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Min Age</span>
                  <span className="text-gray-900">{packageDetails.minAge} years</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Best Time</span>
                  <span className="text-gray-900 text-right">{packageDetails.bestTime}</span>
                </div>
                <div className="flex items-start justify-between">
                  <span className="text-gray-600">Physical Requirements</span>
                  <span className="text-gray-900 text-right">{packageDetails.physicalRequirements}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
