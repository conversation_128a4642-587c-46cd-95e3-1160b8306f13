"use client";

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const ImageSlider = ({ images, aspectRatio = 'landscape', autoSlide = true }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % images.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + images.length) % images.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  useEffect(() => {
    if (!autoSlide || isHovered) return;

    const interval = setInterval(nextSlide, 3000);
    return () => clearInterval(interval);
  }, [autoSlide, isHovered]);

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'portrait':
        return 'aspect-square md:aspect-[4/4]';
      case 'landscape':
        return 'aspect-video md:aspect-[14/9]';
      default:
        return 'aspect-video';
    }
  };

  return (
    <div 
      className={`relative overflow-hidden rounded-none shadow-lg w-full ${getAspectRatioClass()}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative w-full h-full">
        {images.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-800 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image.src}
              alt={image.alt}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>

      {/* Controls */}
      <div className="absolute bottom-4 left-0 right-0 flex items-center justify-between px-4">
        <button
          onClick={prevSlide}
          className="bg-white/70 hover:bg-white/90 text-gray-800 border-none rounded-full w-9 h-9 flex items-center justify-center transition-colors duration-300"
        >
          <ChevronLeft size={18} />
        </button>

        <div className="flex justify-center gap-2">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2.5 h-2.5 rounded-full transition-colors duration-300 ${
                index === currentSlide ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>

        <button
          onClick={nextSlide}
          className="bg-white/70 hover:bg-white/90 text-gray-800 border-none rounded-full w-9 h-9 flex items-center justify-center transition-colors duration-300"
        >
          <ChevronRight size={18} />
        </button>
      </div>
    </div>
  );
};

export default ImageSlider;