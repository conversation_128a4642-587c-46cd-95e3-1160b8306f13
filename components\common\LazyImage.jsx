"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import { useInView } from 'react-intersection-observer';
import SkeletonImage from '../skeleton/SkeletonImage';

/**
 * Lazy Loading Image Component with skeleton placeholder
 * Provides smooth loading experience with intersection observer
 */
const LazyImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  skeletonVariant = 'default',
  priority = false,
  unoptimized = false,
  onLoad,
  onError,
  style,
  ...props
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
    rootMargin: '50px 0px', // Start loading 50px before the image comes into view
  });

  const handleImageLoad = () => {
    setImageLoaded(true);
    if (onLoad) onLoad();
  };

  const handleImageError = () => {
    setImageError(true);
    if (onError) onError();
  };

  // Determine if image should use unoptimized loading
  const shouldUseUnoptimized = unoptimized || (src && src.includes('supabase.co'));

  return (
    <div ref={ref} className={`relative ${className}`} style={style}>
      {/* Show skeleton while loading or not in view */}
      {(!inView || (!imageLoaded && !imageError)) && (
        <SkeletonImage 
          variant={skeletonVariant}
          className="absolute inset-0 z-10"
          showIcon={!inView}
        />
      )}

      {/* Show error state */}
      {imageError && (
        <div className="absolute inset-0 bg-gray-200 flex items-center justify-center z-20">
          <div className="text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-sm">Image not available</p>
          </div>
        </div>
      )}

      {/* Actual image - only load when in view */}
      {inView && (
        <Image
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={`
            transition-opacity duration-300 ease-in-out
            ${imageLoaded ? 'opacity-100' : 'opacity-0'}
            ${className}
          `}
          style={style}
          onLoad={handleImageLoad}
          onError={handleImageError}
          priority={priority}
          unoptimized={shouldUseUnoptimized}
          {...props}
        />
      )}
    </div>
  );
};

export default LazyImage;
