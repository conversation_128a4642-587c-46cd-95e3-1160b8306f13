import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { sendVolunteeringNotificationEmail } from '@/lib/email-service';

// Interface for volunteering application data
interface VolunteeringData {
  name: string;
  email: string;
  arrival_date: string;
  departure_date: string;
  message?: string;
}

// GET - Fetch volunteering applications (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let query = supabase
      .from('sas_volunteering_applications')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching volunteering applications:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch volunteering applications' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET /api/bookings/volunteering:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new volunteering application
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['name', 'email', 'arrivalDate', 'departureDate'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate dates
    const arrivalDate = new Date(body.arrivalDate);
    const departureDate = new Date(body.departureDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (isNaN(arrivalDate.getTime()) || isNaN(departureDate.getTime())) {
      return NextResponse.json(
        { success: false, error: 'Invalid date format' },
        { status: 400 }
      );
    }

    if (arrivalDate < today) {
      return NextResponse.json(
        { success: false, error: 'Arrival date cannot be in the past' },
        { status: 400 }
      );
    }

    if (departureDate <= arrivalDate) {
      return NextResponse.json(
        { success: false, error: 'Departure date must be after arrival date' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const applicationData: VolunteeringData = {
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      arrival_date: body.arrivalDate,
      departure_date: body.departureDate,
      message: body.message?.trim() || ''
    };

    // Insert application into database
    const { data: newApplication, error } = await supabase
      .from('sas_volunteering_applications')
      .insert([applicationData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create volunteering application in database' },
        { status: 500 }
      );
    }

    // Send admin notification email
    try {
      await sendVolunteeringNotificationEmail(newApplication);
      console.log('Volunteering application notification email sent successfully');
      
      // Update email sent status
      await supabase
        .from('sas_volunteering_applications')
        .update({ 
          email_sent: true, 
          email_sent_at: new Date().toISOString() 
        })
        .eq('id', newApplication.id);
        
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the application if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        id: newApplication.id,
        name: newApplication.name,
        email: newApplication.email,
        arrivalDate: newApplication.arrival_date,
        departureDate: newApplication.departure_date,
        message: newApplication.message,
        status: newApplication.status,
        createdAt: newApplication.created_at
      },
      message: 'Volunteering application created successfully'
    });

  } catch (error) {
    console.error('Error creating volunteering application:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create volunteering application' },
      { status: 500 }
    );
  }
}

// PATCH - Update volunteering application status (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, adminNotes } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Application ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    
    if (status) {
      const validStatuses = ['pending', 'approved', 'rejected', 'completed'];
      if (!validStatuses.includes(status)) {
        return NextResponse.json(
          { success: false, error: 'Invalid status' },
          { status: 400 }
        );
      }
      updateData.status = status;
    }

    if (adminNotes !== undefined) {
      updateData.admin_notes = adminNotes;
    }

    const { data, error } = await supabase
      .from('sas_volunteering_applications')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating volunteering application:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update volunteering application' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data,
      message: 'Volunteering application updated successfully'
    });

  } catch (error) {
    console.error('Error in PATCH /api/bookings/volunteering:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
