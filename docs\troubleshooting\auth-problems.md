# Authentication Problems Troubleshooting

## Table of Contents
- [Common Issues](#common-issues)
- [Stuck on "Checking authentication..."](#stuck-on-checking-authentication)
- [Development Bypass Not Working](#development-bypass-not-working)
- [Production Authentication Issues](#production-authentication-issues)
- [Database Connection Problems](#database-connection-problems)
- [Session Management Issues](#session-management-issues)
- [Debug Tools](#debug-tools)

---

## Common Issues

### 1. Stuck on "Checking authentication..."

**Symptoms:**
- Admin page shows loading spinner indefinitely
- "Checking authentication..." message never disappears
- No error messages in console

**Causes:**
- Authentication hook stuck in loading state
- Database connection issues
- Missing user records
- Environment configuration problems

**Solutions:**

#### Quick Fix: Enable Development Bypass
```typescript
// In components/admin/common/AuthGuard.tsx
const isDevelopment = process.env.NODE_ENV === 'development';

if (isDevelopment) {
  console.warn('🚨 Development bypass active');
  return <>{children}</>;
}
```

#### Check Environment Variables
```bash
# Verify NODE_ENV
echo $NODE_ENV

# Should output: development
```

#### Restart Development Server
```bash
# Kill current server
Ctrl+C

# Restart
npm run dev
```

#### Clear Browser Data
```javascript
// In browser console
localStorage.clear();
sessionStorage.clear();
// Then refresh page
```

---

### 2. Development Bypass Not Working

**Symptoms:**
- Still seeing authentication screens in development
- Bypass code exists but not executing
- Environment detection failing

**Diagnostic Steps:**

#### Check NODE_ENV
```bash
# In terminal
echo $NODE_ENV

# Or in code
console.log('NODE_ENV:', process.env.NODE_ENV);
```

#### Verify Bypass Code
```typescript
// Add debug logging
export default function AuthGuard({ children }: AuthGuardProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  console.log('🔧 AuthGuard Debug:', {
    nodeEnv: process.env.NODE_ENV,
    isDevelopment,
    bypassActive: isDevelopment
  });
  
  if (isDevelopment) {
    console.warn('🚨 Authentication bypass active');
    return <>{children}</>;
  }
  
  // ... rest of component
}
```

#### Force Environment
```bash
# Explicitly set NODE_ENV
NODE_ENV=development npm run dev
```

---

### 3. Production Authentication Issues

**Symptoms:**
- Authentication bypass working in production (SECURITY RISK!)
- Users can't log in
- Session expires immediately

**Critical Security Check:**
```typescript
// Verify production environment
if (process.env.NODE_ENV === 'production' && isDevelopment) {
  throw new Error('🚨 SECURITY ALERT: Development bypass in production!');
}
```

**Solutions:**

#### Verify Production Environment
```bash
# Check production build
NODE_ENV=production npm run build
NODE_ENV=production npm start
```

#### Test Authentication Flow
```typescript
// Add production-specific logging
if (process.env.NODE_ENV === 'production') {
  console.log('Production auth flow active');
}
```

---

### 4. Database Connection Problems

**Symptoms:**
- "ECONNREFUSED" errors
- Database timeout errors
- User lookup failures

**Diagnostic Steps:**

#### Check Supabase Connection
```typescript
// Test Supabase connection
import { supabase } from '@/lib/supabase';

const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    console.log('Database connection:', error ? 'Failed' : 'Success');
  } catch (err) {
    console.error('Connection test failed:', err);
  }
};
```

#### Verify Environment Variables
```bash
# Check Supabase configuration
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY
```

#### Test Database Query
```sql
-- Test user table access
SELECT COUNT(*) FROM users WHERE role = 'admin';
```

---

### 5. Session Management Issues

**Symptoms:**
- Frequent logouts
- Session not persisting
- "Invalid session" errors

**Solutions:**

#### Check Session Storage
```javascript
// In browser console
console.log('Session data:', {
  localStorage: localStorage.getItem('supabase.auth.token'),
  sessionStorage: sessionStorage.getItem('supabase.auth.token')
});
```

#### Refresh Session
```typescript
// Force session refresh
const { data, error } = await supabase.auth.refreshSession();
console.log('Session refresh:', { data, error });
```

#### Clear and Restart Session
```typescript
// Complete session reset
await supabase.auth.signOut();
// Then log in again
```

---

## Debug Tools

### 1. Authentication Debug Component

Create a debug component for development:

```typescript
// components/debug/AuthDebug.tsx
export function AuthDebug() {
  const { user, loading, error } = useAuth();
  
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }
  
  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded text-xs">
      <h3>Auth Debug</h3>
      <p>Environment: {process.env.NODE_ENV}</p>
      <p>Loading: {loading ? 'Yes' : 'No'}</p>
      <p>User: {user ? user.email : 'None'}</p>
      <p>Error: {error || 'None'}</p>
    </div>
  );
}
```

### 2. Console Logging

Add comprehensive logging:

```typescript
// Enhanced useAuth hook with logging
export function useAuth() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    console.log('🔧 useAuth: Starting auth check');
    
    const checkAuth = async () => {
      try {
        console.log('🔧 useAuth: Checking session');
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          console.log('🔧 useAuth: Session found, fetching user');
          // Fetch user data
        } else {
          console.log('🔧 useAuth: No session found');
        }
      } catch (error) {
        console.error('🔧 useAuth: Error:', error);
      } finally {
        setLoading(false);
      }
    };
    
    checkAuth();
  }, []);
  
  return { user, loading, error, isAdmin };
}
```

### 3. Network Debugging

Monitor API calls:

```typescript
// Add to your API routes
console.log('API Call:', {
  method: req.method,
  url: req.url,
  headers: req.headers,
  timestamp: new Date().toISOString()
});
```

---

## Step-by-Step Troubleshooting

### When Authentication Completely Fails

1. **Check Environment**
   ```bash
   echo $NODE_ENV
   ```

2. **Enable Development Bypass**
   ```typescript
   // Temporarily add to AuthGuard
   return <>{children}</>; // Skip all auth
   ```

3. **Test Database Connection**
   ```bash
   # Test Supabase connection
   curl -H "apikey: YOUR_ANON_KEY" \
        "YOUR_SUPABASE_URL/rest/v1/users?select=count"
   ```

4. **Check Browser Console**
   - Look for JavaScript errors
   - Check network tab for failed requests
   - Verify environment variables

5. **Restart Everything**
   ```bash
   # Clear all caches
   rm -rf .next
   npm run dev
   ```

---

## Prevention Strategies

### 1. Environment Validation
```typescript
// Add to your app startup
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL');
}
```

### 2. Health Checks
```typescript
// Add health check endpoint
// pages/api/health.ts
export default async function handler(req, res) {
  try {
    // Test database connection
    const { error } = await supabase.from('users').select('count').limit(1);
    
    res.status(200).json({
      status: 'healthy',
      database: error ? 'error' : 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ status: 'error', error: error.message });
  }
}
```

### 3. Graceful Degradation
```typescript
// Fallback for auth failures
if (authError && process.env.NODE_ENV === 'development') {
  console.warn('Auth failed, enabling development bypass');
  return <DevelopmentBypass />;
}
```

---

## Related Documentation

- [**Bypass Authentication for Development**](../authentication/bypass-auth-development.md) ⭐
- [Authentication System Overview](../authentication/auth-overview.md)
- [Environment Configuration](../setup/environment-config.md)
- [Common Issues](./common-issues.md)
- [API Debugging](./api-debugging.md)
