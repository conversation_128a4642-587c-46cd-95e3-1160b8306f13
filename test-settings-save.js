// Test newsletter settings save functionality
const testSettingsSave = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Newsletter Settings Save...\n');
  
  // Test saving SMTP settings
  console.log('1. Saving SMTP settings...');
  try {
    const saveResponse = await fetch(`${baseUrl}/api/newsletter/settings`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        smtp_email: '<EMAIL>',
        smtp_password: 'ABcd16$$',
        smtp_host: 'smtp.hostinger.com',
        smtp_port: 465,
        smtp_secure: true
      })
    });
    
    const saveData = await saveResponse.json();
    console.log('Save Response Status:', saveResponse.status);
    console.log('Save Response:', saveData);
    
    if (saveData.success) {
      console.log('✅ Settings saved successfully!');
    } else {
      console.log('❌ Settings save failed:', saveData.error);
    }
    
  } catch (error) {
    console.error('❌ Settings Save Error:', error.message);
  }
  
  // Test retrieving settings
  console.log('\n2. Retrieving saved settings...');
  try {
    const getResponse = await fetch(`${baseUrl}/api/newsletter/settings`);
    const getData = await getResponse.json();
    
    console.log('Retrieved Settings:', getData);
    
    if (getData.success) {
      console.log('✅ Settings retrieved successfully!');
      console.log('SMTP Email:', getData.data.smtp_email);
      console.log('SMTP Host:', getData.data.smtp_host);
      console.log('SMTP Port:', getData.data.smtp_port);
    }
    
  } catch (error) {
    console.error('❌ Settings Retrieve Error:', error.message);
  }
  
  console.log('\n🎉 Settings test completed!');
};

// Run the test
testSettingsSave().catch(console.error);
