/* eslint-disable react/no-unescaped-entities */
import React, { useState } from 'react';
import { Send, User, Phone, Mail, MapPin, Users, MessageSquare } from 'lucide-react';

const TravelAgentForm = ({ onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    countryCode: '+1',
    whatsappNumber: '',
    email: '',
    country: '',
    touristsPerYear: '',
    message: ''
  });

  const countryCodes = [
    { code: '+1', country: 'US', flag: '🇺🇸' },
    { code: '+1', country: 'CA', flag: '🇨🇦' },
    { code: '+44', country: 'GB', flag: '🇬🇧' },
    { code: '+33', country: 'FR', flag: '🇫🇷' },
    { code: '+49', country: 'DE', flag: '🇩🇪' },
    { code: '+39', country: 'IT', flag: '🇮🇹' },
    { code: '+34', country: 'ES', flag: '🇪🇸' },
    { code: '+81', country: 'JP', flag: '🇯🇵' },
    { code: '+86', country: 'CN', flag: '🇨🇳' },
    { code: '+91', country: 'IN', flag: '🇮🇳' },
    { code: '+61', country: 'AU', flag: '🇦🇺' },
    { code: '+55', country: 'BR', flag: '🇧🇷' },
    { code: '+52', country: 'MX', flag: '🇲🇽' },
    { code: '+27', country: 'ZA', flag: '🇿🇦' },
    { code: '+20', country: 'EG', flag: '🇪🇬' },
    { code: '+971', country: 'AE', flag: '🇦🇪' },
    { code: '+65', country: 'SG', flag: '🇸🇬' },
    { code: '+60', country: 'MY', flag: '🇲🇾' },
    { code: '+66', country: 'TH', flag: '🇹🇭' },
    { code: '+250', country: 'RW', flag: '🇷🇼' }
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (event) => {
    event.preventDefault();
    if (!formData.name || !formData.whatsappNumber || !formData.email || !formData.country || !formData.touristsPerYear) {
      alert('Please fill in all required fields.');
      return;
    }
    console.log('Form submitted:', formData);
    alert('Thank you for your submission! We will contact you soon.');
    onClose?.();
  };

  return (
    <div className="p-4 sm:p-6 md:p-8 max-w-lg mx-auto w-full">
      <div className="text-center mb-6 md:mb-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4" style={{ color: '#163201' }}>
          Are you a Travel Agent?
        </h1>
        <p className="text-base sm:text-lg" style={{ color: 'rgb(7, 7, 7)' }}>
          Join our network of professional travel agents and expand your business opportunities
        </p>
      </div>

      <div className="rounded-lg shadow-lg p-4 sm:p-6 md:p-8" style={{ backgroundColor: '#fffbde' }}>
        <div className="space-y-5 md:space-y-6">
          {/* Name Field */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <User className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              Full Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
              style={{ 
                backgroundColor: '#ffffff',
                borderColor: '#317100',
                color: 'rgb(7, 7, 7)'
              }}
              placeholder="Enter your full name"
            />
          </div>

          {/* WhatsApp Number with Country Code */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <Phone className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              WhatsApp Number *
            </label>
            <div className="flex flex-col sm:flex-row gap-2">
              <select
                name="countryCode"
                value={formData.countryCode}
                onChange={handleInputChange}
                className="px-3 py-2 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all min-w-[100px] sm:min-w-[120px] text-sm sm:text-base"
                style={{ 
                  backgroundColor: '#ffffff',
                  borderColor: '#317100',
                  color: 'rgb(7, 7, 7)'
                }}
              >
                {countryCodes.map((item, index) => (
                  <option key={index} value={item.code}>
                    {item.flag} {item.code}
                  </option>
                ))}
              </select>
              <input
                type="tel"
                name="whatsappNumber"
                value={formData.whatsappNumber}
                onChange={handleInputChange}
                required
                className="flex-1 px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
                style={{ 
                  backgroundColor: '#ffffff',
                  borderColor: '#317100',
                  color: 'rgb(7, 7, 7)'
                }}
                placeholder="Your WhatsApp number"
              />
            </div>
          </div>

          {/* Email Field */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <Mail className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
              style={{ 
                backgroundColor: '#ffffff',
                borderColor: '#317100',
                color: 'rgb(7, 7, 7)'
              }}
              placeholder="<EMAIL>"
            />
          </div>

          {/* Country Field */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <MapPin className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              Country *
            </label>
            <input
              type="text"
              name="country"
              value={formData.country}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
              style={{ 
                backgroundColor: '#ffffff',
                borderColor: '#317100',
                color: 'rgb(7, 7, 7)'
              }}
              placeholder="Your country of operation"
            />
          </div>

          {/* Number of Tourists Per Year */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <Users className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              Number of Tourists Per Year *
            </label>
            <select
              name="touristsPerYear"
              value={formData.touristsPerYear}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all text-sm sm:text-base"
              style={{ 
                backgroundColor: '#ffffff',
                borderColor: '#317100',
                color: 'rgb(7, 7, 7)'
              }}
            >
              <option value="">Select range</option>
              <option value="1-50">1-50 tourists</option>
              <option value="51-100">51-100 tourists</option>
              <option value="101-500">101-500 tourists</option>
              <option value="501-1000">501-1000 tourists</option>
              <option value="1000+">1000+ tourists</option>
            </select>
          </div>

          {/* Message Field */}
          <div>
            <label className="flex items-center text-xs sm:text-sm font-medium mb-1 sm:mb-2" style={{ color: 'rgb(7, 7, 7)' }}>
              <MessageSquare className="w-4 h-4 mr-2" style={{ color: '#d35400' }} />
              Additional Information
            </label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 sm:px-4 sm:py-3 rounded-lg border-2 focus:outline-none focus:ring-2 transition-all resize-vertical text-sm sm:text-base"
              style={{ 
                backgroundColor: '#ffffff',
                borderColor: '#317100',
                color: 'rgb(7, 7, 7)'
              }}
              placeholder="Tell us about your travel agency, specializations, or any additional information you'd like to share..."
            />
          </div>

          {/* Submit Button */}
          <button
            type="button"
            onClick={handleSubmit}
            className="w-full py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-semibold text-white transition-all duration-300 hover:shadow-lg flex items-center justify-center gap-2 transform hover:scale-105 text-base sm:text-lg"
            style={{ backgroundColor: '#163201' }}
          >
            <Send className="w-5 h-5" />
            Submit Application
          </button>
        </div>
      </div>

      <div className="text-center mt-4 md:mt-6">
        <p className="text-xs sm:text-sm" style={{ color: 'rgb(7, 7, 7)' }}>
          * Required fields. We'll review your application and get back to you within 24-48 hours.
        </p>
      </div>
    </div>
  );
};

export default TravelAgentForm;