/**
 * Blog Data Migration Script
 * 
 * This script migrates existing blog data from blogData.js to Supabase database.
 * Run this script once to populate the database with existing blog posts.
 * 
 * Usage: npx ts-node scripts/migrate-blog-data.ts
 */

import { createClient } from '@supabase/supabase-js';
import { blogContents } from '../components/data/blogData';

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface ContentBlock {
  type: string;
  content: any;
  id?: string;
}

interface BlogPost {
  title: string;
  heroImage: string;
  seo?: {
    title: string;
    description: string;
    keywords: string[];
    image: string;
    author: string;
    publishedTime: string;
    modifiedTime: string;
    category: string;
    tags: string[];
  };
  schema?: any;
  sections: ContentBlock[];
}

async function migrateBlogData() {
  console.log('🚀 Starting blog data migration...');

  try {
    // Get existing blog posts to avoid duplicates
    const { data: existingPosts } = await supabase
      .from('sas_blog_posts')
      .select('slug');

    const existingSlugs = new Set(existingPosts?.map(post => post.slug) || []);

    let migratedCount = 0;
    let skippedCount = 0;

    for (const [slug, blogData] of Object.entries(blogContents)) {
      if (existingSlugs.has(slug)) {
        console.log(`⏭️  Skipping ${slug} - already exists`);
        skippedCount++;
        continue;
      }

      console.log(`📝 Migrating: ${slug}`);

      const post = blogData as BlogPost;

      // Prepare blog post data
      const blogPostData = {
        title: post.title,
        slug: slug,
        description: post.seo?.description || `Discover ${post.title} with Swift Africa Safaris`,
        hero_image_url: post.heroImage,
        hero_image_alt: `${post.title} - Swift Africa Safaris`,
        author: post.seo?.author || 'Swift Africa Safaris',
        category: post.seo?.category || 'Travel Guide',
        tags: post.seo?.tags || [],
        status: 'published',
        published_at: post.seo?.publishedTime || new Date().toISOString(),
        seo_title: post.seo?.title || post.title,
        seo_description: post.seo?.description || `Discover ${post.title} with Swift Africa Safaris`,
        seo_keywords: post.seo?.keywords || [],
        og_title: post.seo?.title || post.title,
        og_description: post.seo?.description || `Discover ${post.title} with Swift Africa Safaris`,
        og_image_url: post.seo?.image || post.heroImage,
        canonical_url: `https://swiftafricasafaris.com/blog/${slug}`,
        robots_index: 'index',
        robots_follow: 'follow',
        schema_data: post.schema || null
      };

      // Insert blog post
      const { data: newPost, error: postError } = await supabase
        .from('sas_blog_posts')
        .insert(blogPostData)
        .select('id')
        .single();

      if (postError) {
        console.error(`❌ Error inserting post ${slug}:`, postError);
        continue;
      }

      // Migrate content blocks
      if (post.sections && post.sections.length > 0) {
        const contentBlocks = post.sections.map((section, index) => {
          let blockContent: any = {};

          switch (section.type) {
            case 'h2':
            case 'h3':
            case 'h4':
            case 'h5':
            case 'h6':
              blockContent = {
                content: section.content,
                id: section.id || null
              };
              break;

            case 'paragraph':
              blockContent = {
                content: section.content
              };
              break;

            case 'image':
              blockContent = {
                src: (section.content as any).src || section.content,
                alt: (section.content as any).alt || `${post.title} image`,
                caption: (section.content as any).caption || '',
                width: (section.content as any).width || 'full'
              };
              break;

            case 'video':
              blockContent = {
                src: (section.content as any).src || section.content,
                poster: (section.content as any).poster || '',
                caption: (section.content as any).caption || '',
                width: (section.content as any).width || 'lg'
              };
              break;

            case 'listing':
              blockContent = {
                items: (section.content as any).items || [],
                listType: (section.content as any).listType || 'unordered'
              };
              break;

            case 'quote':
              blockContent = {
                content: (section.content as any).content || section.content,
                author: (section.content as any).author || '',
                source: (section.content as any).source || ''
              };
              break;

            case 'divider':
              blockContent = {
                style: 'line'
              };
              break;

            default:
              blockContent = {
                content: section.content
              };
          }

          return {
            blog_post_id: newPost.id,
            block_type: section.type,
            content: blockContent,
            sort_order: index
          };
        });

        const { error: contentError } = await supabase
          .from('sas_blog_content_blocks')
          .insert(contentBlocks);

        if (contentError) {
          console.error(`❌ Error inserting content blocks for ${slug}:`, contentError);
        } else {
          console.log(`✅ Migrated ${contentBlocks.length} content blocks for ${slug}`);
        }
      }

      migratedCount++;
      console.log(`✅ Successfully migrated: ${slug}`);
    }

    console.log('\n🎉 Migration completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Migrated: ${migratedCount} posts`);
    console.log(`   - Skipped: ${skippedCount} posts (already exist)`);
    console.log(`   - Total processed: ${migratedCount + skippedCount} posts`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  migrateBlogData()
    .then(() => {
      console.log('✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateBlogData };
