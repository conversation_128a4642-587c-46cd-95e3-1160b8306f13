import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY');
}

// Create a single instance to avoid multiple GoTrueClient instances
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
});

// Database Types
export interface Booking {
  id: string;
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  package_name: string;
  package_id: string;
  amount: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  travel_date: string;
  number_of_people: number;
  special_requests?: string;
  created_at: string;
  updated_at: string;
}

export interface Package {
  id: string;
  title: string;
  price: number;
  duration: string;
  location: string;
  image_url: string;
  category?: string;
  max_people?: number;
  gallery?: string[];
  itinerary?: { day: number; title: string; description: string }[];
  included?: string[];
  not_included?: string[];
  accommodation?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'travel_agent' | 'partner';
  is_active: boolean;
  company?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface Review {
  id: string;
  customer_name: string;
  customer_email: string;
  rating: number;
  title: string;
  comment: string;
  package_id?: string;
  is_approved: boolean;
  created_at: string;
}


export interface Donation {
  id: string;
  donor_name: string;
  donor_email: string;
  amount: number;
  message?: string;
  is_anonymous: boolean;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

export interface Subscriber {
  id: string;
  email: string;
  is_active: boolean;
  created_at: string;
}

export interface DashboardStats {
  totalBookings: number;
  totalPackages: number;
  totalRevenue: number;
  totalReviews: number;
  totalSubscribers: number;
  totalUsers: number;
  totalDonations: number;
} 