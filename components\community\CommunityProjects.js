import React from 'react';
import ProjectCard from './ProjectCard';
import { communityProjects } from '../data/communityProjectData';

const CommunityProjects = () => {
  return (
    <section className="py-16 bg-[var(--secondary-background)] relative overflow-hidden">
      <div className="container mx-auto px-4">
        <p className="text-[#d35400] text-sm uppercase tracking-wider text-center mb-4">Find Your Purpose</p>
        <h2 className="text-4xl font-bold text-center mb-6">Volunteering Opportunities</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {communityProjects.map((project, index) => (
            <ProjectCard key={index} {...project} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default CommunityProjects;
