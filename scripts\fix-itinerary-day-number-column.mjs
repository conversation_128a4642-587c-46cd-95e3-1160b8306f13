import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixDayNumberColumn() {
  console.log('🔧 Fixing day_number column in sas_mini_package_itinerary...\n');
  
  try {
    // First, let's check what data exists
    const { data: existingData, error: checkError } = await supabase
      .from('sas_mini_package_itinerary')
      .select('*');
    
    if (checkError) {
      console.log(`❌ Error checking existing data: ${checkError.message}`);
      return false;
    }
    
    console.log(`📊 Found ${existingData?.length || 0} existing itinerary items`);
    
    if (existingData && existingData.length > 0) {
      console.log('📋 Existing data preview:');
      existingData.slice(0, 3).forEach((item, index) => {
        console.log(`   ${index + 1}. Hour: ${item.hour_number || 'N/A'}, Day: ${item.day_number || 'N/A'}, Title: ${item.title}`);
      });
    }
    
    // Execute the SQL to remove the day_number column
    console.log('\n🗑️  Removing day_number column...');
    
    // We'll use a direct SQL approach since the column needs to be dropped
    const sqlCommands = [
      'ALTER TABLE public.sas_mini_package_itinerary DROP COLUMN IF EXISTS day_number;'
    ];
    
    for (const sql of sqlCommands) {
      console.log(`Executing: ${sql}`);
      
      try {
        // Try using a simple query approach
        const { error } = await supabase.rpc('exec_sql', { sql });
        
        if (error) {
          console.log(`⚠️  RPC approach failed: ${error.message}`);
          console.log('This is expected if the exec_sql function is not available.');
          console.log('Please run this SQL manually in your Supabase SQL editor:');
          console.log(`\n${sql}\n`);
        } else {
          console.log('✅ SQL executed successfully via RPC');
        }
      } catch (err) {
        console.log(`⚠️  Error executing SQL: ${err.message}`);
        console.log('Please run this SQL manually in your Supabase SQL editor:');
        console.log(`\n${sql}\n`);
      }
    }
    
    // Test if the fix worked by trying to insert a record with only hour_number
    console.log('\n🧪 Testing the fix...');
    
    const testRecord = {
      mini_package_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
      hour_number: 1,
      title: 'Test Hour Record',
      description: 'Test description for hour-based record',
      sort_order: 0
    };
    
    const { error: testError } = await supabase
      .from('sas_mini_package_itinerary')
      .insert(testRecord);
    
    if (testError) {
      console.log(`❌ Test insert still failed: ${testError.message}`);
      
      if (testError.message.includes('day_number')) {
        console.log('\n⚠️  The day_number column still exists and has constraints.');
        console.log('Please manually run this SQL in your Supabase SQL editor:');
        console.log('\nALTER TABLE public.sas_mini_package_itinerary DROP COLUMN IF EXISTS day_number;\n');
        return false;
      } else if (testError.message.includes('foreign key')) {
        console.log('✅ The day_number column was removed (foreign key error is expected with dummy UUID)');
        return true;
      } else {
        console.log('❌ Unexpected error during test');
        return false;
      }
    } else {
      console.log('✅ Test insert succeeded! The day_number column has been removed.');
      
      // Clean up test record
      await supabase
        .from('sas_mini_package_itinerary')
        .delete()
        .eq('mini_package_id', '00000000-0000-0000-0000-000000000000');
      
      console.log('🧹 Cleaned up test record');
      return true;
    }
    
  } catch (err) {
    console.log(`❌ Unexpected error: ${err.message}`);
    return false;
  }
}

async function verifyFix() {
  console.log('\n✅ Verifying the fix...\n');
  
  try {
    // Try to create a mini package itinerary with only hour_number
    const { data: testPackage, error: packageError } = await supabase
      .from('sas_mini_packages')
      .select('id')
      .eq('slug', 'the-last-login')
      .single();
    
    if (packageError || !testPackage) {
      console.log('⚠️  Could not find test package for verification');
      return false;
    }
    
    const testItinerary = {
      mini_package_id: testPackage.id,
      hour_number: 99, // Use a unique hour number for testing
      title: 'Verification Test Hour',
      description: 'This is a test to verify hour_number works without day_number',
      sort_order: 999
    };
    
    const { data: insertedData, error: insertError } = await supabase
      .from('sas_mini_package_itinerary')
      .insert(testItinerary)
      .select()
      .single();
    
    if (insertError) {
      console.log(`❌ Verification failed: ${insertError.message}`);
      return false;
    }
    
    console.log('✅ Successfully inserted itinerary with only hour_number');
    console.log(`📋 Test record: Hour ${insertedData.hour_number} - ${insertedData.title}`);
    
    // Clean up verification record
    await supabase
      .from('sas_mini_package_itinerary')
      .delete()
      .eq('id', insertedData.id);
    
    console.log('🧹 Cleaned up verification record');
    
    return true;
    
  } catch (err) {
    console.log(`❌ Verification error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Fixing Mini Package Itinerary day_number Column Issue\n');
  
  const fixed = await fixDayNumberColumn();
  
  if (fixed) {
    const verified = await verifyFix();
    
    if (verified) {
      console.log('\n🎉 SUCCESS!');
      console.log('✅ The day_number column has been removed');
      console.log('✅ Mini package itineraries now work with hour_number only');
      console.log('✅ Upload API should now work correctly');
      console.log('\n🔗 You can now test the upload functionality!');
    } else {
      console.log('\n⚠️  Fix applied but verification failed');
      console.log('Please test the upload functionality manually');
    }
  } else {
    console.log('\n❌ MANUAL ACTION REQUIRED');
    console.log('Please run this SQL command in your Supabase SQL editor:');
    console.log('\nALTER TABLE public.sas_mini_package_itinerary DROP COLUMN IF EXISTS day_number;\n');
    console.log('Then test the upload functionality again.');
  }
}

main();
