import { useState, useEffect } from 'react';

export const useRotatingProducts = (products, interval = 5000) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const discountedProducts = products.filter(p => p.discount > 0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % discountedProducts.length);
    }, interval);

    return () => clearInterval(timer);
  }, [discountedProducts.length, interval]);

  return {
    currentProduct: discountedProducts[currentIndex],
    totalProducts: discountedProducts.length
  };
};
