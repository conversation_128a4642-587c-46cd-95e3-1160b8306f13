# 🚀 Development Setup Instructions

## 📋 Prerequisites

Before starting, ensure you have:
- **Node.js** 18.x or higher
- **npm** 8.x or higher
- **Git** installed and configured

## 🛠️ Initial Setup (First Time)

### 1. Clone the Repository
```bash
git clone https://github.com/intoregaby/sas-website-app.git
cd sas-website-app
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Start Development Server
```bash
npm run dev
```

### 4. Open in Browser
Navigate to: http://localhost:3000

## 🔄 Daily Development Workflow

### When Pulling Changes
```bash
# Pull latest changes
git pull origin main

# Install any new dependencies
npm install

# Clear Next.js cache (if styles/components not updating)
rm -rf .next

# Start development server
npm run dev
```

## 🎨 Tailwind CSS Specific Setup

### Verify Tailwind is Working
1. After setup, check that these files exist:
   - `tailwind.config.js`
   - `postcss.config.mjs`
   - `app/globals.css` (with @tailwind directives)

2. Test with this component in any page:
```jsx
<div className="bg-blue-500 text-white p-4 rounded">
  Tailwind Test - Should have blue background
</div>
```

### If Tailwind Styles Don't Work
```bash
# Complete reset
rm -rf node_modules package-lock.json .next
npm install
npm run dev
```

## 📁 Project Structure

```
sas-website-app/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin panel pages
│   ├── partner/           # Partner portal pages
│   ├── globals.css        # Global styles + Tailwind
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── admin/            # Admin-specific components
│   ├── partner/          # Partner-specific components
│   └── common/           # Shared components
├── public/               # Static assets
│   └── font/            # Custom fonts
├── tailwind.config.js    # Tailwind configuration
├── postcss.config.mjs    # PostCSS configuration
└── package.json          # Dependencies
```

## 🔧 Available Scripts

```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## 🎯 Key Features to Test

### Admin Panel
- Navigate to `/admin`
- Test dashboard, sidebar navigation
- Check package/blog management

### Partner Portal
- Navigate to `/partner`
- Test booking system
- Check notification features

### Styling
- Verify custom colors work
- Check Jost font loading
- Test responsive design

## 🐛 Common Issues & Quick Fixes

### Issue: "Module not found" errors
```bash
rm -rf node_modules package-lock.json
npm install
```

### Issue: Styles not updating
```bash
rm -rf .next
npm run dev
```

### Issue: Font not loading
- Check `/public/font/` directory exists
- Verify font files are present
- Clear browser cache (Ctrl+F5)

### Issue: TypeScript errors
```bash
npm run lint
# Fix any reported issues
```

## 🌐 Environment Variables

If the project uses environment variables, create `.env.local`:
```bash
# Copy from .env.example if it exists
cp .env.example .env.local

# Or create manually with required variables
touch .env.local
```

## 📱 Testing on Different Devices

### Desktop Testing
- Chrome, Firefox, Safari, Edge
- Different screen sizes (1920x1080, 1366x768)

### Mobile Testing
- Chrome DevTools mobile simulation
- Actual mobile devices if available

## 🚀 Deployment Preparation

### Before Pushing Changes
```bash
# Test production build
npm run build
npm start

# Check for any build errors
# Test critical functionality
```

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "Description of changes"

# Push to remote
git push origin feature/your-feature-name

# Create pull request on GitHub
```

## 📞 Getting Help

### Check These First
1. **Console errors** in browser DevTools
2. **Terminal errors** in development server
3. **Network tab** for failed requests
4. **TAILWIND_SETUP_GUIDE.md** for styling issues

### Debug Information to Collect
- Node.js version: `node --version`
- npm version: `npm --version`
- Operating system
- Browser and version
- Error messages (full text)
- Steps to reproduce the issue

---

**💡 Remember:** Always run `npm install` after pulling changes that modify dependencies!
