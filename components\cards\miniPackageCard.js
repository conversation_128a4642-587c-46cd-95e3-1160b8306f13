import Link from 'next/link';
import { MapPin } from 'lucide-react';
import { useState } from 'react';
import LazyImage from '../common/LazyImage';

export default function MiniPackageCard({ packageData }) {
  const [imageError, setImageError] = useState(false);

  // Format price to display
  const formatPrice = (price) => {
    if (!price || price === Infinity) return 'Contact Us';
    return `$${price}`;
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  // Fallback image URL
  const fallbackImage = '/images/default-package.jpg';
  const imageUrl = imageError ? fallbackImage : (packageData.image || fallbackImage);

  return (
    <div className="bg-[var(--secondary-background)] shadow-lg overflow-hidden max-w-sm mx-auto">
      {/* Header Image Area */}
      <div className="relative h-48 bg-gray-200">
        <LazyImage
          src={imageUrl}
          alt={packageData.alt || packageData.title || 'Mini package destination'}
          className="w-full h-full object-cover"
          width={400}
          height={192}
          style={{objectFit: 'cover'}}
          onError={handleImageError}
          unoptimized={imageUrl.includes('supabase.co')}
          priority={false}
          skeletonVariant="card"
        />

        {/* Price Badge */}
        <div className="absolute top-4 right-4 bg-white rounded-lg px-3 py-2 shadow-md">
          <div className="text-sm font-medium text-gray-900">From {formatPrice(packageData.price)}</div>
        </div>

        {/* Location Overlay at Bottom */}
        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white px-4 py-3">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-orange-500" />
            <span className="font-medium">{packageData.location}</span>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-[var(--secondary-background)] p-6">
        {/* Title */}
        <h2 className="text-xl font-bold text-gray-900 mb-3">
          {packageData.title}
        </h2>

        {/* Duration */}
        <div className="text-gray-700 text-lg mb-6">
          {packageData.duration}
        </div>

        {/* View Details Button */}
        <div className="flex justify-center">
          <Link
            href={`/mini-package/${packageData.slug}`}
            className="btn text-white font-semibold py-3 px-8 rounded-lg transition-colors duration-200 inline-block text-center"
          >
            View Details
          </Link>
        </div>
      </div>
    </div>
  );
}