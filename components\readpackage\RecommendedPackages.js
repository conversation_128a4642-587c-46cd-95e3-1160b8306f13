'use client';

import { useState, useEffect } from 'react';
import TourCard from '@/components/cards/packageCard';

export default function RecommendedPackages({ currentPackageSlug, currentLocation, currentCategory }) {
  const [packages, setPackages] = useState([]);
  const [recommendedPackages, setRecommendedPackages] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch packages from API
  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/packages?limit=20'); // Get more packages for better recommendations
        const result = await response.json();

        if (result.success) {
          setPackages(result.data);
        } else {
          console.error('Failed to fetch packages:', result.error);
        }
      } catch (error) {
        console.error('Error fetching packages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  // Function to get recommended packages based on current package
  useEffect(() => {
    if (packages.length === 0) return;

    // Filter out the current package and get similar packages
    let recommended = packages.filter(pkg => pkg.slug !== currentPackageSlug);

    // Prioritize packages from the same location or category
    const sameLocation = recommended.filter(pkg => pkg.location === currentLocation);
    const sameCategory = recommended.filter(pkg => pkg.category === currentCategory);
    const others = recommended.filter(pkg =>
      pkg.location !== currentLocation && pkg.category !== currentCategory
    );

    // Combine and limit to 4 packages
    const combined = [...sameLocation, ...sameCategory, ...others];

    // Remove duplicates and limit to 4
    const uniquePackages = combined.filter((pkg, index, self) =>
      index === self.findIndex(p => p.slug === pkg.slug)
    );

    setRecommendedPackages(uniquePackages.slice(0, 4));
  }, [packages, currentPackageSlug, currentLocation, currentCategory]);

  if (loading) {
    return (
      <section className="py-16 bg-[var(--primary-background)]">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-[var(--text)] mb-4">
              Recommended Tour Packages
            </h2>
          </div>
          <div className="flex justify-center items-center min-h-[200px]">
            <div className="text-lg text-gray-600">Loading recommendations...</div>
          </div>
        </div>
      </section>
    );
  }

  if (recommendedPackages.length === 0) {
    return null;
  }

  return (
    <section className="py-16 bg-[var(--primary-background)]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-[var(--text)] mb-4">
            Recommended Tour Packages
          </h2>
          <p className="text-[var(--text-secondary)] max-w-2xl mx-auto">
            Discover more amazing destinations and experiences with our carefully curated tour packages
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {recommendedPackages.map((tour) => (
            <TourCard key={tour.slug || tour.id} tour={tour} />
          ))}
        </div>
      </div>
    </section>
  );
}
