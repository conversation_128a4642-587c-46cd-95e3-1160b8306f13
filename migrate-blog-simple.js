const { createClient } = require('@supabase/supabase-js');
const { blogContents } = require('./components/data/blogData');

// Load environment variables
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateBlogData() {
  console.log('🚀 Starting blog data migration...');
  console.log('Available blog posts:', Object.keys(blogContents));

  try {
    let migratedCount = 0;

    for (const [slug, blogData] of Object.entries(blogContents)) {
      console.log(`📝 Migrating: ${slug}`);

      // Prepare blog post data
      const blogPostData = {
        title: blogData.title,
        slug: slug,
        description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        hero_image_url: blogData.heroImage,
        hero_image_alt: `${blogData.title} - Swift Africa Safaris`,
        category: blogData.seo?.category || 'Travel Guide',
        tags: blogData.seo?.tags || [],
        status: 'published',
        published_at: blogData.seo?.publishedTime || new Date().toISOString(),
        seo_title: blogData.seo?.title || blogData.title,
        seo_description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        seo_keywords: blogData.seo?.keywords || [],
        og_title: blogData.seo?.title || blogData.title,
        og_description: blogData.seo?.description || `Discover ${blogData.title} with Swift Africa Safaris`,
        og_image_url: blogData.seo?.image || blogData.heroImage,
        canonical_url: `https://swiftafricasafaris.com/blog/${slug}`,
        robots_index: 'index',
        robots_follow: 'follow',
        schema_data: blogData.schema || null
      };

      // Insert blog post (upsert to avoid duplicates)
      const { data: newPost, error: postError } = await supabase
        .from('sas_blog_posts')
        .upsert(blogPostData, { onConflict: 'slug' })
        .select('id')
        .single();

      if (postError) {
        console.error(`❌ Error inserting post ${slug}:`, postError);
        continue;
      }

      console.log(`✅ Migrated: ${slug}`);
      migratedCount++;

      // Migrate content blocks if they exist
      if (blogData.sections && blogData.sections.length > 0) {
        // Delete existing content blocks for this post
        await supabase
          .from('sas_blog_content_blocks')
          .delete()
          .eq('blog_post_id', newPost.id);

        const contentBlocks = blogData.sections.map((section, index) => ({
          blog_post_id: newPost.id,
          block_type: section.type,
          content: section.content || section,
          sort_order: index
        }));

        const { error: blocksError } = await supabase
          .from('sas_blog_content_blocks')
          .insert(contentBlocks);

        if (blocksError) {
          console.error(`❌ Error inserting content blocks for ${slug}:`, blocksError);
        } else {
          console.log(`✅ Content blocks migrated for: ${slug}`);
        }
      }
    }

    console.log('\n🎉 Migration completed!');
    console.log(`📊 Summary: Migrated ${migratedCount} posts`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run migration
migrateBlogData()
  .then(() => {
    console.log('✅ Migration script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  });
