import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

// Encryption key for SMTP passwords (in production, use environment variable)
const ENCRYPTION_KEY = process.env.NEWSLETTER_ENCRYPTION_KEY || 'swift-africa-safaris-newsletter-key-2024';

// Encrypt password - simplified for development
function encryptPassword(password: string): string {
  // For development, we'll store passwords in plain text
  // In production, implement proper encryption
  return password;
}

// Decrypt password - simplified for development
function decryptPassword(encryptedPassword: string): string {
  try {
    // For development, we'll store passwords in plain text
    // In production, implement proper encryption
    return encryptedPassword;
  } catch (error) {
    console.error('Password decryption error:', error);
    return '';
  }
}

// GET - Fetch newsletter settings (admin only)
export async function GET(request: NextRequest) {
  try {
    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { data: settings, error } = await supabaseAdmin
      .from('sas_newsletter_settings')
      .select('id, smtp_email, smtp_host, smtp_port, smtp_secure, created_at, updated_at')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error('Database fetch error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch newsletter settings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: settings
    });

  } catch (error) {
    console.error('Newsletter settings GET error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create or update newsletter settings (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { smtp_email, smtp_password, smtp_host, smtp_port, smtp_secure } = body;

    // Validate required fields
    if (!smtp_email || !smtp_password) {
      return NextResponse.json(
        { success: false, error: 'SMTP email and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(smtp_email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid SMTP email format' },
        { status: 400 }
      );
    }

    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Encrypt password
    const encryptedPassword = encryptPassword(smtp_password);

    // Check if settings exist
    const { data: existingSettings } = await supabaseAdmin
      .from('sas_newsletter_settings')
      .select('id')
      .limit(1)
      .single();

    const settingsData = {
      smtp_email,
      smtp_password: encryptedPassword,
      smtp_host: smtp_host || 'smtp.hostinger.com',
      smtp_port: smtp_port || 465,
      smtp_secure: smtp_secure !== undefined ? smtp_secure : true,
      updated_at: new Date().toISOString()
    };

    let result;
    if (existingSettings) {
      // Update existing settings
      result = await supabaseAdmin
        .from('sas_newsletter_settings')
        .update(settingsData)
        .eq('id', existingSettings.id)
        .select()
        .single();
    } else {
      // Create new settings
      result = await supabaseAdmin
        .from('sas_newsletter_settings')
        .insert(settingsData)
        .select()
        .single();
    }

    if (result.error) {
      console.error('Database operation error:', result.error);
      return NextResponse.json(
        { success: false, error: 'Failed to save newsletter settings' },
        { status: 500 }
      );
    }

    // Return settings without password
    const { smtp_password: _, ...safeSettings } = result.data;

    return NextResponse.json({
      success: true,
      message: 'Newsletter settings saved successfully',
      data: safeSettings
    });

  } catch (error) {
    console.error('Newsletter settings POST error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// This function is moved to lib/newsletter-settings.ts to avoid Next.js route export issues
