"use client";
import React, { useEffect, useState, useRef } from 'react';
import { HiChevronLeft, HiChevronRight } from 'react-icons/hi';
import MiniPackageCard from '../cards/miniPackageCard';
import { SkeletonGrid } from '../skeleton';

export default function MiniPackageSection() {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isTransitioning, setIsTransitioning] = useState(false);
    const timerRef = useRef(null);
    const [isMobile, setIsMobile] = useState(false);
    const [isTablet, setIsTablet] = useState(false);
    const [miniPackages, setMiniPackages] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Fetch mini packages from database
    useEffect(() => {
        const fetchMiniPackages = async () => {
            try {
                setLoading(true);
                const response = await fetch('/api/mini-packages?limit=12');
                const result = await response.json();

                if (result.success) {
                    setMiniPackages(result.data);
                } else {
                    setError('Failed to load mini packages');
                }
            } catch (err) {
                console.error('Error fetching mini packages:', err);
                setError('Failed to load mini packages');
            } finally {
                setLoading(false);
            }
        };

        fetchMiniPackages();
    }, []);

    useEffect(() => {
        const handleResize = () => {
            if (typeof window !== 'undefined') {
                setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
                setIsMobile(window.innerWidth < 768);
            }
        };
        handleResize();
        if (typeof window !== 'undefined') {
            window.addEventListener('resize', handleResize);
        }
        return () => {
            if (typeof window !== 'undefined') {
                window.removeEventListener('resize', handleResize);
            }
        };
    }, []);

    // Get number of visible slides based on screen size
    const getVisibleSlides = () => {
        if (isMobile) return 1;
        if (isTablet) return 2;
        return 3; // desktop
    };

    // Create slides array - only duplicate if we have enough unique items
    const visibleSlides = getVisibleSlides();
    const shouldDuplicate = miniPackages.length >= visibleSlides;
    const slides = shouldDuplicate
        ? [...miniPackages, ...miniPackages, ...miniPackages]
        : miniPackages;

    const startTimer = () => {
        if (timerRef.current) clearTimeout(timerRef.current);
        if (miniPackages.length > 1 && shouldDuplicate) {
            timerRef.current = setTimeout(() => {
                setCurrentIndex(prev => (prev + 1) % miniPackages.length);
                setIsTransitioning(true);
            }, 3000); // Wait 3s before next slide
        }
    };

    useEffect(() => {
        startTimer();
        return () => {
            if (timerRef.current) clearTimeout(timerRef.current);
        };
    }, [currentIndex]);

    const handlePrev = () => {
        if (isTransitioning || miniPackages.length === 0) return;
        if (timerRef.current) clearTimeout(timerRef.current);
        setIsTransitioning(true);

        if (shouldDuplicate) {
            setCurrentIndex(prev => prev === 0 ? miniPackages.length - 1 : prev - 1);
        } else {
            // For non-duplicated arrays, just move to previous item with bounds checking
            setCurrentIndex(prev => Math.max(prev - 1, 0));
        }
    };

    const handleNext = () => {
        if (isTransitioning || miniPackages.length === 0) return;
        if (timerRef.current) clearTimeout(timerRef.current);
        setIsTransitioning(true);

        if (shouldDuplicate) {
            setCurrentIndex(prev => (prev + 1) % miniPackages.length);
        } else {
            // For non-duplicated arrays, just move to next item with bounds checking
            const maxIndex = Math.max(0, miniPackages.length - visibleSlides);
            setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
        }
    };

    const handleTransitionEnd = () => {
        setIsTransitioning(false);
        if (shouldDuplicate) {
            startTimer();
        }
    };

    const handleDotClick = (index) => {
        if (isTransitioning) return;
        setCurrentIndex(index);
        if (shouldDuplicate) {
            setIsTransitioning(true);
            startTimer();
        }
    };

    const getSlideWidth = () => {
        if (isTablet) return 50; // 2 cards in tablet
        if (isMobile) return 100; // 1 card in mobile
        return 33.333; // 3 cards in desktop
    };

    // Show loading state
    if (loading) {
        return (
            <div className="relative w-full overflow-hidden py-8 text-justify bg-[var(--primary-background)]">
                <div className="mb-6 text-center">
                    <h2 className="text-3xl font-bold mb-2">Book Your Perfect Day Tour</h2>
                    <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                </div>
                <div className="px-4 sm:px-6 lg:px-8">
                    <SkeletonGrid
                        variant="mini-package"
                        count={4}
                        cardProps={{ showPrice: true, showButton: true }}
                    />
                </div>
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="relative w-full overflow-hidden py-8 text-justify bg-[var(--primary-background)]">
                <div className="mb-6 text-center">
                    <h2 className="text-3xl font-bold mb-2">Book Your Perfect Day Tour</h2>
                    <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                </div>
                <div className="flex justify-center items-center py-16">
                    <div className="text-lg text-red-600">{error}</div>
                </div>
            </div>
        );
    }

    // Show empty state
    if (miniPackages.length === 0) {
        return (
            <div className="relative w-full overflow-hidden py-8 text-justify bg-[var(--primary-background)]">
                <div className="mb-6 text-center">
                    <h2 className="text-3xl font-bold mb-2">Book Your Perfect Day Tour</h2>
                    <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                </div>
                <div className="flex justify-center items-center py-16">
                    <div className="text-lg text-gray-600">No mini packages available at the moment.</div>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-full overflow-hidden py-8 text-justify bg-[var(--primary-background)]">
            <div className="mb-6 text-center">
                <h2 className="text-3xl font-bold mb-2">Book Your Perfect Day Tour</h2>
                <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
            </div>
            <div className="w-full relative px-6">
                <div
                    className="flex transition-transform duration-500 ease-in-out"
                    style={{
                        transform: `translateX(calc(-${currentIndex * getSlideWidth()}%))`,
                    }}
                    onTransitionEnd={handleTransitionEnd}
                >
                    {slides.map((packageData, index) => (
                        <div
                            key={`${packageData.id}-${index}`}
                            className="px-3"
                            style={{
                                width: `${getSlideWidth()}%`,
                                flexShrink: 0,
                            }}
                        >
                            <MiniPackageCard packageData={packageData} />
                        </div>
                    ))}
                </div>
                {/* Only show navigation buttons if we have enough items to navigate */}
                {(shouldDuplicate || miniPackages.length > visibleSlides) && (
                    <>
                        <button
                            onClick={handlePrev}
                            className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-tr-full rounded-br-full shadow-md z-10"
                            disabled={!shouldDuplicate && currentIndex === 0}
                        >
                            <HiChevronLeft className="w-6 h-6 text-gray-700" />
                        </button>
                        <button
                            onClick={handleNext}
                            className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-tl-full rounded-bl-full shadow-md z-10"
                            disabled={!shouldDuplicate && currentIndex >= Math.max(0, miniPackages.length - visibleSlides)}
                        >
                            <HiChevronRight className="w-6 h-6 text-gray-700" />
                        </button>
                    </>
                )}
            </div>
            {/* Add dots container - only show if there are multiple items */}
            {miniPackages.length > 1 && (
                <div className="flex justify-center gap-2 mt-6">
                    {miniPackages.map((_, index) => (
                        <button
                            key={index}
                            onClick={() => handleDotClick(index)}
                            className={`h-2 w-2 rounded-full transition-all duration-300 ${
                                index === currentIndex % miniPackages.length
                                    ? 'bg-[var(--btn)] w-4'
                                    : 'bg-gray-300'
                            }`}
                            aria-label={`Go to slide ${index + 1}`}
                        />
                    ))}
                </div>
            )}
        </div>
    );
}
