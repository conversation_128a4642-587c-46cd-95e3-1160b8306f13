-- Setup Admin Database Schema for Swift Africa Safaris
-- This script creates the necessary tables and policies for user management

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can insert users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can update all users" ON public.users;
DROP POLICY IF EXISTS "Admins can delete users" ON public.users;

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    full_name TEXT,
    role TEXT DEFAULT 'travel-agent' CHECK (role IN ('admin', 'content-creator', 'tour-specialist', 'it-support', 'travel-agent')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    department TEXT DEFAULT 'General',
    phone TEXT,
    company TEXT,
    avatar_url TEXT,
    permissions TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create avatars storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Simple policies that don't cause recursion
-- Policy: Allow users to view their own profile
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Policy: Allow users to update their own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Policy: Allow authenticated users to view all users (for admin functionality)
-- This is a simplified approach - in production you might want more granular control
CREATE POLICY "Authenticated users can view all users" ON public.users
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy: Allow authenticated users to insert users (for admin functionality)
CREATE POLICY "Authenticated users can insert users" ON public.users
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Policy: Allow authenticated users to update all users (for admin functionality)
CREATE POLICY "Authenticated users can update all users" ON public.users
    FOR UPDATE USING (auth.role() = 'authenticated');

-- Policy: Allow authenticated users to delete users (for admin functionality)
CREATE POLICY "Authenticated users can delete all users" ON public.users
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, full_name, role, status, department)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.raw_user_meta_data->>'full_name'),
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
        COALESCE(NEW.raw_user_meta_data->>'role', 'travel-agent'),
        COALESCE(NEW.raw_user_meta_data->>'status', 'active'),
        COALESCE(NEW.raw_user_meta_data->>'department', 'General')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default admin user (you'll need to create this user in Supabase Auth first)
-- Then run: UPDATE public.users SET role = 'admin' WHERE email = '<EMAIL>';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON public.users(status);
CREATE INDEX IF NOT EXISTS idx_users_department ON public.users(department);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.users TO anon, authenticated;

-- Enable storage for avatars bucket
-- Note: You may need to manually create the avatars bucket in the Supabase dashboard
-- and set it to public if the above INSERT doesn't work due to permissions
