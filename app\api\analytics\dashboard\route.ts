import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    const includeAlerts = searchParams.get('include_alerts') === 'true'
    const includeSessions = searchParams.get('include_sessions') === 'true'

    const dashboard = await generatePerformanceDashboard(days, includeAlerts, includeSessions)
    
    return NextResponse.json(dashboard)

  } catch (error) {
    console.error('Dashboard API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function generatePerformanceDashboard(days: number, includeAlerts: boolean, includeSessions: boolean) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
  
  // Fetch performance metrics
  const { data: metrics } = await supabase
    .from('performance_metrics')
    .select('*')
    .gte('timestamp', startDate)
    .order('timestamp', { ascending: false })

  // Core Web Vitals analysis
  const coreWebVitals = analyzeCoreWebVitals(metrics || [])
  
  // Page performance analysis
  const pagePerformance = analyzePagePerformance(metrics || [])
  
  // Device and connection analysis
  const deviceAnalysis = analyzeDevicePerformance(metrics || [])
  
  // Performance trends
  const trends = analyzePerformanceTrends(metrics || [], days)

  let dashboard: any = {
    period_days: days,
    generated_at: new Date().toISOString(),
    core_web_vitals: coreWebVitals,
    page_performance: pagePerformance,
    device_analysis: deviceAnalysis,
    trends,
    health_score: calculateHealthScore(coreWebVitals, pagePerformance),
    recommendations: generateRecommendations(coreWebVitals, pagePerformance, deviceAnalysis)
  }

  // Include alerts if requested
  if (includeAlerts) {
    const { data: alerts } = await supabase
      .from('performance_alerts')
      .select('*')
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })
      .limit(100)

    dashboard.alerts = {
      total: alerts?.length || 0,
      critical: alerts?.filter(a => a.severity === 'critical').length || 0,
      warnings: alerts?.filter(a => a.severity === 'warning').length || 0,
      recent: alerts?.slice(0, 10) || []
    }
  }

  // Include session data if requested
  if (includeSessions) {
    const { data: sessions } = await supabase
      .from('user_sessions')
      .select('*')
      .gte('start_time', startDate)
      .order('start_time', { ascending: false })
      .limit(500)

    dashboard.sessions = analyzeSessionData(sessions || [])
  }

  return dashboard
}

function analyzeCoreWebVitals(metrics: any[]) {
  const vitals = ['LCP', 'FID', 'CLS', 'TTFB', 'FCP', 'INP']
  const analysis: any = {}

  for (const vital of vitals) {
    const vitalMetrics = metrics.filter(m => m.metric_name === vital)
    
    if (vitalMetrics.length === 0) {
      analysis[vital] = { status: 'no_data', count: 0 }
      continue
    }

    const values = vitalMetrics.map(m => m.metric_value)
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length
    const p75 = getPercentile(values, 75)
    const p95 = getPercentile(values, 95)

    // Determine status based on Web Vitals thresholds
    let status = 'good'
    if (vital === 'LCP' && p75 > 2500) status = p75 > 4000 ? 'poor' : 'needs-improvement'
    if (vital === 'FID' && p75 > 100) status = p75 > 300 ? 'poor' : 'needs-improvement'
    if (vital === 'CLS' && p75 > 0.1) status = p75 > 0.25 ? 'poor' : 'needs-improvement'
    if (vital === 'TTFB' && p75 > 800) status = p75 > 1800 ? 'poor' : 'needs-improvement'

    analysis[vital] = {
      status,
      count: vitalMetrics.length,
      average: Math.round(avg * 100) / 100,
      p75: Math.round(p75 * 100) / 100,
      p95: Math.round(p95 * 100) / 100,
      min: Math.min(...values),
      max: Math.max(...values)
    }
  }

  return analysis
}

function analyzePagePerformance(metrics: any[]) {
  const pageMetrics = metrics.reduce((acc: any, metric) => {
    if (!acc[metric.url]) {
      acc[metric.url] = []
    }
    acc[metric.url].push(metric)
    return acc
  }, {})

  const pageAnalysis = Object.entries(pageMetrics).map(([url, urlMetrics]: [string, any]) => {
    const lcpMetrics = urlMetrics.filter((m: any) => m.metric_name === 'LCP')
    const fidMetrics = urlMetrics.filter((m: any) => m.metric_name === 'FID')
    const clsMetrics = urlMetrics.filter((m: any) => m.metric_name === 'CLS')

    const avgLCP = lcpMetrics.length > 0 ? 
      lcpMetrics.reduce((sum: number, m: any) => sum + m.metric_value, 0) / lcpMetrics.length : 0
    const avgFID = fidMetrics.length > 0 ? 
      fidMetrics.reduce((sum: number, m: any) => sum + m.metric_value, 0) / fidMetrics.length : 0
    const avgCLS = clsMetrics.length > 0 ? 
      clsMetrics.reduce((sum: number, m: any) => sum + m.metric_value, 0) / clsMetrics.length : 0

    return {
      url,
      metrics_count: urlMetrics.length,
      avg_lcp: Math.round(avgLCP),
      avg_fid: Math.round(avgFID),
      avg_cls: Math.round(avgCLS * 1000) / 1000,
      performance_score: calculatePageScore(avgLCP, avgFID, avgCLS)
    }
  })

  return pageAnalysis.sort((a, b) => b.performance_score - a.performance_score)
}

function analyzeDevicePerformance(metrics: any[]) {
  const deviceMetrics = metrics.reduce((acc: any, metric) => {
    const device = metric.device_type || 'unknown'
    if (!acc[device]) {
      acc[device] = { lcp: [], fid: [], cls: [], count: 0 }
    }
    
    acc[device].count++
    if (metric.metric_name === 'LCP') acc[device].lcp.push(metric.metric_value)
    if (metric.metric_name === 'FID') acc[device].fid.push(metric.metric_value)
    if (metric.metric_name === 'CLS') acc[device].cls.push(metric.metric_value)
    
    return acc
  }, {})

  return Object.entries(deviceMetrics).map(([device, data]: [string, any]) => ({
    device,
    count: data.count,
    avg_lcp: data.lcp.length > 0 ? Math.round(data.lcp.reduce((sum: number, val: number) => sum + val, 0) / data.lcp.length) : 0,
    avg_fid: data.fid.length > 0 ? Math.round(data.fid.reduce((sum: number, val: number) => sum + val, 0) / data.fid.length) : 0,
    avg_cls: data.cls.length > 0 ? Math.round((data.cls.reduce((sum: number, val: number) => sum + val, 0) / data.cls.length) * 1000) / 1000 : 0
  }))
}

function analyzePerformanceTrends(metrics: any[], days: number) {
  const dailyMetrics = metrics.reduce((acc: any, metric) => {
    const date = new Date(metric.timestamp).toISOString().split('T')[0]
    if (!acc[date]) {
      acc[date] = { lcp: [], fid: [], cls: [], count: 0 }
    }
    
    acc[date].count++
    if (metric.metric_name === 'LCP') acc[date].lcp.push(metric.metric_value)
    if (metric.metric_name === 'FID') acc[date].fid.push(metric.metric_value)
    if (metric.metric_name === 'CLS') acc[date].cls.push(metric.metric_value)
    
    return acc
  }, {})

  return Object.entries(dailyMetrics).map(([date, data]: [string, any]) => ({
    date,
    count: data.count,
    avg_lcp: data.lcp.length > 0 ? Math.round(data.lcp.reduce((sum: number, val: number) => sum + val, 0) / data.lcp.length) : 0,
    avg_fid: data.fid.length > 0 ? Math.round(data.fid.reduce((sum: number, val: number) => sum + val, 0) / data.fid.length) : 0,
    avg_cls: data.cls.length > 0 ? Math.round((data.cls.reduce((sum: number, val: number) => sum + val, 0) / data.cls.length) * 1000) / 1000 : 0
  })).sort((a, b) => a.date.localeCompare(b.date))
}

function analyzeSessionData(sessions: any[]) {
  if (sessions.length === 0) return { total: 0 }

  const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0)
  const totalPageViews = sessions.reduce((sum, s) => sum + (s.page_views || 0), 0)
  const totalInteractions = sessions.reduce((sum, s) => sum + (s.interactions || 0), 0)

  return {
    total: sessions.length,
    avg_duration: Math.round(totalDuration / sessions.length / 1000), // seconds
    avg_page_views: Math.round((totalPageViews / sessions.length) * 10) / 10,
    avg_interactions: Math.round((totalInteractions / sessions.length) * 10) / 10,
    bounce_rate: Math.round((sessions.filter(s => s.page_views <= 1).length / sessions.length) * 100)
  }
}

function calculateHealthScore(coreWebVitals: any, pagePerformance: any): number {
  let score = 100
  
  // Deduct points for poor Core Web Vitals
  Object.values(coreWebVitals).forEach((vital: any) => {
    if (vital.status === 'poor') score -= 20
    else if (vital.status === 'needs-improvement') score -= 10
  })
  
  // Deduct points for poor page performance
  const poorPages = pagePerformance.filter((page: any) => page.performance_score < 50).length
  score -= poorPages * 5
  
  return Math.max(0, Math.min(100, score))
}

function generateRecommendations(coreWebVitals: any, pagePerformance: any, deviceAnalysis: any): string[] {
  const recommendations: string[] = []
  
  if (coreWebVitals.LCP?.status === 'poor') {
    recommendations.push('Optimize Largest Contentful Paint by reducing server response times and optimizing images')
  }
  
  if (coreWebVitals.FID?.status === 'poor') {
    recommendations.push('Improve First Input Delay by reducing JavaScript execution time and breaking up long tasks')
  }
  
  if (coreWebVitals.CLS?.status === 'poor') {
    recommendations.push('Fix Cumulative Layout Shift by setting dimensions for images and avoiding dynamic content insertion')
  }
  
  const mobilePerformance = deviceAnalysis.find((d: any) => d.device === 'mobile')
  if (mobilePerformance && mobilePerformance.avg_lcp > 3000) {
    recommendations.push('Focus on mobile performance optimization - mobile LCP is significantly higher than desktop')
  }
  
  const poorPages = pagePerformance.filter((page: any) => page.performance_score < 50)
  if (poorPages.length > 0) {
    recommendations.push(`Optimize ${poorPages.length} pages with poor performance scores: ${poorPages.slice(0, 3).map((p: any) => p.url).join(', ')}`)
  }
  
  return recommendations
}

function getPercentile(values: number[], percentile: number): number {
  const sorted = values.sort((a, b) => a - b)
  const index = Math.ceil((percentile / 100) * sorted.length) - 1
  return sorted[index] || 0
}

function calculatePageScore(lcp: number, fid: number, cls: number): number {
  let score = 100
  
  if (lcp > 4000) score -= 40
  else if (lcp > 2500) score -= 20
  
  if (fid > 300) score -= 30
  else if (fid > 100) score -= 15
  
  if (cls > 0.25) score -= 30
  else if (cls > 0.1) score -= 15
  
  return Math.max(0, score)
}
