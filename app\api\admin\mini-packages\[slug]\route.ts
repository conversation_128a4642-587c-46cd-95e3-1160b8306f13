import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Helper function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// GET - Fetch single mini package by slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Fetch mini package with all related data
    const { data: miniPackage, error: miniPackageError } = await supabase
      .from('sas_mini_packages')
      .select('*')
      .eq('slug', slug)
      .single();

    if (miniPackageError || !miniPackage) {
      return NextResponse.json(
        { success: false, error: 'Mini package not found' },
        { status: 404 }
      );
    }

    // Fetch content blocks
    const { data: contentBlocks } = await supabase
      .from('sas_mini_package_content_blocks')
      .select('*')
      .eq('mini_package_id', miniPackage.id)
      .order('sort_order');

    // Fetch itinerary
    const { data: itinerary } = await supabase
      .from('sas_mini_package_itinerary')
      .select('*')
      .eq('mini_package_id', miniPackage.id)
      .order('sort_order');

    // Fetch images
    const { data: images } = await supabase
      .from('sas_mini_package_images')
      .select('*')
      .eq('mini_package_id', miniPackage.id)
      .order('sort_order');

    // Transform content blocks to match editor format
    const transformedContent = (contentBlocks || []).map(block => ({
      id: block.id,
      type: block.block_type === 'h2' ? 'heading2' :
            block.block_type === 'h3' ? 'heading3' :
            block.block_type === 'h4' ? 'heading4' :
            block.block_type === 'h5' ? 'heading5' :
            block.block_type === 'h6' ? 'heading6' :
            block.block_type,
      content: block.image_url ? {
        src: block.image_url,
        alt: block.image_alt || '',
        caption: block.image_caption || ''
      } : block.content,
      imageUrl: block.image_url,
      imageAlt: block.image_alt
    }));

    // Transform itinerary to match component format
    const transformedItinerary = (itinerary || []).map((item, index) => ({
      id: index + 1, // Use index-based ID for component
      hour: item.hour_number,
      title: item.title,
      description: item.description,
      isExpanded: true, // Show content by default for existing data
      isEditing: false
    }));

    // Combine all data
    const fullMiniPackage = {
      ...miniPackage,
      content: transformedContent,
      itinerary: transformedItinerary,
      images: images || []
    };

    return NextResponse.json({
      success: true,
      data: fullMiniPackage
    });

  } catch (error) {
    console.error('Error in GET /api/admin/mini-packages/[slug]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update mini package
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // First, get the existing mini package
    const { data: existingMiniPackage, error: fetchError } = await supabase
      .from('sas_mini_packages')
      .select('id, slug')
      .eq('slug', slug)
      .single();

    if (fetchError || !existingMiniPackage) {
      return NextResponse.json(
        { success: false, error: 'Mini package not found' },
        { status: 404 }
      );
    }

    const miniPackageId = existingMiniPackage.id;

    // Extract data from request body
    const {
      title,
      location,
      difficulty,
      category,
      duration,
      pricing,
      status,
      imageUrl,
      imageAlt,
      heroImageUrl,
      heroImageAlt,
      seoTitle,
      seoDescription,
      seoKeywords,
      ogTitle,
      ogDescription,
      ogImageUrl,
      canonicalUrl,
      robotsIndex,
      robotsFollow,
      highlights,
      packingList,
      includes,
      excludes,
      content = [],
      itinerary = [],
      images = []
    } = body;

    // Generate new slug if title changed
    const newSlug = generateSlug(title);
    
    // Check if new slug conflicts with existing mini packages (excluding current one)
    if (newSlug !== existingMiniPackage.slug) {
      const { data: conflictingMiniPackage } = await supabase
        .from('sas_mini_packages')
        .select('id')
        .eq('slug', newSlug)
        .neq('id', miniPackageId)
        .single();

      if (conflictingMiniPackage) {
        return NextResponse.json(
          { success: false, error: 'A mini package with this title already exists' },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      title,
      slug: newSlug,
      location,
      difficulty,
      category,
      duration,
      pricing_solo: pricing?.solo || 0,
      pricing_honeymoon: pricing?.honeymoon || 0,
      pricing_family: pricing?.family || 0,
      pricing_group: pricing?.group || 0,
      status,
      image_url: imageUrl,
      image_alt: imageAlt || '',
      hero_image_url: heroImageUrl,
      hero_image_alt: heroImageAlt || '',
      seo_title: seoTitle,
      seo_description: seoDescription,
      seo_keywords: seoKeywords,
      og_title: ogTitle,
      og_description: ogDescription,
      og_image_url: ogImageUrl,
      canonical_url: canonicalUrl,
      robots_index: robotsIndex || 'index',
      robots_follow: robotsFollow || 'follow',
      highlights,
      packing_list: packingList,
      includes,
      excludes,
      updated_at: new Date().toISOString()
    };

    // Set published_at if status changed to active or published
    if (status === 'active' || status === 'published') {
      updateData.published_at = new Date().toISOString();
      // Normalize status to 'published'
      if (status === 'active') {
        updateData.status = 'published';
      }
    } else if (status === 'draft') {
      updateData.published_at = null;
    }

    // Update mini package
    const { data: updatedMiniPackage, error: updateError } = await supabase
      .from('sas_mini_packages')
      .update(updateData)
      .eq('id', miniPackageId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating mini package:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update mini package' },
        { status: 500 }
      );
    }

    // Update content blocks
    // Delete existing content blocks
    await supabase
      .from('sas_mini_package_content_blocks')
      .delete()
      .eq('mini_package_id', miniPackageId);

    // Insert new content blocks
    if (content && content.length > 0) {
      const contentBlocks = content.map((block: any) => ({
        mini_package_id: miniPackageId,
        block_type: block.block_type,
        content: block.content,
        content_data: block.content_data,
        image_url: block.image_url,
        image_alt: block.image_alt,
        image_caption: block.image_caption,
        sort_order: block.sort_order
      }));

      await supabase
        .from('sas_mini_package_content_blocks')
        .insert(contentBlocks);
    }

    // Update itinerary
    // Delete existing itinerary
    await supabase
      .from('sas_mini_package_itinerary')
      .delete()
      .eq('mini_package_id', miniPackageId);

    // Insert new itinerary
    if (itinerary && itinerary.length > 0) {
      const itineraryData = itinerary.map((day: any) => ({
        mini_package_id: miniPackageId,
        hour_number: day.hour_number,
        day_number: 1, // Mini packages are single-day experiences
        title: day.title,
        description: day.description,
        sort_order: day.sort_order
      }));

      await supabase
        .from('sas_mini_package_itinerary')
        .insert(itineraryData);
    }

    // Update images
    // Delete existing images
    await supabase
      .from('sas_mini_package_images')
      .delete()
      .eq('mini_package_id', miniPackageId);

    // Insert new images
    if (images && images.length > 0) {
      const imageData = images.map((image: any) => ({
        mini_package_id: miniPackageId,
        image_url: image.image_url,
        image_alt: image.image_alt,
        caption: image.caption,
        sort_order: image.sort_order,
        is_featured: image.is_featured || false
      }));

      await supabase
        .from('sas_mini_package_images')
        .insert(imageData);
    }

    return NextResponse.json({
      success: true,
      data: updatedMiniPackage,
      message: 'Mini package updated successfully'
    });

  } catch (error) {
    console.error('Error in PUT /api/admin/mini-packages/[slug]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete mini package
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params;

    // Get mini package ID
    const { data: miniPackage, error: fetchError } = await supabase
      .from('sas_mini_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (fetchError || !miniPackage) {
      return NextResponse.json(
        { success: false, error: 'Mini package not found' },
        { status: 404 }
      );
    }

    // Delete mini package (cascade will handle related tables)
    const { error: deleteError } = await supabase
      .from('sas_mini_packages')
      .delete()
      .eq('id', miniPackage.id);

    if (deleteError) {
      console.error('Error deleting mini package:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Failed to delete mini package' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Mini package deleted successfully'
    });

  } catch (error) {
    console.error('Error in DELETE /api/admin/mini-packages/[slug]:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
