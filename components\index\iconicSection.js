"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import { iconicData } from '../data/iconicData';

const TravelDestinations = () => {
    const router = useRouter();
    const { destinations } = iconicData;

    const handleDestinationClick = (destination) => {
        router.push(`/iconic/${destination.name.toLowerCase()}`);
    };

    return (
        <div className="w-full bg-[var(--secondary-background)] py-8 px-4">
            <div className="mb-6 text-center">
                <h2 className="text-3xl font-bold mb-2">Iconic <br /> Safari Destinations</h2>
                <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
            </div>
            <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                    {destinations.map((destination, index) => (
                        <div
                            key={index}
                            className="relative h-48 sm:h-56 md:h-64 overflow-hidden cursor-pointer transform transition-transform duration-300 hover:scale-105"
                            onClick={() => handleDestinationClick(destination)}
                        >
                            {/* Background image with alt attribute */}
                            <img
                                src={destination.image}
                                alt={destination.alt}
                                className="absolute inset-0 w-full h-full object-cover"
                            />

                            {/* Overlay for better text readability */}
                            <div className="absolute inset-0 bg-black/20"></div>

                            {/* Destination name */}
                            <div className="absolute inset-0 flex items-center justify-center">
                                <h2 className="text-white text-2xl md:text-3xl lg:text-4xl font-light tracking-wide text-center px-4">
                                    {destination.name}
                                </h2>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default TravelDestinations;