"use client";
import { useEffect } from 'react';
import { initPerformanceOptimizations } from '@/lib/performance';

/**
 * Performance Optimizer Component
 * Initializes performance optimizations and Core Web Vitals monitoring
 */
const PerformanceOptimizer = () => {
  useEffect(() => {
    // Initialize performance optimizations
    initPerformanceOptimizations();

    // Add viewport meta tag optimization
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=5');
    }

    // Optimize scroll performance
    const optimizeScroll = () => {
      document.documentElement.style.scrollBehavior = 'smooth';
    };

    // Add passive event listeners for better performance
    const addPassiveListeners = () => {
      const passiveEvents = ['touchstart', 'touchmove', 'wheel'];
      passiveEvents.forEach(event => {
        document.addEventListener(event, () => {}, { passive: true });
      });
    };

    optimizeScroll();
    addPassiveListeners();

    // Cleanup function
    return () => {
      // Clean up any performance monitoring
      if (typeof window !== 'undefined' && 'performance' in window) {
        performance.clearMarks();
        performance.clearMeasures();
      }
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default PerformanceOptimizer;
