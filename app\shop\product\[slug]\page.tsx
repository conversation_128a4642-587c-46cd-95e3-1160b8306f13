/* eslint-disable @next/next/no-img-element */
import type { Metadata } from 'next';
import React from 'react';
import { getProductBySlug } from '@/components/data/productData';
import TourismFooter from '@/components/footer';
import Navbar from '@/components/header';
import ProductDetailClient from '@/components/shop/ProductDetailClient';
import { generateMetadata as generateSEOMetadata } from '@/lib/seo';

interface PageProps {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const product = getProductBySlug(slug);

  if (!product) {
    return generateSEOMetadata({
      title: 'Product Not Found',
      description: 'The requested product could not be found.',
      url: `/shop/product/${slug}`
    });
  }

  const finalPrice = product.price - (product.price * product.discount / 100);

  // Use user-inputted SEO metadata if available, otherwise fallback to defaults
  if (product.seo) {
    return generateSEOMetadata({
      title: product.seo.title,
      description: product.seo.description,
      keywords: product.seo.keywords,
      type: 'product',
      url: `/shop/product/${slug}`,
      image: product.seo.image,
      author: product.seo.author,
      publishedTime: product.seo.publishedTime,
      modifiedTime: product.seo.modifiedTime,
      price: finalPrice,
      currency: 'USD',
      availability: product.stock && product.stock > 0 ? 'InStock' : 'OutOfStock',
      category: product.seo.category
    });
  }

  // Fallback to auto-generated metadata
  return generateSEOMetadata({
    title: `${product.title} - Safari Gear & Travel Essentials | Swift Africa Safaris`,
    description: `${product.description} Shop premium safari gear and travel essentials. Price: $${finalPrice.toFixed(2)}`,
    keywords: [
      product.title.toLowerCase(),
      product.category.toLowerCase(),
      'safari gear',
      'travel essentials',
      'African safari equipment',
      'outdoor gear',
      'safari accessories',
      'travel gear Africa'
    ],
    type: 'product',
    url: `/shop/product/${slug}`,
    image: product.coverImage || product.images?.[0],
    price: finalPrice,
    currency: 'USD',
    availability: product.stock && product.stock > 0 ? 'InStock' : 'OutOfStock',
    category: product.category
  });
}

export default async function ProductPage() {
  return (
    <div>
      <Navbar />
      <ProductDetailClient />
      <TourismFooter />
    </div>
  );
}