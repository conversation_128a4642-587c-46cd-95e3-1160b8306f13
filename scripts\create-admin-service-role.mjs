#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

// Create admin client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createAdminUser() {
  console.log('👤 Creating Admin User with Service Role...\n');

  const adminEmail = '<EMAIL>';
  const adminPassword = 'admin123';
  const adminName = 'Admin User';

  try {
    // Check if user already exists
    console.log('1. Checking if user exists...');

    let userId;
    const { data: existingUser } = await supabase.auth.admin.listUsers();
    const userExists = existingUser.users.find(user => user.email === adminEmail);

    if (userExists) {
      console.log('ℹ️  User already exists, updating password...');
      userId = userExists.id;

      // Update password
      const { error: updateError } = await supabase.auth.admin.updateUserById(userId, {
        password: adminPassword
      });

      if (updateError) {
        console.error('❌ Password update failed:', updateError.message);
        return;
      }

      console.log('✅ Password updated');
    } else {
      console.log('👤 Creating new user...');

      const { data: userData, error: userError } = await supabase.auth.admin.createUser({
        email: adminEmail,
        password: adminPassword,
        email_confirm: true,
        user_metadata: {
          name: adminName,
          role: 'admin'
        }
      });

      if (userError) {
        console.error('❌ User creation failed:', userError.message);
        return;
      }

      console.log('✅ User created successfully');
      userId = userData.user.id;
    }

    // Check if user profile exists and create/update it
    console.log('2. Checking user profile...');

    const { data: existingProfile } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (existingProfile) {
      console.log('ℹ️  User profile exists, updating...');

      const { error: updateError } = await supabase
        .from('users')
        .update({
          email: adminEmail,
          name: adminName,
          role: 'admin',
          status: 'active'
        })
        .eq('id', userId);

      if (updateError) {
        console.error('❌ Profile update failed:', updateError.message);
        return;
      }

      console.log('✅ User profile updated');
    } else {
      console.log('ℹ️  Creating new user profile...');

      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: userId,
          email: adminEmail,
          name: adminName,
          role: 'admin',
          status: 'active'
        });

      if (profileError) {
        console.error('❌ Profile creation failed:', profileError.message);
        console.log('Error details:', profileError);
        return;
      }

      console.log('✅ User profile created');
    }

    console.log('\n🎉 Admin user created successfully!');
    console.log('\n📝 Login Credentials:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   Password: ${adminPassword}`);
    console.log('\n🔗 Access the admin dashboard:');
    console.log('   http://localhost:3001/admin/login');

  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    console.log('\n🔧 Try manual creation:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to Authentication > Users');
    console.log('3. Click "Add user"');
    console.log(`4. Email: ${adminEmail}`);
    console.log(`5. Password: ${adminPassword}`);
    console.log('6. Confirm email: Yes');
    console.log('7. Then run this SQL in the SQL Editor:');
    console.log(`   INSERT INTO users (id, email, name, role, status) 
   SELECT id, email, '${adminName}', 'admin', 'active' 
   FROM auth.users 
   WHERE email = '${adminEmail}';`);
  }
}

createAdminUser();
