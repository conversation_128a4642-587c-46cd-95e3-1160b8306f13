import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugStorageBucket() {
  console.log('🔍 Detailed Storage Bucket Debug\n');

  try {
    // 1. List all buckets with full details
    console.log('📦 Listing all storage buckets:');
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError);
      return;
    }

    if (!buckets || buckets.length === 0) {
      console.log('❌ No buckets found');
      return;
    }

    buckets.forEach((bucket, index) => {
      console.log(`\n${index + 1}. Bucket: ${bucket.name}`);
      console.log(`   ID: ${bucket.id}`);
      console.log(`   Public: ${bucket.public}`);
      console.log(`   File size limit: ${bucket.file_size_limit} bytes`);
      console.log(`   Allowed MIME types: ${bucket.allowed_mime_types?.join(', ') || 'All'}`);
      console.log(`   Created: ${bucket.created_at}`);
      console.log(`   Updated: ${bucket.updated_at}`);
    });

    // 2. Check specifically for package-images
    console.log('\n🔍 Checking for package-images bucket:');
    const packageImagesBucket = buckets.find(b => b.name === 'package-images');
    const packageImagesById = buckets.find(b => b.id === 'package-images');
    
    if (packageImagesBucket) {
      console.log('✅ Found bucket by name: package-images');
      console.log(`   ID: ${packageImagesBucket.id}`);
      console.log(`   Public: ${packageImagesBucket.public}`);
    } else {
      console.log('❌ No bucket found with name: package-images');
    }

    if (packageImagesById) {
      console.log('✅ Found bucket by ID: package-images');
      console.log(`   Name: ${packageImagesById.name}`);
      console.log(`   Public: ${packageImagesById.public}`);
    } else {
      console.log('❌ No bucket found with ID: package-images');
    }

    // 3. Test bucket access
    console.log('\n🧪 Testing bucket access:');
    
    if (packageImagesBucket) {
      console.log('Testing access to package-images bucket...');
      
      // Try to list files
      const { data: files, error: listFilesError } = await supabase.storage
        .from('package-images')
        .list();
      
      if (listFilesError) {
        console.log(`❌ Cannot list files: ${listFilesError.message}`);
      } else {
        console.log(`✅ Can list files. Files count: ${files?.length || 0}`);
      }

      // Try to get bucket info
      const { data: bucketInfo, error: bucketInfoError } = await supabase.storage
        .getBucket('package-images');
      
      if (bucketInfoError) {
        console.log(`❌ Cannot get bucket info: ${bucketInfoError.message}`);
      } else {
        console.log('✅ Can get bucket info');
        console.log(`   Bucket info:`, bucketInfo);
      }
    }

    // 4. Check RLS policies
    console.log('\n🔒 Checking RLS policies:');
    try {
      const { error: policiesError } = await supabase
        .from('storage.objects')
        .select('*')
        .limit(1);
      
      if (policiesError) {
        console.log(`❌ RLS policy issue: ${policiesError.message}`);
      } else {
        console.log('✅ RLS policies seem to be working');
      }
    } catch (error) {
      console.log(`❌ Error checking RLS: ${error.message}`);
    }

    // 5. Test upload capability (without actually uploading)
    console.log('\n📤 Testing upload capability:');
    if (packageImagesBucket) {
      console.log('✅ Bucket exists, upload should work');
      console.log('   Note: This doesn\'t actually upload a file, just checks if the bucket is accessible');
    } else {
      console.log('❌ Bucket doesn\'t exist, upload will fail');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

debugStorageBucket().catch(console.error); 