'use client';

import React from 'react';
import { Users, Mail, TrendingUp, Calendar } from 'lucide-react';

interface NewsletterStatsProps {
  totalSubscribers: number;
  activeSubscribers: number;
  unsubscribed: number;
  recentGrowth?: number;
}

export default function NewsletterStats({
  totalSubscribers,
  activeSubscribers,
  unsubscribed,
  recentGrowth = 0
}: NewsletterStatsProps) {
  const subscriptionRate = totalSubscribers > 0 
    ? Math.round((activeSubscribers / totalSubscribers) * 100) 
    : 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Subscribers */}
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-blue-600 text-sm font-medium">Total Subscribers</p>
            <p className="text-3xl font-bold text-blue-800">{totalSubscribers.toLocaleString()}</p>
            {recentGrowth > 0 && (
              <div className="flex items-center mt-2">
                <TrendingUp className="w-4 h-4 text-green-600 mr-1" />
                <span className="text-sm text-green-600">+{recentGrowth} this month</span>
              </div>
            )}
          </div>
          <Users className="w-8 h-8 text-blue-600" />
        </div>
      </div>

      {/* Active Subscriptions */}
      <div className="bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl border border-green-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-green-600 text-sm font-medium">Active Subscriptions</p>
            <p className="text-3xl font-bold text-green-800">{activeSubscribers.toLocaleString()}</p>
            <div className="flex items-center mt-2">
              <span className="text-sm text-green-600">{subscriptionRate}% active rate</span>
            </div>
          </div>
          <Mail className="w-8 h-8 text-green-600" />
        </div>
      </div>

      {/* Unsubscribed */}
      <div className="bg-gradient-to-r from-red-50 to-red-100 p-6 rounded-xl border border-red-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-red-600 text-sm font-medium">Unsubscribed</p>
            <p className="text-3xl font-bold text-red-800">{unsubscribed.toLocaleString()}</p>
            <div className="flex items-center mt-2">
              <span className="text-sm text-red-600">
                {totalSubscribers > 0 ? Math.round((unsubscribed / totalSubscribers) * 100) : 0}% unsubscribe rate
              </span>
            </div>
          </div>
          <Users className="w-8 h-8 text-red-600" />
        </div>
      </div>

      {/* Engagement Rate */}
      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl border border-purple-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-purple-600 text-sm font-medium">Engagement Rate</p>
            <p className="text-3xl font-bold text-purple-800">{subscriptionRate}%</p>
            <div className="flex items-center mt-2">
              <Calendar className="w-4 h-4 text-purple-600 mr-1" />
              <span className="text-sm text-purple-600">Last 30 days</span>
            </div>
          </div>
          <TrendingUp className="w-8 h-8 text-purple-600" />
        </div>
      </div>
    </div>
  );
}
