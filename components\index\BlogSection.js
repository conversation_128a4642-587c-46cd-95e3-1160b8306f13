"use client";
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import TravelCard from '../cards/blogCard';
import { SkeletonGrid } from '../skeleton';

const BlogSection = () => {
    const [posts, setPosts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Function to shuffle array randomly
    const shuffleArray = (array) => {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    };

    // Fetch random blog posts from database
    const fetchRandomBlogs = async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch more posts than needed so we can randomize selection
            const response = await fetch('/api/blog?limit=20');
            const data = await response.json();

            if (data.success && data.data.posts) {
                // Shuffle the posts and take first 3
                const shuffledPosts = shuffleArray(data.data.posts);
                const selectedPosts = shuffledPosts.slice(0, 3);

                // Transform data to match expected format
                const transformedPosts = selectedPosts.map(post => ({
                    image: post.hero_image_url,
                    title: post.title,
                    description: post.description,
                    slug: post.slug
                }));

                setPosts(transformedPosts);
            } else {
                setError('Failed to fetch blog posts');
                setPosts([]); // Set empty array as fallback
            }
        } catch (err) {
            console.error('Error fetching blog posts:', err);
            setError('Failed to fetch blog posts');
            setPosts([]); // Set empty array as fallback
        } finally {
            setLoading(false);
        }
    };

    // Fetch blogs on component mount
    useEffect(() => {
        fetchRandomBlogs();
    }, []);

    const handleReadMore = (index) => {
        // Handle read more click
        console.log(`Clicked read more for blog ${index}`);
    };

    return (
        <div className="w-full px-4 sm:px-6 lg:px-8 py-8 bg-[var(--secondary-background)]">
            <div className="mb-12 text-center">
                <h2 className="text-3xl font-bold mb-2">Our Travel Blog and Tips</h2>
                <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
            </div>

            {loading ? (
                <SkeletonGrid
                    variant="blog"
                    count={3}
                    cardProps={{ showButton: true }}
                />
            ) : error ? (
                <div className="text-center py-8">
                    <p className="text-gray-600 mb-4">Unable to load blog posts at the moment.</p>
                    <button
                        onClick={fetchRandomBlogs}
                        className="bg-[#d35400] text-white px-4 py-2 rounded hover:bg-[#c0392b] transition duration-300"
                    >
                        Try Again
                    </button>
                </div>
            ) : posts.length === 0 ? (
                <div className="text-center py-8">
                    <p className="text-gray-600">No blog posts available at the moment.</p>
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10">
                    {posts.map((post, index) => (
                        <TravelCard
                            key={`${post.slug}-${index}`}
                            image={post.image}
                            title={post.title}
                            description={post.description}
                            slug={post.slug}
                            onButtonClick={() => handleReadMore(index)}
                            className="w-full h-full"
                        />
                    ))}
                </div>
            )}

            {/* View More button */}
            <div className="mt-8 flex justify-center">
                <Link
                    href="/blog"
                    className="btn rounded-[0.625em] text-xl font-400 bg-[#d35400] text-white px-6 py-3 hover:bg-[#c0392b] transition duration-300"
                >
                    View More
                </Link>
            </div>
        </div>
    );
};

export default BlogSection;
