'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Calendar, 
  Users, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Download,
  Edit,
  MessageSquare,
  Star
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';

interface BookingDetails {
  id: string;
  packageName: string;
  packageId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  customerCountry: string;
  travelers: number;
  totalAmount: number;
  commission: number;
  commissionRate: number;
  status: 'confirmed' | 'pending' | 'cancelled' | 'completed';
  paymentStatus: 'paid' | 'pending' | 'failed' | 'refunded';
  bookingDate: string;
  travelDate: string;
  duration: string;
  destination: string;
  pricingType: string;
  specialRequests?: string;
  emergencyContact: string;
  emergencyPhone: string;
  dietaryRequirements?: string;
  medicalConditions?: string;
  bookingReference: string;
  confirmationNumber?: string;
  notes: string[];
  timeline: Array<{
    date: string;
    action: string;
    description: string;
    user: string;
  }>;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function BookingDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const bookingId = params.id as string;
  
  const [booking, setBooking] = useState<BookingDetails | null>(null);
  const [showNotes, setShowNotes] = useState(false);

  // Mock data - replace with actual API call
  useEffect(() => {
    const mockBooking: BookingDetails = {
      id: bookingId,
      packageName: 'Serengeti & Ngorongoro 5-Day Safari',
      packageId: 'pkg-001',
      customerName: 'Michael Johnson',
      customerEmail: '<EMAIL>',
      customerPhone: '****** 123 4567',
      customerCountry: 'United States',
      travelers: 4,
      totalAmount: 7200,
      commission: 1080,
      commissionRate: 15,
      status: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: '2024-12-15T10:30:00Z',
      travelDate: '2025-01-20',
      duration: '5 Days',
      destination: 'Serengeti & Ngorongoro',
      pricingType: 'Family',
      specialRequests: 'Vegetarian meals for 2 travelers. Window seats preferred for game drives.',
      emergencyContact: 'Sarah Johnson',
      emergencyPhone: '****** 987 6543',
      dietaryRequirements: 'Vegetarian meals for 2 travelers, no nuts',
      medicalConditions: 'One traveler has mild asthma',
      bookingReference: 'SA-B2B-001',
      confirmationNumber: 'SAS-2025-001234',
      notes: [
        'Customer specifically requested early morning game drives',
        'Celebrating 10th wedding anniversary - arrange special dinner',
        'First time safari - provide extra briefing'
      ],
      timeline: [
        {
          date: '2024-12-15T10:30:00Z',
          action: 'Booking Created',
          description: 'Initial booking created by partner',
          user: 'John Safari Tours'
        },
        {
          date: '2024-12-15T14:20:00Z',
          action: 'Payment Received',
          description: 'Full payment received and confirmed',
          user: 'System'
        },
        {
          date: '2024-12-15T15:45:00Z',
          action: 'Booking Confirmed',
          description: 'Booking confirmed and confirmation sent to customer',
          user: 'Sarah Admin'
        },
        {
          date: '2024-12-16T09:15:00Z',
          action: 'Special Request Added',
          description: 'Added note about anniversary celebration',
          user: 'Michael Chen'
        }
      ]
    };
    
    setBooking(mockBooking);
  }, [bookingId]);

  if (!booking) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center">
          <button
            onClick={() => router.back()}
            className="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Booking Details</h1>
            <p className="text-gray-600 mt-1">Booking Reference: {booking.bookingReference}</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link href={`/partner/messages?booking=${booking.id}`}>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
              <MessageSquare className="h-4 w-4 mr-2" />
              Message Admin
            </button>
          </Link>
          <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
            <Download className="h-4 w-4 mr-2" />
            Download PDF
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Status Overview */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Booking Status</h2>
              <div className="flex items-center space-x-3">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                  {getStatusIcon(booking.status)}
                  <span className="ml-1 capitalize">{booking.status}</span>
                </span>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPaymentStatusColor(booking.paymentStatus)}`}>
                  <DollarSign className="h-4 w-4 mr-1" />
                  <span className="capitalize">{booking.paymentStatus}</span>
                </span>
              </div>
            </div>
            
            {booking.confirmationNumber && (
              <div className="bg-green-50 rounded-lg p-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <div>
                    <div className="font-medium text-green-900">Confirmation Number</div>
                    <div className="text-green-800">{booking.confirmationNumber}</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Package Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Package Information</h2>
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-start">
                <Package className="h-6 w-6 text-blue-600 mr-3 mt-1" />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{booking.packageName}</h3>
                  <div className="flex items-center text-sm text-gray-600 mt-1 space-x-4">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {booking.destination}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {booking.duration}
                    </div>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {booking.travelers} travelers
                    </div>
                  </div>
                  <div className="mt-2">
                    <span className="text-sm text-gray-600">Pricing Type: </span>
                    <span className="font-medium text-gray-900">{booking.pricingType}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center mb-3">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">{booking.customerName}</div>
                    <div className="text-sm text-gray-600">{booking.customerCountry}</div>
                  </div>
                </div>
                <div className="flex items-center mb-3">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-gray-900">{booking.customerEmail}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-gray-900">{booking.customerPhone}</div>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Emergency Contact</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>{booking.emergencyContact}</div>
                  <div>{booking.emergencyPhone}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Travel Details */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Travel Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center mb-4">
                  <Calendar className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">Travel Date</div>
                    <div className="text-gray-600">{formatDate(booking.travelDate)}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">Number of Travelers</div>
                    <div className="text-gray-600">{booking.travelers} people</div>
                  </div>
                </div>
              </div>
              <div>
                <div className="mb-4">
                  <div className="font-medium text-gray-900 mb-1">Booking Date</div>
                  <div className="text-gray-600">{formatDate(booking.bookingDate)}</div>
                </div>
              </div>
            </div>

            {booking.specialRequests && (
              <div className="mt-6">
                <h4 className="font-medium text-gray-900 mb-2">Special Requests</h4>
                <div className="bg-gray-50 rounded-lg p-3 text-gray-700">
                  {booking.specialRequests}
                </div>
              </div>
            )}

            {(booking.dietaryRequirements || booking.medicalConditions) && (
              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                {booking.dietaryRequirements && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Dietary Requirements</h4>
                    <div className="bg-gray-50 rounded-lg p-3 text-gray-700">
                      {booking.dietaryRequirements}
                    </div>
                  </div>
                )}
                {booking.medicalConditions && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Medical Conditions</h4>
                    <div className="bg-gray-50 rounded-lg p-3 text-gray-700">
                      {booking.medicalConditions}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Timeline */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Booking Timeline</h2>
            <div className="space-y-4">
              {booking.timeline.map((event, index) => (
                <div key={index} className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-4"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-gray-900">{event.action}</div>
                      <div className="text-sm text-gray-500">{formatDateTime(event.date)}</div>
                    </div>
                    <div className="text-gray-600 text-sm">{event.description}</div>
                    <div className="text-gray-500 text-xs mt-1">by {event.user}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Financial Summary */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Summary</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Total Amount</span>
                  <span className="font-semibold text-gray-900">{formatCurrency(booking.totalAmount)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Commission Rate</span>
                  <span className="text-gray-900">{booking.commissionRate}%</span>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-green-600">Your Commission</span>
                    <span className="font-bold text-green-600">{formatCurrency(booking.commission)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link href={`/partner/bookings/${booking.id}/edit`} className="block">
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Booking
                  </button>
                </Link>
                <Link href={`/partner/packages/${booking.packageId}`} className="block">
                  <button className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center">
                    <Package className="h-4 w-4 mr-2" />
                    View Package
                  </button>
                </Link>
                <button
                  onClick={() => setShowNotes(!showNotes)}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                >
                  {showNotes ? 'Hide Notes' : 'View Notes'}
                </button>
              </div>
            </div>

            {/* Notes */}
            {showNotes && booking.notes.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Internal Notes</h3>
                <div className="space-y-3">
                  {booking.notes.map((note, index) => (
                    <div key={index} className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                      <div className="text-sm text-yellow-800">{note}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Customer Rating */}
            {booking.status === 'completed' && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Feedback</h3>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star key={star} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <div className="text-sm text-gray-600">Excellent experience!</div>
                  <div className="text-xs text-gray-500 mt-1">Rated 2 days ago</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
