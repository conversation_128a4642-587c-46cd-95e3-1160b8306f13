/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        accent: '#d35400',
        background: '#dfe3dc',
        white: '#ffffff',
        btn: '#163201',
        'light-green': '#317100',
        text: 'rgb(7, 7, 7)',
        footer: '#212020',
        hero: '#F5F5F5',
        'card-bg': '#fffbde',
        'primary-background': '#f4f3ee',
        'secondary-background': '#e3dbcc',
      },
      fontFamily: {
        'jost': ['Jost', 'sans-serif'],
      },
      animation: {
        shimmer: 'shimmer 2s infinite linear',
      },
      keyframes: {
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}

