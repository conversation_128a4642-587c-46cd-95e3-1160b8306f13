import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// GET - Fetch single subscriber (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { data: subscriber, error } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Subscriber not found' },
          { status: 404 }
        );
      }
      console.error('Database fetch error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch subscriber' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: subscriber
    });

  } catch (error) {
    console.error('Newsletter subscriber GET error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update subscriber (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { email, status } = body;

    // Validate email if provided
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return NextResponse.json(
          { success: false, error: 'Invalid email format' },
          { status: 400 }
        );
      }
    }

    // Validate status if provided
    if (status && !['subscribed', 'unsubscribed'].includes(status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status. Must be "subscribed" or "unsubscribed"' },
        { status: 400 }
      );
    }

    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Check if subscriber exists
    const { data: existingSubscriber, error: checkError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .select('id, email')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Subscriber not found' },
          { status: 404 }
        );
      }
      console.error('Database check error:', checkError);
      return NextResponse.json(
        { success: false, error: 'Database error occurred' },
        { status: 500 }
      );
    }

    // If email is being changed, check for duplicates
    if (email && email.toLowerCase() !== existingSubscriber.email) {
      const { data: duplicateCheck, error: duplicateError } = await supabaseAdmin
        .from('sas_newsletter_subscribers')
        .select('id')
        .eq('email', email.toLowerCase())
        .neq('id', id)
        .single();

      if (duplicateError && duplicateError.code !== 'PGRST116') {
        console.error('Duplicate check error:', duplicateError);
        return NextResponse.json(
          { success: false, error: 'Database error occurred' },
          { status: 500 }
        );
      }

      if (duplicateCheck) {
        return NextResponse.json(
          { success: false, error: 'Email already exists in the subscriber list' },
          { status: 409 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (email) {
      updateData.email = email.toLowerCase();
    }

    if (status) {
      updateData.status = status;
    }

    // Update subscriber
    const { data: updatedSubscriber, error: updateError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Database update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update subscriber' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Subscriber updated successfully',
      data: updatedSubscriber
    });

  } catch (error) {
    console.error('Newsletter subscriber PUT error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete subscriber (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Create service role client for admin operations
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Check if subscriber exists
    const { data: existingSubscriber, error: checkError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .select('id, email')
      .eq('id', id)
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Subscriber not found' },
          { status: 404 }
        );
      }
      console.error('Database check error:', checkError);
      return NextResponse.json(
        { success: false, error: 'Database error occurred' },
        { status: 500 }
      );
    }

    // Delete subscriber
    const { error: deleteError } = await supabaseAdmin
      .from('sas_newsletter_subscribers')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Database delete error:', deleteError);
      return NextResponse.json(
        { success: false, error: 'Failed to delete subscriber' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Subscriber deleted successfully'
    });

  } catch (error) {
    console.error('Newsletter subscriber DELETE error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
