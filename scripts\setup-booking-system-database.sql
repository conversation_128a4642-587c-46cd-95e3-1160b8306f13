-- Swift Africa Safaris Booking System Database Setup
-- This script creates all necessary tables for the comprehensive booking system

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Tour Bookings Table (Plan Tour Form)
CREATE TABLE IF NOT EXISTS sas_tour_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    whatsapp VARCHAR(50) NOT NULL,
    number_of_people INTEGER NOT NULL CHECK (number_of_people > 0),
    message TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    admin_notes TEXT,
    email_sent BOOLEAN DEFAULT FALSE,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    notification_emails_sent JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Apartment Bookings Table
CREATE TABLE IF NOT EXISTS sas_apartment_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    whatsapp VARCHAR(50) NOT NULL,
    properties TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    admin_notes TEXT,
    email_sent BOOLEAN DEFAULT FALSE,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    notification_emails_sent JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Car Bookings Table
CREATE TABLE IF NOT EXISTS sas_car_bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    whatsapp VARCHAR(50) NOT NULL,
    car_properties TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    admin_notes TEXT,
    email_sent BOOLEAN DEFAULT FALSE,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    notification_emails_sent JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Volunteering Applications Table
CREATE TABLE IF NOT EXISTS sas_volunteering_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    arrival_date DATE NOT NULL,
    departure_date DATE NOT NULL,
    message TEXT,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
    admin_notes TEXT,
    email_sent BOOLEAN DEFAULT FALSE,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    notification_emails_sent JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_dates CHECK (departure_date > arrival_date)
);

-- 5. Contact Submissions Table
CREATE TABLE IF NOT EXISTS sas_contact_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    whatsapp VARCHAR(50) NOT NULL,
    email VARCHAR(255) NOT NULL,
    message TEXT,
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'responded', 'closed')),
    admin_notes TEXT,
    email_sent BOOLEAN DEFAULT FALSE,
    email_sent_at TIMESTAMP WITH TIME ZONE,
    notification_emails_sent JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Notification Emails Management Table
CREATE TABLE IF NOT EXISTS sas_notification_emails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email_address VARCHAR(255) NOT NULL UNIQUE,
    notification_types JSONB DEFAULT '["all"]'::jsonb,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tour_bookings_email ON sas_tour_bookings(email);
CREATE INDEX IF NOT EXISTS idx_tour_bookings_status ON sas_tour_bookings(status);
CREATE INDEX IF NOT EXISTS idx_tour_bookings_created_at ON sas_tour_bookings(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_apartment_bookings_email ON sas_apartment_bookings(email);
CREATE INDEX IF NOT EXISTS idx_apartment_bookings_status ON sas_apartment_bookings(status);
CREATE INDEX IF NOT EXISTS idx_apartment_bookings_created_at ON sas_apartment_bookings(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_car_bookings_email ON sas_car_bookings(email);
CREATE INDEX IF NOT EXISTS idx_car_bookings_status ON sas_car_bookings(status);
CREATE INDEX IF NOT EXISTS idx_car_bookings_created_at ON sas_car_bookings(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_volunteering_email ON sas_volunteering_applications(email);
CREATE INDEX IF NOT EXISTS idx_volunteering_status ON sas_volunteering_applications(status);
CREATE INDEX IF NOT EXISTS idx_volunteering_created_at ON sas_volunteering_applications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_volunteering_dates ON sas_volunteering_applications(arrival_date, departure_date);

CREATE INDEX IF NOT EXISTS idx_contact_email ON sas_contact_submissions(email);
CREATE INDEX IF NOT EXISTS idx_contact_status ON sas_contact_submissions(status);
CREATE INDEX IF NOT EXISTS idx_contact_created_at ON sas_contact_submissions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_notification_emails_active ON sas_notification_emails(is_active);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_tour_bookings_updated_at BEFORE UPDATE ON sas_tour_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_apartment_bookings_updated_at BEFORE UPDATE ON sas_apartment_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_car_bookings_updated_at BEFORE UPDATE ON sas_car_bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_volunteering_applications_updated_at BEFORE UPDATE ON sas_volunteering_applications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contact_submissions_updated_at BEFORE UPDATE ON sas_contact_submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notification_emails_updated_at BEFORE UPDATE ON sas_notification_emails FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default notification email
INSERT INTO sas_notification_emails (email_address, notification_types, is_active) 
VALUES ('<EMAIL>', '["all"]'::jsonb, TRUE)
ON CONFLICT (email_address) DO NOTHING;

-- Row Level Security (RLS) policies
ALTER TABLE sas_tour_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_apartment_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_car_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_volunteering_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_contact_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_notification_emails ENABLE ROW LEVEL SECURITY;

-- Create policies for admin access (you'll need to adjust based on your auth setup)
CREATE POLICY "Admin can view all tour bookings" ON sas_tour_bookings FOR SELECT USING (true);
CREATE POLICY "Admin can insert tour bookings" ON sas_tour_bookings FOR INSERT WITH CHECK (true);
CREATE POLICY "Admin can update tour bookings" ON sas_tour_bookings FOR UPDATE USING (true);

CREATE POLICY "Admin can view all apartment bookings" ON sas_apartment_bookings FOR SELECT USING (true);
CREATE POLICY "Admin can insert apartment bookings" ON sas_apartment_bookings FOR INSERT WITH CHECK (true);
CREATE POLICY "Admin can update apartment bookings" ON sas_apartment_bookings FOR UPDATE USING (true);

CREATE POLICY "Admin can view all car bookings" ON sas_car_bookings FOR SELECT USING (true);
CREATE POLICY "Admin can insert car bookings" ON sas_car_bookings FOR INSERT WITH CHECK (true);
CREATE POLICY "Admin can update car bookings" ON sas_car_bookings FOR UPDATE USING (true);

CREATE POLICY "Admin can view all volunteering applications" ON sas_volunteering_applications FOR SELECT USING (true);
CREATE POLICY "Admin can insert volunteering applications" ON sas_volunteering_applications FOR INSERT WITH CHECK (true);
CREATE POLICY "Admin can update volunteering applications" ON sas_volunteering_applications FOR UPDATE USING (true);

CREATE POLICY "Admin can view all contact submissions" ON sas_contact_submissions FOR SELECT USING (true);
CREATE POLICY "Admin can insert contact submissions" ON sas_contact_submissions FOR INSERT WITH CHECK (true);
CREATE POLICY "Admin can update contact submissions" ON sas_contact_submissions FOR UPDATE USING (true);

CREATE POLICY "Admin can manage notification emails" ON sas_notification_emails FOR ALL USING (true);

-- Grant necessary permissions to service role
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;

-- Success message
SELECT 'Booking system database setup completed successfully!' as message;
