import React, { useState } from 'react';
import { Heart, X } from 'lucide-react';

const DonateButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const donationOptions = [
    { amount: 10, description: "Support local conservation" },
    { amount: 25, description: "Help protect wildlife" },
    { amount: 50, description: "Fund community projects" },
    { amount: 100, description: "Sponsor education programs" },
  ];

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors duration-200 shadow-md hover:shadow-lg flex items-center space-x-2"
      >
        <Heart className="w-4 h-4" />
        <span>Donate</span>
      </button>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6 relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
            >
              <X className="w-5 h-5" />
            </button>

            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Make a Donation</h3>
              <p className="text-gray-600 mt-2">Support our conservation efforts and community projects</p>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6">
              {donationOptions.map((option) => (
                <button
                  key={option.amount}
                  className="border border-green-200 hover:border-green-500 rounded-lg p-4 text-center transition-colors duration-200 hover:bg-green-50"
                >
                  <div className="text-xl font-bold text-green-600">${option.amount}</div>
                  <div className="text-sm text-gray-600 mt-1">{option.description}</div>
                </button>
              ))}
            </div>

            <div className="space-y-4">
              <input
                type="number"
                placeholder="Custom amount"
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
              <button className="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors duration-200">
                Complete Donation
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DonateButton;
