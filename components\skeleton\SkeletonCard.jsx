import React from 'react';
import SkeletonBase from './SkeletonBase';

/**
 * Skeleton Card Component for blog posts, packages, and products
 * Replicates the structure of actual cards with shimmer animation
 */
const SkeletonCard = ({ 
  variant = 'blog', // 'blog', 'package', 'product', 'mini-package'
  className = '',
  showPrice = false,
  showButton = true,
  imageHeight = 'h-48'
}) => {
  const getCardHeight = () => {
    switch (variant) {
      case 'package':
        return 'h-[27rem]';
      case 'mini-package':
        return 'h-80';
      case 'product':
        return 'h-96';
      default:
        return 'h-auto';
    }
  };

  return (
    <div className={`
      bg-[var(--secondary-background)] shadow-lg overflow-hidden
      ${getCardHeight()} text-left flex flex-col ${className}
      animate-pulse transition-all duration-300 ease-in-out
    `}>
      {/* Image Skeleton */}
      <div className={`relative ${imageHeight} flex-shrink-0`}>
        <SkeletonBase 
          width="w-full" 
          height="h-full" 
          rounded="rounded-none"
        />
        
        {/* Price Badge Skeleton (for packages) */}
        {showPrice && (
          <div className="absolute top-4 right-4 bg-[var(--secondary-background)] rounded-lg px-3 py-2 shadow-md">
            <SkeletonBase width="w-8" height="h-3" className="mb-1" />
            <SkeletonBase width="w-16" height="h-4" />
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="p-4 flex-1 flex flex-col">
        {/* Title Skeleton */}
        <SkeletonBase width="w-full" height="h-5" className="mb-3" />
        <SkeletonBase width="w-3/4" height="h-5" className="mb-3" />
        
        {/* Description Skeleton */}
        <div className="flex-1 mb-4">
          <SkeletonBase width="w-full" height="h-3" className="mb-2" />
          <SkeletonBase width="w-full" height="h-3" className="mb-2" />
          <SkeletonBase width="w-2/3" height="h-3" />
        </div>

        {/* Button Skeleton */}
        {showButton && (
          <SkeletonBase 
            width="w-full" 
            height="h-10" 
            rounded="rounded-md"
            className="mt-auto"
          />
        )}
      </div>
    </div>
  );
};

export default SkeletonCard;
