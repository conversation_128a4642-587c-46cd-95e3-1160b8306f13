'use client';
import React, { useState } from 'react';
import { faqData } from '@/components/data/faqData';
import FaqHeader from '@/components/faqs/FaqHeader';
import FaqCategory from '@/components/faqs/FaqCategory';

type OpenItemsType = Record<string, boolean>;

const FaqsClient = () => {
  const [openItems, setOpenItems] = useState<OpenItemsType>({});

  const toggleItem = (categoryIndex: number, questionIndex: number) => {
    const key = `${categoryIndex}-${questionIndex}`;
    setOpenItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="min-h-screen">
      <div className="max-w-full mx-auto bg-[var(--primary-background)] rounded-lg shadow-lg p-8">
        <FaqHeader />

        <div className="space-y-8">
          {faqData.map((category, categoryIndex) => (
            <FaqCategory
              key={categoryIndex}
              category={category.category}
              questions={category.questions}
              openItems={openItems}
              toggleItem={toggleItem}
              categoryIndex={categoryIndex}
            />
          ))}
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <div style={{ backgroundColor: 'var(--btn)' }} className="p-6 text-white shadow-md">
            <h3 className="text-2xl font-bold mb-2">Ready to Explore Africa in a Way That Matters?</h3>
            <p className="text-lg mb-2">This isn&apos;t just another trip. It&apos;s a transformational experience.</p>
            <p className="text-sm opacity-90 mb-6">
              Let Swift Africa Safaris take you on a journey that changes lives yours and those you meet along the way.
            </p>
            <div className="flex justify-center gap-4">
              <a href="/tailor-tour" className="px-6 py-2 bg-white text-[var(--btn)] font-semibold rounded-lg hover:bg-opacity-90 transition-all">
                Tailor Your Tour
              </a>
              <a href="/tours" className="px-6 py-2 border-2 border-white text-white font-semibold rounded-lg hover:bg-white hover:text-[var(--btn)] transition-all">
                Browse Tour Package
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FaqsClient;
