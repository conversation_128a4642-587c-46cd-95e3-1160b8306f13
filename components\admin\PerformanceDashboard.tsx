'use client'

import React, { useState, useEffect } from 'react'
import { analytics } from '@/lib/analytics'

interface CoreWebVital {
  status: 'good' | 'needs-improvement' | 'poor' | 'no_data'
  count: number
  average: number
  p75: number
  p95: number
  min: number
  max: number
}

interface PerformanceAlert {
  metric: string
  value: number
  severity: 'warning' | 'critical'
  timestamp: string
  url: string
}

interface DashboardData {
  period_days: number
  generated_at: string
  core_web_vitals: Record<string, CoreWebVital>
  page_performance: Array<{
    url: string
    metrics_count: number
    avg_lcp: number
    avg_fid: number
    avg_cls: number
    performance_score: number
  }>
  device_analysis: Array<{
    device: string
    count: number
    avg_lcp: number
    avg_fid: number
    avg_cls: number
  }>
  health_score: number
  recommendations: string[]
  alerts?: {
    total: number
    critical: number
    warnings: number
    recent: PerformanceAlert[]
  }
  sessions?: {
    total: number
    avg_duration: number
    avg_page_views: number
    avg_interactions: number
    bounce_rate: number
  }
}

export default function PerformanceDashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState(7)
  const [realTimeAlerts, setRealTimeAlerts] = useState<PerformanceAlert[]>([])

  useEffect(() => {
    loadDashboardData()
    
    // Set up real-time alert monitoring
    const alertInterval = setInterval(checkForNewAlerts, 30000) // Check every 30 seconds
    
    return () => clearInterval(alertInterval)
  }, [selectedPeriod])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/analytics/dashboard?days=${selectedPeriod}&include_alerts=true&include_sessions=true`)
      
      if (!response.ok) {
        throw new Error('Failed to load dashboard data')
      }
      
      const data = await response.json()
      setDashboardData(data)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const checkForNewAlerts = async () => {
    try {
      const response = await fetch('/api/analytics/alerts?hours=1')
      if (response.ok) {
        const data = await response.json()
        setRealTimeAlerts(data.alerts.slice(0, 5)) // Show last 5 alerts
      }
    } catch (error) {
      console.error('Failed to check for new alerts:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-100'
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-100'
      case 'poor': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading dashboard</h3>
            <div className="mt-2 text-sm text-red-700">{error}</div>
            <button 
              onClick={loadDashboardData}
              className="mt-3 bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (!dashboardData) return null

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Performance Dashboard</h1>
          <p className="text-gray-600">Real-time performance monitoring and analytics</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(Number(e.target.value))}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value={1}>Last 24 hours</option>
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
          
          <button
            onClick={loadDashboardData}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Health Score */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Overall Health Score</h2>
            <p className="text-gray-600">Based on Core Web Vitals and page performance</p>
          </div>
          <div className={`text-4xl font-bold ${getHealthScoreColor(dashboardData.health_score)}`}>
            {dashboardData.health_score}/100
          </div>
        </div>
      </div>

      {/* Real-time Alerts */}
      {realTimeAlerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-red-800 mb-3">Recent Performance Alerts</h3>
          <div className="space-y-2">
            {realTimeAlerts.map((alert, index) => (
              <div key={index} className="flex items-center justify-between bg-white p-3 rounded border">
                <div>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    alert.severity === 'critical' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {alert.severity}
                  </span>
                  <span className="ml-2 text-sm font-medium">{alert.metric}</span>
                  <span className="ml-2 text-sm text-gray-600">
                    {alert.value}{alert.metric.includes('CLS') ? '' : 'ms'} on {alert.url}
                  </span>
                </div>
                <div className="text-xs text-gray-500">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Core Web Vitals */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Core Web Vitals</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Object.entries(dashboardData.core_web_vitals).map(([vital, data]) => (
            <div key={vital} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">{vital}</h3>
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(data.status)}`}>
                  {data.status.replace('-', ' ')}
                </span>
              </div>
              <div className="space-y-1 text-sm">
                <div>Avg: <span className="font-medium">{data.average}{vital === 'CLS' ? '' : 'ms'}</span></div>
                <div>P75: <span className="font-medium">{data.p75}{vital === 'CLS' ? '' : 'ms'}</span></div>
                <div className="text-gray-500">Count: {data.count}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Page Performance */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Page Performance</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Page</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LCP</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FID</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CLS</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metrics</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {dashboardData.page_performance.slice(0, 10).map((page, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {page.url}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      page.performance_score >= 90 ? 'bg-green-100 text-green-800' :
                      page.performance_score >= 70 ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {page.performance_score}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{page.avg_lcp}ms</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{page.avg_fid}ms</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{page.avg_cls}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{page.metrics_count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Device Analysis */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Device Performance</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {dashboardData.device_analysis.map((device, index) => (
            <div key={index} className="border rounded-lg p-4">
              <h3 className="font-medium text-gray-900 capitalize mb-2">{device.device}</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>LCP:</span>
                  <span className="font-medium">{device.avg_lcp}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>FID:</span>
                  <span className="font-medium">{device.avg_fid}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>CLS:</span>
                  <span className="font-medium">{device.avg_cls}</span>
                </div>
                <div className="flex justify-between text-gray-500">
                  <span>Samples:</span>
                  <span>{device.count}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recommendations */}
      {dashboardData.recommendations.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 mb-4">Performance Recommendations</h2>
          <ul className="space-y-2">
            {dashboardData.recommendations.map((recommendation, index) => (
              <li key={index} className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                <span className="text-blue-800">{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Session Analytics */}
      {dashboardData.sessions && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Session Analytics</h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{dashboardData.sessions.total}</div>
              <div className="text-sm text-gray-600">Total Sessions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{dashboardData.sessions.avg_duration}s</div>
              <div className="text-sm text-gray-600">Avg Duration</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{dashboardData.sessions.avg_page_views}</div>
              <div className="text-sm text-gray-600">Avg Page Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{dashboardData.sessions.avg_interactions}</div>
              <div className="text-sm text-gray-600">Avg Interactions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{dashboardData.sessions.bounce_rate}%</div>
              <div className="text-sm text-gray-600">Bounce Rate</div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
