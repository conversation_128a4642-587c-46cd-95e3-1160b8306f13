// Email service for Swift Africa Safaris
// This service handles all email communications for bookings and notifications

import nodemailer from 'nodemailer';
import { createClient } from '@supabase/supabase-js';

interface BookingData {
  id: string;
  booking_reference: string;
  package_title: string;
  package_type: string;
  full_name: string;
  email: string;
  phone: string;
  number_of_people: number;
  check_in_date: string;
  check_out_date?: string;
  special_requests?: string;
  amount: number;
  created_at: string;
}

interface MiniPackageBookingData {
  id: string;
  booking_reference: string;
  mini_package_title: string;
  package_type: string;
  package_price: number;
  full_name: string;
  email: string;
  phone: string;
  number_of_travelers: number;
  preferred_travel_date: string;
  special_requests?: string;
  created_at: string;
}

interface EmailConfig {
  adminEmail: string;
  fromEmail: string;
  companyName: string;
  websiteUrl: string;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Create Nodemailer transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Get email configuration from database
async function getEmailConfig(): Promise<EmailConfig> {
  try {
    const { data, error } = await supabase
      .from('sas_email_config')
      .select('setting_key, setting_value');

    if (error) {
      console.error('Error fetching email config:', error);
      // Return default config if database fetch fails
      return {
        adminEmail: process.env.EMAIL_ADMIN_DEFAULT || '<EMAIL>',
        fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
        companyName: 'Swift Africa Safaris',
        websiteUrl: 'https://swiftafricasafaris.com'
      };
    }

    const config: EmailConfig = {
      adminEmail: process.env.EMAIL_ADMIN_DEFAULT || '<EMAIL>',
      fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
      companyName: 'Swift Africa Safaris',
      websiteUrl: 'https://swiftafricasafaris.com'
    };

    // Map database values to config
    data?.forEach(item => {
      switch (item.setting_key) {
        case 'admin_email':
          config.adminEmail = item.setting_value;
          break;
        case 'from_email':
          config.fromEmail = item.setting_value;
          break;
        case 'company_name':
          config.companyName = item.setting_value;
          break;
        case 'website_url':
          config.websiteUrl = item.setting_value;
          break;
      }
    });

    return config;
  } catch (error) {
    console.error('Error in getEmailConfig:', error);
    // Return default config on error
    return {
      adminEmail: process.env.EMAIL_ADMIN_DEFAULT || '<EMAIL>',
      fromEmail: process.env.EMAIL_FROM || '<EMAIL>',
      companyName: 'Swift Africa Safaris',
      websiteUrl: 'https://swiftafricasafaris.com'
    };
  }
}

// Send booking confirmation email to customer
export async function sendBookingConfirmationEmail(booking: BookingData): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const emailContent = generateCustomerConfirmationEmail(booking, emailConfig);
    const transporter = createTransporter();

    const emailData = {
      from: emailConfig.fromEmail,
      to: booking.email,
      subject: `Booking Confirmation - ${booking.package_title} (Ref: ${booking.booking_reference})`,
      html: emailContent,
      text: stripHtml(emailContent)
    };

    console.log('Sending customer confirmation email to:', booking.email);

    const result = await transporter.sendMail(emailData);
    console.log('Customer email sent successfully:', result.messageId);

    return true;
  } catch (error) {
    console.error('Error sending customer confirmation email:', error);
    return false;
  }
}

// Send booking notification email to admin
export async function sendAdminNotificationEmail(booking: BookingData): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const emailContent = generateAdminNotificationEmail(booking, emailConfig);
    const transporter = createTransporter();

    const emailData = {
      from: emailConfig.fromEmail,
      to: emailConfig.adminEmail,
      subject: `New Booking Received - ${booking.package_title} (Ref: ${booking.booking_reference})`,
      html: emailContent,
      text: stripHtml(emailContent)
    };

    console.log('Sending admin notification email to:', emailConfig.adminEmail);

    const result = await transporter.sendMail(emailData);
    console.log('Admin email sent successfully:', result.messageId);

    return true;
  } catch (error) {
    console.error('Error sending admin notification email:', error);
    return false;
  }
}

// Generate customer confirmation email HTML
function generateCustomerConfirmationEmail(booking: BookingData, emailConfig: EmailConfig): string {
  const checkInDate = new Date(booking.check_in_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const checkOutDate = booking.check_out_date 
    ? new Date(booking.check_out_date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Booking Confirmation</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        .button { display: inline-block; background-color: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${emailConfig.companyName}</h1>
          <h2>Booking Confirmation</h2>
        </div>
        
        <div class="content">
          <p>Dear ${booking.full_name},</p>
          
          <p>Thank you for your booking request! We have received your safari booking and our team will contact you within 24 hours to confirm all details and arrange payment.</p>
          
          <div class="booking-details">
            <h3>Booking Details</h3>
            <p><strong>Booking Reference:</strong> ${booking.booking_reference}</p>
            <p><strong>Package:</strong> ${booking.package_title}</p>
            <p><strong>Package Type:</strong> ${booking.package_type.charAt(0).toUpperCase() + booking.package_type.slice(1)}</p>
            <p><strong>Number of Travelers:</strong> ${booking.number_of_people}</p>
            <p><strong>Check-in Date:</strong> ${checkInDate}</p>
            ${checkOutDate ? `<p><strong>Check-out Date:</strong> ${checkOutDate}</p>` : ''}
            <p><strong>Estimated Total:</strong> $${booking.amount}</p>
            ${booking.special_requests ? `<p><strong>Special Requests:</strong> ${booking.special_requests}</p>` : ''}
          </div>
          
          <div class="booking-details">
            <h3>Contact Information</h3>
            <p><strong>Email:</strong> ${booking.email}</p>
            <p><strong>Phone:</strong> ${booking.phone}</p>
          </div>
          
          <p><strong>Next Steps:</strong></p>
          <ul>
            <li>Our team will contact you within 24 hours</li>
            <li>We'll confirm all package details and pricing</li>
            <li>Payment instructions will be provided</li>
            <li>Final itinerary will be sent upon payment confirmation</li>
          </ul>
          
          <p>If you have any immediate questions, please contact us at ${emailConfig.adminEmail}</p>

          <a href="${emailConfig.websiteUrl}" class="button">Visit Our Website</a>
        </div>
        
        <div class="footer">
          <p>© ${new Date().getFullYear()} ${emailConfig.companyName}. All rights reserved.</p>
          <p>This is an automated message. Please do not reply to this email.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate admin notification email HTML
function generateAdminNotificationEmail(booking: BookingData, emailConfig: EmailConfig): string {
  const checkInDate = new Date(booking.check_in_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const checkOutDate = booking.check_out_date 
    ? new Date(booking.check_out_date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : null;

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Booking Notification</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Booking Alert</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>
        
        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New booking received - please contact customer within 24 hours
          </div>
          
          <div class="booking-details">
            <h3>Booking Information</h3>
            <p><strong>Booking Reference:</strong> ${booking.booking_reference}</p>
            <p><strong>Package:</strong> ${booking.package_title}</p>
            <p><strong>Package Type:</strong> ${booking.package_type.charAt(0).toUpperCase() + booking.package_type.slice(1)}</p>
            <p><strong>Booking Date:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
          </div>
          
          <div class="booking-details">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> ${booking.full_name}</p>
            <p><strong>Email:</strong> <a href="mailto:${booking.email}">${booking.email}</a></p>
            <p><strong>Phone:</strong> <a href="tel:${booking.phone}">${booking.phone}</a></p>
          </div>
          
          <div class="booking-details">
            <h3>Travel Details</h3>
            <p><strong>Number of Travelers:</strong> ${booking.number_of_people}</p>
            <p><strong>Check-in Date:</strong> ${checkInDate}</p>
            ${checkOutDate ? `<p><strong>Check-out Date:</strong> ${checkOutDate}</p>` : ''}
            <p><strong>Estimated Total:</strong> $${booking.amount}</p>
          </div>

          ${booking.special_requests ? `
            <div class="booking-details">
              <h3>Special Requests</h3>
              <p>${booking.special_requests}</p>
            </div>
          ` : ''}
          
          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Contact customer within 24 hours</li>
              <li>Confirm package availability for requested dates</li>
              <li>Provide detailed pricing and payment instructions</li>
              <li>Send final itinerary upon payment confirmation</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Send test email
export async function sendTestEmail(testEmailAddress: string): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const transporter = createTransporter();

    const emailData = {
      from: emailConfig.fromEmail,
      to: testEmailAddress,
      subject: 'Test Email - Swift Africa Safaris Email Configuration',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">Email Configuration Test</h2>
          <p>This is a test email from ${emailConfig.companyName}.</p>
          <p>If you received this email, your email configuration is working correctly!</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Current Configuration:</h3>
            <p><strong>From Email:</strong> ${emailConfig.fromEmail}</p>
            <p><strong>Admin Email:</strong> ${emailConfig.adminEmail}</p>
            <p><strong>Company Name:</strong> ${emailConfig.companyName}</p>
            <p><strong>Website URL:</strong> ${emailConfig.websiteUrl}</p>
          </div>
          <p style="color: #6b7280; font-size: 12px;">
            This is an automated test message. Please do not reply to this email.
          </p>
        </div>
      `,
      text: `Email Configuration Test - This is a test email from ${emailConfig.companyName}. If you received this email, your email configuration is working correctly!`
    };

    console.log('Sending test email to:', testEmailAddress);

    const result = await transporter.sendMail(emailData);
    console.log('Test email sent successfully:', result.messageId);

    return true;
  } catch (error) {
    console.error('Error sending test email:', error);
    return false;
  }
}

// Send tour booking notification email to admin
export async function sendTourBookingNotificationEmail(booking: any): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'tour']);
    const emailContent = generateTourBookingNotificationEmail(booking, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Tour Booking Request - ${booking.full_name}`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending tour booking notification email to:', email);
      await transporter.sendMail(emailData);
    }

    return true;
  } catch (error) {
    console.error('Error sending tour booking notification email:', error);
    return false;
  }
}

// Send apartment booking notification email to admin
export async function sendApartmentBookingNotificationEmail(booking: any): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'apartment']);
    const emailContent = generateApartmentBookingNotificationEmail(booking, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Apartment Booking Request - ${booking.full_name}`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending apartment booking notification email to:', email);
      await transporter.sendMail(emailData);
    }

    return true;
  } catch (error) {
    console.error('Error sending apartment booking notification email:', error);
    return false;
  }
}

// Send car booking notification email to admin
export async function sendCarBookingNotificationEmail(booking: any): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'car']);
    const emailContent = generateCarBookingNotificationEmail(booking, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Car Booking Request - ${booking.full_name}`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending car booking notification email to:', email);
      await transporter.sendMail(emailData);
    }

    return true;
  } catch (error) {
    console.error('Error sending car booking notification email:', error);
    return false;
  }
}

// Send volunteering notification email to admin
export async function sendVolunteeringNotificationEmail(application: any): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'volunteering']);
    const emailContent = generateVolunteeringNotificationEmail(application, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Volunteering Application - ${application.name}`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending volunteering notification email to:', email);
      await transporter.sendMail(emailData);
    }

    return true;
  } catch (error) {
    console.error('Error sending volunteering notification email:', error);
    return false;
  }
}

// Send contact notification email to admin
export async function sendContactNotificationEmail(contact: any): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'contact']);
    const emailContent = generateContactNotificationEmail(contact, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Contact Form Submission - ${contact.name}`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending contact notification email to:', email);
      await transporter.sendMail(emailData);
    }

    return true;
  } catch (error) {
    console.error('Error sending contact notification email:', error);
    return false;
  }
}

// Get notification emails based on types
async function getNotificationEmails(types: string[]): Promise<string[]> {
  try {
    const { data, error } = await supabase
      .from('sas_notification_emails')
      .select('email_address, notification_types')
      .eq('is_active', true);

    if (error) {
      console.error('Error fetching notification emails:', error);
      return ['<EMAIL>']; // Fallback
    }

    const emails: string[] = [];
    data?.forEach(item => {
      const notificationTypes = item.notification_types || ['all'];
      // Check if any of the requested types match the email's notification types
      if (types.some(type => notificationTypes.includes(type))) {
        emails.push(item.email_address);
      }
    });

    return emails.length > 0 ? emails : ['<EMAIL>'];
  } catch (error) {
    console.error('Error in getNotificationEmails:', error);
    return ['<EMAIL>']; // Fallback
  }
}

// Generate tour booking notification email HTML
function generateTourBookingNotificationEmail(booking: any, emailConfig: EmailConfig): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Tour Booking Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Tour Booking Request</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>

        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New tour booking request received - please contact customer within 24 hours
          </div>

          <div class="booking-details">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> ${booking.full_name}</p>
            <p><strong>Email:</strong> <a href="mailto:${booking.email}">${booking.email}</a></p>
            <p><strong>WhatsApp:</strong> <a href="tel:${booking.whatsapp}">${booking.whatsapp}</a></p>
            <p><strong>Number of People:</strong> ${booking.number_of_people}</p>
            <p><strong>Submitted:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
          </div>

          ${booking.message ? `
            <div class="booking-details">
              <h3>Message</h3>
              <p>${booking.message}</p>
            </div>
          ` : ''}

          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Contact customer within 24 hours</li>
              <li>Discuss tour preferences and requirements</li>
              <li>Provide detailed pricing and itinerary</li>
              <li>Send booking confirmation upon agreement</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate apartment booking notification email HTML
function generateApartmentBookingNotificationEmail(booking: any, emailConfig: EmailConfig): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Apartment Booking Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Apartment Booking Request</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>

        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New apartment booking request received - please contact customer within 24 hours
          </div>

          <div class="booking-details">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> ${booking.full_name}</p>
            <p><strong>Email:</strong> <a href="mailto:${booking.email}">${booking.email}</a></p>
            <p><strong>WhatsApp:</strong> <a href="tel:${booking.whatsapp}">${booking.whatsapp}</a></p>
            <p><strong>Submitted:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
          </div>

          <div class="booking-details">
            <h3>Apartment Requirements</h3>
            <p>${booking.properties}</p>
          </div>

          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Contact customer within 24 hours</li>
              <li>Review apartment requirements and preferences</li>
              <li>Provide suitable apartment options with pricing</li>
              <li>Arrange viewing or send detailed information</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate car booking notification email HTML
function generateCarBookingNotificationEmail(booking: any, emailConfig: EmailConfig): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Car Booking Request</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Car Booking Request</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>

        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New car booking request received - please contact customer within 24 hours
          </div>

          <div class="booking-details">
            <h3>Customer Information</h3>
            <p><strong>Name:</strong> ${booking.full_name}</p>
            <p><strong>Email:</strong> <a href="mailto:${booking.email}">${booking.email}</a></p>
            <p><strong>WhatsApp:</strong> <a href="tel:${booking.whatsapp}">${booking.whatsapp}</a></p>
            <p><strong>Submitted:</strong> ${new Date(booking.created_at).toLocaleString()}</p>
          </div>

          <div class="booking-details">
            <h3>Car Requirements</h3>
            <p>${booking.car_properties}</p>
          </div>

          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Contact customer within 24 hours</li>
              <li>Review car requirements and preferences</li>
              <li>Provide suitable car options with pricing</li>
              <li>Arrange rental details and documentation</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate volunteering notification email HTML
function generateVolunteeringNotificationEmail(application: any, emailConfig: EmailConfig): string {
  const arrivalDate = new Date(application.arrival_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const departureDate = new Date(application.departure_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Volunteering Application</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Volunteering Application</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>

        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New volunteering application received - please review and respond
          </div>

          <div class="booking-details">
            <h3>Applicant Information</h3>
            <p><strong>Name:</strong> ${application.name}</p>
            <p><strong>Email:</strong> <a href="mailto:${application.email}">${application.email}</a></p>
            <p><strong>Submitted:</strong> ${new Date(application.created_at).toLocaleString()}</p>
          </div>

          <div class="booking-details">
            <h3>Volunteering Details</h3>
            <p><strong>Arrival Date:</strong> ${arrivalDate}</p>
            <p><strong>Departure Date:</strong> ${departureDate}</p>
            <p><strong>Duration:</strong> ${Math.ceil((new Date(application.departure_date).getTime() - new Date(application.arrival_date).getTime()) / (1000 * 60 * 60 * 24))} days</p>
          </div>

          ${application.message ? `
            <div class="booking-details">
              <h3>Additional Information</h3>
              <p>${application.message}</p>
            </div>
          ` : ''}

          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Review application and availability</li>
              <li>Contact applicant to discuss requirements</li>
              <li>Provide volunteering program details</li>
              <li>Send approval or additional requirements</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Generate contact notification email HTML
function generateContactNotificationEmail(contact: any, emailConfig: EmailConfig): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Contact Form Submission</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc2626; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background-color: #f9f9f9; }
        .booking-details { background-color: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .urgent { background-color: #fef2f2; border-left: 4px solid #dc2626; padding: 10px; margin: 10px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>New Contact Form Submission</h1>
          <h2>${emailConfig.companyName}</h2>
        </div>

        <div class="content">
          <div class="urgent">
            <strong>Action Required:</strong> New contact form submission received - please respond promptly
          </div>

          <div class="booking-details">
            <h3>Contact Information</h3>
            <p><strong>Name:</strong> ${contact.name}</p>
            <p><strong>Email:</strong> <a href="mailto:${contact.email}">${contact.email}</a></p>
            <p><strong>WhatsApp:</strong> <a href="tel:${contact.whatsapp}">${contact.whatsapp}</a></p>
            <p><strong>Submitted:</strong> ${new Date(contact.created_at).toLocaleString()}</p>
          </div>

          ${contact.message ? `
            <div class="booking-details">
              <h3>Message</h3>
              <p>${contact.message}</p>
            </div>
          ` : ''}

          <div class="booking-details">
            <h3>Next Steps</h3>
            <ul>
              <li>Respond to the customer promptly</li>
              <li>Address their inquiry or questions</li>
              <li>Provide relevant information or assistance</li>
              <li>Follow up if necessary</li>
            </ul>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

// =====================================================
// MINI PACKAGE BOOKING EMAIL FUNCTIONS
// =====================================================

// Note: Client confirmation emails have been disabled per requirements
// Only admin notifications are sent for mini-package bookings

// Send mini package booking notification email to admin
export async function sendMiniPackageBookingNotificationEmail(booking: MiniPackageBookingData): Promise<boolean> {
  try {
    const emailConfig = await getEmailConfig();
    const notificationEmails = await getNotificationEmails(['all', 'mini-package']);
    const emailContent = generateMiniPackageBookingNotificationEmail(booking, emailConfig);
    const transporter = createTransporter();

    // Send to all notification emails
    for (const email of notificationEmails) {
      const emailData = {
        from: emailConfig.fromEmail,
        to: email,
        subject: `New Mini Package Booking - ${booking.mini_package_title} (Ref: ${booking.booking_reference})`,
        html: emailContent,
        text: stripHtml(emailContent)
      };

      console.log('Sending mini package booking notification email to:', email);
      await transporter.sendMail(emailData);
    }

    // Update notification sent status
    await supabase
      .from('sas_mini_package_bookings')
      .update({
        admin_notification_sent: true,
        notification_emails_sent: notificationEmails
      })
      .eq('id', booking.id);

    console.log('Mini package booking notification emails sent successfully');
    return true;

  } catch (error) {
    console.error('Error sending mini package booking notification emails:', error);
    return false;
  }
}

// Note: Client confirmation email template removed per requirements
// Only admin notification emails are sent for mini-package bookings

// Generate mini package booking notification email HTML for admin
function generateMiniPackageBookingNotificationEmail(booking: MiniPackageBookingData, config: EmailConfig): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Mini Package Booking</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .booking-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
        .booking-details h3 { color: #dc2626; margin-top: 0; }
        .detail-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
        .detail-row:last-child { border-bottom: none; }
        .label { font-weight: bold; color: #555; }
        .value { color: #333; }
        .urgent { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🚨 New Mini Package Booking</h1>
        <p>Action Required - Customer Booking Received</p>
      </div>

      <div class="content">
        <div class="urgent">
          <h3>⚡ Immediate Action Required</h3>
          <p>A new mini package booking has been received and requires your attention. Please review and respond to the customer within 24 hours.</p>
        </div>

        <div class="booking-details">
          <h3>📋 Booking Information</h3>
          <div class="detail-row">
            <span class="label">Booking Reference:</span>
            <span class="value"><strong>${booking.booking_reference}</strong></span>
          </div>
          <div class="detail-row">
            <span class="label">Mini Package:</span>
            <span class="value">${booking.mini_package_title}</span>
          </div>
          <div class="detail-row">
            <span class="label">Package Type:</span>
            <span class="value">${booking.package_type}</span>
          </div>
          <div class="detail-row">
            <span class="label">Price:</span>
            <span class="value">$${booking.package_price}</span>
          </div>
          <div class="detail-row">
            <span class="label">Booking Date:</span>
            <span class="value">${new Date(booking.created_at).toLocaleDateString()}</span>
          </div>
        </div>

        <div class="booking-details">
          <h3>👤 Customer Information</h3>
          <div class="detail-row">
            <span class="label">Name:</span>
            <span class="value">${booking.full_name}</span>
          </div>
          <div class="detail-row">
            <span class="label">Email:</span>
            <span class="value">${booking.email}</span>
          </div>
          <div class="detail-row">
            <span class="label">Phone:</span>
            <span class="value">${booking.phone}</span>
          </div>
          <div class="detail-row">
            <span class="label">Number of Travelers:</span>
            <span class="value">${booking.number_of_travelers}</span>
          </div>
          <div class="detail-row">
            <span class="label">Preferred Travel Date:</span>
            <span class="value">${new Date(booking.preferred_travel_date).toLocaleDateString()}</span>
          </div>
        </div>

        ${booking.special_requests ? `
          <div class="booking-details">
            <h3>📝 Special Requests</h3>
            <p>${booking.special_requests}</p>
          </div>
        ` : ''}

        <div class="booking-details">
          <h3>🎯 Next Steps</h3>
          <ul>
            <li>Contact the customer within 24 hours</li>
            <li>Confirm availability for the requested travel date</li>
            <li>Provide detailed itinerary and preparation information</li>
            <li>Send payment instructions</li>
            <li>Update booking status in the admin panel</li>
          </ul>
        </div>

        <div class="footer">
          <p>This notification was sent automatically when a new mini package booking was received.</p>
          <p>&copy; ${new Date().getFullYear()} ${config.companyName} Admin System</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

// Utility function to strip HTML tags for plain text email
function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
}
