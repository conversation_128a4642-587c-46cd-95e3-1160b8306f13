'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Plus,
  Type,
  Image,
  Quote,
  List,
  ListOrdered,
  Minus,
  Trash2,
  GripVertical,
  Eye,
  EyeOff,
  Upload,
  Loader2
} from 'lucide-react';
import RichTextEditor from './RichTextEditor';

interface ContentBlock {
  id: string;
  block_type: 'paragraph' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'image' | 'video' | 'listing' | 'quote' | 'divider';
  content: any;
  sort_order: number;
}

interface BlogContentEditorProps {
  initialContent?: ContentBlock[];
  onSave?: (content: ContentBlock[]) => void;
}

const BlogContentEditor: React.FC<BlogContentEditorProps> = ({
  initialContent = [],
  onSave
}) => {
  const [blocks, setBlocks] = useState<ContentBlock[]>(initialContent);
  const [selectedBlock, setSelectedBlock] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [showAddMenu, setShowAddMenu] = useState(false);
  const [uploadingImages, setUploadingImages] = useState<Set<string>>(new Set());
  const menuRef = useRef<HTMLDivElement>(null);

  // Auto-save effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSave) {
        onSave(blocks);
      }
    }, 1000); // 1 second debounce

    return () => clearTimeout(timeoutId);
  }, [blocks, onSave]);

  // Close add menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowAddMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const blockTypes = [
    { type: 'paragraph', icon: <Type size={16} />, label: 'Paragraph' },
    { type: 'h2', icon: <Type size={16} />, label: 'Heading 2' },
    { type: 'h3', icon: <Type size={16} />, label: 'Heading 3' },
    { type: 'h4', icon: <Type size={16} />, label: 'Heading 4' },
    { type: 'image', icon: <Image size={16} />, label: 'Image' },
    { type: 'quote', icon: <Quote size={16} />, label: 'Quote' },
    { type: 'listing', icon: <List size={16} />, label: 'Bullet List' },
    { type: 'divider', icon: <Minus size={16} />, label: 'Divider' },
  ];

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'paragraph':
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        return { content: '' };
      case 'image':
        return { src: '', alt: '', caption: '', width: 'full' };
      case 'video':
        return { src: '', poster: '', caption: '', width: 'lg' };
      case 'listing':
        return { items: [''], listType: 'unordered' };
      case 'quote':
        return { content: '', author: '', source: '' };
      case 'divider':
        return { style: 'line' };
      default:
        return { content: '' };
    }
  };

  const addBlock = (type: string) => {
    const newBlock: ContentBlock = {
      id: Date.now().toString(),
      block_type: type as ContentBlock['block_type'],
      content: getDefaultContent(type),
      sort_order: blocks.length
    };
    setBlocks([...blocks, newBlock]);
    setShowAddMenu(false);
    setSelectedBlock(newBlock.id);
  };

  const updateBlock = (id: string, content: any) => {
    setBlocks(blocks.map(block => 
      block.id === id ? { ...block, content } : block
    ));
  };

  const deleteBlock = (id: string) => {
    setBlocks(blocks.filter(block => block.id !== id));
    if (selectedBlock === id) {
      setSelectedBlock(null);
    }
  };

  const moveBlock = (id: string, direction: 'up' | 'down' | 'to', targetIndex?: number) => {
    const currentIndex = blocks.findIndex(block => block.id === id);
    if (currentIndex === -1) return;

    const newBlocks = [...blocks];
    const [movedBlock] = newBlocks.splice(currentIndex, 1);

    if (direction === 'to' && typeof targetIndex === 'number') {
      // Insert at specific position
      newBlocks.splice(targetIndex, 0, movedBlock);
    } else if (direction === 'up' && currentIndex > 0) {
      newBlocks.splice(currentIndex - 1, 0, movedBlock);
    } else if (direction === 'down' && currentIndex < blocks.length - 1) {
      newBlocks.splice(currentIndex + 1, 0, movedBlock);
    } else {
      return; // No valid move
    }

    // Update sort_order
    newBlocks.forEach((block, idx) => {
      block.sort_order = idx;
    });

    setBlocks(newBlocks);
  };

  const handleImageUpload = async (blockId: string, file: File) => {
    setUploadingImages(prev => new Set(prev).add(blockId));
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('bucket', 'sas-blog-images');
      formData.append('folder', 'content');

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        const block = blocks.find(b => b.id === blockId);
        if (block) {
          updateBlock(blockId, {
            ...block.content,
            src: data.data.url,
            alt: block.content.alt || file.name.replace(/\.[^/.]+$/, '')
          });
        }
      } else {
        alert('Failed to upload image: ' + data.error);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload image');
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(blockId);
        return newSet;
      });
    }
  };

  const renderBlockContent = (block: ContentBlock) => {
    const isSelected = selectedBlock === block.id;
    const isUploading = uploadingImages.has(block.id);

    switch (block.block_type) {
      case 'paragraph':
        return previewMode ? (
          <div
            className="blog-content prose prose-gray max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: block.content.content || 'Empty paragraph' }}
          />
        ) : (
          <RichTextEditor
            value={block.content.content || ''}
            onChange={(content: string) => updateBlock(block.id, { content })}
            placeholder="Start typing your paragraph..."
            height={150}
            className="focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent"
          />
        );

      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        const getHeadingClasses = (type: string) => {
          const classes = {
            h2: 'text-3xl font-bold text-gray-900 mb-4',
            h3: 'text-2xl font-bold text-gray-900 mb-3',
            h4: 'text-xl font-bold text-gray-900 mb-3',
            h5: 'text-lg font-bold text-gray-900 mb-2',
            h6: 'text-base font-bold text-gray-900 mb-2'
          };
          return classes[type as keyof typeof classes] || classes.h2;
        };

        return previewMode ? (
          <div
            className={`blog-content prose prose-gray max-w-none ${getHeadingClasses(block.block_type)}`}
            dangerouslySetInnerHTML={{ __html: block.content.content || 'Heading' }}
          />
        ) : (
          <input
            type="text"
            value={block.content.content || ''}
            onChange={(e) => updateBlock(block.id, { content: e.target.value })}
            placeholder={`${block.block_type.toUpperCase()} heading...`}
            className="w-full border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xl font-bold"
          />
        );

      case 'image':
        return (
          <div className="space-y-3">
            {block.content.src ? (
              <div className="relative">
                <img
                  src={block.content.src}
                  alt={block.content.alt || ''}
                  className="max-w-full h-auto rounded-lg"
                />
                {!previewMode && (
                  <button
                    onClick={() => updateBlock(block.id, { ...block.content, src: '' })}
                    className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600"
                  >
                    <Trash2 size={12} />
                  </button>
                )}
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                {isUploading ? (
                  <div className="flex items-center justify-center">
                    <Loader2 size={24} className="animate-spin text-blue-500" />
                    <span className="ml-2 text-gray-600">Uploading...</span>
                  </div>
                ) : (
                  <div>
                    <Upload size={32} className="mx-auto text-gray-400 mb-2" />
                    <p className="text-gray-600 mb-3">Click to upload an image</p>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) handleImageUpload(block.id, file);
                      }}
                      className="hidden"
                      id={`image-upload-${block.id}`}
                    />
                    <label
                      htmlFor={`image-upload-${block.id}`}
                      className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 cursor-pointer"
                    >
                      <Upload size={16} className="mr-2" />
                      Choose Image
                    </label>
                  </div>
                )}
              </div>
            )}
            
            {!previewMode && (
              <div className="space-y-2">
                <input
                  type="text"
                  value={block.content.alt || ''}
                  onChange={(e) => updateBlock(block.id, { ...block.content, alt: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="Alt text (required for accessibility)"
                />
                <input
                  type="text"
                  value={block.content.caption || ''}
                  onChange={(e) => updateBlock(block.id, { ...block.content, caption: e.target.value })}
                  className="w-full p-2 border border-gray-300 rounded text-sm"
                  placeholder="Image caption (optional)"
                />
              </div>
            )}
          </div>
        );

      case 'quote':
        return (
          <div className="border-l-4 border-blue-500 pl-4 py-2 bg-blue-50 rounded-r-lg">
            {previewMode ? (
              <div>
                <blockquote
                  className="blog-content text-lg italic text-gray-700 mb-2 prose prose-gray max-w-none"
                  dangerouslySetInnerHTML={{ __html: block.content.content || 'Quote text' }}
                />
                {(block.content.author || block.content.source) && (
                  <cite className="text-sm text-gray-600">
                    — {block.content.author}
                    {block.content.source && `, ${block.content.source}`}
                  </cite>
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <textarea
                  value={block.content.content || ''}
                  onChange={(e) => updateBlock(block.id, { ...block.content, content: e.target.value })}
                  placeholder="Enter quote text..."
                  className="w-full border border-gray-300 rounded-lg p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none italic"
                  rows={4}
                />
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={block.content.author || ''}
                    onChange={(e) => updateBlock(block.id, { ...block.content, author: e.target.value })}
                    className="flex-1 p-1 border-none outline-none bg-transparent text-sm"
                    placeholder="Author"
                  />
                  <input
                    type="text"
                    value={block.content.source || ''}
                    onChange={(e) => updateBlock(block.id, { ...block.content, source: e.target.value })}
                    className="flex-1 p-1 border-none outline-none bg-transparent text-sm"
                    placeholder="Source"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 'listing':
        const items = Array.isArray(block.content.items) ? block.content.items : [''];
        return (
          <div className="space-y-2">
            {!previewMode && (
              <div className="flex gap-2 mb-3">
                <button
                  onClick={() => updateBlock(block.id, { ...block.content, listType: 'unordered' })}
                  className={`px-3 py-1 rounded text-sm ${
                    block.content.listType === 'unordered' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700'
                  }`}
                >
                  Bullet List
                </button>
                <button
                  onClick={() => updateBlock(block.id, { ...block.content, listType: 'ordered' })}
                  className={`px-3 py-1 rounded text-sm ${
                    block.content.listType === 'ordered' 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-700'
                  }`}
                >
                  Numbered List
                </button>
              </div>
            )}
            
            {previewMode ? (
              block.content.listType === 'ordered' ? (
                <ol className="list-decimal list-inside space-y-1">
                  {items.map((item: string, index: number) => (
                    <li key={index} className="text-gray-700">{item}</li>
                  ))}
                </ol>
              ) : (
                <ul className="list-disc list-inside space-y-1">
                  {items.map((item: string, index: number) => (
                    <li key={index} className="text-gray-700">{item}</li>
                  ))}
                </ul>
              )
            ) : (
              <div className="space-y-2">
                {items.map((item: string, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-gray-400 text-sm w-4">
                      {block.content.listType === 'ordered' ? `${index + 1}.` : '•'}
                    </span>
                    <input
                      type="text"
                      value={item}
                      onChange={(e) => {
                        const newItems = [...items];
                        newItems[index] = e.target.value;
                        updateBlock(block.id, { ...block.content, items: newItems });
                      }}
                      className="flex-1 p-1 border-none outline-none bg-transparent"
                      placeholder="List item..."
                    />
                    {items.length > 1 && (
                      <button
                        onClick={() => {
                          const newItems = items.filter((_: string, i: number) => i !== index);
                          updateBlock(block.id, { ...block.content, items: newItems });
                        }}
                        className="text-red-500 hover:text-red-600"
                      >
                        <Trash2 size={14} />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  onClick={() => {
                    const newItems = [...items, ''];
                    updateBlock(block.id, { ...block.content, items: newItems });
                  }}
                  className="text-blue-500 hover:text-blue-600 text-sm font-medium"
                >
                  + Add item
                </button>
              </div>
            )}
          </div>
        );

      case 'divider':
        return (
          <div className="py-4">
            <hr className="border-gray-300" />
          </div>
        );

      default:
        return (
          <div className="text-red-500 p-2 bg-red-50 rounded">
            Unknown block type: {block.block_type}
          </div>
        );
    }
  };

  const renderBlock = (block: ContentBlock, index: number) => {
    const isSelected = selectedBlock === block.id;

    return (
      <div
        key={block.id}
        className={`group relative border rounded-lg mb-4 transition-all ${
          isSelected
            ? 'border-blue-500 shadow-md'
            : 'border-gray-200 hover:border-gray-300'
        }`}
        onClick={(e) => {
          // Only select block if clicking outside of content area
          const target = e.target as HTMLElement;
          if (!target.closest('[contenteditable="true"]') &&
              !target.closest('.rich-text-editor') &&
              !target.closest('textarea') &&
              !target.closest('input')) {
            setSelectedBlock(block.id);
          }
        }}
        onDragOver={(e) => {
          e.preventDefault();
          e.dataTransfer.dropEffect = 'move';
        }}
        onDrop={(e) => {
          e.preventDefault();
          const draggedId = e.dataTransfer.getData('text/plain');
          if (draggedId && draggedId !== block.id) {
            moveBlock(draggedId, 'to', index);
          }
        }}
      >
        {/* Block Controls */}
        {!previewMode && (
          <div className="absolute -left-12 top-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <button
              className="p-2 bg-white border border-gray-300 hover:bg-gray-50 rounded shadow-sm text-gray-600 cursor-grab active:cursor-grabbing"
              title="Drag to reorder"
              draggable={true}
              onDragStart={(e) => {
                e.dataTransfer.setData('text/plain', block.id);
                e.dataTransfer.effectAllowed = 'move';
              }}
              onMouseDown={(e) => {
                e.stopPropagation();
              }}
            >
              <GripVertical size={14} />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deleteBlock(block.id);
              }}
              className="p-2 bg-white border border-gray-300 hover:bg-red-50 rounded shadow-sm text-red-500"
              title="Delete block"
              onMouseDown={(e) => e.stopPropagation()}
            >
              <Trash2 size={14} />
            </button>
          </div>
        )}

        {/* Block Content */}
        <div
          className="p-4"
          onMouseDown={(e) => {
            // Prevent block selection when interacting with content
            e.stopPropagation();
          }}
        >
          {renderBlockContent(block)}
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Toolbar */}
      <div className="flex items-center justify-between mb-6 p-4 bg-white rounded-lg border border-gray-200">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold text-gray-900">Content Editor</h3>
          <span className="text-sm text-gray-500">{blocks.length} blocks</span>
        </div>
        <button
          onClick={() => setPreviewMode(!previewMode)}
          className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm font-medium"
        >
          {previewMode ? <EyeOff size={16} /> : <Eye size={16} />}
          {previewMode ? 'Edit' : 'Preview'}
        </button>
      </div>

      {/* Content Blocks */}
      <div className="pl-12">
        {blocks.length === 0 ? (
          <div className="text-center py-12 border-2 border-dashed border-gray-200 rounded-xl bg-gray-50">
            <Type size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Start creating your blog content
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Add content blocks to build your story. You can add text, images, quotes, lists, and more.
            </p>
            <button
              onClick={() => addBlock('paragraph')}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Add your first paragraph
            </button>
          </div>
        ) : (
          blocks.map((block, index) => renderBlock(block, index))
        )}
      </div>

      {/* Add Block Button */}
      {!previewMode && (
        <div className="pl-12 mt-6 relative" ref={menuRef}>
          <button
            onClick={() => setShowAddMenu(!showAddMenu)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
          >
            <Plus size={16} />
            <span className="font-medium">Add Content Block</span>
          </button>

          {/* Add Block Menu */}
          {showAddMenu && (
            <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
              <div className="p-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 px-2">
                  Content Blocks
                </div>
                {blockTypes.map((blockType) => (
                  <button
                    key={blockType.type}
                    onClick={() => addBlock(blockType.type)}
                    className="w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-50 rounded-md transition-colors"
                  >
                    <div className="text-gray-500">{blockType.icon}</div>
                    <span className="text-sm font-medium text-gray-700">
                      {blockType.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BlogContentEditor;
