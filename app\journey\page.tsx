//journey page.tsx
import type { Metadata } from "next";
import Navbar from "@/components/header";
import JourneyHero from "@/components/journey/journeyHero";
import Footer from "@/components/footer";
import SoloJourney from "@/components/journey/soloJourneySection";
import Honeymo<PERSON>J<PERSON>ney from "@/components/journey/honeymoonJourney";
import FamilyJourney from "@/components/journey/familyJourney";
import GroupJourney from "@/components/journey/groupJourney";
import StructuredData from "@/components/common/StructuredData";
import {
  generateMetadata,
  generateBreadcrumbSchema,
  defaultSEO,
} from "@/lib/seo";

export const metadata: Metadata = generateMetadata({
  title: "Your Safari, Your Way – Solo, Family, Group & Honeymoon",
  description:
    "Discover how you can explore Africa your way, with solo adventures, family fun, romantic honeymoons, or group journeys tailored to you.",
  url: "/journey",
  keywords: [
    "personalized African safari",
    "What is the best month to book a Great Migration safari in Tanzania?",
    "Where to find family-friendly safari lodges in Tanzania?",
    "What’s included in Tanzania cultural safari packages for couples?",
    "Are there guided cultural experiences after gorilla trekking in Rwanda?",
    "solo safari adventures",
    "honeymoon safari Africa",
    "family safari tours",
    "group safari packages",
    "custom safari itinerary",
    "tailored safari experiences",
    "African adventure travel",
    "safari for couples",
    "family-friendly safari",
    "group travel Africa",
    "solo travel Africa",
    "romantic safari getaway",
    "adventure travel East Africa",
    "customized wildlife tours",
  ],
  type: "website"
});

export default function Journey() {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: "Home", url: "/" },
    { name: "Journey Types", url: "/journey" },
  ]);

  const journeyPageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Your Safari, Your Way – Solo, Family, Group & Honeymoon",
    description:
      "Discover how you can explore Africa your way, with solo adventures, family fun, romantic honeymoons, or group journeys tailored to you.",
    url: `${defaultSEO.siteUrl}/journey`,
    keywords: [
      "personalized African safari",
      "What is the best month to book a Great Migration safari in Tanzania?",
      "Where to find family-friendly safari lodges in Tanzania?",
      "What’s included in Tanzania cultural safari packages for couples?",
      "Are there guided cultural experiences after gorilla trekking in Rwanda?",
      "solo safari adventures",
      "honeymoon safari Africa",
      "family safari tours",
      "group safari packages",
      "custom safari itinerary",
      "tailored safari experiences",
      "African adventure travel",
      "safari for couples",
      "family-friendly safari",
      "group travel Africa",
      "solo travel Africa",
      "romantic safari getaway",
      "adventure travel East Africa",
      "customized wildlife tours",
    ],
    mainEntity: {
      "@type": "ItemList",
      name: "Safari Journey Types",
      description:
        "Personalized African safari experiences for different types of travelers",
      numberOfItems: 4,
      itemListElement: [
        {
          "@type": "TouristTrip",
          name: "Solo Safari Adventures",
          description:
            "Perfect for independent travelers seeking personal discovery and wildlife encounters",
          touristType: "Solo Traveler",
          position: 1,
        },
        {
          "@type": "TouristTrip",
          name: "Honeymoon Safari Getaways",
          description:
            "Romantic safari experiences for couples celebrating their love in Africa",
          touristType: "Couple",
          position: 2,
        },
        {
          "@type": "TouristTrip",
          name: "Family Safari Adventures",
          description:
            "Family-friendly safari tours designed for travelers of all ages",
          touristType: "Family",
          position: 3,
        },
        {
          "@type": "TouristTrip",
          name: "Group Safari Expeditions",
          description:
            "Customized group adventures for friends, organizations, and special interest groups",
          touristType: "Group",
          position: 4,
        },
      ],
    },
  };

  return (
    <div>
      <StructuredData data={[breadcrumbSchema, journeyPageSchema]} />
      <Navbar />

      {/* Hero Section */}
      <main>
        <JourneyHero />
        <SoloJourney />
        <HoneymoonJourney />
        <FamilyJourney />
        <GroupJourney />
      </main>

      <Footer />
    </div>
  );
}
