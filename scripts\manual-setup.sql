-- Manual Database Setup for Swift Africa Safaris
-- Copy and paste this SQL into your Supabase SQL Editor

-- =====================================================
-- PACKAGES TABLE - Main package information
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_packages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    overview TEXT,
    difficulty TEXT NOT NULL CHECK (difficulty IN ('Easy', 'Moderate', 'Hard', 'Challenging')),
    category TEXT NOT NULL CHECK (category IN ('Wildlife', 'Adventure', 'Cultural', 'Beach', 'Safari', 'Nature', 'Luxury')),
    location TEXT NOT NULL,
    duration TEXT,
    
    -- Pricing structure for different traveler types
    pricing_solo DECIMAL(10,2) NOT NULL DEFAULT 0,
    pricing_honeymoon DECIMAL(10,2) NOT NULL DEFAULT 0,
    pricing_family DECIMAL(10,2) NOT NULL DEFAULT 0,
    pricing_group DECIMAL(10,2) NOT NULL DEFAULT 0,
    
    -- Package status and visibility
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'inactive', 'archived')),

    
    -- Main package image
    image_url TEXT,
    image_alt TEXT DEFAULT '',
    hero_image_url TEXT,
    hero_image_alt TEXT DEFAULT '',
    
    -- SEO and metadata fields (user-inputted)
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[], -- Array of keywords
    og_title TEXT,
    og_description TEXT,
    og_image_url TEXT,
    canonical_url TEXT,
    robots_index TEXT DEFAULT 'index' CHECK (robots_index IN ('index', 'noindex')),
    robots_follow TEXT DEFAULT 'follow' CHECK (robots_follow IN ('follow', 'nofollow')),
    
    -- Schema.org structured data (JSON)
    schema_data JSONB,
    
    -- Package highlights
    highlights TEXT[], -- Array of highlight strings
    
    -- What to pack list
    packing_list TEXT[], -- Array of packing items
    
    -- Includes and excludes
    includes TEXT[], -- Array of included items
    excludes TEXT[], -- Array of excluded items
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- PACKAGE CONTENT BLOCKS - Block-based content editor
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_content_blocks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    block_type TEXT NOT NULL CHECK (block_type IN (
        'paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6',
        'list', 'quote', 'code', 'image', 'divider', 'bulleted-list', 'numbered-list'
    )),
    content TEXT,
    content_data JSONB,
    image_url TEXT,
    image_alt TEXT,
    image_caption TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PACKAGE ITINERARY - Day-by-day itinerary
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_itinerary (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    day_number INTEGER NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    activities TEXT[], -- Array of activities for the day
    accommodation TEXT,
    meals TEXT[], -- Array of meals included
    sort_order INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BOOKINGS TABLE - Customer booking information
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_bookings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    booking_reference TEXT UNIQUE NOT NULL,
    
    -- Package information
    package_id UUID REFERENCES public.sas_packages(id),
    package_title TEXT NOT NULL,
    package_type TEXT NOT NULL CHECK (package_type IN ('solo', 'honeymoon', 'family', 'group')),
    
    -- Customer information
    full_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    number_of_people INTEGER NOT NULL CHECK (number_of_people > 0),
    
    -- Travel dates
    check_in_date DATE NOT NULL,
    check_out_date DATE,
    
    -- Additional information
    special_requests TEXT,
    
    -- Pricing and payment
    amount DECIMAL(10,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'refunded')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PACKAGE IMAGES - Additional package gallery images
-- =====================================================
CREATE TABLE IF NOT EXISTS public.sas_package_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_id UUID REFERENCES public.sas_packages(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_alt TEXT NOT NULL, -- MANDATORY alt text
    caption TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    is_featured BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES for performance optimization
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_sas_packages_slug ON public.sas_packages(slug);
CREATE INDEX IF NOT EXISTS idx_sas_packages_status ON public.sas_packages(status);
CREATE INDEX IF NOT EXISTS idx_sas_packages_category ON public.sas_packages(category);
CREATE INDEX IF NOT EXISTS idx_sas_packages_featured ON public.sas_packages(featured);
CREATE INDEX IF NOT EXISTS idx_sas_packages_created_at ON public.sas_packages(created_at);

CREATE INDEX IF NOT EXISTS idx_sas_package_content_blocks_package_id ON public.sas_package_content_blocks(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_package_content_blocks_sort_order ON public.sas_package_content_blocks(sort_order);

CREATE INDEX IF NOT EXISTS idx_sas_package_itinerary_package_id ON public.sas_package_itinerary(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_package_itinerary_sort_order ON public.sas_package_itinerary(sort_order);

CREATE INDEX IF NOT EXISTS idx_sas_bookings_status ON public.sas_bookings(status);
CREATE INDEX IF NOT EXISTS idx_sas_bookings_email ON public.sas_bookings(email);
CREATE INDEX IF NOT EXISTS idx_sas_bookings_created_at ON public.sas_bookings(created_at);

CREATE INDEX IF NOT EXISTS idx_sas_package_images_package_id ON public.sas_package_images(package_id);
CREATE INDEX IF NOT EXISTS idx_sas_package_images_sort_order ON public.sas_package_images(sort_order);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================
ALTER TABLE public.sas_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_itinerary ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_package_images ENABLE ROW LEVEL SECURITY;

-- Public read access for active packages
CREATE POLICY "Anyone can view active packages" ON public.sas_packages
    FOR SELECT USING (status = 'active');

-- Authenticated users can manage all packages
CREATE POLICY "Authenticated users can manage packages" ON public.sas_packages
    FOR ALL USING (auth.role() = 'authenticated');

-- Content blocks follow package permissions
CREATE POLICY "Content blocks follow package permissions" ON public.sas_package_content_blocks
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id 
            AND (status = 'active' OR auth.role() = 'authenticated')
        )
    );

-- Itinerary follows package permissions
CREATE POLICY "Itinerary follows package permissions" ON public.sas_package_itinerary
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id 
            AND (status = 'active' OR auth.role() = 'authenticated')
        )
    );

-- Bookings - users can create, admins can manage
CREATE POLICY "Anyone can create bookings" ON public.sas_bookings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Authenticated users can manage bookings" ON public.sas_bookings
    FOR ALL USING (auth.role() = 'authenticated');

-- Package images follow package permissions
CREATE POLICY "Package images follow package permissions" ON public.sas_package_images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.sas_packages 
            WHERE id = package_id 
            AND (status = 'active' OR auth.role() = 'authenticated')
        )
    );

-- =====================================================
-- SAMPLE DATA
-- =====================================================
INSERT INTO public.sas_packages (
    title, slug, description, overview, difficulty, category, location, duration,
    pricing_solo, pricing_honeymoon, pricing_family, pricing_group,
    status, featured, image_url, image_alt,
    seo_title, seo_description, seo_keywords,
    highlights, packing_list, includes, excludes
) VALUES (
    '4 Day Queen Elizabeth National Park & Lake Mutanda Safari',
    '4-day-queen-elizabeth-lake-mutanda-safari',
    'Experience the beauty of Uganda''s wildlife in this comprehensive 4-day safari adventure.',
    'Start this exciting 4-day journey through the wild heart of Uganda, where nature and culture intertwine to create a truly remarkable adventure.',
    'Moderate',
    'Wildlife',
    'Uganda',
    '4 days',
    1348.00,
    1200.00,
    950.00,
    800.00,
    'active',
    true,
    '/images/packages/uganda-safari.jpg',
    'Uganda safari wildlife adventure',
    'Uganda Wildlife Safari - 4 Days Queen Elizabeth National Park',
    'Discover Uganda''s incredible wildlife on this 4-day safari adventure through Queen Elizabeth National Park and Lake Mutanda.',
    ARRAY['uganda safari', 'wildlife tour', 'queen elizabeth national park', 'lake mutanda', 'africa safari'],
    ARRAY['Game drives in Queen Elizabeth National Park', 'Boat safari on Kazinga Channel', 'Bird watching', 'Lake Mutanda scenic views'],
    ARRAY['Comfortable walking shoes', 'Sunscreen', 'Camera', 'Binoculars', 'Light jacket'],
    ARRAY['Accommodation', 'All meals', 'Transport', 'Park fees', 'Professional guide'],
    ARRAY['International flights', 'Visa fees', 'Personal expenses', 'Tips']
) ON CONFLICT (slug) DO NOTHING;
