/* eslint-disable @next/next/no-img-element */


/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import {
  List,
  ListOrdered,
  Quote,
  Link,
  Image as ImageIcon,
  FileText,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  Hash,
  Minus,
  GripVertical,
  Trash2,
  Upload,
  Plus,
  X,
  Edit3,
  MessageSquare,
  ClipboardList,
  Bold,
  Italic,
  Underline,
  Type,
  Play
} from 'lucide-react';
import RichTextEditor from './RichTextEditor';

// Type definitions
interface ImageContent {
  src: string;
  alt: string;
  caption: string;
}

interface VideoContent {
  src: string;
  poster?: string;
  caption: string;
  width?: 'sm' | 'md' | 'lg' | 'full';
}

interface QuoteContent {
  content: string;
  author: string;
  source: string;
}

interface Block {
  id: number;
  type: string;
  content: string | string[] | ImageContent | VideoContent | QuoteContent | null;
}

interface BlockType {
  type: string;
  icon: React.ReactNode;
  label: string;
}

interface BlogEditorProps {
  initialContent?: Block[];
  onSave?: (content: Block[]) => void;
}

const BlogEditor: React.FC<BlogEditorProps> = ({
  initialContent = [],
  onSave
}) => {

  const [title, setTitle] = useState<string>("");
  const [blocks, setBlocks] = useState<Block[]>(initialContent);
  const [previewMode, setPreviewMode] = useState<boolean>(false);
  const [uploadingImages, setUploadingImages] = useState<Set<number>>(new Set());

  // Update blocks when initialContent changes
  useEffect(() => {
    console.log('🎯 BlogEditor received initialContent:', initialContent?.length, 'blocks');
    console.log('🎯 First 3 blocks:', initialContent?.slice(0, 3));
    setBlocks(initialContent);
  }, [initialContent]);
  const [showAddMenu, setShowAddMenu] = useState<boolean>(false);
  const [draggedBlock, setDraggedBlock] = useState<Block | null>(null);
  const [savedStatus, setSavedStatus] = useState<string>("Draft");
  const [selectedBlock, setSelectedBlock] = useState<number | null>(null);
  const dragOverIndex = useRef<number | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);

  // Auto-save functionality
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSave && blocks.length > 0) {
        onSave(blocks);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [blocks, onSave]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowAddMenu(false);
      }
    };

    if (showAddMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showAddMenu]);

  const blockTypes = [
    { type: "paragraph", icon: <AlignLeft size={16} />, label: "Paragraph" },
    { type: "heading2", icon: <Heading1 size={16} />, label: "Heading 2" },
    { type: "heading3", icon: <Heading2 size={16} />, label: "Heading 3" },
    { type: "heading4", icon: <Heading3 size={16} />, label: "Heading 4" },
    { type: "heading5", icon: <Hash size={16} />, label: "Heading 5" },
    { type: "heading6", icon: <Hash size={16} />, label: "Heading 6" },
    // FIX: Use a valid icon component for "image" instead of the DOM Image constructor
    { type: "image", icon: <ImageIcon size={16} />, label: "Image" },
    { type: "video", icon: <Play size={16} />, label: "Video" },
    { type: "quote", icon: <Quote size={16} />, label: "Quote" },
    { type: "divider", icon: <Minus size={16} />, label: "Divider" },
    { type: "bulleted-list", icon: <List size={16} />, label: "Bullet List" },
    {
      type: "numbered-list",
      icon: <ListOrdered size={16} />,
      label: "Numbered List",
    },
  ];

  const addBlock = (type: string) => {
    const newBlock: Block = {
      id: Date.now(),
      type,
      content: getDefaultContent(type),
    };
    setBlocks([...blocks, newBlock]);
    setShowAddMenu(false);
  };

  // Removed old formatting functions - now handled by RichTextEditor

  const getDefaultContent = (
    type: string
  ): string | string[] | ImageContent | VideoContent | QuoteContent | null => {
    switch (type) {
      case "paragraph":
        return "Start typing...";
      case "heading2":
        return "Heading 2";
      case "heading3":
        return "Heading 3";
      case "heading4":
        return "Heading 4";
      case "heading5":
        return "Heading 5";
      case "heading6":
        return "Heading 6";
      case "image":
        return { src: "", alt: "", caption: "" };
      case "video":
        return { src: "", poster: "", caption: "", width: "lg" };
      case "quote":
        return { content: "Enter your quote here...", author: "", source: "" };
      case "divider":
        return null;
      case "bulleted-list":
        return ["List item 1", "List item 2"];
      case "numbered-list":
        return ["List item 1", "List item 2"];
      default:
        return "";
    }
  };

  const updateBlock = (
    id: number,
    content: string | string[] | ImageContent | null
  ) => {
    setBlocks(
      blocks.map((block) => (block.id === id ? { ...block, content } : block))
    );
  };

  const handleImageUpload = async (blockId: number, file: File) => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      alert("Please select an image file");
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert("File size must be less than 10MB");
      return;
    }

    // Set uploading state
    setUploadingImages(prev => new Set(prev).add(blockId));

    // Get current block content
    const currentBlock = blocks.find((block) => block.id === blockId);
    const currentContent = currentBlock?.content as ImageContent | null;

    // Create temporary preview URL while uploading
    const tempImageUrl = URL.createObjectURL(file);

    // Update block with temporary image for immediate preview
    updateBlock(blockId, {
      src: tempImageUrl,
      alt: currentContent?.alt || file.name.split(".")[0],
      caption: currentContent?.caption || "",
    });

    try {
      // Upload to Supabase storage
      const formData = new FormData();
      formData.append('file', file);
      formData.append('altText', currentContent?.alt || file.name.split(".")[0]);
      formData.append('bucket', 'sas-blog-images');

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Update block with actual uploaded URL
        updateBlock(blockId, {
          src: result.data.url,
          alt: result.data.altText || currentContent?.alt || file.name.split(".")[0],
          caption: currentContent?.caption || "",
        });

        // Clean up temporary URL
        URL.revokeObjectURL(tempImageUrl);
      } else {
        console.error('Failed to upload image:', result.error);
        alert('Failed to upload image: ' + result.error);
        // Revert to no image on error
        updateBlock(blockId, {
          src: "",
          alt: "",
          caption: "",
        });
        URL.revokeObjectURL(tempImageUrl);
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload image. Please try again.');
      // Revert to no image on error
      updateBlock(blockId, {
        src: "",
        alt: "",
        caption: "",
      });
      URL.revokeObjectURL(tempImageUrl);
    } finally {
      // Clear uploading state
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(blockId);
        return newSet;
      });
    }
  };

  const handleImageDrop = (blockId: number, e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    const imageFile = files.find((file) => file.type.startsWith("image/"));

    if (imageFile) {
      handleImageUpload(blockId, imageFile);
    }
  };

  const handleImageDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const deleteBlock = (id: number) => {
    setBlocks(blocks.filter((block) => block.id !== id));
  };

  const handleDragStart = (e: React.DragEvent, block: Block) => {
    setDraggedBlock(block);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    dragOverIndex.current = index;
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (draggedBlock && dragOverIndex.current !== null) {
      const newBlocks = [...blocks];
      const draggedIndex = blocks.findIndex((b) => b.id === draggedBlock.id);
      const targetIndex = dragOverIndex.current;

      newBlocks.splice(draggedIndex, 1);
      newBlocks.splice(targetIndex, 0, draggedBlock);

      setBlocks(newBlocks);
    }
    setDraggedBlock(null);
    dragOverIndex.current = null;
  };

  const renderBlock = (block: Block, index: number) => {
    const isBeingDragged = draggedBlock && draggedBlock.id === block.id;

    return (
      <div
        key={block.id}
        className={`group relative bg-[var(--white)] border border-gray-200 rounded-lg mb-3 transition-all duration-200 ${
          isBeingDragged ? "opacity-50" : "hover:border-gray-300"
        }`}
        onDragOver={(e) => handleDragOver(e, index)}
        onDrop={handleDrop}
        onClick={(e) => {
          // Only select block if clicking outside of content area
          const target = e.target as HTMLElement;
          if (!target.closest('[contenteditable="true"]') && !target.closest('.rich-text-editor') && !target.closest('textarea') && !target.closest('input')) {
            // Handle block selection if needed
          }
        }}
      >
        {/* Block Controls */}
        <div className="absolute -left-10 top-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex flex-col space-y-1">
          <button
            className="p-1 bg-[var(--hero)] hover:bg-gray-200 rounded border cursor-grab active:cursor-grabbing"
            title="Drag to reorder"
            draggable={true}
            onDragStart={(e) => {
              handleDragStart(e, block);
              e.stopPropagation();
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
            }}
          >
            <GripVertical size={12} className="text-gray-500" />
          </button>
          <button
            onClick={() => deleteBlock(block.id)}
            className="p-1 bg-red-50 hover:bg-red-100 rounded border text-red-500 hover:text-red-600"
            title="Delete block"
          >
            <Trash2 size={12} />
          </button>
        </div>

        {/* Block Content */}
        <div className="p-3">{renderBlockContent(block)}</div>
      </div>
    );
  };

  // Removed old FormattingToolbar - now handled by RichTextEditor

  const renderBlockContent = (block: Block) => {
    const isTextBlock = ['paragraph', 'heading2', 'heading3', 'heading4', 'heading5', 'heading6'].includes(block.type);

    switch (block.type) {
      case "paragraph":
        return (
          <RichTextEditor
            value={typeof block.content === "string" ? block.content : ""}
            onChange={(content: string) => updateBlock(block.id, content)}
            placeholder="Start typing your paragraph..."
            height={120}
            className="bg-transparent"
          />
        );

      case "heading2":
      case "heading3":
      case "heading4":
      case "heading5":
      case "heading6":
        const HeadingTag = block.type.replace(
          "heading",
          "h"
        ) as keyof typeof fontSizeMap;
        const fontSizeMap = {
          h2: "text-2xl",
          h3: "text-xl",
          h4: "text-lg",
          h5: "text-base",
          h6: "text-sm",
        };
        const fontSize = fontSizeMap[HeadingTag];

        return (
          <input
            type="text"
            value={typeof block.content === "string" ? block.content : ""}
            onChange={(e) => updateBlock(block.id, e.target.value)}
            placeholder={`${block.type.charAt(0).toUpperCase() + block.type.slice(1)} heading...`}
            className={`w-full border-none outline-none font-bold text-[var(--text)] ${fontSize} bg-transparent`}
          />
        );

      case "image":
        const imageContent = block.content as ImageContent | null;
        return (
          <div className="space-y-4">
            {/* Image Upload Area */}
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleImageUpload(block.id, file);
                  }
                }}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                id={`image-upload-${block.id}`}
              />
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-[var(--accent)] hover:bg-[var(--hero)] transition-colors"
                onDrop={(e) => handleImageDrop(block.id, e)}
                onDragOver={handleImageDragOver}
                onDragEnter={(e) => e.preventDefault()}
              >
                {uploadingImages.has(block.id) ? (
                  <div className="text-gray-500">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--accent)] mx-auto mb-2"></div>
                    <p className="text-base font-medium mb-1">
                      Uploading image...
                    </p>
                    <p className="text-sm">Please wait</p>
                  </div>
                ) : imageContent?.src ? (
                  <div className="relative group">
                    <img
                      src={imageContent.src}
                      alt={imageContent.alt || 'Blog image'}
                      className="max-w-full h-auto mx-auto rounded shadow-sm"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="bg-white rounded-lg p-2 shadow-lg">
                          <Upload size={20} className="text-gray-600" />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-500">
                    <Upload size={36} className="mx-auto mb-2 text-gray-400" />
                    <p className="text-base font-medium mb-1">
                      Click to upload image
                    </p>
                    <p className="text-sm">or drag and drop</p>
                    <p className="text-xs mt-1 text-gray-400">
                      PNG, JPG, GIF up to 10MB
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Image URL Input (Alternative) */}
            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <Link
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Or paste image URL"
                  value={imageContent?.src || ""}
                  onChange={(e) =>
                    updateBlock(block.id, {
                      ...imageContent,
                      src: e.target.value,
                      alt: imageContent?.alt || "",
                      caption: imageContent?.caption || "",
                    })
                  }
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                />
              </div>
            </div>

            {/* Image Metadata */}
            <div className="space-y-2">
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-1">
                  Alt Text
                </label>
                <input
                  type="text"
                  placeholder="Describe the image for accessibility"
                  value={imageContent?.alt || ""}
                  onChange={(e) =>
                    updateBlock(block.id, {
                      ...imageContent,
                      alt: e.target.value,
                      src: imageContent?.src || "",
                      caption: imageContent?.caption || "",
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-1">
                  Caption (Optional)
                </label>
                <input
                  type="text"
                  placeholder="Add a caption for your image"
                  value={imageContent?.caption || ""}
                  onChange={(e) =>
                    updateBlock(block.id, {
                      ...imageContent,
                      caption: e.target.value,
                      src: imageContent?.src || "",
                      alt: imageContent?.alt || "",
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                />
              </div>
            </div>

            {/* Image Actions */}
            {imageContent?.src && (
              <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  Image uploaded successfully
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      updateBlock(block.id, { src: "", alt: "", caption: "" })
                    }
                    className="text-xs text-red-600 hover:text-red-700 font-medium"
                  >
                    Remove Image
                  </button>
                  <label
                    htmlFor={`image-upload-${block.id}`}
                    className="text-xs text-[var(--accent)] hover:text-[var(--btn)] font-medium cursor-pointer"
                  >
                    Replace Image
                  </label>
                </div>
              </div>
            )}
          </div>
        );

      case "video":
        const videoContent = block.content as VideoContent | null;

        // Helper function to extract YouTube video ID
        const getYouTubeVideoId = (url: string) => {
          const match = url.match(/(?:youtube\.com\/embed\/|youtu\.be\/|youtube\.com\/watch\?v=)([^&\n?#]+)/);
          return match ? match[1] : null;
        };

        // Helper function to generate YouTube thumbnail
        const getYouTubeThumbnail = (url: string) => {
          const videoId = getYouTubeVideoId(url);
          return videoId ? `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg` : '';
        };

        // Helper function to convert YouTube URL to embed format
        const convertToEmbedUrl = (url: string) => {
          const videoId = getYouTubeVideoId(url);
          if (videoId) {
            return `https://www.youtube.com/embed/${videoId}`;
          }
          return url; // Return as-is if not a YouTube URL
        };

        return (
          <div className="space-y-4">
            {/* Video URL Input */}
            <div>
              <label className="block text-sm font-medium text-[var(--text)] mb-2">
                Video URL
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-1 relative">
                  <Play
                    size={16}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  />
                  <input
                    type="text"
                    placeholder="Paste YouTube URL or video file URL"
                    value={videoContent?.src || ""}
                    onChange={(e) => {
                      const inputUrl = e.target.value;
                      const embedUrl = convertToEmbedUrl(inputUrl);
                      const thumbnail = getYouTubeThumbnail(inputUrl);

                      updateBlock(block.id, {
                        ...videoContent,
                        src: embedUrl,
                        poster: thumbnail || videoContent?.poster || "",
                        caption: videoContent?.caption || "",
                        width: videoContent?.width || "lg",
                      });
                    }}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Supports YouTube URLs, direct video file URLs (.mp4, .webm, etc.)
              </p>
            </div>

            {/* Video Preview */}
            {videoContent?.src && (
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                {videoContent.src.includes('youtube.com/embed') ? (
                  <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
                    <iframe
                      src={videoContent.src}
                      title={videoContent.caption || 'Video preview'}
                      className="absolute top-0 left-0 w-full h-full"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  </div>
                ) : (
                  <video
                    src={videoContent.src}
                    poster={videoContent.poster}
                    controls
                    className="w-full h-auto"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
              </div>
            )}

            {/* Video Metadata */}
            <div className="space-y-2">
              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-1">
                  Caption
                </label>
                <input
                  type="text"
                  placeholder="Add a caption for your video"
                  value={videoContent?.caption || ""}
                  onChange={(e) =>
                    updateBlock(block.id, {
                      ...videoContent,
                      caption: e.target.value,
                      src: videoContent?.src || "",
                      poster: videoContent?.poster || "",
                      width: videoContent?.width || "lg",
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-[var(--text)] mb-1">
                  Width
                </label>
                <select
                  value={videoContent?.width || "lg"}
                  onChange={(e) =>
                    updateBlock(block.id, {
                      ...videoContent,
                      width: e.target.value as 'sm' | 'md' | 'lg' | 'full',
                      src: videoContent?.src || "",
                      poster: videoContent?.poster || "",
                      caption: videoContent?.caption || "",
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent text-sm"
                >
                  <option value="sm">Small</option>
                  <option value="md">Medium</option>
                  <option value="lg">Large</option>
                  <option value="full">Full Width</option>
                </select>
              </div>
            </div>

            {/* Video Actions */}
            {videoContent?.src && (
              <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                <div className="text-xs text-gray-500">
                  Video configured successfully
                </div>
                <button
                  onClick={() =>
                    updateBlock(block.id, { src: "", poster: "", caption: "", width: "lg" })
                  }
                  className="text-xs text-red-600 hover:text-red-700 font-medium"
                >
                  Remove Video
                </button>
              </div>
            )}
          </div>
        );

      case "quote":
        const quoteContent = typeof block.content === "object" && block.content !== null
          ? block.content as { content: string; author: string; source: string }
          : { content: typeof block.content === "string" ? block.content : "", author: "", source: "" };

        return (
          <div className="border-l-4 border-gray-300 pl-4 space-y-2">
            <textarea
              value={quoteContent.content}
              onChange={(e) => updateBlock(block.id, { ...quoteContent, content: e.target.value })}
              className="w-full border-none outline-none resize-none text-[var(--text)] italic text-base leading-relaxed bg-transparent"
              placeholder="Enter your quote here..."
              rows={3}
            />
            <div className="flex gap-2">
              <input
                type="text"
                value={quoteContent.author}
                onChange={(e) => updateBlock(block.id, { ...quoteContent, author: e.target.value })}
                className="flex-1 p-1 border-none outline-none bg-transparent text-sm text-[var(--text)]"
                placeholder="Author"
              />
              <input
                type="text"
                value={quoteContent.source}
                onChange={(e) => updateBlock(block.id, { ...quoteContent, source: e.target.value })}
                className="flex-1 p-1 border-none outline-none bg-transparent text-sm text-[var(--text)]"
                placeholder="Source"
              />
            </div>
          </div>
        );

      case "divider":
        return <hr className="border-gray-300" />;

      case "bulleted-list":
      case "numbered-list":
        const listContent = Array.isArray(block.content) ? block.content : [];
        return (
          <div className="space-y-2">
            {listContent.map((item: string, index: number) => (
              <div key={index} className="flex items-center space-x-2 group">
                <span className="text-gray-500 min-w-4">
                  {block.type === "numbered-list" ? `${index + 1}.` : "•"}
                </span>
                <input
                  type="text"
                  value={item}
                  onChange={(e) => {
                    const newItems = [...listContent];
                    newItems[index] = e.target.value;
                    updateBlock(block.id, newItems);
                  }}
                  className="flex-1 border-none outline-none text-[var(--text)]"
                  placeholder="List item"
                />
                <button
                  onClick={() => {
                    const newItems = listContent.filter(
                      (_, i: number) => i !== index
                    );
                    updateBlock(block.id, newItems);
                  }}
                  className="text-red-500 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Trash2 size={14} />
                </button>
              </div>
            ))}
            <button
              onClick={() =>
                updateBlock(block.id, [...listContent, "New item"])
              }
              className="text-[var(--accent)] hover:text-[var(--btn)] text-sm font-medium"
            >
              + Add item
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Title */}
        <div className="mb-6">
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full text-3xl font-bold text-[var(--text)] border-none outline-none bg-transparent"
            placeholder="Blog post title..."
            disabled={previewMode}
          />
        </div>

        {/* Blocks */}
        <div className="pl-8">
          {blocks.length === 0 ? (
            <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-xl bg-[var(--hero)]">
              <FileText size={36} className="mx-auto text-gray-400 mb-3" />
              <h3 className="text-base font-medium text-[var(--text)] mb-2">
                Start writing your blog post
              </h3>
              <p className="text-gray-600 mb-4 max-w-md mx-auto text-sm">
                Add content blocks to build your story. You can add text,
                images, lists, quotes, and more.
              </p>
              <button
                onClick={() => addBlock("paragraph")}
                className="inline-flex items-center px-3 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--accent)]/90 transition-colors text-sm"
              >
                <Plus size={14} className="mr-2" />
                Add your first paragraph
              </button>
            </div>
          ) : (
            blocks.map((block, index) => renderBlock(block, index))
          )}
        </div>

        {/* Add Block Button */}
        {!previewMode && (
          <div className="pl-8 mt-6 relative">
            <button
              onClick={() => setShowAddMenu(!showAddMenu)}
              className="group flex items-center space-x-2 px-4 py-2 bg-[var(--accent)] hover:bg-[var(--btn)] text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
            >
              <div className="flex items-center justify-center w-5 h-5 bg-white/20 rounded group-hover:bg-white/30 transition-colors">
                <Plus size={14} />
              </div>
              <span className="font-medium text-sm">Add Content Block</span>
              <div className="text-xs bg-white/20 px-2 py-1 rounded">
                {blocks.length} blocks
              </div>
            </button>

            {/* Add Block Menu */}
            {showAddMenu && (
              <div
                ref={menuRef}
                className="absolute top-10 left-0 bg-[var(--white)] border border-gray-200 rounded-lg shadow-xl z-20 w-[36rem]"
              >
                <div className="p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h3 className="text-sm font-semibold text-[var(--text)] mb-1">
                        Add Content Block
                      </h3>
                      <p className="text-xs text-gray-500">
                        Choose a block type to add to your post
                      </p>
                    </div>
                    <button
                      onClick={() => setShowAddMenu(false)}
                      className="p-1 hover:bg-[var(--hero)] rounded-md transition-colors"
                    >
                      <X size={14} className="text-gray-400" />
                    </button>
                  </div>

                  {/* Grid Layout */}
                  <div className="grid grid-cols-6 gap-2">
                    {blockTypes.map((blockType) => (
                      <button
                        key={blockType.type}
                        onClick={() => addBlock(blockType.type)}
                        className="group flex flex-col items-center justify-center p-2 hover:bg-[var(--hero)] rounded-lg text-center text-[var(--text)] hover:text-[var(--text)] transition-all duration-200 border border-transparent hover:border-gray-200 hover:shadow-sm"
                      >
                        <div className="flex items-center justify-center w-7 h-7 mb-1 rounded-md bg-[var(--hero)] group-hover:bg-[var(--accent)] group-hover:text-white transition-all duration-200">
                          {blockType.icon}
                        </div>
                        <span className="text-xs font-medium">
                          {blockType.label}
                        </span>
                      </button>
                    ))}
                  </div>

                  {/* Quick Templates */}
                  <div className="mt-3 pt-2 border-t border-gray-100">
                    <div className="text-xs font-medium text-[var(--text)] mb-2 uppercase tracking-wide">
                      Quick Templates
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => {
                          addBlock("heading2");
                          addBlock("paragraph");
                          addBlock("paragraph");
                        }}
                        className="flex items-center justify-center p-2 text-xs bg-[var(--card-bg)] text-[var(--text)] rounded-lg hover:bg-[var(--hero)] transition-all duration-200 font-medium space-x-1"
                      >
                        <Edit3 size={12} />
                        <span>Article Section</span>
                      </button>
                      <button
                        onClick={() => {
                          addBlock("image");
                          addBlock("paragraph");
                        }}
                        className="flex items-center justify-center p-2 text-xs bg-[var(--card-bg)] text-[var(--text)] rounded-lg hover:bg-[var(--hero)] transition-all duration-200 font-medium space-x-1"
                      >
                        <ImageIcon size={12} />
                        <span>Image + Caption</span>
                      </button>
                      <button
                        onClick={() => {
                          addBlock("quote");
                          addBlock("paragraph");
                        }}
                        className="flex items-center justify-center p-2 text-xs bg-[var(--card-bg)] text-[var(--text)] rounded-lg hover:bg-[var(--hero)] transition-all duration-200 font-medium space-x-1"
                      >
                        <MessageSquare size={12} />
                        <span>Quote Block</span>
                      </button>
                      <button
                        onClick={() => {
                          addBlock("bulleted-list");
                          addBlock("paragraph");
                        }}
                        className="flex items-center justify-center p-2 text-xs bg-[var(--card-bg)] text-[var(--text)] rounded-lg hover:bg-[var(--hero)] transition-all duration-200 font-medium space-x-1"
                      >
                        <ClipboardList size={12} />
                        <span>List + Text</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {blocks.length === 0 && !previewMode && (
          <div className="pl-12 text-center py-12">
            <div className="text-gray-400 mb-4">
              <FileText size={48} className="mx-auto mb-3" />
              <p className="text-lg">Start creating your blog post</p>
              <p className="text-sm">Add your first block to get started</p>
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close menu */}
      {showAddMenu && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setShowAddMenu(false)}
        />
      )}
    </div>
  );
};

export default BlogEditor;
