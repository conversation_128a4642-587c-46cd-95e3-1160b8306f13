import { NextRequest, NextResponse } from 'next/server';
import { sendSubscriptionNotification } from '@/lib/newsletter-service';

// POST - Send admin notification for newsletter events
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, email } = body;

    if (!type || !email) {
      return NextResponse.json(
        { success: false, error: 'Type and email are required' },
        { status: 400 }
      );
    }

    let success = false;

    switch (type) {
      case 'new_subscription':
        success = await sendSubscriptionNotification(email);
        break;
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid notification type' },
          { status: 400 }
        );
    }

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Admin notification sent successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send admin notification' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Admin notification error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
