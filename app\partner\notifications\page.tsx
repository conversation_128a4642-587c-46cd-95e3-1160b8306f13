'use client';

import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  <PERSON>, 
  Check, 
  CheckCheck, 
  Trash2,
  Calendar, 
  DollarSign, 
  Package, 
  MessageSquare, 
  AlertCircle,
  Info,
  Clock,
  Eye,
  Settings} from 'lucide-react';
import Link from 'next/link';

interface Notification {
  id: string;
  type: 'booking' | 'payment' | 'message' | 'system' | 'package';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: {
    bookingId?: string;
    amount?: number;
    packageName?: string;
  };
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Mock notifications - replace with actual API calls
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'booking',
        title: 'New Booking Confirmed',
        message: 'Booking SA-B2B-001 for Serengeti Safari has been confirmed. Customer: Michael Johnson, Travel Date: January 20, 2025',
        timestamp: '2024-12-20T10:30:00Z',
        read: false,
        priority: 'high',
        actionUrl: '/partner/bookings/SA-B2B-001',
        metadata: {
          bookingId: 'SA-B2B-001',
          packageName: 'Serengeti 5-Day Safari'
        }
      },
      {
        id: '2',
        type: 'payment',
        title: 'Commission Payment Processed',
        message: 'Your commission payment of $1,080 has been processed and will be transferred to your account within 2-3 business days.',
        timestamp: '2024-12-20T09:15:00Z',
        read: false,
        priority: 'medium',
        actionUrl: '/partner/reports',
        metadata: {
          amount: 1080
        }
      },
      {
        id: '3',
        type: 'message',
        title: 'New Message from Admin',
        message: 'Sarah Johnson replied to your booking modification request for SA-B2B-001. Please check the conversation for details.',
        timestamp: '2024-12-19T16:45:00Z',
        read: true,
        priority: 'medium',
        actionUrl: '/partner/messages',
        metadata: {
          bookingId: 'SA-B2B-001'
        }
      },
      {
        id: '4',
        type: 'package',
        title: 'New Package Available',
        message: 'Zanzibar Beach Extension package is now available for booking. Perfect add-on for safari customers looking to relax after their adventure.',
        timestamp: '2024-12-19T14:20:00Z',
        read: true,
        priority: 'low',
        actionUrl: '/partner/packages',
        metadata: {
          packageName: 'Zanzibar Beach Extension'
        }
      },
      {
        id: '5',
        type: 'system',
        title: 'System Maintenance Notice',
        message: 'Scheduled maintenance on December 25th from 2-4 AM UTC. The partner portal will be temporarily unavailable during this time.',
        timestamp: '2024-12-18T11:30:00Z',
        read: false,
        priority: 'medium',
        actionUrl: '/partner/profile'
      },
      {
        id: '6',
        type: 'booking',
        title: 'Booking Modification Request',
        message: 'Customer requested date change for booking SA-B2B-002 from February 10th to February 15th. Please review and confirm availability.',
        timestamp: '2024-12-18T08:15:00Z',
        read: true,
        priority: 'high',
        actionUrl: '/partner/bookings/SA-B2B-002',
        metadata: {
          bookingId: 'SA-B2B-002'
        }
      },
      {
        id: '7',
        type: 'payment',
        title: 'Monthly Commission Report',
        message: 'Your November commission report is ready. Total earnings: $3,270 from 28 bookings. View detailed breakdown in reports.',
        timestamp: '2024-12-01T09:00:00Z',
        read: true,
        priority: 'low',
        actionUrl: '/partner/reports',
        metadata: {
          amount: 3270
        }
      },
      {
        id: '8',
        type: 'package',
        title: 'Package Price Update',
        message: 'Kilimanjaro Climbing Adventure package prices have been updated for 2025 season. New rates are now effective.',
        timestamp: '2024-11-28T14:30:00Z',
        read: true,
        priority: 'medium',
        actionUrl: '/partner/packages',
        metadata: {
          packageName: 'Kilimanjaro Climbing Adventure'
        }
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || notification.type === typeFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'read' && notification.read) ||
                         (statusFilter === 'unread' && !notification.read);
    return matchesSearch && matchesType && matchesStatus;
  });

  const totalPages = Math.ceil(filteredNotifications.length / itemsPerPage);
  const currentNotifications = filteredNotifications.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = priority === 'high' ? 'text-red-600' : priority === 'medium' ? 'text-yellow-600' : 'text-blue-600';
    
    switch (type) {
      case 'booking': return <Calendar className={`h-5 w-5 ${iconClass}`} />;
      case 'payment': return <DollarSign className={`h-5 w-5 ${iconClass}`} />;
      case 'message': return <MessageSquare className={`h-5 w-5 ${iconClass}`} />;
      case 'package': return <Package className={`h-5 w-5 ${iconClass}`} />;
      case 'system': return <AlertCircle className={`h-5 w-5 ${iconClass}`} />;
      default: return <Info className={`h-5 w-5 ${iconClass}`} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  const markAsRead = (notificationIds: string[]) => {
    setNotifications(prev => 
      prev.map(n => 
        notificationIds.includes(n.id) ? { ...n, read: true } : n
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const deleteNotifications = (notificationIds: string[]) => {
    setNotifications(prev => 
      prev.filter(n => !notificationIds.includes(n.id))
    );
    setSelectedNotifications([]);
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === currentNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(currentNotifications.map(n => n.id));
    }
  };

  const handleSelectNotification = (notificationId: string) => {
    setSelectedNotifications(prev => 
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    );
  };

  const notificationStats = {
    total: notifications.length,
    unread: unreadCount,
    booking: notifications.filter(n => n.type === 'booking').length,
    payment: notifications.filter(n => n.type === 'payment').length,
    message: notifications.filter(n => n.type === 'message').length,
    system: notifications.filter(n => n.type === 'system').length,
    package: notifications.filter(n => n.type === 'package').length
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Bell className="mr-3 text-blue-600" />
            Notifications
          </h1>
          <p className="text-gray-600 mt-1">Stay updated with your bookings, payments, and messages</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            <CheckCheck className="h-4 w-4 mr-2" />
            Mark All Read
          </button>
          <Link href="/partner/profile">
            <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Bell className="h-6 w-6 text-gray-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Total</p>
              <p className="text-xl font-bold text-gray-900">{notificationStats.total}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Eye className="h-6 w-6 text-red-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Unread</p>
              <p className="text-xl font-bold text-red-900">{notificationStats.unread}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Calendar className="h-6 w-6 text-blue-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Bookings</p>
              <p className="text-xl font-bold text-blue-900">{notificationStats.booking}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <DollarSign className="h-6 w-6 text-green-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Payments</p>
              <p className="text-xl font-bold text-green-900">{notificationStats.payment}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <MessageSquare className="h-6 w-6 text-purple-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Messages</p>
              <p className="text-xl font-bold text-purple-900">{notificationStats.message}</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center">
            <Package className="h-6 w-6 text-orange-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">Packages</p>
              <p className="text-xl font-bold text-orange-900">{notificationStats.package}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          <div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="booking">Bookings</option>
              <option value="payment">Payments</option>
              <option value="message">Messages</option>
              <option value="package">Packages</option>
              <option value="system">System</option>
            </select>
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="unread">Unread</option>
              <option value="read">Read</option>
            </select>
          </div>
        </div>
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''} found
          </div>
          {selectedNotifications.length > 0 && (
            <div className="flex items-center space-x-2">
              <button
                onClick={() => markAsRead(selectedNotifications)}
                className="text-sm text-blue-600 hover:text-blue-500 flex items-center"
              >
                <Check className="h-3 w-3 mr-1" />
                Mark as read ({selectedNotifications.length})
              </button>
              <button
                onClick={() => deleteNotifications(selectedNotifications)}
                className="text-sm text-red-600 hover:text-red-500 flex items-center"
              >
                <Trash2 className="h-3 w-3 mr-1" />
                Delete ({selectedNotifications.length})
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Bulk Actions Header */}
        {currentNotifications.length > 0 && (
          <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedNotifications.length === currentNotifications.length}
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label className="ml-3 text-sm text-gray-700">
                Select all ({currentNotifications.length})
              </label>
            </div>
          </div>
        )}

        {/* Notifications */}
        <div className="divide-y divide-gray-200">
          {currentNotifications.length === 0 ? (
            <div className="p-12 text-center">
              <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 font-medium">No notifications found</p>
              <p className="text-gray-400 text-sm mt-1">
                Try adjusting your filters or check back later for updates.
              </p>
            </div>
          ) : (
            currentNotifications.map((notification) => {
              const { date, time } = formatTimestamp(notification.timestamp);
              return (
                <div
                  key={notification.id}
                  className={`p-6 hover:bg-gray-50 transition-colors border-l-4 ${getPriorityColor(notification.priority)} ${
                    !notification.read ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={() => handleSelectNotification(notification.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    />
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type, notification.priority)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </h3>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-3">
                            {notification.message}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500">
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {date} at {time}
                            </div>
                            <span className="capitalize">{notification.type}</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              notification.priority === 'high' ? 'bg-red-100 text-red-800' :
                              notification.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {notification.priority} priority
                            </span>
                            {notification.metadata?.amount && (
                              <span className="text-green-600 font-medium">
                                ${notification.metadata.amount.toLocaleString()}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          {!notification.read && (
                            <button
                              onClick={() => markAsRead([notification.id])}
                              className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                              title="Mark as read"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                          )}
                          {notification.actionUrl && (
                            <Link href={notification.actionUrl}>
                              <button className="p-1 text-gray-600 hover:bg-gray-100 rounded transition-colors">
                                <Eye className="h-4 w-4" />
                              </button>
                            </Link>
                          )}
                          <button
                            onClick={() => deleteNotifications([notification.id])}
                            className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredNotifications.length)} of {filteredNotifications.length} notifications
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-1 border rounded-md ${
                    currentPage === page
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              ))}
              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
