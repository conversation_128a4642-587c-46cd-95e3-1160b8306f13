-- Blog CRUD System Database Setup
-- Execute this script in your Supabase SQL Editor

-- =============================================
-- 1. CREATE BLOG POSTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS sas_blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT NOT NULL,
  hero_image_url TEXT NOT NULL,
  hero_image_alt TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('published', 'draft', 'archived')),
  published_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- SEO Fields
  seo_title TEXT,
  seo_description TEXT,
  seo_keywords TEXT[] DEFAULT '{}',
  og_title TEXT,
  og_description TEXT,
  og_image_url TEXT,
  canonical_url TEXT,
  robots_index TEXT DEFAULT 'index' CHECK (robots_index IN ('index', 'noindex')),
  robots_follow TEXT DEFAULT 'follow' CHECK (robots_follow IN ('follow', 'nofollow')),
  
  -- Schema.org structured data
  schema_data JSONB,
  
  -- Analytics
  view_count INTEGER DEFAULT 0,
  
  -- Soft delete
  deleted_at TIMESTAMPTZ
);

-- =============================================
-- 2. CREATE BLOG CONTENT BLOCKS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS sas_blog_content_blocks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID NOT NULL REFERENCES sas_blog_posts(id) ON DELETE CASCADE,
  block_type TEXT NOT NULL CHECK (block_type IN (
    'paragraph', 'h2', 'h3', 'h4', 'h5', 'h6', 
    'image', 'video', 'listing', 'quote', 'divider'
  )),
  content JSONB NOT NULL,
  sort_order INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- 3. CREATE BLOG COMMENTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS sas_blog_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID NOT NULL REFERENCES sas_blog_posts(id) ON DELETE CASCADE,
  parent_comment_id UUID REFERENCES sas_blog_comments(id) ON DELETE CASCADE,
  author_name TEXT NOT NULL,
  author_email TEXT NOT NULL,
  content TEXT NOT NULL,
  is_admin_reply BOOLEAN DEFAULT FALSE,
  status TEXT NOT NULL DEFAULT 'approved' CHECK (status IN ('approved', 'pending', 'rejected')),
  likes_count INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Soft delete
  deleted_at TIMESTAMPTZ
);

-- =============================================
-- 4. CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Blog Posts Indexes
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_slug ON sas_blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_status ON sas_blog_posts(status);
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_published_at ON sas_blog_posts(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_category ON sas_blog_posts(category);
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_tags ON sas_blog_posts USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_deleted_at ON sas_blog_posts(deleted_at);

-- Full-text search index
CREATE INDEX IF NOT EXISTS idx_sas_blog_posts_search ON sas_blog_posts USING GIN(
  to_tsvector('english', title || ' ' || description)
);

-- Content Blocks Indexes
CREATE INDEX IF NOT EXISTS idx_sas_blog_content_blocks_blog_post_id ON sas_blog_content_blocks(blog_post_id);
CREATE INDEX IF NOT EXISTS idx_sas_blog_content_blocks_sort_order ON sas_blog_content_blocks(blog_post_id, sort_order);

-- Comments Indexes
CREATE INDEX IF NOT EXISTS idx_sas_blog_comments_blog_post_id ON sas_blog_comments(blog_post_id);
CREATE INDEX IF NOT EXISTS idx_sas_blog_comments_parent_comment_id ON sas_blog_comments(parent_comment_id);
CREATE INDEX IF NOT EXISTS idx_sas_blog_comments_status ON sas_blog_comments(status);
CREATE INDEX IF NOT EXISTS idx_sas_blog_comments_created_at ON sas_blog_comments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_sas_blog_comments_deleted_at ON sas_blog_comments(deleted_at);

-- =============================================
-- 5. CREATE UPDATED_AT TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_sas_blog_posts_updated_at 
  BEFORE UPDATE ON sas_blog_posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_blog_content_blocks_updated_at 
  BEFORE UPDATE ON sas_blog_content_blocks 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sas_blog_comments_updated_at 
  BEFORE UPDATE ON sas_blog_comments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 6. ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE sas_blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_blog_content_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE sas_blog_comments ENABLE ROW LEVEL SECURITY;

-- Blog Posts Policies
CREATE POLICY "Public can read published blog posts" ON sas_blog_posts
  FOR SELECT USING (status = 'published' AND deleted_at IS NULL);

CREATE POLICY "Authenticated users can read all blog posts" ON sas_blog_posts
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can manage blog posts" ON sas_blog_posts
  FOR ALL TO authenticated USING (true);

-- Content Blocks Policies
CREATE POLICY "Public can read content blocks for published posts" ON sas_blog_content_blocks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

CREATE POLICY "Authenticated users can manage content blocks" ON sas_blog_content_blocks
  FOR ALL TO authenticated USING (true);

-- Comments Policies
CREATE POLICY "Public can read approved comments" ON sas_blog_comments
  FOR SELECT USING (
    status = 'approved' 
    AND deleted_at IS NULL
    AND EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

CREATE POLICY "Public can insert comments" ON sas_blog_comments
  FOR INSERT WITH CHECK (
    NOT is_admin_reply 
    AND EXISTS (
      SELECT 1 FROM sas_blog_posts 
      WHERE id = blog_post_id 
      AND status = 'published' 
      AND deleted_at IS NULL
    )
  );

CREATE POLICY "Authenticated users can manage comments" ON sas_blog_comments
  FOR ALL TO authenticated USING (true);

-- =============================================
-- 7. SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =============================================

-- Insert a sample blog post for testing
-- INSERT INTO sas_blog_posts (
--   title, slug, description, hero_image_url, hero_image_alt,
--   category, tags, status, published_at,
--   seo_title, seo_description, seo_keywords
-- ) VALUES (
--   'Welcome to Our Blog',
--   'welcome-to-our-blog',
--   'This is our first blog post to test the new system.',
--   'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1200&q=80',
--   'Welcome blog post hero image',
--   'Swift Africa Safaris',
--   'General',
--   ARRAY['welcome', 'blog', 'test'],
--   'published',
--   NOW(),
--   'Welcome to Our Blog | Swift Africa Safaris',
--   'Welcome to our new blog system. Read about our latest updates and travel tips.',
--   ARRAY['blog', 'welcome', 'safari', 'travel']
-- );

-- =============================================
-- SETUP COMPLETE
-- =============================================

-- Verify tables were created successfully
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE tablename LIKE 'sas_blog_%'
ORDER BY tablename;
