#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { readFileSync, existsSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { config } from 'dotenv';

// Load environment variables
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Fallback images to upload (using existing hero images as source)
const fallbackImages = [
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/safari-default.webp',
    description: 'Default safari package image'
  },
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/wildlife-safari.webp',
    description: 'Wildlife safari fallback image'
  },
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/adventure-safari.webp',
    description: 'Adventure safari fallback image'
  },
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/cultural-safari.webp',
    description: 'Cultural safari fallback image'
  },
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/nature-safari.webp',
    description: 'Nature safari fallback image'
  },
  {
    localPath: '../public/images/hero/great-migration-serengeti-national-park.webp',
    supabasePath: 'fallback/luxury-safari.webp',
    description: 'Luxury safari fallback image'
  }
];

async function uploadFallbackImages() {
  console.log('🚀 Uploading fallback images to Supabase storage...\n');

  const bucket = 'sas-package-images';
  let successCount = 0;
  let errorCount = 0;

  for (const image of fallbackImages) {
    try {
      const localImagePath = join(__dirname, image.localPath);
      
      // Check if local image exists
      if (!existsSync(localImagePath)) {
        console.log(`⚠️  Local image not found: ${image.localPath}`);
        errorCount++;
        continue;
      }

      // Read the image file
      const imageBuffer = readFileSync(localImagePath);
      
      console.log(`📤 Uploading: ${image.description}`);
      console.log(`   Local: ${image.localPath}`);
      console.log(`   Supabase: ${image.supabasePath}`);

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(image.supabasePath, imageBuffer, {
          contentType: 'image/webp',
          cacheControl: '3600',
          upsert: true // Overwrite if exists
        });

      if (error) {
        console.log(`   ❌ Failed: ${error.message}`);
        errorCount++;
      } else {
        console.log(`   ✅ Success: ${data.path}`);
        
        // Get and display the public URL
        const { data: urlData } = supabase.storage
          .from(bucket)
          .getPublicUrl(image.supabasePath);
        
        console.log(`   🔗 URL: ${urlData.publicUrl}`);
        successCount++;
      }
      
      console.log('');
    } catch (err) {
      console.log(`   ❌ Error: ${err.message}`);
      errorCount++;
    }
  }

  console.log('📊 Upload Summary:');
  console.log(`✅ Successful uploads: ${successCount}`);
  console.log(`❌ Failed uploads: ${errorCount}`);
  console.log(`📦 Total images: ${fallbackImages.length}`);

  if (successCount > 0) {
    console.log('\n🎉 Fallback images are now available in Supabase storage!');
    console.log('🔧 You can now use the fallback image utilities in your app.');
  }

  if (errorCount > 0) {
    console.log('\n⚠️  Some uploads failed. Check the errors above.');
  }
}

uploadFallbackImages().catch(console.error);
