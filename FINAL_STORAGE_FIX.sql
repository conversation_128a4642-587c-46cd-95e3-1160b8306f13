-- FINAL SOLUTION: Create package-images bucket (COMPLETELY PUBLIC)
-- Copy and paste this ENTIRE block into your Supabase SQL Editor
-- This creates the bucket without modifying the storage.objects table directly

-- Step 1: Create the bucket (this should work)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'package-images',
  'package-images',
  true,
  5242880,
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- Step 2: Verify the bucket was created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'package-images';

-- Note: The bucket will be public by default when created with public=true
-- RLS policies are automatically handled by Supabase for public buckets
-- No need to manually create policies for public buckets