#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDatabasePolicies() {
  console.log('🔧 Fixing Database Policies...\n');

  try {
    // Read the SQL file
    const sqlFilePath = join(__dirname, 'setup-admin-database.sql');
    const sqlContent = readFileSync(sqlFilePath, 'utf8');

    console.log('1. Applying fixed database schema...');
    
    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    let successCount = 0;
    let errorCount = 0;

    for (const statement of statements) {
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) {
          console.log(`   ⚠️  Statement skipped: ${error.message}`);
          errorCount++;
        } else {
          successCount++;
        }
      } catch (err) {
        console.log(`   ⚠️  Statement skipped: ${err.message}`);
        errorCount++;
      }
    }

    console.log(`   ✅ Applied ${successCount} statements successfully`);
    if (errorCount > 0) {
      console.log(`   ⚠️  Skipped ${errorCount} statements (may already exist)`);
    }
    console.log('');

    // Test the fix
    console.log('2. Testing database access...');
    const { data: users, error: testError } = await supabase
      .from('users')
      .select('id, name, email, role, status')
      .limit(1);

    if (testError) {
      console.error('❌ Database access test failed:', testError.message);
      return;
    }

    console.log('✅ Database access working correctly');
    console.log(`   Found ${users?.length || 0} users in database\n`);

    // Check for admin users
    console.log('3. Checking for admin users...');
    const { data: adminUsers, error: adminError } = await supabase
      .from('users')
      .select('id, name, email, role, status')
      .eq('role', 'admin')
      .eq('status', 'active');

    if (adminError) {
      console.error('❌ Admin users query failed:', adminError.message);
      return;
    }

    if (adminUsers && adminUsers.length > 0) {
      console.log('✅ Admin users found:');
      adminUsers.forEach(user => {
        console.log(`   - ${user.name} (${user.email}) - ${user.status}`);
      });
    } else {
      console.log('⚠️  No active admin users found');
      console.log('   You may need to create an admin user using the setup script');
    }
    console.log('');

    console.log('🎉 Database policies fixed successfully!');
    console.log('\n📝 Next steps:');
    console.log('   1. Run: node scripts/setup-admin-user.mjs (if no admin user exists)');
    console.log('   2. Test login at: http://localhost:3000/admin/login');
    console.log('   3. Run: node scripts/test-login.mjs (to verify everything works)');

  } catch (error) {
    console.error('❌ Failed to fix database policies:', error.message);
    console.log('\n🔧 Manual fix required:');
    console.log('   1. Go to your Supabase dashboard');
    console.log('   2. Navigate to SQL Editor');
    console.log('   3. Run the contents of scripts/setup-admin-database.sql');
    console.log('   4. Test the login functionality');
  }
}

// Run the fix
fixDatabasePolicies(); 