import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeBlogFields() {
  console.log('🔍 Analyzing blog post fields for: mountain-gorilla-trekking-in-rwanda-and-uganda\n');
  
  try {
    // Get the blog post with all data
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (postError || !post) {
      console.error('❌ Error fetching blog post:', postError);
      return;
    }
    
    console.log(`✅ Found blog post: ${post.title}\n`);
    
    // Analyze each field size
    console.log('📊 FIELD SIZE ANALYSIS:');
    console.log('=' .repeat(50));
    
    const fieldSizes = [];
    
    for (const [key, value] of Object.entries(post)) {
      const fieldSize = JSON.stringify(value).length;
      fieldSizes.push({ field: key, size: fieldSize, value });
      
      const sizeKB = (fieldSize / 1024).toFixed(2);
      const sizeMB = (fieldSize / 1024 / 1024).toFixed(2);
      
      if (fieldSize > 1024) { // Show fields larger than 1KB
        console.log(`📝 ${key}:`);
        console.log(`   Size: ${sizeKB} KB (${sizeMB} MB)`);
        
        if (fieldSize > 100000) { // 100KB
          console.log(`   ⚠️  VERY LARGE FIELD!`);
          
          if (typeof value === 'string') {
            console.log(`   Preview: ${value.substring(0, 200)}...`);
            console.log(`   Length: ${value.length} characters`);
          } else {
            console.log(`   Type: ${typeof value}`);
            console.log(`   Preview: ${JSON.stringify(value).substring(0, 200)}...`);
          }
        } else {
          if (typeof value === 'string' && value.length > 100) {
            console.log(`   Preview: ${value.substring(0, 100)}...`);
          } else {
            console.log(`   Value: ${JSON.stringify(value)}`);
          }
        }
        console.log('');
      }
    }
    
    // Sort by size and show top 10 largest fields
    fieldSizes.sort((a, b) => b.size - a.size);
    
    console.log('\n🏆 TOP 10 LARGEST FIELDS:');
    console.log('=' .repeat(50));
    
    fieldSizes.slice(0, 10).forEach((field, index) => {
      const sizeKB = (field.size / 1024).toFixed(2);
      const sizeMB = (field.size / 1024 / 1024).toFixed(2);
      console.log(`${index + 1}. ${field.field}: ${sizeKB} KB (${sizeMB} MB)`);
    });
    
    // Check for specific problematic patterns
    console.log('\n🔍 CHECKING FOR PROBLEMATIC PATTERNS:');
    console.log('=' .repeat(50));
    
    // Check for base64 encoded data
    for (const [key, value] of Object.entries(post)) {
      if (typeof value === 'string') {
        if (value.includes('data:image/') || value.includes('base64,')) {
          console.log(`⚠️  Field '${key}' contains base64 encoded data (${(value.length / 1024).toFixed(2)} KB)`);
        }
        
        if (value.includes('blob:')) {
          console.log(`⚠️  Field '${key}' contains blob URL: ${value.substring(0, 100)}...`);
        }
        
        // Check for repeated content
        if (value.length > 10000) {
          const firstPart = value.substring(0, 1000);
          const occurrences = (value.match(new RegExp(firstPart.substring(0, 100), 'g')) || []).length;
          if (occurrences > 5) {
            console.log(`⚠️  Field '${key}' may contain repeated content (${occurrences} occurrences)`);
          }
        }
      }
    }
    
    // Calculate total size
    const totalSize = fieldSizes.reduce((sum, field) => sum + field.size, 0);
    console.log(`\n📊 TOTAL POST SIZE: ${(totalSize / 1024).toFixed(2)} KB (${(totalSize / 1024 / 1024).toFixed(2)} MB)`);
    
    if (totalSize > 19 * 1024 * 1024) { // 19MB (close to Vercel's 19.07MB limit)
      console.log(`🚨 POST SIZE EXCEEDS VERCEL'S ISR LIMIT!`);
      console.log(`   Current size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
      console.log(`   Vercel limit: 19.07 MB`);
      console.log(`   Needs reduction: ${((totalSize - 19 * 1024 * 1024) / 1024 / 1024).toFixed(2)} MB`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

analyzeBlogFields();
