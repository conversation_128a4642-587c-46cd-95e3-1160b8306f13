import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { sendTourBookingNotificationEmail } from '@/lib/email-service';

// Interface for tour booking data
interface TourBookingData {
  full_name: string;
  email: string;
  whatsapp: string;
  number_of_people: number;
  message?: string;
}

// GET - Fetch tour bookings (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let query = supabase
      .from('sas_tour_bookings')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`full_name.ilike.%${search}%,email.ilike.%${search}%,whatsapp.ilike.%${search}%`);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching tour bookings:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch tour bookings' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error in GET /api/bookings/tour:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new tour booking
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['fullName', 'email', 'whatsapp', 'numberOfPeople'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate number of people
    const numberOfPeople = parseInt(body.numberOfPeople);
    if (isNaN(numberOfPeople) || numberOfPeople < 1) {
      return NextResponse.json(
        { success: false, error: 'Invalid number of people' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const bookingData: TourBookingData = {
      full_name: body.fullName.trim(),
      email: body.email.toLowerCase().trim(),
      whatsapp: body.whatsapp.trim(),
      number_of_people: numberOfPeople,
      message: body.message?.trim() || ''
    };

    // Insert booking into database
    const { data: newBooking, error } = await supabase
      .from('sas_tour_bookings')
      .insert([bookingData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create tour booking in database' },
        { status: 500 }
      );
    }

    // Send admin notification email
    try {
      await sendTourBookingNotificationEmail(newBooking);
      console.log('Tour booking notification email sent successfully');
      
      // Update email sent status
      await supabase
        .from('sas_tour_bookings')
        .update({ 
          email_sent: true, 
          email_sent_at: new Date().toISOString() 
        })
        .eq('id', newBooking.id);
        
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the booking if email fails
    }

    return NextResponse.json({
      success: true,
      data: {
        id: newBooking.id,
        fullName: newBooking.full_name,
        email: newBooking.email,
        whatsapp: newBooking.whatsapp,
        numberOfPeople: newBooking.number_of_people,
        message: newBooking.message,
        status: newBooking.status,
        createdAt: newBooking.created_at
      },
      message: 'Tour booking created successfully'
    });

  } catch (error) {
    console.error('Error creating tour booking:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create tour booking' },
      { status: 500 }
    );
  }
}

// PATCH - Update tour booking status (admin only)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, status, adminNotes } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Booking ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};
    
    if (status) {
      const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
      if (!validStatuses.includes(status)) {
        return NextResponse.json(
          { success: false, error: 'Invalid status' },
          { status: 400 }
        );
      }
      updateData.status = status;
    }

    if (adminNotes !== undefined) {
      updateData.admin_notes = adminNotes;
    }

    const { data, error } = await supabase
      .from('sas_tour_bookings')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating tour booking:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update tour booking' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data,
      message: 'Tour booking updated successfully'
    });

  } catch (error) {
    console.error('Error in PATCH /api/bookings/tour:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
