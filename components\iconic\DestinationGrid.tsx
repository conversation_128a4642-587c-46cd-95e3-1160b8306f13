"use client";
import React from 'react';
import { useRouter } from 'next/navigation';

interface Destination {
  name: string;
  image: string;
}

interface DestinationGridProps {
  destinations: Destination[];
}

const DestinationGrid: React.FC<DestinationGridProps> = ({ destinations }) => {
  const router = useRouter();

  const handleDestinationClick = (destination: Destination) => {
    router.push(`/iconic/${destination.name.toLowerCase()}`);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
      {destinations.map((destination, index) => (
        <div
          key={index}
          className="relative h-48 sm:h-56 md:h-64 overflow-hidden cursor-pointer transform transition-transform duration-300 hover:scale-105"
          onClick={() => handleDestinationClick(destination)}
        >
          {/* Background image with alt attribute */}
          <img
            src={destination.image}
            alt={destination.alt}
            className="absolute inset-0 w-full h-full object-cover"
          />

          {/* Overlay for better text readability */}
          <div className="absolute inset-0 bg-black/20"></div>

          {/* Destination name */}
          <div className="absolute inset-0 flex items-center justify-center">
            <h2 className="text-white text-2xl md:text-3xl lg:text-4xl font-light tracking-wide text-center px-4">
              {destination.name}
            </h2>
          </div>
        </div>
      ))}
    </div>
  );
};

export default DestinationGrid;
