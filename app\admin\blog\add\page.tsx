'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, Save } from 'lucide-react';
import BlogCard from '@/components/admin/blog/blogCard';
import BlogEditor from '@/components/admin/blog/editor';
import BlogSEO from '@/components/admin/blog/seo';

interface ImageContent {
  src: string;
  alt: string;
  caption: string;
}

interface VideoContent {
  src: string;
  poster?: string;
  caption: string;
  width?: 'sm' | 'md' | 'lg' | 'full';
}

interface QuoteContent {
  content: string;
  author: string;
  source: string;
}

interface ContentBlock {
  id: number;
  type: string;
  content: string | string[] | ImageContent | VideoContent | QuoteContent | null;
}

interface BlogFormData {
  title: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  category: string;
  tags: string[];
  status: 'published' | 'draft' | 'archived';
  content: ContentBlock[];
  publishedAt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  schemaData: any;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function AddBlogPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<BlogFormData>({
    title: '',
    description: '',
    imageUrl: '',
    imageAlt: '',
    category: '',
    tags: [],
    status: 'draft',
    content: [],
    publishedAt: '',
    slug: '',
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    ogTitle: '',
    ogDescription: '',
    ogImageUrl: '',
    canonicalUrl: '',
    robotsIndex: 'index',
    robotsFollow: 'follow',
    schemaData: null
  });

  const tabs = [
    { id: 'basic', label: 'Basic Info', component: 'BlogCard' },
    { id: 'content', label: 'Content', component: 'BlogEditor' },
    { id: 'seo', label: 'SEO', component: 'BlogSEO' }
  ];

  const handleFormDataChange = (data: Partial<BlogFormData>) => {
    setFormData(prev => ({ ...prev, ...data }));
  };

  const handleSave = async (statusOverride?: string) => {
    setLoading(true);
    try {
      // Validate required fields
      if (!formData.title || !formData.description || !formData.category) {
        alert('Please fill in all required fields (Title, Description, Category)');
        setLoading(false);
        return;
      }

      // Check for blob URLs in content blocks
      const hasBlobUrls = formData.content.some(block => {
        if (block.type === 'image' && block.content) {
          const imageContent = block.content as ImageContent;
          return imageContent.src && imageContent.src.startsWith('blob:');
        }
        return false;
      });

      if (hasBlobUrls) {
        alert('Please wait for all images to finish uploading before saving.');
        setLoading(false);
        return;
      }

      // Generate slug if not provided
      if (!formData.slug) {
        const slug = formData.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '');
        setFormData(prev => ({ ...prev, slug }));
      }

      // Prepare data for API
      // Convert content blocks to API format
      const contentForAPI = formData.content.map((block) => ({
        type: block.type,
        content: block.content
      }));

      const blogData = {
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        hero_image_url: formData.imageUrl,
        hero_image_alt: formData.imageAlt,
        category: formData.category,
        tags: formData.tags,
        status: statusOverride || formData.status,
        content: contentForAPI,
        seo_title: formData.seoTitle,
        seo_description: formData.seoDescription,
        seo_keywords: formData.seoKeywords,
        og_title: formData.ogTitle,
        og_description: formData.ogDescription,
        og_image_url: formData.ogImageUrl,
        canonical_url: formData.canonicalUrl,
        robots_index: formData.robotsIndex,
        robots_follow: formData.robotsFollow,
        schema_data: formData.schemaData
      };

      // Make API call to create blog post
      const response = await fetch('/api/admin/blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(blogData),
      });

      const data = await response.json();

      if (data.success) {
        alert('Blog post created successfully!');
        router.push('/admin/blog');
      } else {
        // Handle 413 errors with tips
        if (response.status === 413 && data.tips) {
          let message = data.error || 'Blog content is too large.';
          message += '\n\nTips to reduce content size:\n• ' + data.tips.join('\n• ');
          alert(message);
          return;
        }
        throw new Error(data.error || 'Failed to create blog post');
      }
    } catch (error) {
      console.error('Error saving blog post:', error);

      // Handle other error types
      if (error instanceof Error) {
        alert(`Error saving blog post: ${error.message}`);
      } else {
        alert('Error saving blog post. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return (
          <BlogCard
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      case 'content':
        return (
          <BlogEditor
            initialContent={formData.content}
            onSave={(content) => handleFormDataChange({ content })}
          />
        );
      case 'seo':
        return (
          <BlogSEO
            formData={formData}
            onFormDataChange={handleFormDataChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className='bg-white rounded-lg shadow-sm'>

      {/* Header */}
      <div className="p-6 border-b border-gray-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ArrowLeft className="w-5 h-5 text-gray-600" />
            </button>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-[var(--accent)] rounded-lg">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-[var(--text)]">Add New Blog Post</h1>
                <p className="text-gray-600">Create and publish a new blog post</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => handleSave('draft')}
              disabled={loading}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
            >
              Save as Draft
            </button>
            <button
              onClick={() => handleSave('published')}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              Publish
            </button>
            <button
              onClick={() => handleSave()}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--accent)]/90 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {loading ? 'Saving...' : `Save as ${formData.status === 'published' ? 'Published' : formData.status === 'draft' ? 'Draft' : 'Archived'}`}
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-6">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-[var(--accent)] text-[var(--accent)]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        <div className="max-w-7xl mx-auto">
          {renderTabContent()}
        </div>
      </div>
      </div>
    </div>
  );
}
