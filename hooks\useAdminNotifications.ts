'use client';

import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

interface Notification {
  id: string;
  type: 'new_comment' | 'new_booking' | 'new_contact' | 'system';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  created_at: string;
}

interface UseAdminNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  markAsRead: (id: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
  deleteNotification: (id: string) => Promise<boolean>;
  clearAll: () => Promise<boolean>;
}

export const useAdminNotifications = (): UseAdminNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // For now, we'll create notifications based on recent comments
      // In a full implementation, you'd have a dedicated notifications table
      const { data: recentComments, error: commentsError } = await supabase
        .from('sas_blog_comments')
        .select(`
          id,
          author_name,
          content,
          created_at,
          is_admin_reply,
          sas_blog_posts!inner(title, slug)
        `)
        .eq('status', 'pending')
        .eq('is_admin_reply', false)
        .order('created_at', { ascending: false })
        .limit(20);

      if (commentsError) throw commentsError;

      // Transform comments into notifications
      const commentNotifications: Notification[] = (recentComments || []).map(comment => ({
        id: `comment-${comment.id}`,
        type: 'new_comment' as const,
        title: 'New Comment',
        message: `${comment.author_name} commented on "${comment.sas_blog_posts.title}"`,
        data: {
          commentId: comment.id,
          blogSlug: comment.sas_blog_posts.slug,
          authorName: comment.author_name,
          content: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : '')
        },
        read: false,
        created_at: comment.created_at
      }));

      setNotifications(commentNotifications);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError('Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  }, []);

  // Set up real-time subscription for new comments
  useEffect(() => {
    let subscription: any = null;

    const setupRealtimeSubscription = () => {
      subscription = supabase
        .channel('admin-notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'sas_blog_comments',
            filter: 'is_admin_reply=eq.false'
          },
          async (payload) => {
            console.log('New comment notification:', payload);
            
            // Fetch blog post title for the notification
            const { data: blogPost } = await supabase
              .from('sas_blog_posts')
              .select('title, slug')
              .eq('id', payload.new.blog_post_id)
              .single();

            if (blogPost) {
              const newNotification: Notification = {
                id: `comment-${payload.new.id}`,
                type: 'new_comment',
                title: 'New Comment',
                message: `${payload.new.author_name} commented on "${blogPost.title}"`,
                data: {
                  commentId: payload.new.id,
                  blogSlug: blogPost.slug,
                  authorName: payload.new.author_name,
                  content: payload.new.content.substring(0, 100) + (payload.new.content.length > 100 ? '...' : '')
                },
                read: false,
                created_at: payload.new.created_at
              };

              setNotifications(prev => [newNotification, ...prev]);

              // Show browser notification if permission granted
              if (Notification.permission === 'granted') {
                new Notification('New Comment', {
                  body: `${payload.new.author_name} commented on "${blogPost.title}"`,
                  icon: '/favicon.ico',
                  tag: `comment-${payload.new.id}`
                });
              }
            }
          }
        )
        .subscribe((status) => {
          console.log('Admin notifications subscription status:', status);
        });
    };

    setupRealtimeSubscription();

    return () => {
      if (subscription) {
        supabase.removeChannel(subscription);
      }
    };
  }, []);

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Mark notification as read
  const markAsRead = async (id: string): Promise<boolean> => {
    try {
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, read: true }
            : notification
        )
      );
      
      // In a full implementation, you'd update the database here
      return true;
    } catch (err) {
      console.error('Error marking notification as read:', err);
      return false;
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async (): Promise<boolean> => {
    try {
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, read: true }))
      );
      
      // In a full implementation, you'd update the database here
      return true;
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
      return false;
    }
  };

  // Delete notification
  const deleteNotification = async (id: string): Promise<boolean> => {
    try {
      setNotifications(prev => prev.filter(notification => notification.id !== id));
      
      // In a full implementation, you'd update the database here
      return true;
    } catch (err) {
      console.error('Error deleting notification:', err);
      return false;
    }
  };

  // Clear all notifications
  const clearAll = async (): Promise<boolean> => {
    try {
      setNotifications([]);
      
      // In a full implementation, you'd update the database here
      return true;
    } catch (err) {
      console.error('Error clearing all notifications:', err);
      return false;
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll
  };
};
