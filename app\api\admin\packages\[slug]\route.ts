import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

interface PackageData {
  id?: string;
  title: string;
  slug?: string;
  difficulty: string;
  category: string;
  location: string;
  duration?: string;
  pricing_solo: number;
  pricing_honeymoon: number;
  pricing_family: number;
  pricing_group: number;
  status?: string;

  image_url?: string;
  image_alt?: string;
  hero_image_url?: string;
  hero_image_alt?: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_url?: string;
  canonical_url?: string;
  robots_index?: string;
  robots_follow?: string;
  schema_data?: any;
  highlights?: string[];
  packing_list?: string[];
  includes?: string[];
  excludes?: string[];
  created_at?: string;
  updated_at?: string;
  published_at?: string;
}

// GET - Fetch single package by slug
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Check if the parameter is a UUID (ID) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(slug);

    // Fetch package with all related data
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select(`
        *,
        sas_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_package_itinerary (
          id,
          day_number,
          title,
          description,
          activities,
          accommodation,
          meals,
          sort_order
        ),
        sas_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq(isUUID ? 'id' : 'slug', slug)
      .single();

    if (packageError) {
      if (packageError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Package not found' },
          { status: 404 }
        );
      }
      console.error('Supabase error:', packageError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch package from database' },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedPackage = {
      id: packageData.id,
      title: packageData.title,
      slug: packageData.slug,
      difficulty: packageData.difficulty,
      category: packageData.category,
      location: packageData.location,
      duration: packageData.duration,
      pricing: {
        solo: Number(packageData.pricing_solo) || 0,
        honeymoon: Number(packageData.pricing_honeymoon) || 0,
        family: Number(packageData.pricing_family) || 0,
        group: Number(packageData.pricing_group) || 0
      },
      status: packageData.status,
      featured: packageData.featured,
      imageUrl: packageData.image_url,
      imageAlt: packageData.image_alt,
      heroImageUrl: packageData.hero_image_url,
      heroImageAlt: packageData.hero_image_alt,
      seoTitle: packageData.seo_title,
      seoDescription: packageData.seo_description,
      seoKeywords: packageData.seo_keywords,
      ogTitle: packageData.og_title,
      ogDescription: packageData.og_description,
      ogImageUrl: packageData.og_image_url,
      canonicalUrl: packageData.canonical_url,
      robotsIndex: packageData.robots_index,
      robotsFollow: packageData.robots_follow,
      schemaData: packageData.schema_data,
      highlights: packageData.highlights,
      packingList: packageData.packing_list,
      includes: packageData.includes,
      excludes: packageData.excludes,
      content: packageData.sas_package_content_blocks?.sort((a: any, b: any) => a.sort_order - b.sort_order).map((block: any) => ({
        id: block.id,
        type: block.block_type,
        content: block.content,
        imageUrl: block.image_url,
        imageAlt: block.image_alt,
        level: block.content_data?.level,
        items: block.content_data?.items
      })) || [],
      itinerary: packageData.sas_package_itinerary?.sort((a: any, b: any) => a.sort_order - b.sort_order) || [],
      images: packageData.sas_package_images?.sort((a: any, b: any) => a.sort_order - b.sort_order) || [],
      createdAt: packageData.created_at,
      updatedAt: packageData.updated_at,
      publishedAt: packageData.published_at
    };

    return NextResponse.json({
      success: true,
      data: transformedPackage
    });
  } catch (error) {
    console.error('Error fetching package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch package' },
      { status: 500 }
    );
  }
}

// PUT - Update single package
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Log the incoming data for debugging
    console.log('PUT request for slug:', slug);
    console.log('Request body:', JSON.stringify(body, null, 2));

    // Validate required fields
    const requiredFields = ['title', 'location', 'difficulty', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        console.error(`Missing required field: ${field}`);
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate image URLs - reject blob URLs
    if (body.imageUrl && body.imageUrl.startsWith('blob:')) {
      console.error('Package Update API - Blob URL detected in imageUrl:', body.imageUrl);
      return NextResponse.json(
        { success: false, error: 'Invalid image URL. Please upload the image properly.' },
        { status: 400 }
      );
    }

    if (body.heroImageUrl && body.heroImageUrl.startsWith('blob:')) {
      console.error('Package Update API - Blob URL detected in heroImageUrl:', body.heroImageUrl);
      return NextResponse.json(
        { success: false, error: 'Invalid hero image URL. Please upload the image properly.' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format (only include columns that exist in DB)
    const packageData: Partial<PackageData> = {
      title: body.title,
      difficulty: body.difficulty,
      category: body.category,
      location: body.location,
      duration: body.duration || '',
      status: body.status || 'draft',

      image_url: body.imageUrl || '',
      image_alt: body.imageAlt || '',
      hero_image_url: body.heroImageUrl || '',
      hero_image_alt: body.heroImageAlt || '',
      seo_title: body.seoTitle || '',
      seo_description: body.seoDescription || '',
      seo_keywords: body.seoKeywords || [],
      og_title: body.ogTitle || '',
      og_description: body.ogDescription || '',
      og_image_url: body.ogImageUrl || '',
      canonical_url: body.canonicalUrl || '',
      robots_index: body.robotsIndex || 'index',
      robots_follow: body.robotsFollow || 'follow',
      schema_data: body.schemaData || null,
      highlights: body.highlights || [],
      packing_list: body.packingList || [],
      includes: body.includes || [],
      excludes: body.excludes || []
    };

    // Update pricing if provided
    if (body.pricing && typeof body.pricing === 'object') {
      packageData.pricing_solo = Number(body.pricing.solo) || 0;
      packageData.pricing_honeymoon = Number(body.pricing.honeymoon) || 0;
      packageData.pricing_family = Number(body.pricing.family) || 0;
      packageData.pricing_group = Number(body.pricing.group) || 0;

      console.log('Pricing data being saved:', {
        solo: packageData.pricing_solo,
        honeymoon: packageData.pricing_honeymoon,
        family: packageData.pricing_family,
        group: packageData.pricing_group
      });
    }

    // Always update the updated_at timestamp
    packageData.updated_at = new Date().toISOString();

    // Check if the parameter is a UUID (ID) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(slug);
    console.log('Update slug:', slug, 'isUUID:', isUUID);

    // Log the data being sent to the database
    console.log('Data being sent to database:', JSON.stringify(packageData, null, 2));

    // Update package in database
    const { data: updatedPackage, error } = await supabase
      .from('sas_packages')
      .update(packageData)
      .eq(isUUID ? 'id' : 'slug', slug)
      .select()
      .single();

    // Log the response from the database
    console.log('Database update response:', updatedPackage ? 'Success' : 'Failed');
    if (updatedPackage) {
      console.log('Updated package data:', JSON.stringify(updatedPackage, null, 2));
    }

    if (error) {
      console.error('Supabase error:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      return NextResponse.json(
        { success: false, error: `Failed to update package in database: ${error.message}` },
        { status: 500 }
      );
    }

    // Transform the updated package data to match frontend expectations
    const transformedUpdatedPackage = {
      id: updatedPackage.id,
      title: updatedPackage.title,
      slug: updatedPackage.slug,
      difficulty: updatedPackage.difficulty,
      category: updatedPackage.category,
      location: updatedPackage.location,
      duration: updatedPackage.duration,
      pricing: {
        solo: Number(updatedPackage.pricing_solo) || 0,
        honeymoon: Number(updatedPackage.pricing_honeymoon) || 0,
        family: Number(updatedPackage.pricing_family) || 0,
        group: Number(updatedPackage.pricing_group) || 0
      },
      status: updatedPackage.status,
      featured: updatedPackage.featured,
      imageUrl: updatedPackage.image_url,
      imageAlt: updatedPackage.image_alt,
      heroImageUrl: updatedPackage.hero_image_url,
      heroImageAlt: updatedPackage.hero_image_alt,
      seoTitle: updatedPackage.seo_title,
      seoDescription: updatedPackage.seo_description,
      seoKeywords: updatedPackage.seo_keywords,
      ogTitle: updatedPackage.og_title,
      ogDescription: updatedPackage.og_description,
      ogImageUrl: updatedPackage.og_image_url,
      canonicalUrl: updatedPackage.canonical_url,
      robotsIndex: updatedPackage.robots_index,
      robotsFollow: updatedPackage.robots_follow,
      schemaData: updatedPackage.schema_data,
      highlights: updatedPackage.highlights,
      packingList: updatedPackage.packing_list,
      includes: updatedPackage.includes,
      excludes: updatedPackage.excludes,
      content: body.content || [],
      itinerary: body.itinerary || [],
      images: body.images || [],
      createdAt: updatedPackage.created_at,
      updatedAt: updatedPackage.updated_at,
      publishedAt: updatedPackage.published_at
    };

    return NextResponse.json({
      success: true,
      data: transformedUpdatedPackage,
      message: 'Package updated successfully'
    });
  } catch (error) {
    console.error('Error updating package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update package' },
      { status: 500 }
    );
  }
}

// DELETE - Delete single package
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Check if the parameter is a UUID (ID) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(slug);

    // Delete package from database (cascade will handle related records)
    const { error } = await supabase
      .from('sas_packages')
      .delete()
      .eq(isUUID ? 'id' : 'slug', slug);

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete package from database' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Package deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete package' },
      { status: 500 }
    );
  }
}
