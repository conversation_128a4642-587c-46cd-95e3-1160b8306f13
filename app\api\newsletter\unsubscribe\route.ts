import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// POST - Unsubscribe from newsletter
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // Validate email
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Check if email exists
    const { data: subscriber, error: checkError } = await supabase
      .from('sas_newsletter_subscribers')
      .select('id, status')
      .eq('email', email.toLowerCase())
      .single();

    if (checkError) {
      if (checkError.code === 'PGRST116') {
        return NextResponse.json(
          { success: false, error: 'Email not found in our newsletter list' },
          { status: 404 }
        );
      }
      console.error('Database check error:', checkError);
      return NextResponse.json(
        { success: false, error: 'Database error occurred' },
        { status: 500 }
      );
    }

    if (subscriber.status === 'unsubscribed') {
      return NextResponse.json(
        { success: false, error: 'Email is already unsubscribed' },
        { status: 409 }
      );
    }

    // Update subscription status
    const { error: updateError } = await supabase
      .from('sas_newsletter_subscribers')
      .update({ 
        status: 'unsubscribed',
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriber.id);

    if (updateError) {
      console.error('Database update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to unsubscribe from newsletter' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully unsubscribed from our newsletter'
    });

  } catch (error) {
    console.error('Newsletter unsubscribe error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Unsubscribe via email link (for email unsubscribe links)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return new Response(
        `<!DOCTYPE html>
        <html>
        <head>
          <title>Unsubscribe - Swift Africa Safaris</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
            .error { color: #d35400; background: #fdf2e9; padding: 15px; border-radius: 5px; }
          </style>
        </head>
        <body>
          <h1>Newsletter Unsubscribe</h1>
          <div class="error">Invalid unsubscribe link. Email parameter is missing.</div>
        </body>
        </html>`,
        { 
          status: 400,
          headers: { 'Content-Type': 'text/html' }
        }
      );
    }

    // Process unsubscribe
    const unsubscribeResponse = await fetch(`${request.nextUrl.origin}/api/newsletter/unsubscribe`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    });

    const result = await unsubscribeResponse.json();

    const htmlResponse = `<!DOCTYPE html>
    <html>
    <head>
      <title>Unsubscribe - Swift Africa Safaris</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          max-width: 600px; 
          margin: 50px auto; 
          padding: 20px; 
          background-color: #f5f5f5;
        }
        .container { 
          background: white; 
          padding: 30px; 
          border-radius: 10px; 
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { 
          color: #317100; 
          background: #f0f8e8; 
          padding: 15px; 
          border-radius: 5px; 
          border-left: 4px solid #317100;
        }
        .error { 
          color: #d35400; 
          background: #fdf2e9; 
          padding: 15px; 
          border-radius: 5px; 
          border-left: 4px solid #d35400;
        }
        .logo { text-align: center; margin-bottom: 30px; }
        h1 { color: #163201; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="logo">
          <h1>Swift Africa Safaris</h1>
        </div>
        <h2>Newsletter Unsubscribe</h2>
        <div class="${result.success ? 'success' : 'error'}">
          ${result.message || result.error}
        </div>
        ${result.success ? '<p>You will no longer receive newsletter emails from us.</p>' : ''}
        <p><a href="/" style="color: #317100;">Return to Swift Africa Safaris</a></p>
      </div>
    </body>
    </html>`;

    return new Response(htmlResponse, {
      status: result.success ? 200 : 400,
      headers: { 'Content-Type': 'text/html' }
    });

  } catch (error) {
    console.error('Newsletter unsubscribe GET error:', error);
    return new Response(
      `<!DOCTYPE html>
      <html>
      <head>
        <title>Error - Swift Africa Safaris</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
          .error { color: #d35400; background: #fdf2e9; padding: 15px; border-radius: 5px; }
        </style>
      </head>
      <body>
        <h1>Newsletter Unsubscribe</h1>
        <div class="error">An error occurred while processing your unsubscribe request.</div>
      </body>
      </html>`,
      { 
        status: 500,
        headers: { 'Content-Type': 'text/html' }
      }
    );
  }
}
