import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// Helper function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Interface definitions
interface PackageData {
  id?: string;
  title: string;
  slug?: string;
  difficulty: string;
  category: string;
  location: string;
  duration?: string;
  pricing_solo: number;
  pricing_honeymoon: number;
  pricing_family: number;
  pricing_group: number;
  status?: string;

  image_url?: string;
  image_alt?: string;
  hero_image_url?: string;
  hero_image_alt?: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_url?: string;
  canonical_url?: string;
  robots_index?: string;
  robots_follow?: string;
  schema_data?: any;
  highlights?: string[];
  packing_list?: string[];
  includes?: string[];
  excludes?: string[];
}

interface ContentBlock {
  id?: string;
  block_type: string;
  content?: string;
  content_data?: any;
  image_url?: string;
  image_alt?: string;
  image_caption?: string;
  sort_order: number;
}

interface ItineraryDay {
  id?: string;
  day_number: number;
  title: string;
  description: string;
  activities?: string[];
  accommodation?: string;
  meals?: string[];
  sort_order: number;
}

interface PackageImage {
  id?: string;
  image_url: string;
  image_alt: string;
  caption?: string;
  sort_order: number;
  is_featured?: boolean;
}

// GET - Fetch packages with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const category = searchParams.get('category') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build query
    let query = supabase
      .from('sas_packages')
      .select(`
        id,
        title,
        slug,
        location,
        pricing_solo,
        pricing_honeymoon,
        pricing_family,
        pricing_group,
        difficulty,
        category,
        status,

        image_url,
        image_alt,
        created_at,
        updated_at
      `);

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,location.ilike.%${search}%`);
    }

    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (category !== 'all') {
      query = query.eq('category', category);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    query = query
      .order('created_at', { ascending: false })
      .range(startIndex, startIndex + limit - 1);

    const { data: packages, error, count } = await query;

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch packages from database' },
        { status: 500 }
      );
    }

    // Transform data to match frontend expectations
    const transformedPackages = packages?.map(pkg => ({
      id: pkg.id,
      title: pkg.title,
      slug: pkg.slug,
      location: pkg.location,
      pricing: {
        solo: pkg.pricing_solo,
        honeymoon: pkg.pricing_honeymoon,
        family: pkg.pricing_family,
        group: pkg.pricing_group
      },
      difficulty: pkg.difficulty,
      category: pkg.category,
      status: pkg.status,
      imageUrl: pkg.image_url,
      createdAt: pkg.created_at,
      updatedAt: pkg.updated_at
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedPackages,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching packages:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch packages' },
      { status: 500 }
    );
  }
}

// POST - Create new package
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();



    // Validate required fields
    const requiredFields = ['title', 'location', 'difficulty', 'category'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate pricing structure
    if (!body.pricing || typeof body.pricing !== 'object') {
      return NextResponse.json(
        { success: false, error: 'Invalid pricing structure' },
        { status: 400 }
      );
    }

    // Generate slug from title
    let baseSlug = body.slug || generateSlug(body.title);
    let slug = baseSlug;
    let counter = 1;

    // Check if slug already exists and make it unique
    while (true) {
      const { data: existingPackage } = await supabase
        .from('sas_packages')
        .select('id')
        .eq('slug', slug)
        .single();

      if (!existingPackage) {
        break; // Slug is unique
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // Debug: Log what we received from frontend
    console.log('Package API - Received from frontend:', {
      imageUrl: body.imageUrl,
      heroImageUrl: body.heroImageUrl,
      imageAlt: body.imageAlt,
      heroImageAlt: body.heroImageAlt
    });

    // Validate image URLs - reject blob URLs
    if (body.imageUrl && body.imageUrl.startsWith('blob:')) {
      console.error('Package API - Blob URL detected in imageUrl:', body.imageUrl);
      return NextResponse.json(
        { success: false, error: 'Invalid image URL. Please upload the image properly.' },
        { status: 400 }
      );
    }

    if (body.heroImageUrl && body.heroImageUrl.startsWith('blob:')) {
      console.error('Package API - Blob URL detected in heroImageUrl:', body.heroImageUrl);
      return NextResponse.json(
        { success: false, error: 'Invalid hero image URL. Please upload the image properly.' },
        { status: 400 }
      );
    }

    // Transform frontend data to database format
    const packageData: PackageData = {
      title: body.title,
      slug: slug,
      difficulty: body.difficulty,
      category: body.category,
      location: body.location,
      duration: body.duration || '',
      pricing_solo: parseFloat(body.pricing.solo) || 0,
      pricing_honeymoon: parseFloat(body.pricing.honeymoon) || 0,
      pricing_family: parseFloat(body.pricing.family) || 0,
      pricing_group: parseFloat(body.pricing.group) || 0,
      status: body.status || 'draft',
      image_url: body.imageUrl || '',
      image_alt: body.imageAlt || '',
      hero_image_url: body.heroImageUrl || body.imageUrl || '',
      hero_image_alt: body.heroImageAlt || body.imageAlt || '',
      seo_title: body.seoTitle || body.title || '',
      seo_description: body.seoDescription || '',
      seo_keywords: body.seoKeywords || [],
      og_title: body.ogTitle || body.title || '',
      og_description: body.ogDescription || '',
      og_image_url: body.ogImageUrl || body.imageUrl || '',
      canonical_url: body.canonicalUrl || '',
      robots_index: body.robotsIndex || 'index',
      robots_follow: body.robotsFollow || 'follow',
      schema_data: body.schemaData || null,
      highlights: body.highlights || [],
      packing_list: body.packingList || [],
      includes: body.includes || [],
      excludes: body.excludes || []
    };

    // Debug: Log what we're storing in database
    console.log('Package API - Storing in database:', {
      image_url: packageData.image_url,
      hero_image_url: packageData.hero_image_url,
      image_alt: packageData.image_alt,
      hero_image_alt: packageData.hero_image_alt
    });

    // Extract related data
    const contentBlocks: ContentBlock[] = body.content || [];
    const itineraryDays: ItineraryDay[] = body.itinerary || [];
    const packageImages: PackageImage[] = body.images || [];

    // Remove any featured field that might have been added
    const { featured, ...cleanPackageData } = packageData as any;

    // Start transaction by inserting main package first
    const { data: newPackage, error: packageError } = await supabase
      .from('sas_packages')
      .insert([cleanPackageData])
      .select()
      .single();

    if (packageError) {
      console.error('Supabase error:', packageError);
      console.error('Package data that failed:', packageData);
      return NextResponse.json(
        {
          success: false,
          error: `Failed to create package in database: ${packageError.message}`,
          details: packageError
        },
        { status: 500 }
      );
    }

    const packageId = newPackage.id;
    const errors: string[] = [];

    // Insert content blocks if provided
    if (contentBlocks.length > 0) {
      const contentBlocksData = contentBlocks.map((block, index) => ({
        package_id: packageId,
        block_type: block.block_type,
        content: block.content || null,
        content_data: block.content_data || null,
        image_url: block.image_url || null,
        image_alt: block.image_alt || null,
        image_caption: block.image_caption || null,
        sort_order: block.sort_order || index
      }));

      const { error: contentError } = await supabase
        .from('sas_package_content_blocks')
        .insert(contentBlocksData);

      if (contentError) {
        console.error('Content blocks error:', contentError);
        errors.push(`Content blocks: ${contentError.message}`);
      }
    }

    // Insert itinerary days if provided
    if (itineraryDays.length > 0) {
      const itineraryData = itineraryDays.map((day, index) => ({
        package_id: packageId,
        day_number: day.day_number,
        title: day.title,
        description: day.description,
        activities: day.activities || [],
        accommodation: day.accommodation || null,
        meals: day.meals || [],
        sort_order: day.sort_order || index
      }));

      const { error: itineraryError } = await supabase
        .from('sas_package_itinerary')
        .insert(itineraryData);

      if (itineraryError) {
        console.error('Itinerary error:', itineraryError);
        errors.push(`Itinerary: ${itineraryError.message}`);
      }
    }

    // Insert package images if provided
    if (packageImages.length > 0) {
      const imagesData = packageImages.map((image, index) => ({
        package_id: packageId,
        image_url: image.image_url,
        image_alt: image.image_alt,
        caption: image.caption || null,
        sort_order: image.sort_order || index,
        is_featured: image.is_featured || false
      }));

      const { error: imagesError } = await supabase
        .from('sas_package_images')
        .insert(imagesData);

      if (imagesError) {
        console.error('Images error:', imagesError);
        errors.push(`Images: ${imagesError.message}`);
      }
    }

    // Return response with any warnings
    const response = {
      success: true,
      data: newPackage,
      message: 'Package created successfully'
    };

    if (errors.length > 0) {
      response.message += ` (with warnings: ${errors.join(', ')})`;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error creating package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create package' },
      { status: 500 }
    );
  }
}

// PUT - Bulk operations (delete multiple packages)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, packageIds } = body;

    if (!action || !packageIds || !Array.isArray(packageIds)) {
      return NextResponse.json(
        { success: false, error: 'Invalid request body' },
        { status: 400 }
      );
    }

    if (action === 'delete') {
      // Delete packages from database
      const { error } = await supabase
        .from('sas_packages')
        .delete()
        .in('id', packageIds);

      if (error) {
        console.error('Supabase error:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to delete packages from database' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: `${packageIds.length} package(s) deleted successfully`
      });
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
