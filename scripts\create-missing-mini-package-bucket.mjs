import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createMissingBucket() {
  console.log('🔧 Creating missing mini package storage bucket...\n');
  
  const bucketName = 'sas-mini-package-content';
  
  try {
    // Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError.message);
      return false;
    }
    
    const bucketExists = buckets?.some(bucket => bucket.name === bucketName);
    
    if (bucketExists) {
      console.log(`✅ Bucket ${bucketName} already exists`);
      return true;
    }
    
    // Create the bucket
    const { data, error } = await supabase.storage.createBucket(bucketName, {
      public: true,
      fileSizeLimit: 52428800, // 50MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    });
    
    if (error) {
      console.error(`❌ Error creating bucket ${bucketName}:`, error.message);
      return false;
    }
    
    console.log(`✅ Successfully created bucket: ${bucketName}`);
    
    // Set up storage policies using SQL
    const policies = [
      {
        name: 'Public can view mini package content',
        sql: `CREATE POLICY "Public can view mini package content" ON storage.objects FOR SELECT USING (bucket_id = '${bucketName}');`
      },
      {
        name: 'Authenticated users can upload mini package content',
        sql: `CREATE POLICY "Authenticated users can upload mini package content" ON storage.objects FOR INSERT WITH CHECK (bucket_id = '${bucketName}' AND auth.role() = 'authenticated');`
      },
      {
        name: 'Authenticated users can update mini package content',
        sql: `CREATE POLICY "Authenticated users can update mini package content" ON storage.objects FOR UPDATE USING (bucket_id = '${bucketName}' AND auth.role() = 'authenticated');`
      },
      {
        name: 'Authenticated users can delete mini package content',
        sql: `CREATE POLICY "Authenticated users can delete mini package content" ON storage.objects FOR DELETE USING (bucket_id = '${bucketName}' AND auth.role() = 'authenticated');`
      }
    ];
    
    console.log('\n🔒 Setting up storage policies...');
    
    for (const policy of policies) {
      try {
        const { error: policyError } = await supabase.rpc('exec_sql', {
          sql: policy.sql
        });
        
        if (policyError) {
          console.log(`⚠️  Warning: Could not create policy "${policy.name}": ${policyError.message}`);
        } else {
          console.log(`✅ Created policy: ${policy.name}`);
        }
      } catch (err) {
        console.log(`⚠️  Warning: Could not create policy "${policy.name}": ${err.message}`);
      }
    }
    
    return true;
    
  } catch (err) {
    console.error(`❌ Unexpected error: ${err.message}`);
    return false;
  }
}

async function verifyBuckets() {
  console.log('\n🔍 Verifying all mini package buckets...\n');
  
  const expectedBuckets = [
    'sas-mini-package-images',
    'sas-mini-package-content'
  ];
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('❌ Error listing buckets:', error.message);
      return false;
    }
    
    let allExist = true;
    
    for (const bucketName of expectedBuckets) {
      const bucketExists = buckets?.some(bucket => bucket.name === bucketName);
      
      if (bucketExists) {
        console.log(`✅ ${bucketName}: EXISTS`);
        
        // Test bucket access
        const { data: files, error: listError } = await supabase.storage
          .from(bucketName)
          .list('', { limit: 1 });
        
        if (listError) {
          console.log(`   ⚠️  Warning: Cannot list files: ${listError.message}`);
        } else {
          console.log(`   ✅ Accessible (${files?.length || 0} files)`);
        }
      } else {
        console.log(`❌ ${bucketName}: MISSING`);
        allExist = false;
      }
    }
    
    return allExist;
    
  } catch (err) {
    console.error(`❌ Error verifying buckets: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Mini Package Storage Bucket Setup\n');
  
  const created = await createMissingBucket();
  const verified = await verifyBuckets();
  
  console.log('\n' + '='.repeat(50));
  
  if (created && verified) {
    console.log('🎉 SUCCESS!');
    console.log('✅ All mini package storage buckets are now available');
    console.log('✅ Storage policies configured');
    console.log('\n📁 Available buckets:');
    console.log('   • sas-mini-package-images (main/hero images)');
    console.log('   • sas-mini-package-content (content block images)');
    console.log('\n🔗 Ready for image uploads!');
  } else {
    console.log('❌ FAILED');
    console.log('Some buckets could not be created or verified.');
    console.log('Please check the errors above and try again.');
  }
}

main();
