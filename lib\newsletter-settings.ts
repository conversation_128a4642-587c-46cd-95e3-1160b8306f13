import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

// Encryption key for SMTP passwords (in production, use environment variable)
const ENCRYPTION_KEY = process.env.NEWSLETTER_ENCRYPTION_KEY || 'swift-africa-safaris-newsletter-key-2024';

// Decrypt password - simplified for development
function decryptPassword(encryptedPassword: string): string {
  try {
    // For development, we'll store passwords in plain text
    // In production, implement proper encryption
    return encryptedPassword;
  } catch (error) {
    console.error('Password decryption error:', error);
    return '';
  }
}

// Helper function to get decrypted SMTP settings (for internal use)
export async function getNewsletterSettings() {
  try {
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    const { data: settings, error } = await supabaseAdmin
      .from('sas_newsletter_settings')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error || !settings) {
      return null;
    }

    // Decrypt password
    const decryptedPassword = decryptPassword(settings.smtp_password);

    return {
      ...settings,
      smtp_password: decryptedPassword
    };

  } catch (error) {
    console.error('Error fetching newsletter settings:', error);
    return null;
  }
}
