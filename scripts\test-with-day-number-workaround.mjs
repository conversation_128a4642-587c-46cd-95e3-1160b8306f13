import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testWithDayNumberWorkaround() {
  console.log('🔧 Testing upload with day_number workaround...\n');
  
  const testSlug = `test-workaround-${Date.now()}`;
  
  // Sample mini package data
  const testMiniPackage = {
    title: 'Test Workaround Package',
    slug: testSlug,
    location: 'Test Location',
    difficulty: 'Easy',
    category: 'adventure',
    duration: '8 hours',
    pricing_solo: 100,
    pricing_honeymoon: 180,
    pricing_family: 300,
    pricing_group: 250,
    status: 'draft',
    image_url: 'https://example.com/test-image.jpg',
    image_alt: 'Test image alt text',
    seo_title: 'Test SEO Title',
    seo_description: 'Test SEO Description',
    highlights: ['Test highlight 1'],
    includes: ['Test include 1'],
    excludes: ['Test exclude 1'],
    packing_list: ['Test item 1']
  };
  
  try {
    // Create the mini package
    const { data: miniPackage, error: createError } = await supabase
      .from('sas_mini_packages')
      .insert(testMiniPackage)
      .select()
      .single();
    
    if (createError) {
      console.log(`❌ Failed to create mini package: ${createError.message}`);
      return false;
    }
    
    console.log(`✅ Created mini package: ${miniPackage.title}`);
    
    // Create test itinerary with BOTH hour_number AND day_number
    const testItinerary = [
      {
        mini_package_id: miniPackage.id,
        hour_number: 1,
        day_number: 1, // Add day_number as workaround
        title: 'Hour 1: Arrival and Welcome',
        description: 'Welcome briefing and equipment check',
        sort_order: 0
      },
      {
        mini_package_id: miniPackage.id,
        hour_number: 2,
        day_number: 1, // All mini package hours are on day 1
        title: 'Hour 2: Activity Start',
        description: 'Begin the main activity',
        sort_order: 1
      },
      {
        mini_package_id: miniPackage.id,
        hour_number: 3,
        day_number: 1, // All mini package hours are on day 1
        title: 'Hour 3: Mid-point Break',
        description: 'Rest and refreshments',
        sort_order: 2
      }
    ];
    
    // Insert itinerary with both fields
    const { data: itinerary, error: itineraryError } = await supabase
      .from('sas_mini_package_itinerary')
      .insert(testItinerary)
      .select();
    
    if (itineraryError) {
      console.log(`❌ Failed to create itinerary: ${itineraryError.message}`);
      return false;
    }
    
    console.log(`✅ Created itinerary with ${itinerary.length} hours (using day_number workaround)`);
    
    // Test reading the data back
    const { data: readBack, error: readError } = await supabase
      .from('sas_mini_packages')
      .select(`
        *,
        sas_mini_package_itinerary (
          id,
          hour_number,
          day_number,
          title,
          description,
          sort_order
        )
      `)
      .eq('slug', testSlug)
      .single();
    
    if (readError) {
      console.log(`❌ Failed to read back data: ${readError.message}`);
      return false;
    }
    
    console.log(`✅ Successfully read back mini package data`);
    console.log(`📋 Itinerary items: ${readBack.sas_mini_package_itinerary?.length || 0}`);
    
    // Verify data
    if (readBack.sas_mini_package_itinerary) {
      console.log('\n📅 Itinerary verification:');
      readBack.sas_mini_package_itinerary.forEach(item => {
        console.log(`   Day ${item.day_number}, Hour ${item.hour_number}: ${item.title}`);
      });
    }
    
    // Clean up test data
    await supabase
      .from('sas_mini_package_itinerary')
      .delete()
      .eq('mini_package_id', miniPackage.id);
    
    await supabase
      .from('sas_mini_packages')
      .delete()
      .eq('id', miniPackage.id);
    
    console.log('✅ Test data cleaned up');
    
    return true;
    
  } catch (err) {
    console.log(`❌ Unexpected error: ${err.message}`);
    return false;
  }
}

async function suggestAPIFix() {
  console.log('\n🔧 SUGGESTED API FIX:\n');
  
  console.log('Since the day_number column exists and has NOT NULL constraint,');
  console.log('we need to update the API endpoints to provide day_number = 1 for all mini packages.\n');
  
  console.log('In app/api/admin/mini-packages/route.ts (POST endpoint):');
  console.log('Change line ~300:');
  console.log('  hour_number: day.hour_number,');
  console.log('To:');
  console.log('  hour_number: day.hour_number,');
  console.log('  day_number: 1, // Mini packages are single-day experiences');
  console.log('');
  
  console.log('In app/api/admin/mini-packages/[slug]/route.ts (PUT endpoint):');
  console.log('Change line ~276:');
  console.log('  hour_number: day.hour_number,');
  console.log('To:');
  console.log('  hour_number: day.hour_number,');
  console.log('  day_number: 1, // Mini packages are single-day experiences');
  console.log('');
  
  console.log('This will make the upload API work immediately while preserving the database schema.');
}

async function main() {
  console.log('🚀 Testing Mini Package Upload with day_number Workaround\n');
  
  const success = await testWithDayNumberWorkaround();
  
  if (success) {
    console.log('\n🎉 WORKAROUND SUCCESSFUL!');
    console.log('✅ Upload works when both hour_number and day_number are provided');
    console.log('✅ Mini package creation and reading work correctly');
    
    await suggestAPIFix();
    
    console.log('\n🔗 After applying the API fix, the upload functionality will work!');
  } else {
    console.log('\n❌ WORKAROUND FAILED');
    console.log('There may be other issues with the database schema.');
  }
}

main();
