# Supabase Storage Setup Guide

This guide will help you set up the required storage buckets for your Swift Africa Safaris website.

## 🗂️ Required Storage Buckets

Your application needs the following storage buckets:

1. **`sas-package-images`** - For package main images and gallery
2. **`sas-blog-images`** - For blog post images
3. **`sas-user-avatars`** - For user profile pictures
4. **`sas-general-uploads`** - For miscellaneous uploads

## 📋 Step-by-Step Setup

### Method 1: Using Supabase Dashboard (Recommended)

1. **Go to your Supabase Dashboard**
   - Visit [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Select your project

2. **Navigate to Storage**
   - Click on "Storage" in the left sidebar
   - Click "Create a new bucket"

3. **Create Each Bucket**
   
   **For `sas-package-images` bucket:**
   - Name: `sas-package-images`
   - Public: ✅ **Enable** (so images can be displayed on website)
   - File size limit: `10 MB`
   - Allowed MIME types: `image/jpeg, image/png, image/webp, image/gif`
   - Click "Create bucket"

   **For `sas-blog-images` bucket:**
   - Name: `sas-blog-images`
   - Public: ✅ **Enable**
   - File size limit: `10 MB`
   - Allowed MIME types: `image/jpeg, image/png, image/webp, image/gif`
   - Click "Create bucket"

   **For `sas-user-avatars` bucket:**
   - Name: `sas-user-avatars`
   - Public: ✅ **Enable**
   - File size limit: `5 MB`
   - Allowed MIME types: `image/jpeg, image/png, image/webp`
   - Click "Create bucket"

   **For `sas-general-uploads` bucket:**
   - Name: `sas-general-uploads`
   - Public: ✅ **Enable**
   - File size limit: `50 MB`
   - Allowed MIME types: `image/*, application/pdf, text/*`
   - Click "Create bucket"

### Method 2: Using SQL Commands

If you prefer to set up via SQL, copy and paste this into your Supabase SQL Editor:

```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('sas-package-images', 'sas-package-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('sas-blog-images', 'sas-blog-images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('sas-user-avatars', 'sas-user-avatars', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('sas-general-uploads', 'sas-general-uploads', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf', 'text/plain', 'text/csv'])
ON CONFLICT (id) DO NOTHING;
```

## 🔐 Storage Policies Setup

After creating the buckets, you need to set up Row Level Security (RLS) policies:

### Copy this SQL to your Supabase SQL Editor:

```sql
-- =====================================================
-- STORAGE POLICIES
-- =====================================================

-- Policy for sas-package-images bucket
CREATE POLICY "Anyone can view package images" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-package-images');

CREATE POLICY "Authenticated users can upload package images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update package images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete package images" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');

-- Policy for sas-blog-images bucket
CREATE POLICY "Anyone can view blog images" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-blog-images');

CREATE POLICY "Authenticated users can upload blog images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update blog images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete blog images" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

-- Policy for sas-user-avatars bucket
CREATE POLICY "Anyone can view user avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-user-avatars');

CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update their own avatars" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

CREATE POLICY "Users can delete their own avatars" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

-- Policy for sas-general-uploads bucket
CREATE POLICY "Anyone can view general uploads" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-general-uploads');

CREATE POLICY "Authenticated users can upload general files" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update general files" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete general files" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');
```

## ✅ Verification

After setup, verify your buckets are working:

1. **Check in Supabase Dashboard:**
   - Go to Storage → All buckets
   - You should see all 4 buckets listed
   - Each should show as "Public" = true

2. **Test Upload:**
   - Try uploading an image through your admin panel
   - Check if the image appears in the correct bucket
   - Verify the public URL works

## 🚨 Important Notes

- **Public Buckets**: All buckets are set to public so images can be displayed on your website
- **File Size Limits**: 
  - Package/Blog images: 10MB max
  - User avatars: 5MB max  
  - General uploads: 50MB max
- **MIME Types**: Only allowed image formats can be uploaded
- **Security**: Only authenticated users can upload/modify files
- **Alt Text**: Your upload API requires alt text for all images (accessibility)

## 🔧 Environment Variables

Make sure your `.env.local` file has the correct Supabase configuration:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 📁 Bucket Usage in Your App

- **`sas-package-images`**: Used by package upload components
- **`sas-blog-images`**: Used by blog post editor
- **`sas-user-avatars`**: Used by user management system
- **`sas-general-uploads`**: Used for any other file uploads

Your upload API at `/api/upload/image` is already configured to use these buckets!
