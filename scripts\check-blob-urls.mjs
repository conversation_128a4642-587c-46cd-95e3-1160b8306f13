#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkBlobUrls() {
  console.log('🔍 Checking for blob URLs in packages...\n');

  try {
    // Fetch all packages
    const { data: packages, error } = await supabase
      .from('sas_packages')
      .select('id, title, slug, image_url, hero_image_url')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching packages:', error.message);
      return;
    }

    if (!packages || packages.length === 0) {
      console.log('📝 No packages found in database');
      return;
    }

    console.log(`📦 Found ${packages.length} packages\n`);

    let blobCount = 0;
    let validCount = 0;

    packages.forEach((pkg, index) => {
      const hasBlobImageUrl = pkg.image_url && pkg.image_url.startsWith('blob:');
      const hasBlobHeroUrl = pkg.hero_image_url && pkg.hero_image_url.startsWith('blob:');
      
      if (hasBlobImageUrl || hasBlobHeroUrl) {
        blobCount++;
        console.log(`❌ Package ${index + 1}: "${pkg.title}"`);
        console.log(`   Slug: ${pkg.slug}`);
        if (hasBlobImageUrl) {
          console.log(`   🔗 image_url: ${pkg.image_url}`);
        }
        if (hasBlobHeroUrl) {
          console.log(`   🔗 hero_image_url: ${pkg.hero_image_url}`);
        }
        console.log('');
      } else {
        validCount++;
        console.log(`✅ Package ${index + 1}: "${pkg.title}" - Valid URLs`);
      }
    });

    console.log('\n📊 Summary:');
    console.log(`✅ Packages with valid URLs: ${validCount}`);
    console.log(`❌ Packages with blob URLs: ${blobCount}`);

    if (blobCount > 0) {
      console.log('\n🔧 To fix blob URLs, you can:');
      console.log('1. Re-upload images through the admin interface');
      console.log('2. Run a database update script to replace blob URLs');
      console.log('3. Use the fallback image system we implemented');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkBlobUrls();
