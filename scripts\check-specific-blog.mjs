#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function checkSpecificBlogPost() {
  console.log('🔍 Checking for blog post: mountain-gorilla-trekking-in-rwanda-and-uganda\n');
  
  const { data, error } = await supabase
    .from('sas_blog_posts')
    .select('id, title, slug, status, published_at, deleted_at')
    .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda');
    
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  if (data && data.length > 0) {
    console.log('✅ Found blog post:');
    console.log('   Title:', data[0].title);
    console.log('   Slug:', data[0].slug);
    console.log('   Status:', data[0].status);
    console.log('   Published:', data[0].published_at);
    console.log('   Deleted:', data[0].deleted_at);
  } else {
    console.log('❌ Blog post not found in database');
  }
  
  console.log('\n📋 All published blog posts:');
  const { data: allPosts } = await supabase
    .from('sas_blog_posts')
    .select('title, slug, status, published_at')
    .eq('status', 'published')
    .is('deleted_at', null)
    .order('created_at', { ascending: false });
    
  if (allPosts && allPosts.length > 0) {
    allPosts.forEach((post, index) => {
      console.log(`   ${index + 1}. ${post.title} (${post.slug})`);
    });
  } else {
    console.log('   No published blog posts found');
  }
  
  // Check if there are any unpublished posts with this slug
  console.log('\n🔍 Checking for unpublished posts with this slug:');
  const { data: unpublishedPosts } = await supabase
    .from('sas_blog_posts')
    .select('title, slug, status, published_at')
    .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
    .neq('status', 'published');
    
  if (unpublishedPosts && unpublishedPosts.length > 0) {
    unpublishedPosts.forEach((post, index) => {
      console.log(`   ${index + 1}. ${post.title} (${post.slug}) - Status: ${post.status}`);
    });
  } else {
    console.log('   No unpublished posts found with this slug');
  }
}

checkSpecificBlogPost().catch(console.error);
