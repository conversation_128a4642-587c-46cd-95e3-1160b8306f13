"use client";
import React from 'react';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const PartnersSection = () => {
    const partners = [
        { logo: '/images/partners/African-travel-and-tourism-association-with-swift-africa-safaris.png', alt: 'African Travel and Tourism Association' },
        { logo: '/images/partners/rwanda-community-tourism-association-with-swift-africa-safaris.png', alt: 'Rwanda Community Tourism Association' },
        { logo: '/images/partners/rent- in-kigali-with-swift-africa-safaris-travel-adventures-travel-agency.png', alt: 'Rent in Kigali' },
        { logo: '/images/partners/Real-Estate-Agency-Interior-Decor-swift-africa-safaris-travel-tours.png', alt: 'MyInzu Real Estate Agency' },
        { logo: '/images/partners/swift-africa-safaris-travel-agency-regited-by-rwanda-development-board-in rwanda.png', alt: 'Rwanda Development Board' },
        { logo: '/images/partners/karisimbi-cave-resort-urban-female-power-at-rwanda-speak-with-swift-africa-safaris-travel-agency.png', alt: 'Karisimbi Cave Resort' },
        { logo: '/images/partners/chamber-of-tourism-rwanda-with-swift-africa-safaris.png', alt: 'Chamber of Tourism Rwanda' },
        { logo: '/images/partners/rwanda-tours-and-travel-association-with-swift-africa-safaris.webp', alt: 'Rwanda Tours and Travel Association' }
    ];

    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 4,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 2000,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 4,
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 3,
                }
            },
            {
                breakpoint: 480,
                settings: {
                    slidesToShow: 2,
                }
            }
        ]
    };

    return (
        <div className="w-full bg-[var(--primary-background)] py-8 px-4">
            <div className="max-w-6xl mx-auto text-center">
                {/* Header */}
                <div className="mb-6 text-center">
                    <h2 className="text-3xl font-bold mb-2">Our Trusted Partners & Membership</h2>
                    <div className="w-48 h-1 bg-[#d35400] mx-auto"></div>
                </div>

                {/* Partners Slider */}
                <Slider {...settings}>
                    {partners.map((partner, index) => (
                        <div key={index} className="flex justify-center items-center h-24 px-4">
                            <img
                                src={partner.logo}
                                alt={partner.alt}
                                className="max-h-full max-w-full object-contain"
                            />
                        </div>
                    ))}
                </Slider>
            </div>
        </div>
    );
};

export default PartnersSection;