import React from 'react';
import Image from 'next/image';

const ReviewCard = ({ review, setIsModalOpen }) => {
  const renderStars = (rating) => {
    return (
      <div className="flex gap-1.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <div
            key={star}
            className={`w-3 h-3 rounded-full ${
              star <= rating ? 'bg-emerald-600' : 'bg-gray-200'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="bg-[var(--primary-background)] border border-gray-100 p-6 shadow-sm text-left h-[340px] min-h-[320px] sm:h-[340px] flex flex-col w-full max-w-[480px] relative">
      {/* Header with profile and rating */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-3">
          {/* Review Logo */}
          <div className="w-10 h-10 bg-[var(--secondary-background)] rounded-full border border-gray-200 flex items-center justify-center overflow-hidden">
            <Image
              src="/images/logo/swift-africa-safaris-reviews.png"
              alt="Swift Africa Safaris Reviews"
              className="w-6 h-6 object-contain"
              width={24}
              height={24}
              style={{ objectFit: 'contain' }}
            />
          </div>
          <p className="text-base font-medium text-gray-800">{review.author}</p>
        </div>

        <div className="w-8 h-8 bg-[#006F45] text-white rounded-full flex items-center justify-center font-bold text-sm">
          {review.rating}
        </div>
      </div>

      {/* Review title */}
      <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3">
        {review.title}
      </h3>

      {/* Star rating */}
      <div className="mb-4">{renderStars(review.rating)}</div>

      {/* Review preview */}
      <p className="text-gray-600 text-sm leading-relaxed line-clamp-3 flex-grow">
        {review.preview}
      </p>

      {/* Read more button */}
      <button
        onClick={() => setIsModalOpen(true)}
        className="text-[var(--btn)] font-medium hover:underline text-left mt-3"
      >
        Read more
      </button>
    </div>
  );
};

export default ReviewCard;