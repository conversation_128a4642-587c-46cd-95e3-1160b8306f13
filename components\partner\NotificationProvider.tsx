/* eslint-disable react-hooks/exhaustive-deps */
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import ToastNotificationComponent, { useToastNotifications } from './ToastNotification';

interface NotificationSettings {
  enableToasts: boolean;
  enableSounds: boolean;
  toastDuration: number;
  showBookingNotifications: boolean;
  showPaymentNotifications: boolean;
  showMessageNotifications: boolean;
  showPackageNotifications: boolean;
  showSystemNotifications: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

interface NotificationContextType {
  showSuccess: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showError: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showWarning: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showInfo: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showBookingNotification: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showPaymentNotification: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showMessageNotification: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  showPackageNotification: (title: string, message: string, options?: { [key: string]: unknown }) => string;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  settings: NotificationSettings;
  updateSettings: (newSettings: Partial<NotificationSettings>) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const router = useRouter();
  const {
    notifications,
    removeNotification,
    clearAllNotifications,
    showSuccess: originalShowSuccess,
    showError: originalShowError,
    showWarning: originalShowWarning,
    showInfo: originalShowInfo,
    showBookingNotification: originalShowBookingNotification,
    showPaymentNotification: originalShowPaymentNotification,
    showMessageNotification: originalShowMessageNotification,
    showPackageNotification: originalShowPackageNotification
  } = useToastNotifications();

  // Default notification settings
  const defaultSettings: NotificationSettings = {
    enableToasts: true,
    enableSounds: false,
    toastDuration: 5000,
    showBookingNotifications: true,
    showPaymentNotifications: true,
    showMessageNotifications: true,
    showPackageNotifications: true,
    showSystemNotifications: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  };

  const [settings, setSettings] = useState<NotificationSettings>(defaultSettings);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load settings from localStorage after component mounts (client-side only)
  useEffect(() => {
    try {
      const saved = localStorage.getItem('partner-notification-settings');
      if (saved) {
        const parsedSettings = JSON.parse(saved);
        setSettings(parsedSettings);
      }
    } catch (error) {
      console.warn('Failed to load notification settings from localStorage:', error);
    }
    setIsInitialized(true);
  }, []);

  // Save settings to localStorage whenever they change (but only after initialization)
  useEffect(() => {
    if (isInitialized) {
      try {
        localStorage.setItem('partner-notification-settings', JSON.stringify(settings));
      } catch (error) {
        console.warn('Failed to save notification settings to localStorage:', error);
      }
    }
  }, [settings, isInitialized]);

  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // Check if we're in quiet hours
  const isQuietHours = () => {
    if (!settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const [startHour, startMin] = settings.quietHours.start.split(':').map(Number);
    const [endHour, endMin] = settings.quietHours.end.split(':').map(Number);

    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // Quiet hours span midnight
      return currentTime >= startTime || currentTime <= endTime;
    }
  };

  // Wrapper functions that respect settings
  const showSuccess = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showSystemNotifications || isQuietHours()) {
      return '';
    }
    return originalShowSuccess(title, message, { ...options, duration: settings.toastDuration });
  };

  const showError = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showSystemNotifications || isQuietHours()) {
      return '';
    }
    return originalShowError(title, message, { ...options, duration: settings.toastDuration });
  };

  const showWarning = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showSystemNotifications || isQuietHours()) {
      return '';
    }
    return originalShowWarning(title, message, { ...options, duration: settings.toastDuration });
  };

  const showInfo = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showSystemNotifications || isQuietHours()) {
      return '';
    }
    return originalShowInfo(title, message, { ...options, duration: settings.toastDuration });
  };

  const showBookingNotification = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showBookingNotifications || isQuietHours()) {
      return '';
    }
    return originalShowBookingNotification(title, message, { ...options, duration: settings.toastDuration });
  };

  const showPaymentNotification = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showPaymentNotifications || isQuietHours()) {
      return '';
    }
    return originalShowPaymentNotification(title, message, { ...options, duration: settings.toastDuration });
  };

  const showMessageNotification = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showMessageNotifications || isQuietHours()) {
      return '';
    }
    return originalShowMessageNotification(title, message, { ...options, duration: settings.toastDuration });
  };

  const showPackageNotification = (title: string, message: string, options?: { [key: string]: unknown }) => {
    if (!settings.enableToasts || !settings.showPackageNotifications || isQuietHours()) {
      return '';
    }
    return originalShowPackageNotification(title, message, { ...options, duration: settings.toastDuration });
  };

  // Simulate real-time notifications (replace with actual WebSocket/SSE implementation)
  useEffect(() => {
    const simulateRealTimeNotifications = () => {
      // Only show demo notifications if enabled and not in quiet hours
      if (!settings.enableToasts) return;

      // Simulate booking confirmation (less frequent)
      setTimeout(() => {
        if (Math.random() > 0.7) { // 30% chance
          showBookingNotification(
            'Booking Confirmed',
            'SA-B2B-003 has been confirmed by our team',
            {
              actionUrl: '/partner/bookings/SA-B2B-003',
              actionText: 'View Booking'
            }
          );
        }
      }, 30000); // 30 seconds after load

      // Simulate payment notification (even less frequent)
      setTimeout(() => {
        if (Math.random() > 0.8) { // 20% chance
          showPaymentNotification(
            'Commission Payment',
            'Your December commission of $2,850 has been processed',
            {
              actionUrl: '/partner/reports',
              actionText: 'View Reports'
            }
          );
        }
      }, 120000); // 2 minutes after load

      // Simulate new message (moderate frequency)
      setTimeout(() => {
        if (Math.random() > 0.6) { // 40% chance
          showMessageNotification(
            'New Message',
            'Admin replied to your custom package request',
            {
              actionUrl: '/partner/messages',
              actionText: 'Read Message'
            }
          );
        }
      }, 180000); // 3 minutes after load
    };

    // Only simulate in development mode and with a delay to avoid immediate notifications
    if (process.env.NODE_ENV === 'development') {
      const timer = setTimeout(simulateRealTimeNotifications, 5000); // Wait 5 seconds before starting
      return () => clearTimeout(timer);
    }
  }, [settings.enableToasts, showBookingNotification, showPaymentNotification, showMessageNotification]);

  const handleNotificationAction = (notification: { actionUrl?: string }) => {
    if (notification.actionUrl) {
      router.push(notification.actionUrl);
    }
  };

  const contextValue: NotificationContextType = {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showBookingNotification,
    showPaymentNotification,
    showMessageNotification,
    showPackageNotification,
    removeNotification,
    clearAllNotifications,
    settings,
    updateSettings
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      <ToastNotificationComponent
        notifications={notifications}
        onRemove={removeNotification}
        onAction={handleNotificationAction}
      />
    </NotificationContext.Provider>
  );
};

// Example usage hook for common notification patterns
export const useCommonNotifications = () => {
  const notifications = useNotifications();

  const notifyBookingCreated = (bookingId: string, packageName: string) => {
    notifications.showBookingNotification(
      'Booking Created',
      `New booking ${bookingId} for ${packageName} has been created`,
      {
        actionUrl: `/partner/bookings/${bookingId}`,
        actionText: 'View Booking'
      }
    );
  };

  const notifyBookingConfirmed = (bookingId: string) => {
    notifications.showSuccess(
      'Booking Confirmed',
      `Booking ${bookingId} has been confirmed by our team`,
      {
        actionUrl: `/partner/bookings/${bookingId}`,
        actionText: 'View Details'
      }
    );
  };

  const notifyPaymentReceived = (amount: number) => {
    notifications.showPaymentNotification(
      'Payment Received',
      `Commission payment of $${amount.toLocaleString()} has been processed`,
      {
        actionUrl: '/partner/reports',
        actionText: 'View Reports'
      }
    );
  };

  const notifyNewMessage = (from: string) => {
    notifications.showMessageNotification(
      'New Message',
      `${from} sent you a message`,
      {
        actionUrl: '/partner/messages',
        actionText: 'Read Message'
      }
    );
  };

  const notifyCustomPackageResponse = (status: 'approved' | 'needs_revision' | 'declined') => {
    const messages = {
      approved: 'Your custom package request has been approved',
      needs_revision: 'Your custom package request needs some revisions',
      declined: 'Your custom package request has been declined'
    };

    const type = status === 'approved' ? 'success' : status === 'needs_revision' ? 'warning' : 'error';
    
    if (type === 'success') {
      notifications.showSuccess('Custom Package Approved', messages[status]);
    } else if (type === 'warning') {
      notifications.showWarning('Package Needs Revision', messages[status]);
    } else {
      notifications.showError('Package Request Declined', messages[status]);
    }
  };

  const notifyFormSuccess = (action: string) => {
    notifications.showSuccess(
      'Success',
      `${action} completed successfully`
    );
  };

  const notifyFormError = (action: string, error?: string) => {
    notifications.showError(
      'Error',
      error || `Failed to ${action.toLowerCase()}. Please try again.`
    );
  };

  const notifyNetworkError = () => {
    notifications.showError(
      'Connection Error',
      'Unable to connect to server. Please check your internet connection.',
      { duration: 10000 }
    );
  };

  return {
    notifyBookingCreated,
    notifyBookingConfirmed,
    notifyPaymentReceived,
    notifyNewMessage,
    notifyCustomPackageResponse,
    notifyFormSuccess,
    notifyFormError,
    notifyNetworkError,
    ...notifications
  };
};

export default NotificationProvider;
