/* eslint-disable @next/next/no-img-element */
"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  Calendar,
  Tag,
} from "lucide-react";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  description: string;
  hero_image_url: string;
  hero_image_alt: string;
  category: string;
  tags: string[];
  status: "published" | "draft" | "archived";
  published_at: string;
  created_at: string;
  updated_at: string;
  view_count: number;
}

interface Pagination {
  currentPage: number;
  totalPages: number;
  totalPosts: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function BlogManagement() {
  const router = useRouter();
  const [blogs, setBlogs] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    currentPage: 1,
    totalPages: 1,
    totalPosts: 0,
    hasNextPage: false,
    hasPrevPage: false,
    limit: 20,
  });

  // Fetch blog posts from API
  const fetchBlogs = async (
    page = 1,
    search = "",
    status = "all",
    category = ""
  ) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(search && { search }),
        ...(status !== "all" && { status }),
        ...(category && { category }),
        _t: Date.now().toString(), // Cache busting timestamp
      });

      const response = await fetch(`/api/admin/blog?${params}`, {
        headers: {
          'Cache-Control': 'no-cache',
        },
      });
      const data = await response.json();

      if (data.success) {
        setBlogs(data.data.posts);
        setPagination(data.data.pagination);
      } else {
        setError(data.error || "Failed to fetch blog posts");
      }
    } catch (err) {
      console.error("Error fetching blogs:", err);
      setError("Failed to fetch blog posts");
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/blog", { method: "OPTIONS" });
      const data = await response.json();

      if (data.success) {
        setCategories(data.data.categories);
      }
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  };

  // Initial load
  useEffect(() => {
    fetchBlogs();
    fetchCategories();
  }, []);

  // Handle search and filters
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchBlogs(1, searchTerm, statusFilter, categoryFilter);
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, statusFilter, categoryFilter]);

  // Handle pagination
  const handlePageChange = (page: number) => {
    fetchBlogs(page, searchTerm, statusFilter, categoryFilter);
  };

  // Handle delete blog
  const handleDeleteBlog = async (slug: string) => {
    if (!confirm("Are you sure you want to delete this blog post?")) return;

    try {
      setLoading(true);

      const response = await fetch(`/api/admin/blog/${slug}`, {
        method: "DELETE",
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      const data = await response.json();

      if (data.success) {
        // Optimistic update: remove the deleted blog from current state immediately
        setBlogs(prevBlogs => prevBlogs.filter(blog => blog.slug !== slug));

        // Small delay to ensure database consistency
        await new Promise(resolve => setTimeout(resolve, 100));

        // Wait for the refresh to complete to ensure consistency
        await fetchBlogs(
          pagination.currentPage,
          searchTerm,
          statusFilter,
          categoryFilter
        );
      } else {
        alert(data.error || "Failed to delete blog post");
        setLoading(false);
      }
    } catch (err) {
      console.error("Error deleting blog:", err);
      alert("Failed to delete blog post");
      setLoading(false);
    }
  };

  // Handle status change
  const handleStatusChange = async (slug: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/blog/${slug}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      const data = await response.json();

      if (data.success) {
        // Optimistic update: update the status in current state immediately
        setBlogs(prevBlogs =>
          prevBlogs.map(blog =>
            blog.slug === slug
              ? { ...blog, status: newStatus as "published" | "draft" | "archived" }
              : blog
          )
        );

        // Refresh to ensure consistency
        await fetchBlogs(
          pagination.currentPage,
          searchTerm,
          statusFilter,
          categoryFilter
        );
      } else {
        alert(data.error || "Failed to update blog status");
      }
    } catch (err) {
      console.error("Error updating blog status:", err);
      alert("Failed to update blog status");
    }
  };

  const handleSelectBlog = (blogSlug: string) => {
    setSelectedBlogs((prev) =>
      prev.includes(blogSlug)
        ? prev.filter((slug) => slug !== blogSlug)
        : [...prev, blogSlug]
    );
  };

  const handleSelectAll = () => {
    if (selectedBlogs.length === blogs.length) {
      setSelectedBlogs([]);
    } else {
      setSelectedBlogs(blogs.map((blog) => blog.slug));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedBlogs.length === 0) return;

    if (
      !confirm(
        `Are you sure you want to delete ${selectedBlogs.length} blog post(s)?`
      )
    )
      return;

    try {
      await Promise.all(
        selectedBlogs.map((slug) =>
          fetch(`/api/admin/blog/${slug}`, { method: "DELETE" })
        )
      );

      setSelectedBlogs([]);
      fetchBlogs(
        pagination.currentPage,
        searchTerm,
        statusFilter,
        categoryFilter
      );
    } catch (err) {
      console.error("Error in bulk delete:", err);
      alert("Failed to delete some blog posts");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusStyles = {
      published: "bg-green-100 text-green-800",
      draft: "bg-yellow-100 text-yellow-800",
      archived: "bg-gray-100 text-gray-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusStyles[status as keyof typeof statusStyles]
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="p-6 border-b border-gray-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-[var(--accent)] rounded-lg">
              <BookOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-[var(--text)]">
                Blog Management
              </h1>
              <p className="text-gray-600">
                Manage your blog posts and content
              </p>
            </div>
          </div>

          <button
            onClick={() => router.push("/admin/blog/add")}
            className="inline-flex items-center px-4 py-2 bg-[var(--accent)] text-white rounded-lg hover:bg-[var(--accent)]/90 transition-colors"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add New Blog Post
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white border-b border-gray-200 p-6 mx-6">
        <div className="flex items-center justify-between">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search blog posts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent"
            />
          </div>

          {/* Filters */}
          <div className="flex gap-3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
              <option value="archived">Archived</option>
            </select>

            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--accent)] focus:border-transparent"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedBlogs.length > 0 && (
          <div className="mt-4 flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg px-4 py-3">
            <span className="text-sm text-blue-700">
              {selectedBlogs.length} blog post(s) selected
            </span>
            <div className="flex gap-2">
              <button
                onClick={handleDeleteSelected}
                className="px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors"
              >
                Delete Selected
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--accent)]"></div>
          </div>
        ) : (
          <>
            {/* Error Display */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-2 text-red-700">
                <span>{error}</span>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto text-red-500 hover:text-red-700"
                >
                  ×
                </button>
              </div>
            )}

            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              {/* Table Header */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={
                      selectedBlogs.length === blogs.length && blogs.length > 0
                    }
                    onChange={handleSelectAll}
                    className="mr-4"
                  />
                  <div className="grid grid-cols-12 gap-4 w-full text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="col-span-4">Blog Post</div>
                    <div className="col-span-2">Category</div>
                    <div className="col-span-1">Status</div>
                    <div className="col-span-1">Views</div>
                    <div className="col-span-3">Published</div>
                    <div className="col-span-1">Actions</div>
                  </div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-200">
                {blogs.length === 0 ? (
                  <div className="px-6 py-12 text-center">
                    <div className="mx-auto h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                      <BookOpen className="w-6 h-6 text-gray-400" />
                    </div>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">
                      No blog posts found
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm || statusFilter !== "all" || categoryFilter
                        ? "Try adjusting your search or filters."
                        : "Get started by creating your first blog post."}
                    </p>
                    {!searchTerm &&
                      statusFilter === "all" &&
                      !categoryFilter && (
                        <div className="mt-6">
                          <button
                            onClick={() => router.push("/admin/blog/add")}
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[var(--accent)] hover:bg-[var(--accent)]/90"
                          >
                            <Plus className="w-4 h-4 mr-2" />
                            Add New Blog Post
                          </button>
                        </div>
                      )}
                  </div>
                ) : (
                  blogs.map((blog) => (
                    <div key={blog.id} className="px-6 py-4 hover:bg-gray-50">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedBlogs.includes(blog.slug)}
                          onChange={() => handleSelectBlog(blog.slug)}
                          className="mr-4"
                        />
                        <div className="grid grid-cols-12 gap-4 w-full">
                          {/* Blog Info */}
                          <div className="col-span-4 flex items-center">
                            <div className="flex-shrink-0 h-12 w-12">
                              {blog.hero_image_url ? (
                                <img
                                  className="h-12 w-12 rounded-lg object-cover"
                                  src={blog.hero_image_url}
                                  alt={blog.hero_image_alt || blog.title}
                                />
                              ) : (
                                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                                  <BookOpen className="w-6 h-6 text-gray-400" />
                                </div>
                              )}
                            </div>
                            <div className="ml-4 min-w-0 flex-1">
                              <div className="text-sm font-medium text-gray-900 truncate">
                                {blog.title}
                              </div>
                              <div className="text-sm text-gray-500 truncate">
                                {blog.description}
                              </div>
                            </div>
                          </div>

                          {/* Category */}
                          <div className="col-span-2 flex items-center">
                            <div className="flex items-center">
                              <Tag className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {blog.category}
                              </span>
                            </div>
                          </div>

                          {/* Status */}
                          <div className="col-span-1 flex items-center">
                            {getStatusBadge(blog.status)}
                          </div>

                          {/* Views */}
                          <div className="col-span-1 flex items-center">
                            <div className="flex items-center">
                              <Eye className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {(blog.view_count || 0).toLocaleString()}
                              </span>
                            </div>
                          </div>

                          {/* Published Date */}
                          <div className="col-span-2 flex items-center">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="text-sm text-gray-900">
                                {blog.status === "published" &&
                                blog.published_at
                                  ? new Date(
                                      blog.published_at
                                    ).toLocaleDateString()
                                  : "Not published"}
                              </span>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="col-span-1 flex items-center justify-end">
                            <div className="flex items-center space-x-1">
                              {blog.status === "published" ? (
                                <button
                                  onClick={() => handleStatusChange(blog.slug, "draft")}
                                  className="p-1 text-yellow-600 hover:text-yellow-700"
                                  title="Unpublish"
                                >
                                  <div className="w-4 h-4 rounded-full bg-yellow-600"></div>
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleStatusChange(blog.slug, "published")}
                                  className="p-1 text-green-600 hover:text-green-700"
                                  title="Publish"
                                >
                                  <div className="w-4 h-4 rounded-full bg-green-600"></div>
                                </button>
                              )}
                              <button
                                onClick={() =>
                                  router.push(`/admin/blog/edit/${blog.slug}`)
                                }
                                className="p-1 text-gray-400 hover:text-gray-600"
                                title="Edit"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() =>
                                  window.open(`/blog/${blog.slug}`, "_blank")
                                }
                                className="p-1 text-gray-400 hover:text-gray-600"
                                title="View"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteBlog(blog.slug)}
                                className="p-1 text-gray-400 hover:text-red-600"
                                title="Delete"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() =>
                          handlePageChange(pagination.currentPage - 1)
                        }
                        disabled={!pagination.hasPrevPage}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>
                      <button
                        onClick={() =>
                          handlePageChange(pagination.currentPage + 1)
                        }
                        disabled={!pagination.hasNextPage}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700">
                          Showing{" "}
                          <span className="font-medium">
                            {(pagination.currentPage - 1) * pagination.limit +
                              1}
                          </span>{" "}
                          to{" "}
                          <span className="font-medium">
                            {Math.min(
                              pagination.currentPage * pagination.limit,
                              pagination.totalPosts
                            )}
                          </span>{" "}
                          of{" "}
                          <span className="font-medium">
                            {pagination.totalPosts}
                          </span>{" "}
                          results
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                          <button
                            onClick={() =>
                              handlePageChange(pagination.currentPage - 1)
                            }
                            disabled={!pagination.hasPrevPage}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Previous
                          </button>

                          {/* Page numbers */}
                          {Array.from(
                            { length: Math.min(5, pagination.totalPages) },
                            (_, i) => {
                              const pageNum = Math.max(
                                1,
                                Math.min(
                                  pagination.currentPage - 2 + i,
                                  pagination.totalPages - 4 + i
                                )
                              );

                              if (pageNum > pagination.totalPages) return null;

                              return (
                                <button
                                  key={pageNum}
                                  onClick={() => handlePageChange(pageNum)}
                                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                    pageNum === pagination.currentPage
                                      ? "z-10 bg-blue-50 border-blue-500 text-blue-600"
                                      : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                                  }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            }
                          )}

                          <button
                            onClick={() =>
                              handlePageChange(pagination.currentPage + 1)
                            }
                            disabled={!pagination.hasNextPage}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Next
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
      </div>
    </main>
  );
}
