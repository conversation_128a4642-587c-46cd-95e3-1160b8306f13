import type { Metadata } from 'next';
import Navbar from '@/components/header';
import Footer from '@/components/footer';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Privacy Policy',
  description: 'Learn how Swift Africa Safaris protects your personal information and privacy. Our commitment to data security and transparency in all your safari experiences.',
  keywords: [
    'privacy policy',
    'data protection',
    'Swift Africa Safaris privacy',
    'personal information security',
    'safari booking privacy',
    'travel data protection',
    'GDPR compliance',
    'data privacy Africa',
    'safari tour privacy',
    'booking information security'
  ],
  url: '/privacy-policy'
});

export default function PrivacyPolicy() {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Privacy Policy', url: '/privacy-policy' }
  ]);

  const privacyPolicySchema = {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Privacy Policy - Swift Africa Safaris',
    description: 'Privacy policy and data protection information for Swift Africa Safaris customers and website visitors.',
    url: `${defaultSEO.siteUrl}/privacy-policy`,
    mainEntity: {
      '@type': 'PrivacyPolicy',
      name: 'Swift Africa Safaris Privacy Policy',
      description: 'Our commitment to protecting your personal information and privacy during your African safari experience.',
      dateModified: new Date().toISOString(),
      publisher: {
        '@type': 'Organization',
        name: 'Swift Africa Safaris',
        url: defaultSEO.siteUrl
      }
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, privacyPolicySchema]} />
      <Navbar />

      <main className="min-h-screen">
        {/* Hero Section */}
        <div className="bg-[var(--primary-background)] py-16">
          <div className="max-w-4xl mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-[var(--text)] mb-6">
              Privacy Policy
            </h1>
            <div className="w-24 h-1 bg-[var(--accent)] mx-auto mb-6"></div>
            <p className="text-lg text-[var(--text)] leading-relaxed max-w-3xl mx-auto">
              At Swift Africa Safaris, your trust is the journey we value most. As you explore Africa with us from the savannahs of Tanzania to the gorilla forests of Rwanda we are committed to protecting the personal information that makes your experience safe, smooth, and unforgettable.
            </p>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="bg-white rounded-lg shadow-lg p-8 md:p-12 space-y-12">

            {/* Section 1 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                1. Information We Collect
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                We gather only the data we truly need to serve you better:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Booking Information:</strong> Names, contact details, and preferences to tailor your tour, car hire, or apartment rental.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Email Subscriptions:</strong> Email addresses for our newsletter, filled with exclusive travel tips, stories, and updates.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Cookies & Tracking:</strong> To understand your experience on our site and improve how we serve you.
                  </div>
                </li>
              </ul>
            </section>

            {/* Section 2 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                2. How We Use Your Data
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                Every piece of information is handled with purpose and care:
              </p>
              <ul className="space-y-3 text-[var(--text)] ml-6">
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Customized Travel Planning:</strong> Personal details help us craft safe, tailored adventures.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Communication & Support:</strong> To confirm bookings, provide updates, and respond to inquiries.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Inspiration & Connection:</strong> Newsletter content brings Africa closer to you before and after your journey.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="text-[var(--accent)] mr-3 mt-1">•</span>
                  <div>
                    <strong>Site Optimization:</strong> Cookies help us refine your browsing experience.
                  </div>
                </li>
              </ul>
            </section>

            {/* Section 3 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                3. Protecting Your Privacy
              </h2>
              <p className="text-[var(--text)] leading-relaxed">
                Your safety doesn't stop at the safari vehicle. We use secure technology and trusted practices to guard your information against unauthorized access, sharing, or misuse.
              </p>
            </section>

            {/* Section 4 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                4. Third-Party Sharing
              </h2>
              <p className="text-[var(--text)] leading-relaxed">
                We never sell or trade your personal information. We may only share it with verified local partners strictly necessary for arranging your travel services, all of whom are equally committed to privacy and professionalism.
              </p>
            </section>

            {/* Section 5 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                5. Your Choices & Control
              </h2>
              <p className="text-[var(--text)] leading-relaxed">
                You can unsubscribe from emails anytime. You can also manage or disable cookies in your browser. Want to access or delete your data? Just ask we respect your right to transparency.
              </p>
            </section>

            {/* Section 6 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                6. Updates to This Policy
              </h2>
              <p className="text-[var(--text)] leading-relaxed">
                As we grow, this policy may evolve. Any changes will be shared here, and you'll always have the latest version.
              </p>
            </section>

            {/* Section 7 */}
            <section>
              <h2 className="text-2xl font-bold text-[var(--text)] mb-6 border-b-2 border-[var(--accent)] pb-2">
                7. Contact Us
              </h2>
              <p className="text-[var(--text)] leading-relaxed mb-4">
                Questions? Concerns? Just want to say hi?
              </p>
              <p className="text-[var(--text)] leading-relaxed">
                Reach out at{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-[var(--accent)] hover:underline font-semibold"
                >
                  <EMAIL>
                </a>
                .
              </p>
              <p className="text-[var(--text)] leading-relaxed mt-2">
                We're here for you on and off the trail.
              </p>
            </section>

            {/* Last Updated */}
            <div className="border-t border-gray-200 pt-8 text-center">
              <p className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
