'use client';

import React, { useState, useEffect } from "react";
import { CheckCircle, Calendar, Users, DollarSign, User, Mail, Phone, MessageSquare } from "lucide-react";

const MiniPackageBookingForm = ({ pricing, packageTitle, packageId }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showNotification, setShowNotification] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState("");


  const [formData, setFormData] = useState({
    packageType: "",
    fullName: "",
    email: "",
    phone: "",
    numberOfTravelers: "",
    preferredTravelDate: "",
    specialRequests: "",
  });
  const [today, setToday] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      setToday(new Date().toISOString().split("T")[0]);
    }
  }, []);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError("");

    try {
      // Get the selected package price
      const selectedPackagePrice = pricing[formData.packageType]?.price || pricing[formData.packageType] || 0;

      // Map form data to API expected format
      const bookingData = {
        miniPackageId: packageId,
        miniPackageTitle: packageTitle,
        packageType: formData.packageType,
        packagePrice: selectedPackagePrice,
        fullName: formData.fullName,
        email: formData.email,
        phone: formData.phone,
        numberOfTravelers: parseInt(formData.numberOfTravelers) || 1,
        preferredTravelDate: formData.preferredTravelDate,
        specialRequests: formData.specialRequests,
      };

      const response = await fetch('/api/mini-package-bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      const result = await response.json();

      if (result.success) {
        setShowNotification(true);
        setTimeout(() => {
          setShowNotification(false);
        }, 6000);

        // Reset form after successful submission
        setFormData({
          packageType: "",
          fullName: "",
          email: "",
          phone: "",
          numberOfTravelers: "",
          preferredTravelDate: "",
          specialRequests: "",
        });
        setCurrentStep(1);
      } else {
        setSubmitError(result.error || 'Failed to submit booking. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting mini package booking:', error);
      setSubmitError('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateStep = (step) => {
    switch (step) {
      case 1:
        return formData.packageType !== "";
      case 2:
        return (
          formData.fullName.trim() !== "" &&
          formData.email.trim() !== "" &&
          formData.phone.trim() !== "" &&
          formData.numberOfTravelers !== "" &&
          formData.preferredTravelDate !== ""
        );
      default:
        return true;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };

  const getSelectedPrice = () => {
    if (!formData.packageType || !pricing[formData.packageType]) return 0;
    return pricing[formData.packageType]?.price || pricing[formData.packageType] || 0;
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <DollarSign className="mr-2 text-[var(--btn)]" size={20} />
              Select Package Type
            </h3>
            <div className="grid gap-3">
              {pricing && Object.keys(pricing).length > 0 ? (
                Object.entries(pricing).map(([type, priceData]) => {
                  const price = priceData?.price || priceData || 0;
                  if (price <= 0) {
                    return null;
                  }

                  return (
                    <label
                      key={type}
                      className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-all ${
                        formData.packageType === type
                          ? "border-[var(--btn)] bg-[var(--btn)]/5"
                          : "border-gray-300 hover:border-[var(--btn)]/50"
                      }`}
                    >
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="packageType"
                          value={type}
                          checked={formData.packageType === type}
                          onChange={(e) => handleInputChange("packageType", e.target.value)}
                          className="mr-3 text-[var(--btn)]"
                        />
                        <div>
                          <span className="font-medium capitalize">{type}</span>
                          <div className="text-sm text-gray-500">
                            {priceData?.text || `Per ${type === 'group' ? 'group' : 'person'}`}
                          </div>
                        </div>
                      </div>
                      <span className="font-bold text-[var(--btn)]">
                        ${price}
                      </span>
                    </label>
                  );
                })
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <p>Loading pricing options...</p>
                  <p className="text-sm mt-1">
                    {!pricing ? 'Pricing data not available' : 'No valid pricing options found'}
                  </p>
                </div>
              )}
            </div>
            <button
              type="button"
              onClick={nextStep}
              disabled={!validateStep(1)}
              className={`w-full py-2 rounded-lg transition-colors ${
                validateStep(1)
                  ? "btn text-white"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
            >
              Continue
            </button>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <User className="mr-2 text-[var(--btn)]" size={20} />
              Your Information
            </h3>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Full Name *
              </label>
              <input
                type="text"
                value={formData.fullName}
                onChange={(e) => handleInputChange("fullName", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Email Address *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="Enter your email"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Phone Number *
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Number of Travelers *
              </label>
              <input
                type="number"
                min="1"
                max="20"
                value={formData.numberOfTravelers}
                onChange={(e) => handleInputChange("numberOfTravelers", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="How many people?"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Preferred Travel Date *
              </label>
              <input
                type="date"
                min={today}
                value={formData.preferredTravelDate}
                onChange={(e) => handleInputChange("preferredTravelDate", e.target.value)}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                required
              />
            </div>

            <div className="flex gap-2">
              <button
                type="button"
                onClick={prevStep}
                className="flex-1 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Back
              </button>
              <button
                type="button"
                onClick={nextStep}
                disabled={!validateStep(2)}
                className={`flex-1 py-2 rounded-lg transition-colors ${
                  validateStep(2)
                    ? "btn text-white"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                Continue
              </button>
            </div>
          </div>
        );

      case 3:
        return (
          <form onSubmit={handleSubmit} className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <CheckCircle className="mr-2 text-[var(--btn)]" size={20} />
              Review & Submit
            </h3>

            {/* Booking Summary */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">Booking Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Package:</span>
                  <span className="font-medium">{packageTitle}</span>
                </div>
                <div className="flex justify-between">
                  <span>Type:</span>
                  <span className="font-medium capitalize">{formData.packageType}</span>
                </div>
                <div className="flex justify-between">
                  <span>Travelers:</span>
                  <span className="font-medium">{formData.numberOfTravelers}</span>
                </div>
                <div className="flex justify-between">
                  <span>Travel Date:</span>
                  <span className="font-medium">{new Date(formData.preferredTravelDate).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between border-t pt-1 mt-2">
                  <span className="font-medium">Total Price:</span>
                  <span className="font-bold text-[var(--btn)]">${getSelectedPrice()}</span>
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                Special Requests or Questions
              </label>
              <textarea
                value={formData.specialRequests}
                onChange={(e) => handleInputChange("specialRequests", e.target.value)}
                rows={4}
                className="w-full p-2 border rounded focus:ring-2 focus:ring-[var(--btn)] focus:border-transparent"
                placeholder="Any special requirements, dietary restrictions, or questions?"
              />
            </div>

            {submitError && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                {submitError}
              </div>
            )}

            <div className="flex gap-2">
              <button
                type="button"
                onClick={prevStep}
                className="flex-1 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`flex-1 btn text-white py-2 rounded-lg transition-colors ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Booking Request'}
              </button>
            </div>
          </form>
        );
    }
  };

  return (
    <div className="booking-form-section bg-white rounded-lg shadow-lg p-6 sticky top-24">
      {/* Success Notification */}
      {showNotification && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl max-w-md mx-4">
            <div className="text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Booking Request Submitted!
              </h3>
              <p className="text-gray-600 mb-4">
                Thank you for your booking request. We'll contact you within 24 hours to confirm your day tour.
              </p>
              <button
                onClick={() => setShowNotification(false)}
                className="btn text-white px-4 py-2 rounded-lg"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-6">
        {[1, 2, 3].map((step) => (
          <div key={step} className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step
                  ? "bg-[var(--btn)] text-white"
                  : "bg-gray-200 text-gray-500"
              }`}
            >
              {step}
            </div>
            {step < 3 && (
              <div
                className={`w-12 h-1 mx-2 ${
                  currentStep > step ? "bg-[var(--btn)]" : "bg-gray-200"
                }`}
              />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      {renderStep()}
    </div>
  );
};

export default MiniPackageBookingForm;
