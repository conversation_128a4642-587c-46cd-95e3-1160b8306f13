import { supabase } from './supabase';

export async function checkToursBucket() {
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error checking buckets:', error.message);
      return false;
    }
    
    const toursBucket = buckets?.find(b => b.name === 'tours');
    return !!toursBucket;
  } catch (error) {
    console.error('Error in checkToursBucket:', error.message);
    return false;
  }
}

export async function ensureToursBucket() {
  const exists = await checkToursBucket();
  
  if (!exists) {
    console.error('Storage bucket "tours" does not exist. Please create it in Supabase.');
    console.error('Run the SQL script: CREATE_TOURS_BUCKET.sql');
    return false;
  }
  
  return true;
}