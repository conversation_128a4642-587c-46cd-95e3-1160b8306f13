import { useState, useEffect } from 'react';
import { Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';


interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
}

interface AuthState {
  user: UserProfile | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
}

export function useAuth() {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    let mounted = true;

    const getSession = async () => {
      try {
        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Session error:', sessionError);
          if (mounted) {
            setAuthState(prev => ({
              ...prev,
              loading: false,
              error: sessionError.message
            }));
          }
          return;
        }

        if (!session) {
          if (mounted) {
            setAuthState({
              user: null,
              session: null,
              loading: false,
              error: null
            });
          }
          return;
        }

        // Get user profile
        const { data: userProfile, error: profileError } = await supabase
          .from('users')
          .select('id, name, email, role, status')
          .eq('id', session.user.id)
          .single();

        if (profileError) {
          console.error('Profile error:', profileError);
          if (mounted) {
            setAuthState(prev => ({
              ...prev,
              loading: false,
              error: 'User profile not found'
            }));
          }
          return;
        }

        if (mounted) {
          setAuthState({
            user: userProfile,
            session,
            loading: false,
            error: null
          });
        }
      } catch (error: unknown) {
        console.error('Auth error:', error);
        if (mounted) {
          setAuthState(prev => ({
            ...prev,
            loading: false,
            error: 'Authentication failed'
          }));
        }
      }
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          // Get user profile on sign in
          const { data: userProfile } = await supabase
            .from('users')
            .select('id, name, email, role, status')
            .eq('id', session.user.id)
            .single();

          if (mounted) {
            setAuthState({
              user: userProfile,
              session,
              loading: false,
              error: null
            });
          }
        } else if (event === 'SIGNED_OUT') {
          if (mounted) {
            setAuthState({
              user: null,
              session: null,
              loading: false,
              error: null
            });
          }
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      sessionStorage.removeItem('adminUser');
      router.push('/login?context=admin');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const isAdmin = () => {
    return authState.user?.role === 'admin' && authState.user?.status === 'active';
  };

  return {
    ...authState,
    signOut,
    isAdmin,
    supabase
  };
} 