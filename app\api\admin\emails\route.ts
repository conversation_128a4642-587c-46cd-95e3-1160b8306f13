import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { sendTestEmail } from '@/lib/email-service';

// Interface for notification email data
interface NotificationEmailData {
  email_address: string;
  notification_types: string[];
  is_active: boolean;
}

// GET - Fetch notification emails (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const active = searchParams.get('active');

    let query = supabase
      .from('sas_notification_emails')
      .select('*')
      .order('created_at', { ascending: false });

    // Apply active filter
    if (active === 'true') {
      query = query.eq('is_active', true);
    } else if (active === 'false') {
      query = query.eq('is_active', false);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching notification emails:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch notification emails' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });
  } catch (error) {
    console.error('Error in GET /api/admin/emails:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Add new notification email
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.emailAddress) {
      return NextResponse.json(
        { success: false, error: 'Email address is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.emailAddress)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate notification types
    const validTypes = ['all', 'tour', 'apartment', 'car', 'volunteering', 'contact'];
    const notificationTypes = body.notificationTypes || ['all'];
    
    for (const type of notificationTypes) {
      if (!validTypes.includes(type)) {
        return NextResponse.json(
          { success: false, error: `Invalid notification type: ${type}` },
          { status: 400 }
        );
      }
    }

    // Transform frontend data to database format
    const emailData: NotificationEmailData = {
      email_address: body.emailAddress.toLowerCase().trim(),
      notification_types: notificationTypes,
      is_active: body.isActive !== false // Default to true
    };

    // Insert email into database
    const { data: newEmail, error } = await supabase
      .from('sas_notification_emails')
      .insert([emailData])
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { success: false, error: 'Email address already exists' },
          { status: 409 }
        );
      }
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to add notification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newEmail,
      message: 'Notification email added successfully'
    });

  } catch (error) {
    console.error('Error adding notification email:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to add notification email' },
      { status: 500 }
    );
  }
}

// PATCH - Update notification email
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, emailAddress, notificationTypes, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Email ID is required' },
        { status: 400 }
      );
    }

    const updateData: any = {};

    if (emailAddress) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailAddress)) {
        return NextResponse.json(
          { success: false, error: 'Invalid email format' },
          { status: 400 }
        );
      }
      updateData.email_address = emailAddress.toLowerCase().trim();
    }

    if (notificationTypes) {
      const validTypes = ['all', 'tour', 'apartment', 'car', 'volunteering', 'contact'];
      for (const type of notificationTypes) {
        if (!validTypes.includes(type)) {
          return NextResponse.json(
            { success: false, error: `Invalid notification type: ${type}` },
            { status: 400 }
          );
        }
      }
      updateData.notification_types = notificationTypes;
    }

    if (isActive !== undefined) {
      updateData.is_active = isActive;
    }

    const { data, error } = await supabase
      .from('sas_notification_emails')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { success: false, error: 'Email address already exists' },
          { status: 409 }
        );
      }
      console.error('Error updating notification email:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update notification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data,
      message: 'Notification email updated successfully'
    });

  } catch (error) {
    console.error('Error in PATCH /api/admin/emails:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Remove notification email
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Email ID is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('sas_notification_emails')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting notification email:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete notification email' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Notification email deleted successfully'
    });

  } catch (error) {
    console.error('Error in DELETE /api/admin/emails:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
