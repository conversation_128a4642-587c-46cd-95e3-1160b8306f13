/* eslint-disable react/no-unescaped-entities */

import React, { useState } from 'react';
import { Car, User, Mail, Phone, MessageSquare, CheckCircle } from 'lucide-react';

export default function CarBookingForm({ onClose }) {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    whatsapp: '',
    carProperties: ''
  });
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async () => {
    if (formData.fullName && formData.email && formData.whatsapp && formData.carProperties) {
      try {
        const response = await fetch('/api/bookings/car', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fullName: formData.fullName,
            email: formData.email,
            whatsapp: formData.whatsapp,
            carProperties: formData.carProperties
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to submit booking');
        }

        const result = await response.json();
        if (result.success) {
          setIsSubmitted(true);
          console.log('Car booking submitted successfully:', result.data);

          // Close modal after 3 seconds
          setTimeout(() => {
            onClose && onClose();
          }, 3000);
        } else {
          throw new Error(result.error || 'Failed to submit booking');
        }
      } catch (error) {
        console.error('Error submitting car booking:', error);
        alert('Failed to submit booking. Please try again.');
      }
    } else {
      alert('Please fill in all required fields');
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-[var(--white)] rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="mb-6">
            <CheckCircle className="w-16 h-16 text-[var(--light-green)] mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-[var(--text)] mb-2">Thank You!</h2>
            <div className="w-16 h-1 bg-[var(--light-green)] mx-auto mb-4"></div>
          </div>

          <p className="text-gray-600 mb-6 leading-relaxed">
            We appreciate your interest in booking a car with us. Our team will review your requirements and contact you within 24 hours.
          </p>

          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-blue-700">
              <strong>What's next?</strong><br />
              We'll reach out to you via WhatsApp or email to discuss your car preferences and arrange the booking details.
            </p>
          </div>

          <button
            onClick={() => setIsSubmitted(false)}
            className="bg-[var(--btn)] text-[var(--white)] px-6 py-2 rounded-lg hover:bg-[var(--light-green)] transition-colors"
          >
            Book Another Car
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="bg-[var(--white)] backdrop-blur-sm rounded-xl shadow-xl p-3 px-6 max-w-lg w-full">
        <div className="text-center mb-2">
          <Car className="w-8 h-8 text-[var(--accent)] mx-auto mb-2" />
          <h1 className="text-xl font-bold text-[var(--text)]">Book Your Car</h1>
          <p className="text-sm text-gray-600">Fill in your details below</p>
        </div>

        <div className="space-y-2">
          <div>
            <label htmlFor="fullName" className="flex items-center text-sm font-medium text-gray-700 mb-2">
              <User className="w-4 h-4 mr-2 text-blue-600" />
              Full Name
            </label>
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formData.fullName}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Enter your full name"
            />
          </div>

          <div>
            <label htmlFor="email" className="flex items-center text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 mr-2 text-blue-600" />
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Enter your email address"
            />
          </div>

          <div>
            <label htmlFor="whatsapp" className="flex items-center text-sm font-medium text-gray-700 mb-2">
              <Phone className="w-4 h-4 mr-2 text-blue-600" />
              WhatsApp Number
            </label>
            <input
              type="tel"
              id="whatsapp"
              name="whatsapp"
              value={formData.whatsapp}
              onChange={handleChange}
              required
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
              placeholder="Enter your WhatsApp number"
            />
          </div>

          <div>
            <label htmlFor="carProperties" className="flex items-center text-sm font-medium text-gray-700 mb-2">
              <MessageSquare className="w-4 h-4 mr-2 text-blue-600" />
              Tell us about the car you really want
            </label>
            <textarea
              id="carProperties"
              name="carProperties"
              value={formData.carProperties}
              onChange={handleChange}
              required
              rows="2"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
              placeholder="Describe your ideal car (e.g., SUV, sedan, automatic, fuel type, color, budget range, special features, etc.)"
            />
          </div>

          <button
            type="button"
            onClick={handleSubmit}
            className="w-2/4 bg-[var(--btn)] text-[var(--white)] py-2 px-6 rounded-lg hover:bg-[var(--light-green)] focus:ring-4 focus:ring-[var(--btn-background)] transition-all duration-200 font-medium text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            Book My Car
          </button>
        </div>

        <div className="mt-2 text-center">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span>We'll contact you within 24 hours</span>
          </div>
        </div>
      </div>
    </div>
  );
}