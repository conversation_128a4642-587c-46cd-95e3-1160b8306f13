-- =====================================================
-- SUPABASE STORAGE SETUP FOR SWIFT AFRICA SAFARIS
-- Copy and paste this SQL into your Supabase SQL Editor
-- =====================================================

-- =====================================================
-- CREATE STORAGE BUCKETS
-- =====================================================

-- Create storage buckets with proper configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  -- Package images bucket (10MB limit)
  ('sas-package-images', 'sas-package-images', true, 10485760, 
   ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  
  -- Blog images bucket (10MB limit)
  ('sas-blog-images', 'sas-blog-images', true, 10485760, 
   ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  
  -- User avatars bucket (5MB limit)
  ('sas-user-avatars', 'sas-user-avatars', true, 5242880, 
   ARRAY['image/jpeg', 'image/png', 'image/webp']),
  
  -- General uploads bucket (50MB limit)
  ('sas-general-uploads', 'sas-general-uploads', true, 52428800, 
   ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'application/pdf', 'text/plain', 'text/csv'])
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- STORAGE POLICIES FOR sas-package-images
-- =====================================================

-- Anyone can view package images (public access)
DO $$ BEGIN
  CREATE POLICY "Anyone can view package images" ON storage.objects
    FOR SELECT USING (bucket_id = 'sas-package-images');
EXCEPTION WHEN duplicate_object THEN NULL;
END $$;

-- Authenticated users can upload package images
DO $$ BEGIN
  CREATE POLICY "Authenticated users can upload package images" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');
EXCEPTION WHEN duplicate_object THEN NULL;
END $$;

-- Authenticated users can update package images
DO $$ BEGIN
  CREATE POLICY "Authenticated users can update package images" ON storage.objects
    FOR UPDATE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');
EXCEPTION WHEN duplicate_object THEN NULL;
END $$;

-- Authenticated users can delete package images
DO $$ BEGIN
  CREATE POLICY "Authenticated users can delete package images" ON storage.objects
    FOR DELETE USING (bucket_id = 'sas-package-images' AND auth.role() = 'authenticated');
EXCEPTION WHEN duplicate_object THEN NULL;
END $$;

-- =====================================================
-- STORAGE POLICIES FOR sas-blog-images
-- =====================================================

-- Anyone can view blog images (public access)
CREATE POLICY "Anyone can view blog images" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-blog-images');

-- Authenticated users can upload blog images
CREATE POLICY "Authenticated users can upload blog images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

-- Authenticated users can update blog images
CREATE POLICY "Authenticated users can update blog images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

-- Authenticated users can delete blog images
CREATE POLICY "Authenticated users can delete blog images" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-blog-images' AND auth.role() = 'authenticated');

-- =====================================================
-- STORAGE POLICIES FOR sas-user-avatars
-- =====================================================

-- Anyone can view user avatars (public access)
CREATE POLICY "Anyone can view user avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-user-avatars');

-- Authenticated users can upload avatars
CREATE POLICY "Authenticated users can upload avatars" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

-- Users can update their own avatars
CREATE POLICY "Users can update their own avatars" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

-- Users can delete their own avatars
CREATE POLICY "Users can delete their own avatars" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-user-avatars' AND auth.role() = 'authenticated');

-- =====================================================
-- STORAGE POLICIES FOR sas-general-uploads
-- =====================================================

-- Anyone can view general uploads (public access)
CREATE POLICY "Anyone can view general uploads" ON storage.objects
  FOR SELECT USING (bucket_id = 'sas-general-uploads');

-- Authenticated users can upload general files
CREATE POLICY "Authenticated users can upload general files" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');

-- Authenticated users can update general files
CREATE POLICY "Authenticated users can update general files" ON storage.objects
  FOR UPDATE USING (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');

-- Authenticated users can delete general files
CREATE POLICY "Authenticated users can delete general files" ON storage.objects
  FOR DELETE USING (bucket_id = 'sas-general-uploads' AND auth.role() = 'authenticated');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Run these queries to verify your setup:

-- 1. Check if buckets were created successfully
SELECT id, name, public, file_size_limit, allowed_mime_types 
FROM storage.buckets 
WHERE id LIKE 'sas-%';

-- 2. Check if policies were created successfully
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%sas-%';

-- =====================================================
-- BUCKET INFORMATION
-- =====================================================

/*
BUCKET DETAILS:

1. sas-package-images (10MB limit)
   - Used for: Package main images, hero images, gallery images
   - Formats: JPEG, PNG, WebP, GIF
   - Access: Public read, authenticated write

2. sas-blog-images (10MB limit)
   - Used for: Blog post images, featured images
   - Formats: JPEG, PNG, WebP, GIF
   - Access: Public read, authenticated write

3. sas-user-avatars (5MB limit)
   - Used for: User profile pictures, admin avatars
   - Formats: JPEG, PNG, WebP
   - Access: Public read, authenticated write

4. sas-general-uploads (50MB limit)
   - Used for: PDFs, documents, other files
   - Formats: Images, PDFs, text files
   - Access: Public read, authenticated write

IMPORTANT NOTES:
- All buckets are PUBLIC for website display
- Only authenticated users can upload/modify files
- File size limits are enforced
- MIME type restrictions are in place
- Your upload API requires alt text for images (accessibility)
*/
