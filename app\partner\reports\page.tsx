'use client';

import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Download, 
  ArrowUp,
  ArrowDown,
  Package,
  Users} from 'lucide-react';

interface CommissionData {
  month: string;
  bookings: number;
  revenue: number;
  commission: number;
  commissionRate: number;
}

interface TopPackage {
  name: string;
  bookings: number;
  revenue: number;
  commission: number;
}

interface ReportStats {
  totalCommission: number;
  monthlyCommission: number;
  averageCommissionRate: number;
  totalBookings: number;
  monthlyGrowth: number;
  yearToDateCommission: number;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerReportsPage() {
  const [reportPeriod, setReportPeriod] = useState('last-6-months');
  const [stats, setStats] = useState<ReportStats>({
    totalCommission: 0,
    monthlyCommission: 0,
    averageCommissionRate: 0,
    totalBookings: 0,
    monthlyGrowth: 0,
    yearToDateCommission: 0
  });
  const [commissionData, setCommissionData] = useState<CommissionData[]>([]);
  const [topPackages, setTopPackages] = useState<TopPackage[]>([]);

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockStats: ReportStats = {
      totalCommission: 13410,
      monthlyCommission: 2850,
      averageCommissionRate: 15,
      totalBookings: 156,
      monthlyGrowth: 18.5,
      yearToDateCommission: 45600
    };

    const mockCommissionData: CommissionData[] = [
      { month: 'Jul 2024', bookings: 18, revenue: 12400, commission: 1860, commissionRate: 15 },
      { month: 'Aug 2024', bookings: 22, revenue: 15600, commission: 2340, commissionRate: 15 },
      { month: 'Sep 2024', bookings: 25, revenue: 18200, commission: 2730, commissionRate: 15 },
      { month: 'Oct 2024', bookings: 28, revenue: 21800, commission: 3270, commissionRate: 15 },
      { month: 'Nov 2024', bookings: 31, revenue: 24600, commission: 3690, commissionRate: 15 },
      { month: 'Dec 2024', bookings: 32, revenue: 26400, commission: 3960, commissionRate: 15 }
    ];

    const mockTopPackages: TopPackage[] = [
      { name: 'Serengeti & Ngorongoro 5-Day Safari', bookings: 45, revenue: 108000, commission: 16200 },
      { name: 'Kilimanjaro Climbing Adventure', bookings: 28, revenue: 89600, commission: 10752 },
      { name: 'Tarangire Day Trip', bookings: 67, revenue: 13400, commission: 2680 },
      { name: 'Maasai Cultural Experience', bookings: 16, revenue: 6400, commission: 1152 }
    ];

    setStats(mockStats);
    setCommissionData(mockCommissionData);
    setTopPackages(mockTopPackages);
  }, [reportPeriod]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleExportReport = () => {
    const csvContent = [
      ['Month', 'Bookings', 'Revenue', 'Commission', 'Commission Rate'],
      ...commissionData.map(data => [
        data.month,
        data.bookings.toString(),
        data.revenue.toString(),
        data.commission.toString(),
        `${data.commissionRate}%`
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `commission-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const maxCommission = Math.max(...commissionData.map(d => d.commission));

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Commission Reports</h1>
          <p className="text-gray-600">Track your earnings and performance analytics</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={reportPeriod}
            onChange={(e) => setReportPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="last-3-months">Last 3 Months</option>
            <option value="last-6-months">Last 6 Months</option>
            <option value="last-12-months">Last 12 Months</option>
            <option value="year-to-date">Year to Date</option>
          </select>
          <button
            onClick={handleExportReport}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Commission</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalCommission)}</p>
              <div className="flex items-center mt-2">
                <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">{stats.monthlyGrowth}% this month</span>
              </div>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Commission</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.monthlyCommission)}</p>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-600">This month</span>
              </div>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Rate</p>
              <p className="text-2xl font-bold text-gray-900">{stats.averageCommissionRate}%</p>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-600">Commission rate</span>
              </div>
            </div>
            <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">YTD Commission</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.yearToDateCommission)}</p>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-600">Year to date</span>
              </div>
            </div>
            <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Commission Chart */}
        <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">Commission Trends</h2>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-gray-600">Commission</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-gray-600">Revenue</span>
              </div>
            </div>
          </div>
          
          {/* Simple Bar Chart */}
          <div className="space-y-4">
            {commissionData.map((data, index) => (
              <div key={index} className="flex items-center">
                <div className="w-20 text-sm text-gray-600">{data.month}</div>
                <div className="flex-1 ml-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                      <div
                        className="bg-blue-500 h-6 rounded-full flex items-center justify-end pr-2"
                        style={{ width: `${(data.commission / maxCommission) * 100}%` }}
                      >
                        <span className="text-white text-xs font-medium">
                          {formatCurrency(data.commission)}
                        </span>
                      </div>
                    </div>
                    <div className="w-16 text-sm text-gray-600 text-right">
                      {data.bookings} bookings
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Top Performing Packages */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Top Performing Packages</h2>
          <div className="space-y-4">
            {topPackages.map((pkg, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 text-sm line-clamp-2">{pkg.name}</div>
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center text-xs text-gray-500">
                      <Package className="h-3 w-3 mr-1" />
                      {pkg.bookings} bookings
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-sm font-medium text-green-600">
                      {formatCurrency(pkg.commission)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatCurrency(pkg.revenue)} revenue
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Detailed Commission Table */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Detailed Commission Breakdown</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Period</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Bookings</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Revenue</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Commission</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Rate</th>
                <th className="text-left py-3 px-6 font-medium text-gray-600 text-sm">Growth</th>
              </tr>
            </thead>
            <tbody>
              {commissionData.map((data, index) => {
                const prevData = commissionData[index - 1];
                const growth = prevData 
                  ? ((data.commission - prevData.commission) / prevData.commission * 100).toFixed(1)
                  : null;
                
                return (
                  <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-4 px-6">
                      <span className="font-medium text-gray-900">{data.month}</span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center">
                        <Users className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-gray-900">{data.bookings}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-medium text-gray-900">{formatCurrency(data.revenue)}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-medium text-green-600">{formatCurrency(data.commission)}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="text-gray-900">{data.commissionRate}%</span>
                    </td>
                    <td className="py-4 px-6">
                      {growth !== null ? (
                        <div className="flex items-center">
                          {parseFloat(growth) >= 0 ? (
                            <ArrowUp className="h-4 w-4 text-green-500 mr-1" />
                          ) : (
                            <ArrowDown className="h-4 w-4 text-red-500 mr-1" />
                          )}
                          <span className={`text-sm font-medium ${
                            parseFloat(growth) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {Math.abs(parseFloat(growth))}%
                          </span>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">-</span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
