/* eslint-disable @next/next/no-img-element */
import React, { useState, ChangeEvent } from 'react';
import { Upload, MapPin, Star, TreePine, Mountain, Building, Waves } from 'lucide-react';

interface PackageFormData {
  title: string;
  location: string;
  difficulty: string;
  content: unknown[];
  duration: string;
  pricing: {
    solo: number;
    honeymoon: number;
    family: number;
    group: number;
  };
  category: string;
  status: 'published' | 'draft' | 'archived';

  imageUrl: string;
  imageAlt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
  highlights: string[];
  packingList: string[];
  includes: string[];
  excludes: string[];
}

interface PackageCardProps {
  formData: PackageFormData;
  onFormDataChange: (data: Partial<PackageFormData>) => void;
}

// Validation function to check if form data contains blob URLs
export const validatePackageFormData = (formData: PackageFormData): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (formData.imageUrl && formData.imageUrl.startsWith('blob:')) {
    errors.push('Main image contains invalid URL. Please upload the image properly.');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

const PackageCard: React.FC<PackageCardProps> = ({ formData, onFormDataChange }) => {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [customDifficulty, setCustomDifficulty] = useState('');
  const [customCategory, setCustomCategory] = useState('');
  const [showCustomDifficulty, setShowCustomDifficulty] = useState(false);
  const [showCustomCategory, setShowCustomCategory] = useState(false);

  // Ensure pricing object is always defined and properly structured
  const safeFormData = {
    ...formData,
    pricing: formData.pricing && typeof formData.pricing === 'object'
      ? {
          solo: Number(formData.pricing.solo) || 0,
          honeymoon: Number(formData.pricing.honeymoon) || 0,
          family: Number(formData.pricing.family) || 0,
          group: Number(formData.pricing.group) || 0
        }
      : { solo: 0, honeymoon: 0, family: 0, group: 0 }
  };

  const updateFormData = (updates: Partial<PackageFormData>) => {
    onFormDataChange(updates);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle pricing fields
    if (name.startsWith('pricing.')) {
      const pricingType = name.split('.')[1] as keyof PackageFormData['pricing'];
      updateFormData({
        pricing: {
          ...safeFormData.pricing,
          [pricingType]: Number(value)
        }
      });
    } else {
      updateFormData({
        [name]: value
      });
    }
  };

  const handleImageUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB');
      return;
    }

    try {
      // Create preview immediately
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload to server
      const uploadFormData = new FormData();
      uploadFormData.append('file', file);
      uploadFormData.append('altText', formData.imageAlt || file.name.split('.')[0]); // Use user input or filename as fallback
      uploadFormData.append('bucket', 'sas-package-images');

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: uploadFormData
      });

      const result = await response.json();

      // Debug: Log upload result
      console.log('PackageCard - Upload result:', result);

      if (result.success) {
        // Validate that we got a proper Supabase URL, not a blob URL
        if (result.data.url && result.data.url.startsWith('blob:')) {
          console.error('PackageCard - Received blob URL from upload API:', result.data.url);
          alert('Upload failed: Invalid image URL received. Please try again.');
          return;
        }

        // Validate that we got a Supabase storage URL
        if (!result.data.url || !result.data.url.includes('supabase.co/storage')) {
          console.error('PackageCard - Invalid URL from upload API:', result.data.url);
          alert('Upload failed: Invalid image URL. Please try again.');
          return;
        }

        // Debug: Log what we're updating form data with
        console.log('PackageCard - Updating form data with:', {
          imageUrl: result.data.url,
          imageAlt: result.data.altText
        });

        // Update form data with uploaded image URL
        const updateData = {
          imageUrl: result.data.url,
          imageAlt: result.data.altText
        };

        console.log('PackageCard - About to update form data with:', updateData);
        updateFormData(updateData);

        // Clear the blob URL preview so it uses the Supabase URL from formData
        setImagePreview(null);

        // Debug: Log form data after update (with timeout to allow state update)
        setTimeout(() => {
          console.log('PackageCard - Form data after update (delayed):', formData);
        }, 100);
      } else {
        console.error('Failed to upload image:', result.error);
        alert('Failed to upload image: ' + result.error);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    }
  };

  const getDifficultyColor = (difficulty: string): React.CSSProperties => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return { color: 'var(--light-green)' };
      case 'moderate': return { color: 'var(--accent)' };
      case 'hard': return { color: '#dc2626' }; // red-600 equivalent
      default: return { color: 'var(--text)' };
    }
  };

  const getCategoryIcon = (category: string): React.ReactNode => {
    switch (category.toLowerCase()) {
      case 'wildlife': return <TreePine className="w-4 h-4" />;
      case 'adventure': return <Mountain className="w-4 h-4" />;
      case 'cultural': return <Building className="w-4 h-4" />;
      case 'beach': return <Waves className="w-4 h-4" />;
      default: return <MapPin className="w-4 h-4" />;
    }
  };

  const getLowestPrice = (): number => {
    if (!formData.pricing || typeof formData.pricing !== 'object') {
      return 0;
    }

    const prices = Object.values(formData.pricing).filter(price => typeof price === 'number' && price > 0);
    return prices.length > 0 ? Math.min(...prices) : 0;
  };

  // Debug: Log current image state
  console.log('PackageCard - Current image state:', {
    imagePreview,
    formDataImageUrl: formData.imageUrl,
    displayUrl: imagePreview || formData.imageUrl
  });

  return (
    <div className="min-h-screen p-4" style={{ backgroundColor: 'var(--background)' }}>
      <div className="max-w-5xl mx-auto">
        <h1 className="text-2xl font-bold mb-6 text-center" style={{ color: 'var(--text)' }}>Package Card Creator</h1>

        <div className="grid lg:grid-cols-2 gap-6">
          {/* Form Section */}
          <div className="rounded-lg shadow-lg p-4" style={{ backgroundColor: 'var(--white)' }}>
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2" style={{ color: 'var(--text)' }}>
              <Upload className="w-4 h-4" style={{ color: 'var(--accent)' }} />
              Package Details
            </h2>

            <div className="space-y-3">
              {/* Image Upload */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Package Image
                </label>
                <div className="border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer hover:opacity-80"
                     style={{ borderColor: 'var(--light-green)' }}>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label htmlFor="image-upload" className="cursor-pointer">
                    <Upload className="w-6 h-6 mx-auto mb-1" style={{ color: 'var(--accent)' }} />
                    <p className="text-xs" style={{ color: 'var(--text)' }}>Click to upload image</p>
                  </label>
                </div>
              </div>

              {/* Image Alt Text */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Image Alt Text *
                </label>
                <input
                  type="text"
                  name="imageAlt"
                  value={formData.imageAlt}
                  onChange={handleInputChange}
                  className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                  style={{
                    borderColor: 'var(--light-green)',
                    color: 'var(--text)',
                    backgroundColor: 'var(--white)'
                  }}
                  placeholder="Describe the image for accessibility"
                  required
                />
              </div>

              {/* Title */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Package Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                  style={{
                    borderColor: 'var(--light-green)',
                    color: 'var(--text)',
                    backgroundColor: 'var(--white)'
                  }}
                  placeholder="Enter package title"
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Location
                </label>
                <select
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                  style={{
                    borderColor: 'var(--light-green)',
                    color: 'var(--text)',
                    backgroundColor: 'var(--white)'
                  }}
                  required
                >
                  <option value="">Select location</option>
                  <option value="Rwanda">Rwanda</option>
                  <option value="Tanzania">Tanzania</option>
                  <option value="Uganda">Uganda</option>
                  <option value="South Africa">South Africa</option>
                  <option value="Luxury">Luxury</option>
                </select>
              </div>

              {/* Duration */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Duration
                </label>
                <input
                  type="text"
                  name="duration"
                  value={formData.duration}
                  onChange={handleInputChange}
                  className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                  style={{
                    borderColor: 'var(--light-green)',
                    color: 'var(--text)',
                    backgroundColor: 'var(--white)'
                  }}
                  placeholder="e.g., 4 Days 3 Nights"
                />
              </div>

              {/* Pricing */}
              <div>
                <label className="block text-xs font-medium mb-2" style={{ color: 'var(--text)' }}>
                  Pricing per Person ($)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="block text-xs mb-1" style={{ color: 'var(--text)' }}>Solo</label>
                    <input
                      type="number"
                      name="pricing.solo"
                      value={safeFormData.pricing.solo}
                      onChange={handleInputChange}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Solo price"
                    />
                  </div>
                  <div>
                    <label className="block text-xs mb-1" style={{ color: 'var(--text)' }}>Honeymoon</label>
                    <input
                      type="number"
                      name="pricing.honeymoon"
                      value={safeFormData.pricing.honeymoon}
                      onChange={handleInputChange}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Honeymoon price"
                    />
                  </div>
                  <div>
                    <label className="block text-xs mb-1" style={{ color: 'var(--text)' }}>Family</label>
                    <input
                      type="number"
                      name="pricing.family"
                      value={safeFormData.pricing.family}
                      onChange={handleInputChange}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Family price"
                    />
                  </div>
                  <div>
                    <label className="block text-xs mb-1" style={{ color: 'var(--text)' }}>Group</label>
                    <input
                      type="number"
                      name="pricing.group"
                      value={safeFormData.pricing.group}
                      onChange={handleInputChange}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Group price"
                    />
                  </div>
                </div>
              </div>

              {/* Difficulty */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Difficulty
                </label>
                {!showCustomDifficulty ? (
                  <div className="space-y-2">
                    <select
                      name="difficulty"
                      value={formData.difficulty}
                      onChange={(e) => {
                        if (e.target.value === 'custom') {
                          setShowCustomDifficulty(true);
                          setCustomDifficulty('');
                        } else {
                          handleInputChange(e);
                        }
                      }}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                    >
                      <option value="Easy">Easy</option>
                      <option value="Moderate">Moderate</option>
                      <option value="Hard">Hard</option>
                      <option value="Challenging">Challenging</option>
                      <option value="custom">+ Add Custom Difficulty</option>
                    </select>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={customDifficulty}
                      onChange={(e) => setCustomDifficulty(e.target.value)}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Enter custom difficulty"
                    />
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={() => {
                          if (customDifficulty.trim()) {
                            updateFormData({ difficulty: customDifficulty.trim() });
                            setShowCustomDifficulty(false);
                            setCustomDifficulty('');
                          }
                        }}
                        className="px-3 py-1 text-xs rounded-md"
                        style={{ backgroundColor: 'var(--light-green)', color: 'var(--white)' }}
                      >
                        Add
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowCustomDifficulty(false);
                          setCustomDifficulty('');
                        }}
                        className="px-3 py-1 text-xs rounded-md border"
                        style={{ borderColor: 'var(--light-green)', color: 'var(--text)' }}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Category */}
              <div>
                <label className="block text-xs font-medium mb-1" style={{ color: 'var(--text)' }}>
                  Tour Category
                </label>
                {!showCustomCategory ? (
                  <div className="space-y-2">
                    <select
                      name="category"
                      value={formData.category}
                      onChange={(e) => {
                        if (e.target.value === 'custom') {
                          setShowCustomCategory(true);
                          setCustomCategory('');
                        } else {
                          handleInputChange(e);
                        }
                      }}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                    >
                      <option value="Wildlife">Wildlife</option>
                      <option value="Adventure">Adventure</option>
                      <option value="Cultural">Cultural</option>
                      <option value="Beach">Beach</option>
                      <option value="Safari">Safari</option>
                      <option value="Nature">Nature</option>
                      <option value="Luxury">Luxury</option>
                      <option value="custom">+ Add Custom Category</option>
                    </select>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={customCategory}
                      onChange={(e) => setCustomCategory(e.target.value)}
                      className="w-full px-2 py-1.5 text-sm border rounded-md focus:outline-none focus:ring-2"
                      style={{
                        borderColor: 'var(--light-green)',
                        color: 'var(--text)',
                        backgroundColor: 'var(--white)'
                      }}
                      placeholder="Enter custom category"
                    />
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={() => {
                          if (customCategory.trim()) {
                            updateFormData({ category: customCategory.trim() });
                            setShowCustomCategory(false);
                            setCustomCategory('');
                          }
                        }}
                        className="px-3 py-1 text-xs rounded-md"
                        style={{ backgroundColor: 'var(--light-green)', color: 'var(--white)' }}
                      >
                        Add
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setShowCustomCategory(false);
                          setCustomCategory('');
                        }}
                        className="px-3 py-1 text-xs rounded-md border"
                        style={{ borderColor: 'var(--light-green)', color: 'var(--text)' }}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Preview Section */}
          <div className="rounded-lg shadow-lg p-4" style={{ backgroundColor: 'var(--white)' }}>
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2" style={{ color: 'var(--text)' }}>
              <Star className="w-4 h-4" style={{ color: 'var(--accent)' }} />
              Live Preview
            </h2>

            {/* Package Card Preview */}
            <div className="rounded-xl shadow-lg overflow-hidden max-w-sm mx-auto border"
                 style={{ backgroundColor: 'var(--white)', borderColor: 'var(--light-green)' }}>
              {/* Image Section */}
              <div className="relative h-36">
                {imagePreview || formData.imageUrl ? (
                  <img
                    src={imagePreview || formData.imageUrl}
                    alt={formData.imageAlt || "Package preview"}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      console.error('PackageCard - Image failed to load:', target.src);
                      // If it's a blob URL that failed, try to use the Supabase URL
                      if (target.src.startsWith('blob:') && formData.imageUrl && !formData.imageUrl.startsWith('blob:')) {
                        target.src = formData.imageUrl;
                      }
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center"
                       style={{ backgroundColor: 'var(--light-green)' }}>
                    <div className="text-center" style={{ color: 'var(--white)' }}>
                      <Upload className="w-8 h-8 mx-auto mb-1 opacity-70" />
                      <p className="text-xs opacity-70">Upload an image</p>
                    </div>
                  </div>
                )}

                {/* Location Badge */}
                <div className="absolute bottom-2 left-2">
                  <div className="rounded-full px-2 py-0.5 flex items-center gap-1 shadow-md"
                       style={{ backgroundColor: 'var(--white)' }}>
                    <MapPin className="w-3 h-3" style={{ color: 'var(--accent)' }} />
                    <span className="text-xs font-medium" style={{ color: 'var(--text)' }}>{formData.location}</span>
                  </div>
                </div>

                {/* Price Badge */}
                <div className="absolute top-2 right-2">
                  <div className="rounded-lg px-2 py-1 shadow-md" style={{ backgroundColor: 'var(--white)' }}>
                    <div className="text-xs" style={{ color: 'var(--text)' }}>From</div>
                    <div className="text-xs font-bold" style={{ color: 'var(--text)' }}>
                      ${getLowestPrice().toLocaleString()}/Person
                    </div>
                  </div>
                </div>
              </div>

              {/* Content Section */}
              <div className="p-4" style={{ backgroundColor: 'var(--card-bg)' }}>
                <h3 className="text-lg font-bold mb-3 leading-tight" style={{ color: 'var(--text)' }}>
                  {formData.title}
                </h3>

                <div className="flex justify-between items-center mb-4">
                  <div>
                    <div className="text-xs mb-0.5" style={{ color: 'var(--text)' }}>Difficulty</div>
                    <div className="text-sm font-semibold" style={getDifficultyColor(formData.difficulty)}>
                      {formData.difficulty}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs mb-0.5" style={{ color: 'var(--text)' }}>Tour Category</div>
                    <div className="text-sm font-semibold flex items-center gap-1" style={{ color: 'var(--text)' }}>
                      {getCategoryIcon(formData.category)}
                      {formData.category}
                    </div>
                  </div>
                </div>

                <button className="w-full font-semibold py-2 px-4 rounded-lg transition-colors duration-200 text-sm hover:opacity-90"
                        style={{ backgroundColor: 'var(--btn)', color: 'var(--white)' }}>
                  Book Now
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackageCard;