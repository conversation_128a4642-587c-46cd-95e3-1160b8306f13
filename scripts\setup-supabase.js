#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🚀 Setting up Supabase for Swift Africa Safaris Admin Dashboard\n');

// Check if .env.local already exists
const envPath = path.join(process.cwd(), '.env.local');
const envExists = fs.existsSync(envPath);

if (envExists) {
  console.log('⚠️  .env.local already exists. Please update it manually with your Supabase credentials.\n');
} else {
  // Create .env.local template
  const envTemplate = `# Supabase Configuration
# Replace these values with your actual Supabase project credentials
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Example:
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
`;

  fs.writeFileSync(envPath, envTemplate);
  console.log('✅ Created .env.local template\n');
}

console.log('📋 Next Steps:');
console.log('1. Go to your Supabase project dashboard');
console.log('2. Copy your Project URL and anon/public key');
console.log('3. Update .env.local with your actual values');
console.log('4. Run the SQL commands from SUPABASE_SETUP.md in your Supabase SQL editor');
console.log('5. Start your development server: npm run dev');
console.log('\n📖 See SUPABASE_SETUP.md for detailed instructions\n'); 