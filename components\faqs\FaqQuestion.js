import { ChevronDown, ChevronUp } from 'lucide-react';

const FaqQuestion = ({ question, answer, isOpen, onToggle }) => {
  return (
    <div className="flex flex-col rounded-lg h-auto">
      <button
        onClick={onToggle}
        className="bg-[var(--secondary-background)] w-full p-4 text-left flex items-center justify-between border-gray-200 hover:bg-gray-50 transition-colors"
      >
        <span className="font-medium text-gray-900 pr-4">
          {question}
        </span>
        <div className="flex-shrink-0 w-8 h-8 bg-black rounded-full flex items-center justify-center">
          {isOpen ? (
            <ChevronUp className="w-4 h-4 text-white" />
          ) : (
            <ChevronDown className="w-4 h-4 text-white" />
          )}
        </div>
      </button>
      
      <div className={`transition-all duration-200 ease-in-out overflow-hidden ${isOpen ? 'max-h-96' : 'max-h-0'}`}>
        <div className="bg-[var(--primary-background)] px-4 py-4">
          <p className="text-gray-600 text-sm leading-relaxed text-justify">
            {answer}
          </p>
        </div>
      </div>
    </div>
  );
};

export default FaqQuestion;
