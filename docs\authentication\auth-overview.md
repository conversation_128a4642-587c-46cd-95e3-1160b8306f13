# Authentication System Overview

## Table of Contents
- [Architecture](#architecture)
- [Components](#components)
- [Flow Diagrams](#flow-diagrams)
- [Database Schema](#database-schema)
- [API Endpoints](#api-endpoints)
- [Security Features](#security-features)
- [Development vs Production](#development-vs-production)

---

## Architecture

The Swift Africa Safaris authentication system uses a hybrid approach combining Supabase Auth with custom user management.

### Key Components
1. **Supabase Auth** - Handles authentication, sessions, and security
2. **Custom Users Table** - Stores additional user data and roles
3. **AuthGuard Component** - Protects admin routes
4. **useAuth Hook** - Provides authentication state and methods
5. **Role-Based Access Control** - Manages permissions by user role

### Authentication Flow
```
User Login → Supabase Auth → Custom User Lookup → Role Verification → Access Granted
```

---

## Components

### AuthGuard Component
**Location**: `components/admin/common/AuthGuard.tsx`

Protects admin routes and handles authentication state:
- Checks user authentication status
- Verifies admin permissions
- Redirects unauthorized users
- Provides loading states
- **Development Mode**: Bypasses authentication for faster development

### useAuth Hook
**Location**: `hooks/useAuth.ts`

Provides authentication state and methods:
```typescript
const { user, loading, error, isAdmin, logout } = useAuth();
```

### Login Component
**Location**: `app/login/page.tsx`

Handles user login with email/password authentication.

---

## Flow Diagrams

### Production Authentication Flow
```mermaid
graph TD
    A[User Accesses Admin] --> B[AuthGuard Check]
    B --> C{User Authenticated?}
    C -->|No| D[Redirect to Login]
    C -->|Yes| E{Is Admin Role?}
    E -->|No| F[Access Denied]
    E -->|Yes| G[Grant Access]
    D --> H[Login Form]
    H --> I[Supabase Auth]
    I --> J{Auth Success?}
    J -->|No| K[Show Error]
    J -->|Yes| L[Lookup User Data]
    L --> M[Set User State]
    M --> G
```

### Development Authentication Flow
```mermaid
graph TD
    A[User Accesses Admin] --> B[AuthGuard Check]
    B --> C{NODE_ENV === 'development'?}
    C -->|Yes| D[Bypass Authentication]
    C -->|No| E[Normal Auth Flow]
    D --> F[Grant Immediate Access]
    E --> G[Production Flow]
```

---

## Database Schema

### auth.users (Supabase Auth)
```sql
-- Managed by Supabase
id: uuid (primary key)
email: text
encrypted_password: text
email_confirmed_at: timestamp
created_at: timestamp
updated_at: timestamp
```

### users (Custom Table)
```sql
-- Custom user data
id: uuid (foreign key to auth.users.id)
email: text (unique)
name: text
role: text ('admin', 'content_creator', 'tour_specialist', 'it')
status: text ('active', 'inactive', 'pending')
created_at: timestamp
updated_at: timestamp
```

### Relationship
```sql
-- Foreign key constraint
ALTER TABLE users 
ADD CONSTRAINT users_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;
```

---

## API Endpoints

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/user` - Get current user data
- `POST /api/auth/refresh` - Refresh session

### User Management Endpoints
- `GET /api/admin/users` - List all users (admin only)
- `POST /api/admin/users` - Create new user (admin only)
- `PUT /api/admin/users/[id]` - Update user (admin only)
- `DELETE /api/admin/users/[id]` - Delete user (admin only)

---

## Security Features

### Production Security
1. **JWT Tokens** - Secure session management via Supabase
2. **Role-Based Access** - Different permission levels
3. **Route Protection** - AuthGuard on all admin routes
4. **Session Validation** - Automatic token refresh
5. **CSRF Protection** - Built into Supabase Auth

### Development Security
1. **Environment Isolation** - Bypass only works in development
2. **Clear Indicators** - Visual warnings when bypass is active
3. **Audit Logging** - Console warnings for bypass usage
4. **Production Validation** - Checks prevent production bypass

### Password Security
- Minimum 8 characters
- Supabase handles hashing and salting
- Password reset functionality
- Account lockout after failed attempts

---

## Development vs Production

### Development Mode Features
```typescript
// When NODE_ENV === 'development'
- Authentication bypass available
- Mock user data
- Console debug logging
- Visual bypass indicators
- Faster development workflow
```

### Production Mode Features
```typescript
// When NODE_ENV === 'production'
- Full authentication required
- Real user database lookups
- Session management
- Security logging
- Rate limiting
```

### Environment Detection
```typescript
const isDevelopment = process.env.NODE_ENV === 'development';

if (isDevelopment) {
  // Development-only features
  console.warn('🚨 Development mode active');
  return <DevelopmentBypass />;
}

// Production authentication flow
return <ProductionAuth />;
```

---

## User Roles

### Admin
- Full system access
- User management
- Content management
- System configuration

### Content Creator
- Create/edit content
- Manage blog posts
- Upload media
- Limited admin access

### Tour Specialist
- Manage packages
- Handle bookings
- Customer communication
- Package pricing

### IT
- System maintenance
- Technical support
- Database access
- Security monitoring

---

## Session Management

### Session Lifecycle
1. **Login** - Create session with Supabase
2. **Validation** - Check session on each request
3. **Refresh** - Automatic token refresh
4. **Logout** - Clear session data
5. **Expiry** - Handle expired sessions

### Session Storage
- **Server-side** - Supabase manages JWT tokens
- **Client-side** - Minimal session data in localStorage
- **Security** - HttpOnly cookies for sensitive data

---

## Error Handling

### Common Authentication Errors
- Invalid credentials
- Expired sessions
- Insufficient permissions
- Network connectivity issues
- Database connection problems

### Error Recovery
- Automatic retry for network issues
- Graceful degradation for offline mode
- Clear error messages for users
- Fallback authentication methods

---

## Related Documentation

- [**Bypass Authentication for Development**](./bypass-auth-development.md) ⭐
- [User Management](./user-management.md)
- [Role-Based Access Control](./rbac.md)
- [Environment Configuration](../setup/environment-config.md)
- [Security Considerations](../deployment/security.md)
- [Authentication Problems](../troubleshooting/auth-problems.md)
