/**
 * Advanced Analytics and Performance Monitoring
 * Tracks Core Web Vitals, user interactions, and SEO metrics
 */

interface PerformanceMetric {
  name: string
  value: number
  url: string
  timestamp: number
  id?: string
  delta?: number
  rating?: 'good' | 'needs-improvement' | 'poor'
}

interface UserInteraction {
  event: string
  element?: string
  url: string
  timestamp: number
  userId?: string
}

interface PerformanceAlert {
  metric: string
  threshold: number
  value: number
  severity: 'warning' | 'critical'
  timestamp: number
}

interface SessionData {
  sessionId: string
  startTime: number
  pageViews: number
  interactions: number
  errors: number
  deviceInfo: {
    type: string
    userAgent: string
    viewport: { width: number; height: number }
    connection: string
  }
}

class AnalyticsManager {
  private isInitialized = false
  private queue: Array<PerformanceMetric | UserInteraction> = []
  private batchSize = 10
  private flushInterval = 30000 // 30 seconds
  private sessionData: SessionData | null = null
  private performanceObserver: PerformanceObserver | null = null
  private alertThresholds = {
    LCP: { warning: 2500, critical: 4000 },
    FID: { warning: 100, critical: 300 },
    CLS: { warning: 0.1, critical: 0.25 },
    TTFB: { warning: 800, critical: 1800 },
    page_load_time: { warning: 3000, critical: 5000 }
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.init()
    }
  }

  private async init() {
    if (this.isInitialized) return

    try {
      // Initialize session tracking
      this.initializeSession()

      // Initialize Web Vitals monitoring
      const { getCLS, getFID, getFCP, getLCP, getTTFB, onINP } = await import('web-vitals')

      // Track Core Web Vitals with alerting
      getCLS((metric) => this.handleMetricWithAlerting(metric))
      getFID((metric) => this.handleMetricWithAlerting(metric))
      getFCP((metric) => this.handleMetricWithAlerting(metric))
      getLCP((metric) => this.handleMetricWithAlerting(metric))
      getTTFB((metric) => this.handleMetricWithAlerting(metric))
      onINP((metric) => this.handleMetricWithAlerting(metric))

      // Track custom performance metrics
      this.trackCustomMetrics()

      // Initialize real-time monitoring
      this.initializeRealTimeMonitoring()

      // Set up periodic flushing
      setInterval(() => this.flush(), this.flushInterval)

      // Flush on page unload and send session data
      window.addEventListener('beforeunload', () => {
        this.flush()
        this.sendSessionData()
      })

      // Track page visibility changes
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
          this.flush()
        }
      })

      this.isInitialized = true
      console.log('✅ Enhanced Analytics initialized with real-time monitoring')

    } catch (error) {
      console.error('❌ Analytics initialization failed:', error)
    }
  }

  private initializeSession() {
    const sessionId = this.generateSessionId()
    this.sessionData = {
      sessionId,
      startTime: Date.now(),
      pageViews: 1,
      interactions: 0,
      errors: 0,
      deviceInfo: {
        type: this.getDeviceType(),
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        connection: (navigator as any).connection?.effectiveType || 'unknown'
      }
    }

    // Store session in localStorage for persistence
    localStorage.setItem('analytics_session', JSON.stringify(this.sessionData))
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeRealTimeMonitoring() {
    // Monitor memory usage
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory
        this.trackMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024) // MB
        this.trackMetric('memory_total', memory.totalJSHeapSize / 1024 / 1024) // MB
      }, 30000) // Every 30 seconds
    }

    // Monitor network status
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection.addEventListener('change', () => {
        this.trackMetric('connection_change', 1)
        this.trackInteraction('network_change', `${connection.effectiveType}_${connection.downlink}mbps`)
      })
    }

    // Monitor page performance continuously
    this.performanceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.processPerformanceEntry(entry)
      }
    })

    this.performanceObserver.observe({
      entryTypes: ['navigation', 'resource', 'paint', 'layout-shift', 'long-animation-frame']
    })
  }

  private handleMetricWithAlerting(metric: any) {
    const performanceMetric: PerformanceMetric = {
      name: metric.name,
      value: metric.value,
      url: window.location.pathname,
      timestamp: Date.now(),
      id: metric.id,
      delta: metric.delta,
      rating: metric.rating
    }

    this.queue.push(performanceMetric)

    // Check for performance alerts
    this.checkPerformanceAlert(metric.name, metric.value, metric.rating)

    // Log critical metrics immediately
    if (metric.rating === 'poor') {
      console.warn(`⚠️ Poor ${metric.name}: ${metric.value}${metric.name.includes('CLS') ? '' : 'ms'}`)
      this.sendRealTimeAlert(metric.name, metric.value, 'critical')
    } else if (metric.rating === 'needs-improvement') {
      console.log(`⚠️ Needs improvement ${metric.name}: ${metric.value}${metric.name.includes('CLS') ? '' : 'ms'}`)
      this.sendRealTimeAlert(metric.name, metric.value, 'warning')
    }

    // Flush if queue is full
    if (this.queue.length >= this.batchSize) {
      this.flush()
    }
  }

  private checkPerformanceAlert(metricName: string, value: number, rating?: string) {
    const thresholds = this.alertThresholds[metricName as keyof typeof this.alertThresholds]
    if (!thresholds) return

    let severity: 'warning' | 'critical' | null = null

    if (value >= thresholds.critical) {
      severity = 'critical'
    } else if (value >= thresholds.warning) {
      severity = 'warning'
    }

    if (severity) {
      const alert: PerformanceAlert = {
        metric: metricName,
        threshold: severity === 'critical' ? thresholds.critical : thresholds.warning,
        value,
        severity,
        timestamp: Date.now()
      }

      this.handlePerformanceAlert(alert)
    }
  }

  private handlePerformanceAlert(alert: PerformanceAlert) {
    // Store alert locally for dashboard
    const alerts = JSON.parse(localStorage.getItem('performance_alerts') || '[]')
    alerts.push(alert)

    // Keep only last 50 alerts
    if (alerts.length > 50) {
      alerts.splice(0, alerts.length - 50)
    }

    localStorage.setItem('performance_alerts', JSON.stringify(alerts))

    // Send to server for monitoring
    this.sendAlertToServer(alert)
  }

  private async sendRealTimeAlert(metric: string, value: number, severity: 'warning' | 'critical') {
    try {
      await fetch('/api/analytics/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          metric,
          value,
          severity,
          url: window.location.pathname,
          timestamp: new Date().toISOString(),
          session_id: this.sessionData?.sessionId
        })
      })
    } catch (error) {
      console.error('Failed to send real-time alert:', error)
    }
  }

  private async sendAlertToServer(alert: PerformanceAlert) {
    try {
      await fetch('/api/analytics/performance-alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...alert,
          url: window.location.pathname,
          session_id: this.sessionData?.sessionId,
          user_agent: navigator.userAgent,
          timestamp: new Date(alert.timestamp).toISOString()
        })
      })
    } catch (error) {
      console.error('Failed to send alert to server:', error)
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry) {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming
        this.trackMetric('dns_lookup', navEntry.domainLookupEnd - navEntry.domainLookupStart)
        this.trackMetric('tcp_connect', navEntry.connectEnd - navEntry.connectStart)
        this.trackMetric('server_response', navEntry.responseEnd - navEntry.requestStart)
        this.trackMetric('dom_processing', navEntry.domComplete - navEntry.domLoading)
        break

      case 'resource':
        const resourceEntry = entry as PerformanceResourceTiming
        if (resourceEntry.name.includes('.js') || resourceEntry.name.includes('.css')) {
          const resourceName = resourceEntry.name.split('/').pop()?.split('?')[0] || 'unknown'
          this.trackMetric(`resource_${resourceName}`, resourceEntry.duration)
        }
        break

      case 'paint':
        if (entry.name === 'first-paint') {
          this.trackMetric('first_paint', entry.startTime)
        }
        break

      case 'layout-shift':
        const layoutShift = entry as any
        if (!layoutShift.hadRecentInput) {
          this.trackMetric('layout_shift', layoutShift.value)
        }
        break
    }
  }

  private trackCustomMetrics() {
    // Track page load time
    window.addEventListener('load', () => {
      const loadTime = performance.now()
      this.trackMetric('page_load_time', loadTime)
    })

    // Track DOM content loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        const domTime = performance.now()
        this.trackMetric('dom_content_loaded', domTime)
      })
    }

    // Track resource loading
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resource = entry as PerformanceResourceTiming
          if (resource.name.includes('.js') || resource.name.includes('.css')) {
            this.trackMetric(`resource_load_${resource.name.split('/').pop()}`, resource.duration)
          }
        }
      }
    })
    
    observer.observe({ entryTypes: ['resource'] })
  }

  public trackMetric(name: string, value: number, url?: string) {
    const metric: PerformanceMetric = {
      name,
      value,
      url: url || window.location.pathname,
      timestamp: Date.now()
    }
    
    this.queue.push(metric)
  }

  public trackInteraction(event: string, element?: string, userId?: string) {
    const interaction: UserInteraction = {
      event,
      element,
      url: window.location.pathname,
      timestamp: Date.now(),
      userId
    }

    this.queue.push(interaction)

    // Update session data
    if (this.sessionData) {
      this.sessionData.interactions++
      localStorage.setItem('analytics_session', JSON.stringify(this.sessionData))
    }
  }

  private async sendSessionData() {
    if (!this.sessionData) return

    try {
      await fetch('/api/analytics/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...this.sessionData,
          endTime: Date.now(),
          duration: Date.now() - this.sessionData.startTime,
          url: window.location.pathname
        })
      })
    } catch (error) {
      console.error('Failed to send session data:', error)
    }
  }

  // Enhanced public methods
  public trackPageView(url?: string) {
    this.trackMetric('page_view', 1, url)

    if (this.sessionData) {
      this.sessionData.pageViews++
      localStorage.setItem('analytics_session', JSON.stringify(this.sessionData))
    }
  }

  public trackError(error: string, url?: string) {
    this.trackMetric('javascript_error', 1, url)
    console.error('Tracked error:', error)

    if (this.sessionData) {
      this.sessionData.errors++
      localStorage.setItem('analytics_session', JSON.stringify(this.sessionData))
    }

    // Send critical errors immediately
    this.sendRealTimeAlert('javascript_error', 1, 'critical')
  }

  public trackCustomEvent(eventName: string, value?: number, metadata?: Record<string, any>) {
    this.trackMetric(eventName, value || 1)

    if (metadata) {
      this.trackInteraction(eventName, JSON.stringify(metadata))
    }
  }

  public trackConversion(type: string, value?: number) {
    this.trackMetric(`conversion_${type}`, value || 1)
    this.trackInteraction('conversion', type)
  }

  public trackFormSubmission(formName: string, success: boolean) {
    this.trackMetric(`form_${formName}_${success ? 'success' : 'error'}`, 1)
    this.trackInteraction('form_submit', `${formName}_${success}`)
  }

  public trackSearchQuery(query: string, resultsCount: number) {
    this.trackMetric('search_query', 1)
    this.trackMetric('search_results', resultsCount)
    this.trackInteraction('search', query)
  }

  // Performance monitoring controls
  public setAlertThreshold(metric: string, warning: number, critical: number) {
    this.alertThresholds[metric as keyof typeof this.alertThresholds] = { warning, critical }
  }

  public getPerformanceAlerts(): PerformanceAlert[] {
    return JSON.parse(localStorage.getItem('performance_alerts') || '[]')
  }

  public clearPerformanceAlerts() {
    localStorage.removeItem('performance_alerts')
  }

  public getSessionData(): SessionData | null {
    return this.sessionData
  }

  public async getPerformanceSummary(days: number = 7) {
    try {
      const response = await fetch(`/api/analytics/performance?days=${days}`)
      return await response.json()
    } catch (error) {
      console.error('Failed to get performance summary:', error)
      return null
    }
  }

  private async flush() {
    if (this.queue.length === 0) return

    const batch = [...this.queue]
    this.queue = []

    try {
      // Send performance metrics
      const metrics = batch.filter(item => 'value' in item) as PerformanceMetric[]
      if (metrics.length > 0) {
        await this.sendMetrics(metrics)
      }

      // Send user interactions
      const interactions = batch.filter(item => 'event' in item) as UserInteraction[]
      if (interactions.length > 0) {
        await this.sendInteractions(interactions)
      }

    } catch (error) {
      console.error('❌ Failed to send analytics:', error)
      // Re-queue failed items (with limit to prevent infinite growth)
      if (this.queue.length < 100) {
        this.queue.unshift(...batch)
      }
    }
  }

  private async sendMetrics(metrics: PerformanceMetric[]) {
    for (const metric of metrics) {
      try {
        await fetch('/api/analytics/performance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            metric_name: metric.name,
            metric_value: metric.value,
            url: metric.url,
            user_agent: navigator.userAgent,
            connection_type: (navigator as any).connection?.effectiveType || 'unknown',
            device_type: this.getDeviceType(),
            timestamp: new Date(metric.timestamp).toISOString()
          }),
        })
      } catch (error) {
        console.error('Failed to send metric:', metric.name, error)
      }
    }
  }

  private async sendInteractions(interactions: UserInteraction[]) {
    // Implement interaction tracking if needed
    console.log('Interactions to track:', interactions.length)
  }

  private getDeviceType(): string {
    const width = window.innerWidth
    if (width < 768) return 'mobile'
    if (width < 1024) return 'tablet'
    return 'desktop'
  }

  // Public methods for manual tracking
  public trackPageView(url?: string) {
    this.trackMetric('page_view', 1, url)
  }

  public trackClick(element: string) {
    this.trackInteraction('click', element)
  }

  public trackScroll(percentage: number) {
    this.trackMetric('scroll_depth', percentage)
  }

  public trackError(error: string, url?: string) {
    this.trackMetric('javascript_error', 1, url)
    console.error('Tracked error:', error)
  }
}

// Create singleton instance
export const analytics = new AnalyticsManager()

// Export utility functions
export const trackPageView = (url?: string) => analytics.trackPageView(url)
export const trackClick = (element: string) => analytics.trackClick(element)
export const trackScroll = (percentage: number) => analytics.trackScroll(percentage)
export const trackError = (error: string, url?: string) => analytics.trackError(error, url)
export const trackMetric = (name: string, value: number, url?: string) => analytics.trackMetric(name, value, url)

// SEO-specific tracking
export const trackSEOMetrics = () => {
  if (typeof window === 'undefined') return

  // Track time to first contentful paint
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.name === 'first-contentful-paint') {
        analytics.trackMetric('first_contentful_paint', entry.startTime)
      }
    }
  })
  
  observer.observe({ entryTypes: ['paint'] })

  // Track largest contentful paint
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1]
    analytics.trackMetric('largest_contentful_paint', lastEntry.startTime)
  })
  
  lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
}
