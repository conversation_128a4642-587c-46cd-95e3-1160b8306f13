import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

interface ItineraryDay {
  id?: string;
  package_id: string;
  day_number: number;
  title: string;
  description: string;
  activities?: string[];
  accommodation?: string;
  meals?: string[];
  sort_order: number;
}

// GET - Fetch itinerary for a package
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // First get the package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Fetch itinerary days
    const { data: itinerary, error } = await supabase
      .from('sas_package_itinerary')
      .select('*')
      .eq('package_id', packageData.id)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch itinerary' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: itinerary || []
    });

  } catch (error) {
    console.error('Error fetching itinerary:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch itinerary' },
      { status: 500 }
    );
  }
}

// POST - Create new itinerary day
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Validate required fields
    const requiredFields = ['day_number', 'title', 'description'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate day number
    const dayNumber = parseInt(body.day_number);
    if (isNaN(dayNumber) || dayNumber < 1) {
      return NextResponse.json(
        { success: false, error: 'Invalid day number' },
        { status: 400 }
      );
    }

    // Get next sort order
    const { data: lastDay } = await supabase
      .from('sas_package_itinerary')
      .select('sort_order')
      .eq('package_id', packageData.id)
      .order('sort_order', { ascending: false })
      .limit(1)
      .single();

    const nextSortOrder = lastDay ? lastDay.sort_order + 1 : 0;

    // Prepare itinerary day data
    const itineraryData: ItineraryDay = {
      package_id: packageData.id,
      day_number: dayNumber,
      title: body.title.trim(),
      description: body.description.trim(),
      activities: Array.isArray(body.activities) ? body.activities : [],
      accommodation: body.accommodation?.trim() || '',
      meals: Array.isArray(body.meals) ? body.meals : [],
      sort_order: body.sort_order !== undefined ? body.sort_order : nextSortOrder
    };

    // Insert itinerary day
    const { data: newDay, error } = await supabase
      .from('sas_package_itinerary')
      .insert([itineraryData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create itinerary day' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: newDay,
      message: 'Itinerary day created successfully'
    });

  } catch (error) {
    console.error('Error creating itinerary day:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create itinerary day' },
      { status: 500 }
    );
  }
}

// PUT - Update itinerary days (bulk update for reordering)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const body = await request.json();

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    if (!Array.isArray(body.days)) {
      return NextResponse.json(
        { success: false, error: 'Invalid days data' },
        { status: 400 }
      );
    }

    // Update each day
    const updatePromises = body.days.map(async (day: any) => {
      // Validate required fields
      if (!day.title || !day.description) {
        throw new Error('Title and description are required for all itinerary days');
      }

      const dayNumber = parseInt(day.day_number);
      if (isNaN(dayNumber) || dayNumber < 1) {
        throw new Error('Invalid day number');
      }

      return supabase
        .from('sas_package_itinerary')
        .update({
          day_number: dayNumber,
          title: day.title.trim(),
          description: day.description.trim(),
          activities: Array.isArray(day.activities) ? day.activities : [],
          accommodation: day.accommodation?.trim() || '',
          meals: Array.isArray(day.meals) ? day.meals : [],
          sort_order: day.sort_order
        })
        .eq('id', day.id)
        .eq('package_id', packageData.id);
    });

    const results = await Promise.all(updatePromises);
    
    // Check for errors
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('Supabase errors:', errors);
      return NextResponse.json(
        { success: false, error: 'Failed to update some itinerary days' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Itinerary updated successfully'
    });

  } catch (error) {
    console.error('Error updating itinerary:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to update itinerary' },
      { status: 500 }
    );
  }
}

// DELETE - Delete itinerary day
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const dayId = searchParams.get('dayId');

    if (!dayId) {
      return NextResponse.json(
        { success: false, error: 'Day ID is required' },
        { status: 400 }
      );
    }

    // Get package ID from slug
    const { data: packageData, error: packageError } = await supabase
      .from('sas_packages')
      .select('id')
      .eq('slug', slug)
      .single();

    if (packageError || !packageData) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Delete itinerary day
    const { error } = await supabase
      .from('sas_package_itinerary')
      .delete()
      .eq('id', dayId)
      .eq('package_id', packageData.id);

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete itinerary day' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Itinerary day deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting itinerary day:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete itinerary day' },
      { status: 500 }
    );
  }
}
