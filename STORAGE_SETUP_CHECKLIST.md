# 🚀 Quick Storage Setup Checklist

## ✅ Step-by-Step Setup (5 minutes)

### 1. **Open Supabase Dashboard**
- Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
- Select your Swift Africa Safaris project

### 2. **Run the SQL Setup**
- Click on "SQL Editor" in the left sidebar
- Copy the entire content from `scripts/supabase-storage-setup.sql`
- Paste it into the SQL editor
- Click "Run" button
- ✅ You should see "Success. No rows returned" message

### 3. **Verify Storage Buckets**
- Click on "Storage" in the left sidebar
- You should see 4 buckets:
  - ✅ `sas-package-images` (Public: Yes, Size limit: 10MB)
  - ✅ `sas-blog-images` (Public: Yes, Size limit: 10MB)  
  - ✅ `sas-user-avatars` (Public: Yes, Size limit: 5MB)
  - ✅ `sas-general-uploads` (Public: Yes, Size limit: 50MB)

### 4. **Test Upload (Optional)**
- Go to your admin panel: `/admin/packages/add`
- Try uploading an image in the package form
- Check if it appears in the `sas-package-images` bucket
- ✅ Image should upload successfully and display

## 🔧 Environment Check

Make sure your `.env.local` file has:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🚨 Troubleshooting

**If upload fails:**
1. Check browser console for errors
2. Verify buckets exist in Supabase Storage
3. Ensure policies were created (run verification queries in SQL)
4. Check if you're authenticated in your admin panel

**If images don't display:**
1. Verify buckets are set to "Public: Yes"
2. Check the image URL format
3. Ensure CORS is properly configured

## 📁 What Each Bucket Does

- **`sas-package-images`**: Package photos, hero images ← **Your CRUD uses this**
- **`sas-blog-images`**: Blog post images
- **`sas-user-avatars`**: User profile pictures  
- **`sas-general-uploads`**: PDFs, documents, misc files

## ✨ You're All Set!

After completing these steps, your package CRUD operations will work perfectly:
- ✅ **Delete**: Removes packages from database
- ✅ **Upload**: Saves images to Supabase Storage  
- ✅ **Edit**: Updates package data and images
- ✅ **Create**: Adds new packages with image upload

Your image upload API at `/api/upload/image` is already configured to use these buckets!
