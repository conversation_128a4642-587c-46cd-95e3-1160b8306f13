'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Bold, Italic, Underline, Link, Type } from 'lucide-react';

interface FormattingToolbarProps {
  onFormat: (command: string, value?: string) => void;
  isVisible: boolean;
  position: { x: number; y: number };
}

const FormattingToolbar: React.FC<FormattingToolbarProps> = ({
  onFormat,
  isVisible,
  position
}) => {
  const toolbarRef = useRef<HTMLDivElement>(null);
  const [activeFormats, setActiveFormats] = useState<Set<string>>(new Set());

  // Update active formats based on current selection
  useEffect(() => {
    if (isVisible) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const parentElement = range.commonAncestorContainer.parentElement;
        
        const formats = new Set<string>();
        
        // Check for bold
        if (parentElement?.closest('strong') || parentElement?.closest('b')) {
          formats.add('bold');
        }
        
        // Check for italic
        if (parentElement?.closest('em') || parentElement?.closest('i')) {
          formats.add('italic');
        }
        
        // Check for underline
        if (parentElement?.closest('u')) {
          formats.add('underline');
        }
        
        // Check for link
        if (parentElement?.closest('a')) {
          formats.add('link');
        }
        
        setActiveFormats(formats);
      }
    }
  }, [isVisible, position]);

  const handleFormat = (command: string) => {
    switch (command) {
      case 'bold':
        onFormat('Bold');
        break;
      case 'italic':
        onFormat('Italic');
        break;
      case 'underline':
        onFormat('Underline');
        break;
      case 'link':
        handleLinkInsertion();
        break;
    }
  };

  const handleLinkInsertion = () => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const selectedText = selection.toString();
    const url = prompt('Enter URL:', 'https://');
    
    if (url && url.trim()) {
      if (selectedText) {
        onFormat('insertHTML', `<a href="${url.trim()}">${selectedText}</a>`);
      } else {
        const linkText = prompt('Enter link text:', url.trim());
        if (linkText) {
          onFormat('insertHTML', `<a href="${url.trim()}">${linkText}</a>`);
        }
      }
    }
  };

  const formatButtons = [
    {
      command: 'bold',
      icon: Bold,
      tooltip: 'Bold (Ctrl+B)',
      shortcut: 'Ctrl+B'
    },
    {
      command: 'italic',
      icon: Italic,
      tooltip: 'Italic (Ctrl+I)',
      shortcut: 'Ctrl+I'
    },
    {
      command: 'underline',
      icon: Underline,
      tooltip: 'Underline (Ctrl+U)',
      shortcut: 'Ctrl+U'
    },
    {
      command: 'link',
      icon: Link,
      tooltip: 'Insert Link (Ctrl+K)',
      shortcut: 'Ctrl+K'
    }
  ];

  if (!isVisible) return null;

  return (
    <div
      ref={toolbarRef}
      className="fixed z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-1 flex items-center gap-1"
      style={{
        left: `${position.x}px`,
        top: `${position.y - 50}px`, // Position above the selection
        transform: 'translateX(-50%)', // Center horizontally
      }}
    >
      {formatButtons.map((button) => {
        const IconComponent = button.icon;
        const isActive = activeFormats.has(button.command);
        
        return (
          <button
            key={button.command}
            onClick={() => handleFormat(button.command)}
            className={`p-2 rounded hover:bg-gray-100 transition-colors ${
              isActive 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
            title={button.tooltip}
            type="button"
          >
            <IconComponent size={16} />
          </button>
        );
      })}
      
      {/* Toolbar arrow pointing down */}
      <div 
        className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-200"
        style={{ marginTop: '-1px' }}
      />
    </div>
  );
};

export default FormattingToolbar;
