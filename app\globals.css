@tailwind base;
@tailwind components;
@tailwind utilities;

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Local Jost Font */
@font-face {
  font-family: 'Jost';
  src: url('/font/Jost-VariableFont_wght.woff2') format('woff2'),
       url('/font/Jost-VariableFont_wght.woff') format('woff'),
       url('/font/Jost-VariableFont_wght.otf') format('opentype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

:root {
  --accent: #d35400;
  --background: #dfe3dc;
  --white: #ffffff;
  --btn: #163201;
  --light-green: #317100;
  --text: rgb(7, 7, 7);
  --footer: #212020;
  --hero: #F5F5F5;
  --card-bg: #fffbde;
  --primary-background: #f4f3ee;
  --secondary-background: #e3dbcc; 
}

/*  --secondary-background: #e0d6cce3;  --primary-background: #f5f8f0; v1*/
/*  --primary-background: #f7f6f2; --secondary-background: #ebe7de; v2*/
/*    --primary-background: #f3f2ec; --secondary-background: #e5ded1; v3*/
/* --primary-background: #f8f6f1; --secondary-background: #e9e3d9; v4*/
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--background);
  color: var(--text);
  font-family: 'Jost', sans-serif;
}

/* TinyMCE and Rich Text Editor Styles */
.rich-text-editor .tox-tinymce {
  border: 1px solid #d1d5db !important;
  border-radius: 0.5rem !important;
}

.rich-text-editor .tox-toolbar {
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.rich-text-editor .tox-edit-area {
  padding: 0.5rem !important;
}

/* Prose styles for blog content */
.prose-blog-content {
  @apply prose prose-gray max-w-none;
}

.prose-blog-content h1,
.prose-blog-content h2,
.prose-blog-content h3,
.prose-blog-content h4,
.prose-blog-content h5,
.prose-blog-content h6 {
  @apply font-bold text-gray-900 leading-tight;
}

.prose-blog-content p {
  @apply text-gray-700 leading-relaxed mb-4;
}

.prose-blog-content a {
  @apply text-accent hover:text-accent underline;
  color: var(--accent);
}

.prose-blog-content a:hover {
  color: var(--accent);
  filter: brightness(0.8);
}

.prose-blog-content ul,
.prose-blog-content ol {
  @apply mb-4 pl-6;
}

.prose-blog-content li {
  @apply mb-1;
}

.prose-blog-content blockquote {
  @apply border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4;
}

.prose-blog-content strong {
  @apply font-semibold text-gray-900;
}

.prose-blog-content em {
  @apply italic;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

main {
  flex: 1;
  padding: 0rem;
}

.logo {
  height: 50px;
  width: auto;
}

.btn {
  /* padding: 0.5em 1.5em; */
  margin: 0.5em;
  background: var(--btn);
  color: var(--white);
  border: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.btn:hover {
  color: var(--white);
}

.btn:after {
  content: "";
  background: var(--accent);
  position: absolute;
  z-index: -1;
  left: -20%;
  right: -20%;
  top: 0;
  bottom: 0;
  transform: skewX(-45deg) scale(0, 1);
  transition: all 0.5s;
}

.btn:hover:after {
  transform: skewX(-45deg) scale(1, 1);
  -webkit-transition: all 0.5s;
  transition: all 0.5s;
}


/* community */


.africa-shape {
  position: relative;
  width: 100%;
  height: 300px;
  display: block;
  visibility: visible;
  background-color: #ccc;
  background-size: cover;
  background-position: center;
  -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M495.001,189.882c-0.938-1.114-2.43-1.587-3.833-1.228l-37.986,9.826c-1.562,0.403-3.194-0.228-4.088-1.562l-16.51-24.756c-0.193-0.29-0.421-0.554-0.693-0.773l-15.58-13.247c-0.422-0.359-0.755-0.807-0.992-1.316l-19.458-42.969c-0.088-0.219-0.202-0.42-0.342-0.605L384.122,96.97c-0.027-0.044-0.053-0.079-0.079-0.114l-15.756-24.424c-0.526-0.825-0.729-1.825-0.526-2.79l0.464-2.317c0.22-1.105,0.922-2.053,1.912-2.587c1.001-0.535,2.176-0.596,3.22-0.167l2.413,1c1.351,0.562,2.912,0.281,3.991-0.719c1.071-0.992,1.474-2.518,1.036-3.913l-2.711-8.51c-0.403-1.28-1.456-2.246-2.772-2.535l-26.932-5.992c-0.57-0.122-1.176-0.114-1.737,0.027l-21.44,5.359c-0.632,0.158-1.298,0.149-1.931-0.035l-18.756-5.246c-0.693-0.192-1.324-0.578-1.798-1.123l-4.861-5.474c-0.676-0.755-1.622-1.211-2.631-1.255l-11.641-0.587c-1.931-0.088-3.632,1.289-3.913,3.21l-1.403,9.37c-0.158,1.018-0.711,1.93-1.544,2.518c-0.843,0.588-1.886,0.816-2.886,0.632l-24.73-4.676c-1.351-0.263-2.448-1.22-2.877-2.518l-1.798-5.396c-0.5-1.491-1.878-2.518-3.448-2.57l-19.677-0.676c-1.15-0.035-2.211-0.596-2.895-1.509l-6.378-8.51c-0.641-0.851-0.886-1.939-0.684-2.982c0.201-1.053,0.842-1.957,1.754-2.509l1.675-1.009c1.72-1.027,2.325-3.22,1.403-4.992L209.93,2.004c-0.747-1.404-2.29-2.194-3.869-1.966L132.685,10.4c-0.307,0.035-0.606,0.123-0.895,0.237l-21.432,8.729c-0.386,0.166-0.799,0.254-1.22,0.272L95.4,20.357c-0.921,0.053-1.78,0.43-2.43,1.071L67.441,46.176c-0.578,0.562-0.965,1.289-1.096,2.079l-2.018,12.08c-0.166,1.062-0.789,1.992-1.692,2.571l-22.696,14.37c-0.536,0.333-0.983,0.807-1.281,1.359l-20.625,37.294c-0.342,0.614-0.508,1.298-0.474,2l1.596,34.231c0.009,0.298-0.009,0.588-0.07,0.878l-2.903,15.256c-0.211,1.114,0.096,2.272,0.824,3.15l55.522,65.831c0.922,1.088,2.378,1.561,3.773,1.237l24.59-5.974c0.377-0.088,0.763-0.123,1.158-0.097l34.433,2.404c0.474,0.044,0.956-0.026,1.421-0.166l25.608-8.291c1.219-0.394,2.552-0.14,3.544,0.666l15.519,12.704c0.754,0.605,1.728,0.912,2.702,0.834l14.344-1.202c1.053-0.079,2.088,0.271,2.859,0.991c0.782,0.71,1.229,1.71,1.229,2.772v13.905c0,0.192-0.018,0.377-0.044,0.562l-2.211,14.712c-0.149,1.035,0.131,2.105,0.799,2.921l15.905,19.686c0.334,0.42,0.579,0.912,0.719,1.43l12.818,49.69c0.184,0.692,0.158,1.421-0.061,2.096l-11.694,36.661c-0.28,0.878-0.228,1.834,0.14,2.676l19.976,44.733c0.149,0.333,0.254,0.684,0.298,1.062l3.938,30.774c0.088,0.711,0.395,1.378,0.843,1.931l19.212,23.046c0.562,0.685,0.868,1.526,0.868,2.413v16.791c0,1.079,0.465,2.114,1.29,2.833c0.807,0.72,1.894,1.036,2.974,0.895l33.977-4.421l28.529-3.966c0.895-0.131,1.72-0.57,2.316-1.263l43.312-49.716c0.42-0.49,0.719-1.096,0.842-1.736l2.001-9.957c0.193-0.974,0.772-1.842,1.605-2.395l4.553-3.026c1.018-0.694,1.649-1.825,1.675-3.053l0.747-30.775c0.026-0.868,0.35-1.71,0.921-2.368l9.202-10.616c0.474-0.544,1.079-0.93,1.772-1.14l18.686-5.545c1.606-0.473,2.702-1.938,2.702-3.615v-37.442c0-0.316-0.035-0.624-0.114-0.922l-9.106-35.67c-0.439-1.72,0.377-3.509,1.965-4.308l3.088-1.543c0.562-0.281,1.035-0.685,1.386-1.185l27.125-38.293c0.386-0.552,0.921-0.982,1.526-1.263l21.871-9.799c0.834-0.377,1.509-1.053,1.886-1.886l23.599-52.207C496.142,192.532,495.94,190.988,495.001,189.882z"/></svg>');
  mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M495.001,189.882c-0.938-1.114-2.43-1.587-3.833-1.228l-37.986,9.826c-1.562,0.403-3.194-0.228-4.088-1.562l-16.51-24.756c-0.193-0.29-0.421-0.554-0.693-0.773l-15.58-13.247c-0.422-0.359-0.755-0.807-0.992-1.316l-19.458-42.969c-0.088-0.219-0.202-0.42-0.342-0.605L384.122,96.97c-0.027-0.044-0.053-0.079-0.079-0.114l-15.756-24.424c-0.526-0.825-0.729-1.825-0.526-2.79l0.464-2.317c0.22-1.105,0.922-2.053,1.912-2.587c1.001-0.535,2.176-0.596,3.22-0.167l2.413,1c1.351,0.562,2.912,0.281,3.991-0.719c1.071-0.992,1.474-2.518,1.036-3.913l-2.711-8.51c-0.403-1.28-1.456-2.246-2.772-2.535l-26.932-5.992c-0.57-0.122-1.176-0.114-1.737,0.027l-21.44,5.359c-0.632,0.158-1.298,0.149-1.931-0.035l-18.756-5.246c-0.693-0.192-1.324-0.578-1.798-1.123l-4.861-5.474c-0.676-0.755-1.622-1.211-2.631-1.255l-11.641-0.587c-1.931-0.088-3.632,1.289-3.913,3.21l-1.403,9.37c-0.158,1.018-0.711,1.93-1.544,2.518c-0.843,0.588-1.886,0.816-2.886,0.632l-24.73-4.676c-1.351-0.263-2.448-1.22-2.877-2.518l-1.798-5.396c-0.5-1.491-1.878-2.518-3.448-2.57l-19.677-0.676c-1.15-0.035-2.211-0.596-2.895-1.509l-6.378-8.51c-0.641-0.851-0.886-1.939-0.684-2.982c0.201-1.053,0.842-1.957,1.754-2.509l1.675-1.009c1.72-1.027,2.325-3.22,1.403-4.992L209.93,2.004c-0.747-1.404-2.29-2.194-3.869-1.966L132.685,10.4c-0.307,0.035-0.606,0.123-0.895,0.237l-21.432,8.729c-0.386,0.166-0.799,0.254-1.22,0.272L95.4,20.357c-0.921,0.053-1.78,0.43-2.43,1.071L67.441,46.176c-0.578,0.562-0.965,1.289-1.096,2.079l-2.018,12.08c-0.166,1.062-0.789,1.992-1.692,2.571l-22.696,14.37c-0.536,0.333-0.983,0.807-1.281,1.359l-20.625,37.294c-0.342,0.614-0.508,1.298-0.474,2l1.596,34.231c0.009,0.298-0.009,0.588-0.07,0.878l-2.903,15.256c-0.211,1.114,0.096,2.272,0.824,3.15l55.522,65.831c0.922,1.088,2.378,1.561,3.773,1.237l24.59-5.974c0.377-0.088,0.763-0.123,1.158-0.097l34.433,2.404c0.474,0.044,0.956-0.026,1.421-0.166l25.608-8.291c1.219-0.394,2.552-0.14,3.544,0.666l15.519,12.704c0.754,0.605,1.728,0.912,2.702,0.834l14.344-1.202c1.053-0.079,2.088,0.271,2.859,0.991c0.782,0.71,1.229,1.71,1.229,2.772v13.905c0,0.192-0.018,0.377-0.044,0.562l-2.211,14.712c-0.149,1.035,0.131,2.105,0.799,2.921l15.905,19.686c0.334,0.42,0.579,0.912,0.719,1.43l12.818,49.69c0.184,0.692,0.158,1.421-0.061,2.096l-11.694,36.661c-0.28,0.878-0.228,1.834,0.14,2.676l19.976,44.733c0.149,0.333,0.254,0.684,0.298,1.062l3.938,30.774c0.088,0.711,0.395,1.378,0.843,1.931l19.212,23.046c0.562,0.685,0.868,1.526,0.868,2.413v16.791c0,1.079,0.465,2.114,1.29,2.833c0.807,0.72,1.894,1.036,2.974,0.895l33.977-4.421l28.529-3.966c0.895-0.131,1.72-0.57,2.316-1.263l43.312-49.716c0.42-0.49,0.719-1.096,0.842-1.736l2.001-9.957c0.193-0.974,0.772-1.842,1.605-2.395l4.553-3.026c1.018-0.694,1.649-1.825,1.675-3.053l0.747-30.775c0.026-0.868,0.35-1.71,0.921-2.368l9.202-10.616c0.474-0.544,1.079-0.93,1.772-1.14l18.686-5.545c1.606-0.473,2.702-1.938,2.702-3.615v-37.442c0-0.316-0.035-0.624-0.114-0.922l-9.106-35.67c-0.439-1.72,0.377-3.509,1.965-4.308l3.088-1.543c0.562-0.281,1.035-0.685,1.386-1.185l27.125-38.293c0.386-0.552,0.921-0.982,1.526-1.263l21.871-9.799c0.834-0.377,1.509-1.053,1.886-1.886l23.599-52.207C496.142,192.532,495.94,190.988,495.001,189.882z"/></svg>');
  -webkit-mask-size: contain;
  mask-size: contain;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center;
  mask-position: center;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 12px;
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--light-green);
  border-radius: 8px;
  border: 2px solid var(--background);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

::-webkit-scrollbar-track {
  background: var(--background);
  border-radius: 8px;
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--btn) var(--background);
}

/* Blog Content Formatting Styles */
/* These styles ensure TinyMCE formatted content displays properly on reading pages */

.blog-content {
  @apply text-gray-700 leading-relaxed;
}

/* Typography */
.blog-content p {
  @apply mb-6 text-base leading-relaxed;
  text-align: justify;
  hyphens: auto;
}

.blog-content h1 {
  @apply text-4xl font-bold text-gray-900 mb-6 mt-8 leading-tight;
}

.blog-content h2 {
  @apply text-3xl font-bold text-gray-900 mb-6 mt-8 leading-tight;
}

.blog-content h3 {
  @apply text-2xl font-bold text-gray-900 mb-4 mt-6 leading-tight;
}

.blog-content h4 {
  @apply text-xl font-bold text-gray-900 mb-4 mt-6 leading-tight;
}

.blog-content h5 {
  @apply text-lg font-bold text-gray-900 mb-3 mt-4 leading-tight;
}

.blog-content h6 {
  @apply text-base font-bold text-gray-900 mb-3 mt-4 leading-tight;
}

/* Text formatting */
.blog-content strong,
.blog-content b {
  @apply font-bold text-gray-900;
}

.blog-content em,
.blog-content i {
  @apply italic;
}

.blog-content u {
  @apply underline;
}

.blog-content s,
.blog-content strike {
  @apply line-through;
}

.blog-content sub {
  @apply align-sub text-sm;
}

.blog-content sup {
  @apply align-super text-sm;
}

/* Links */
.blog-content a {
  @apply text-accent underline hover:text-accent transition-colors duration-200;
  color: var(--accent);
}

.blog-content a:hover {
  color: var(--accent);
  filter: brightness(0.8);
}

.blog-content a:visited {
  @apply text-purple-600;
}

/* Lists */
.blog-content ul {
  @apply list-disc list-inside mb-6 space-y-2 pl-4;
}

.blog-content ol {
  @apply list-decimal list-inside mb-6 space-y-2 pl-4;
}

.blog-content li {
  @apply leading-relaxed;
}

.blog-content ul ul,
.blog-content ol ol,
.blog-content ul ol,
.blog-content ol ul {
  @apply mt-2 mb-0 ml-6;
}

/* Blockquotes */
.blog-content blockquote {
  @apply border-l-4 border-accent pl-6 py-4 my-6 bg-gray-50 rounded-r-lg italic text-gray-700;
}

.blog-content blockquote p {
  @apply mb-0;
}

/* Tables */
.blog-content table {
  @apply w-full border-collapse border border-gray-300 my-6 rounded-lg overflow-hidden;
}

.blog-content th {
  @apply bg-gray-100 border border-gray-300 px-4 py-3 text-left font-semibold text-gray-900;
}

.blog-content td {
  @apply border border-gray-300 px-4 py-3;
}

.blog-content tr:nth-child(even) {
  @apply bg-gray-50;
}

/* Images */
.blog-content img {
  @apply max-w-full h-auto rounded-lg my-6 shadow-sm;
}

.blog-content figure {
  @apply my-6;
}

.blog-content figcaption {
  @apply text-sm text-gray-600 text-center mt-2 italic;
}

/* Code */
.blog-content code {
  @apply bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono;
}

.blog-content pre {
  @apply bg-gray-100 text-gray-800 p-4 rounded-lg my-6 overflow-x-auto;
}

.blog-content pre code {
  @apply bg-transparent p-0;
}

/* Horizontal rules */
.blog-content hr {
  @apply border-0 border-t-2 border-gray-300 my-8;
}

/* Text alignment utilities - using direct CSS to avoid circular dependencies */


/* image effect */
img {
  filter: brightness(1) contrast(1.3) saturate(1.1);
  image-rendering: crisp-edges;
}

/* Enhanced Package Hero Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeInDown {
  animation: fadeInDown 0.6s ease-out forwards;
}

/* Enhanced button hover effects */
.hero-cta-button {
  background: var(--accent);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-cta-button:hover {
  background: var(--accent);
  filter: brightness(0.9);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(211, 84, 0, 0.3);
}

/* Info card hover effects */
.hero-info-card {
  transition: all 0.3s ease;
}

.hero-info-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.3);
}