import { NextRequest, NextResponse } from 'next/server'
import { seoMonitor } from '@/lib/seo-monitoring'
import { supabase } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    const action = searchParams.get('action') || 'summary'

    switch (action) {
      case 'summary':
        return await getSEOSummary(days)
      
      case 'crawl-events':
        return await getCrawlEvents(days)
      
      case 'performance-metrics':
        return await getPerformanceMetrics(days)
      
      case 'error-analysis':
        return await getErrorAnalysis(days)
      
      case 'bot-analysis':
        return await getBotAnalysis(days)
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('SEO Dashboard API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getSEOSummary(days: number) {
  try {
    const summary = await seoMonitor.getSEOSummary(days)
    
    // Get additional metrics from database
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
    
    // Get performance metrics summary
    const { data: performanceData } = await supabase
      .from('performance_metrics')
      .select('metric_name, metric_value')
      .gte('timestamp', startDate)
      .in('metric_name', ['LCP', 'FID', 'CLS', 'TTFB'])

    const performanceSummary = performanceData?.reduce((acc, metric) => {
      if (!acc[metric.metric_name]) {
        acc[metric.metric_name] = []
      }
      acc[metric.metric_name].push(parseFloat(metric.metric_value))
      return acc
    }, {} as Record<string, number[]>) || {}

    // Calculate averages
    const performanceAverages = Object.entries(performanceSummary).reduce((acc, [key, values]) => {
      acc[key] = values.reduce((sum, val) => sum + val, 0) / values.length
      return acc
    }, {} as Record<string, number>)

    // Get recent blog posts and packages for indexing status
    const { data: recentContent } = await supabase
      .from('blog_posts')
      .select('slug, title, published_at, updated_at')
      .eq('status', 'published')
      .order('published_at', { ascending: false })
      .limit(10)

    return NextResponse.json({
      period_days: days,
      crawl_summary: summary,
      performance_averages: performanceAverages,
      recent_content: recentContent || [],
      generated_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error getting SEO summary:', error)
    return NextResponse.json({ error: 'Failed to get SEO summary' }, { status: 500 })
  }
}

async function getCrawlEvents(days: number) {
  try {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
    
    const { data: crawlEvents, error } = await supabase
      .from('seo_crawl_events')
      .select('*')
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })
      .limit(1000)

    if (error) {
      throw error
    }

    // Group by hour for trending analysis
    const hourlyData = crawlEvents?.reduce((acc, event) => {
      const hour = new Date(event.timestamp).toISOString().slice(0, 13) + ':00:00.000Z'
      if (!acc[hour]) {
        acc[hour] = { total: 0, search_engine: 0, social: 0, other: 0, errors: 0 }
      }
      acc[hour].total++
      acc[hour][event.bot_type as keyof typeof acc[typeof hour]]++
      if (event.status_code >= 400) {
        acc[hour].errors++
      }
      return acc
    }, {} as Record<string, any>) || {}

    return NextResponse.json({
      events: crawlEvents || [],
      hourly_trends: hourlyData,
      total_events: crawlEvents?.length || 0
    })

  } catch (error) {
    console.error('Error getting crawl events:', error)
    return NextResponse.json({ error: 'Failed to get crawl events' }, { status: 500 })
  }
}

async function getPerformanceMetrics(days: number) {
  try {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
    
    const { data: metrics, error } = await supabase
      .from('performance_metrics')
      .select('*')
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })
      .limit(5000)

    if (error) {
      throw error
    }

    // Group by metric type and calculate statistics
    const metricStats = metrics?.reduce((acc, metric) => {
      const name = metric.metric_name
      if (!acc[name]) {
        acc[name] = { values: [], count: 0, sum: 0 }
      }
      const value = parseFloat(metric.metric_value)
      acc[name].values.push(value)
      acc[name].count++
      acc[name].sum += value
      return acc
    }, {} as Record<string, any>) || {}

    // Calculate percentiles and statistics
    const statistics = Object.entries(metricStats).map(([name, data]: [string, any]) => {
      const sortedValues = data.values.sort((a: number, b: number) => a - b)
      const p50 = sortedValues[Math.floor(sortedValues.length * 0.5)] || 0
      const p75 = sortedValues[Math.floor(sortedValues.length * 0.75)] || 0
      const p95 = sortedValues[Math.floor(sortedValues.length * 0.95)] || 0

      return {
        metric: name,
        count: data.count,
        average: Math.round(data.sum / data.count * 100) / 100,
        median: p50,
        p75,
        p95,
        min: Math.min(...data.values),
        max: Math.max(...data.values)
      }
    })

    return NextResponse.json({
      raw_metrics: metrics || [],
      statistics,
      total_measurements: metrics?.length || 0
    })

  } catch (error) {
    console.error('Error getting performance metrics:', error)
    return NextResponse.json({ error: 'Failed to get performance metrics' }, { status: 500 })
  }
}

async function getErrorAnalysis(days: number) {
  try {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
    
    // Get 404 errors and other SEO-related errors
    const { data: errors, error } = await supabase
      .from('seo_metrics')
      .select('*')
      .eq('metric_type', 'error')
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })

    if (error) {
      throw error
    }

    // Get crawl errors
    const { data: crawlErrors } = await supabase
      .from('seo_crawl_events')
      .select('*')
      .gte('status_code', 400)
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })

    // Analyze error patterns
    const errorPatterns = errors?.reduce((acc, error) => {
      const url = error.url
      if (!acc[url]) {
        acc[url] = { count: 0, first_seen: error.timestamp, last_seen: error.timestamp }
      }
      acc[url].count++
      if (error.timestamp > acc[url].last_seen) {
        acc[url].last_seen = error.timestamp
      }
      return acc
    }, {} as Record<string, any>) || {}

    const topErrors = Object.entries(errorPatterns)
      .sort(([, a], [, b]) => (b as any).count - (a as any).count)
      .slice(0, 20)
      .map(([url, data]) => ({ url, ...data }))

    return NextResponse.json({
      seo_errors: errors || [],
      crawl_errors: crawlErrors || [],
      error_patterns: topErrors,
      total_errors: (errors?.length || 0) + (crawlErrors?.length || 0)
    })

  } catch (error) {
    console.error('Error getting error analysis:', error)
    return NextResponse.json({ error: 'Failed to get error analysis' }, { status: 500 })
  }
}

async function getBotAnalysis(days: number) {
  try {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()
    
    const { data: crawlEvents, error } = await supabase
      .from('seo_crawl_events')
      .select('user_agent, bot_type, url, timestamp, response_time')
      .gte('timestamp', startDate)
      .order('timestamp', { ascending: false })

    if (error) {
      throw error
    }

    // Analyze bot behavior
    const botAnalysis = crawlEvents?.reduce((acc, event) => {
      const botName = extractBotName(event.user_agent)
      if (!acc[botName]) {
        acc[botName] = {
          type: event.bot_type,
          total_requests: 0,
          unique_pages: new Set(),
          avg_response_time: 0,
          response_times: [],
          first_seen: event.timestamp,
          last_seen: event.timestamp
        }
      }
      
      acc[botName].total_requests++
      acc[botName].unique_pages.add(event.url)
      acc[botName].response_times.push(event.response_time)
      
      if (event.timestamp > acc[botName].last_seen) {
        acc[botName].last_seen = event.timestamp
      }
      
      return acc
    }, {} as Record<string, any>) || {}

    // Calculate averages and convert Sets to counts
    const botStats = Object.entries(botAnalysis).map(([botName, data]: [string, any]) => ({
      bot_name: botName,
      type: data.type,
      total_requests: data.total_requests,
      unique_pages: data.unique_pages.size,
      avg_response_time: Math.round(data.response_times.reduce((sum: number, time: number) => sum + time, 0) / data.response_times.length),
      first_seen: data.first_seen,
      last_seen: data.last_seen
    }))

    return NextResponse.json({
      bot_statistics: botStats,
      total_bot_requests: crawlEvents?.length || 0
    })

  } catch (error) {
    console.error('Error getting bot analysis:', error)
    return NextResponse.json({ error: 'Failed to get bot analysis' }, { status: 500 })
  }
}

function extractBotName(userAgent: string): string {
  const botPatterns = [
    { pattern: /googlebot/i, name: 'Googlebot' },
    { pattern: /bingbot/i, name: 'Bingbot' },
    { pattern: /slurp/i, name: 'Yahoo Slurp' },
    { pattern: /duckduckbot/i, name: 'DuckDuckBot' },
    { pattern: /baiduspider/i, name: 'Baidu Spider' },
    { pattern: /yandexbot/i, name: 'YandexBot' },
    { pattern: /facebookexternalhit/i, name: 'Facebook' },
    { pattern: /twitterbot/i, name: 'Twitter' },
    { pattern: /linkedinbot/i, name: 'LinkedIn' }
  ]

  for (const { pattern, name } of botPatterns) {
    if (pattern.test(userAgent)) {
      return name
    }
  }

  return 'Unknown Bot'
}
