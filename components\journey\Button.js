import React from 'react';

const buttonStyles = {
  base: "btn px-8 py-3 text-white font-medium rounded transition-colors duration-300",
  mobile: "md:hidden inline-block mt-6",
  desktop: "hidden md:inline-block"
};

const Button = ({ variant = 'desktop', children, className = "", ...props }) => {
  return (
    <a
      href={'/package'}
      className={`${buttonStyles.base} ${buttonStyles[variant]} ${className}`}
      data-testid="explore-more-button"
      {...props}
    >
      {children}
    </a>
  );
};

export default Button;
