import React from 'react';
import Link from 'next/link';

function SafariHero() {
    return (
        <div className="relative h-56 w-full overflow-hidden">
            {/* Background Image */}
            <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                    backgroundImage: `url('/images/common/safaris-vehicle.webp')`
                }}
            />

            {/* Dark Overlay */}
            <div className="absolute inset-0 bg-black/60"></div>

            {/* Content */}
            <div className="relative z-10 flex items-center justify-center h-full px-4">
                <div className="text-center max-w-4xl">
                    {/* Main Heading */}
                    <h1 className="text-2xl md:text-3xl lg:text-3xl font-light text-white mb-6 leading-tight">
                        With{' '}
                        <span className="text-orange-500 font-medium">
                            Swift Africa Safaris
                        </span>{' '}
                        Find Your Perfect Journey
                    </h1>

                    {/* Call to Action Button */}
                    <Link
                        href="/journey"
                        className="btn text-white font-medium rounded-md transition-colors duration-300 text-base shadow-lg hover:shadow-xl transform hover:scale-105 inline-block"
                        style={{ backgroundColor: '#163201', padding: '0.5rem 1rem' }}
                    >
                        Learn More
                    </Link>
                </div>
            </div>
        </div>
    );
}

export default SafariHero;