-- Secure Admin Database Setup
-- Execute this script in your Supabase SQL Editor

-- =============================================
-- 1. CREATE ADMIN AUDIT LOG TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.sas_admin_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    user_email TEXT,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    ip_address INET,
    user_agent TEXT,
    request_path TEXT,
    request_method TEXT,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_user_id ON public.sas_admin_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action ON public.sas_admin_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_created_at ON public.sas_admin_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_ip_address ON public.sas_admin_audit_log(ip_address);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_success ON public.sas_admin_audit_log(success);

-- =============================================
-- 2. CREATE ADMIN SESSION TRACKING TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.sas_admin_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_token TEXT UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_admin_sessions_user_id ON public.sas_admin_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_token ON public.sas_admin_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires_at ON public.sas_admin_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_active ON public.sas_admin_sessions(is_active);

-- =============================================
-- 3. CREATE FAILED LOGIN ATTEMPTS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.sas_failed_login_attempts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT,
    ip_address INET NOT NULL,
    user_agent TEXT,
    attempt_count INTEGER DEFAULT 1,
    last_attempt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_failed_login_ip ON public.sas_failed_login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_failed_login_email ON public.sas_failed_login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_failed_login_blocked_until ON public.sas_failed_login_attempts(blocked_until);

-- =============================================
-- 4. ENHANCE RLS POLICIES FOR USERS TABLE
-- =============================================

-- Drop existing permissive policies
DROP POLICY IF EXISTS "Authenticated users can view all users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can insert users" ON public.users;
DROP POLICY IF EXISTS "Authenticated users can update all users" ON public.users;

-- Create more restrictive admin-only policies
CREATE POLICY "Admin users can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "Admin users can insert users" ON public.users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "Admin users can update users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "Admin users can delete users" ON public.users
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

-- =============================================
-- 5. RLS POLICIES FOR AUDIT TABLES
-- =============================================

-- Enable RLS on audit tables
ALTER TABLE public.sas_admin_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_admin_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sas_failed_login_attempts ENABLE ROW LEVEL SECURITY;

-- Admin audit log policies
CREATE POLICY "Admin users can view audit logs" ON public.sas_admin_audit_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "System can insert audit logs" ON public.sas_admin_audit_log
    FOR INSERT WITH CHECK (true);

-- Admin sessions policies
CREATE POLICY "Users can view own sessions" ON public.sas_admin_sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admin users can view all sessions" ON public.sas_admin_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "System can manage sessions" ON public.sas_admin_sessions
    FOR ALL USING (true);

-- Failed login attempts policies
CREATE POLICY "Admin users can view failed attempts" ON public.sas_failed_login_attempts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u 
            WHERE u.id = auth.uid() 
            AND u.role = 'admin' 
            AND u.status = 'active'
        )
    );

CREATE POLICY "System can manage failed attempts" ON public.sas_failed_login_attempts
    FOR ALL USING (true);

-- =============================================
-- 6. UTILITY FUNCTIONS
-- =============================================

-- Function to log admin actions
CREATE OR REPLACE FUNCTION public.log_admin_action(
    p_action TEXT,
    p_resource_type TEXT DEFAULT NULL,
    p_resource_id TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_request_path TEXT DEFAULT NULL,
    p_request_method TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT true,
    p_error_message TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
    current_user_email TEXT;
BEGIN
    -- Get current user email
    SELECT email INTO current_user_email FROM auth.users WHERE id = auth.uid();
    
    -- Insert audit log
    INSERT INTO public.sas_admin_audit_log (
        user_id, user_email, action, resource_type, resource_id,
        ip_address, user_agent, request_path, request_method,
        success, error_message, metadata
    ) VALUES (
        auth.uid(), current_user_email, p_action, p_resource_type, p_resource_id,
        p_ip_address, p_user_agent, p_request_path, p_request_method,
        p_success, p_error_message, p_metadata
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired sessions
CREATE OR REPLACE FUNCTION public.cleanup_expired_admin_sessions() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.sas_admin_sessions 
    WHERE expires_at < NOW() OR is_active = false;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is active admin
CREATE OR REPLACE FUNCTION public.is_active_admin(user_id UUID) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id 
        AND role = 'admin' 
        AND status = 'active'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 7. GRANT PERMISSIONS
-- =============================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.sas_admin_audit_log TO authenticated;
GRANT ALL ON public.sas_admin_sessions TO authenticated;
GRANT ALL ON public.sas_failed_login_attempts TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.log_admin_action TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_expired_admin_sessions TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_active_admin TO authenticated;

-- =============================================
-- 8. CREATE CLEANUP JOB (Optional - requires pg_cron extension)
-- =============================================

-- Uncomment the following lines if you have pg_cron extension enabled
-- This will automatically clean up expired sessions daily at 2 AM

/*
SELECT cron.schedule(
    'cleanup-expired-admin-sessions',
    '0 2 * * *',
    'SELECT public.cleanup_expired_admin_sessions();'
);
*/
