# Sample Formatted Blog Content for Testing

Use this content to quickly test all formatting features in the blog editor:

## Basic Information
- **Title**: "Complete Guide to Safari Adventures - Formatting Test"
- **Description**: "A comprehensive test of all formatting features including headings, lists, quotes, and more."
- **Category**: "Travel Guide"
- **Status**: "Published"

## Content Blocks to Add

### 1. Paragraph Block (Rich Text Formatting)
```
Welcome to our **comprehensive safari guide**! This post demonstrates *all formatting capabilities* of our enhanced blog editor. You'll find <u>underlined text</u>, ~~strikethrough examples~~, and even some ^superscript^ and ~subscript~ text.

We can also create links like [Swift Africa Safaris](https://swiftafricasafaris.com) and use different colors for emphasis. This text should be in a different color if the color picker is working properly.
```

### 2. H2 Heading Block
```
Planning Your Perfect Safari Adventure
```

### 3. Paragraph Block (More Formatting)
```
Safari planning requires careful consideration of multiple factors. Here's what you need to know about timing, locations, and preparation for your African adventure.
```

### 4. H3 Heading Block
```
Best Safari Destinations in East Africa
```

### 5. List Block (Bullet List)
```
• Rwanda - Volcanoes National Park for gorilla trekking
• Uganda - Bwindi Impenetrable Forest
• Tanzania - Serengeti National Park
• Kenya - Maasai Mara National Reserve
• South Africa - Kruger National Park
```

### 6. H4 Heading Block
```
What to Pack for Your Safari
```

### 7. List Block (Numbered List)
```
1. Comfortable hiking boots
2. Lightweight, neutral-colored clothing
3. High-quality camera with extra batteries
4. Binoculars for wildlife viewing
5. Sunscreen and insect repellent
6. First aid kit and personal medications
```

### 8. Quote Block
```
Content: "The best time to plant a tree was 20 years ago. The second best time is now. The same applies to planning your dream safari adventure."
Author: African Proverb
Source: Traditional Wisdom
```

### 9. H3 Heading Block
```
Safari Photography Tips
```

### 10. Paragraph Block (Table Test)
Insert a table with the following content:

| Camera Setting | Recommended Value | Purpose |
|----------------|-------------------|---------|
| ISO | 400-800 | Low light performance |
| Aperture | f/5.6-f/8 | Sharp focus |
| Shutter Speed | 1/500s+ | Freeze motion |
| Focus Mode | Continuous AF | Track moving animals |

### 11. H3 Heading Block
```
Conservation and Responsible Tourism
```

### 12. Paragraph Block (Blockquote Test)
```
Conservation is at the heart of every safari experience. When you choose responsible tour operators, you're directly contributing to wildlife protection and local community development.

> "In every walk with nature, one receives far more than they seek. This is especially true in Africa, where every safari supports conservation efforts and local communities."

Your safari adventure becomes a meaningful contribution to preserving these incredible ecosystems for future generations.
```

### 13. H2 Heading Block
```
Booking Your Safari Adventure
```

### 14. Paragraph Block (Final Content)
```
Ready to embark on your African safari adventure? Our experienced team at Swift Africa Safaris specializes in creating **personalized safari experiences** that combine wildlife viewing, cultural immersion, and conservation education.

Contact us today to start planning your dream safari. We offer:
- Customized itineraries
- Expert local guides
- Sustainable tourism practices
- 24/7 support during your trip
```

### 15. Divider Block
(Add a divider to separate sections)

## Testing Instructions

1. **Create New Blog Post**: Go to `/admin/blog/add`
2. **Fill Basic Tab**: Use the title, description, and category above
3. **Add Content Blocks**: In the Content tab, add each block type listed above
4. **Test Formatting**: In each paragraph block, apply the formatting as indicated
5. **Use Preview**: Toggle preview mode to see how content looks
6. **Test Colors**: Try different text and background colors
7. **Test Fonts**: Try different font families and sizes
8. **Test Alignment**: Try different text alignments
9. **Save and Publish**: Save the post and set status to "Published"
10. **View Reading Page**: Navigate to the blog post URL to see final result

## Expected Results

### In Editor:
- All formatting buttons should work
- Preview should show styled content
- Colors and fonts should apply correctly
- Lists should format properly
- Tables should display with borders
- Blockquotes should have blue left border

### On Reading Page:
- All formatting should be preserved
- Headings should have proper hierarchy
- Lists should be properly styled
- Links should be clickable and styled
- Tables should have alternating row colors
- Blockquotes should have background and border
- Content should be responsive on mobile

## Quick Verification Checklist

- [ ] Bold, italic, underline text displays correctly
- [ ] Different font sizes and families work
- [ ] Text and background colors are preserved
- [ ] Headings show proper hierarchy (H2 > H3 > H4)
- [ ] Bullet and numbered lists format correctly
- [ ] Blockquotes have proper styling
- [ ] Links are clickable and styled
- [ ] Tables display with proper formatting
- [ ] Content is responsive on mobile devices
- [ ] Preview mode matches reading page

If all items check out ✅, your formatting tools are working perfectly!
