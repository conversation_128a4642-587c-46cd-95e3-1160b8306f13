// Test the actual newsletter send functionality
const testNewsletterSend = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('Testing Newsletter Send API...\n');
  
  // Test the actual send API
  console.log('Testing newsletter send...');
  try {
    const sendResponse = await fetch(`${baseUrl}/api/newsletter/send`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        subject: 'Test Newsletter from Swift Africa Safaris',
        content: `
          <div style="text-align: center; padding: 20px;">
            <h1 style="color: #163201;">Welcome to Swift Africa Safaris!</h1>
            <p>This is a test newsletter to verify our email system is working correctly.</p>
            <p>Discover the wild beauty of Africa with our expert guides and sustainable tourism practices.</p>
            <div style="margin: 20px 0; padding: 15px; background-color: #f0f8e8; border-left: 4px solid #317100; border-radius: 5px;">
              <p style="margin: 0; color: #317100;"><strong>🦁 Featured Safari Experience</strong></p>
              <p style="margin: 5px 0 0 0;">Experience the Great Migration in the Serengeti with our expert guides.</p>
            </div>
            <p>Thank you for subscribing to our newsletter!</p>
          </div>
        `,
        contentType: 'html'
      })
    });
    
    const sendData = await sendResponse.json();
    console.log('Send Response Status:', sendResponse.status);
    console.log('Send Response:', JSON.stringify(sendData, null, 2));
    
    if (sendData.success) {
      console.log('✅ Newsletter send API is working!');
    } else {
      console.log('❌ Newsletter send failed:', sendData.error);
    }
    
  } catch (error) {
    console.error('❌ Send Error:', error.message);
  }
  
  console.log('\nTest completed!');
};

// Run the test
testNewsletterSend().catch(console.error);
