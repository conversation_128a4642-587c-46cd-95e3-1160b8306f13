/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, DragEvent } from 'react';
import { Upload, Building2, Globe, ImageIcon, Plus, X } from 'lucide-react';

interface Partner {
  id: number;
  companyName: string;
  website: string;
  logo: File | null;
  logoPreview: string | null;
}

interface CurrentPartner {
  companyName: string;
  website: string;
  logo: File | null;
  logoPreview: string | null;
}

const PartnerUploadForm: React.FC = () => {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [currentPartner, setCurrentPartner] = useState<CurrentPartner>({
    companyName: '',
    website: '',
    logo: null,
    logoPreview: null
  });
  const [dragActive, setDragActive] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleInputChange = (field: keyof CurrentPartner, value: string) => {
    setCurrentPartner(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setCurrentPartner(prev => ({
          ...prev,
          logo: file,
          logoPreview: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrag = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleLogoUpload(e.dataTransfer.files[0]);
    }
  };

  const addPartner = () => {
    if (currentPartner.companyName && currentPartner.website && currentPartner.logo) {
      const newPartner: Partner = {
        ...currentPartner,
        id: Date.now()
      };
      setPartners(prev => [...prev, newPartner]);
      setCurrentPartner({
        companyName: '',
        website: '',
        logo: null,
        logoPreview: null
      });
    }
  };

  const removePartner = (id: number) => {
    setPartners(prev => prev.filter(partner => partner.id !== id));
  };

  const submitAllPartners = async () => {
    setIsSubmitting(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    alert('Partners uploaded successfully!');
    console.log('Submitted partners:', partners);
  };

  const isFormValid = currentPartner.companyName && currentPartner.website && currentPartner.logo;

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center">
          <Building2 className="mr-3 text-blue-600" />
          Partner Management
        </h1>
        <p className="text-gray-600">
          Add and manage your business partners. Upload company logos and information.
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Add Partner Form */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <Plus className="mr-2" />
              Add New Partner
            </h2>

            <div className="space-y-4">
              {/* Company Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company Name *
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="text"
                    value={currentPartner.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    placeholder="Enter company name"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Website */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Website URL *
                </label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input
                    type="url"
                    value={currentPartner.website}
                    onChange={(e) => handleInputChange('website', e.target.value)}
                    placeholder="https://example.com"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Logo Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company Logo *
                </label>
                
                <div
                  className={`border-2 border-dashed p-8 text-center transition-all cursor-pointer ${
                    dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                  onClick={() => document.getElementById('logo-input')?.click()}
                >
                  <input
                    id="logo-input"
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handleLogoUpload(e.target.files[0])}
                    className="hidden"
                  />
                  
                  {currentPartner.logoPreview ? (
                    <div className="space-y-4">
                      <img
                        src={currentPartner.logoPreview}
                        alt="Logo preview"
                        className="mx-auto h-24 w-24 object-contain rounded-lg border border-gray-200"
                      />
                      <p className="text-sm text-gray-600">Click to change logo</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
                      <div>
                        <p className="text-lg font-medium text-gray-700">Upload Company Logo</p>
                        <p className="text-sm text-gray-500">Drag and drop or click to select</p>
                        <p className="text-xs text-gray-400 mt-1">PNG, JPG, GIF up to 10MB</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Add Button */}
              <button
                onClick={addPartner}
                disabled={!isFormValid}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  isFormValid
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <Plus className="inline mr-2" size={16} />
                Add Partner
              </button>
            </div>
          </div>
        </div>

        {/* Partners List */}
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Added Partners ({partners.length})
              </h2>
              {partners.length > 0 && (
                <button
                  onClick={submitAllPartners}
                  disabled={isSubmitting}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    isSubmitting
                      ? 'bg-gray-400 text-white cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="inline mr-2" size={16} />
                      Upload All
                    </>
                  )}
                </button>
              )}
            </div>

            {partners.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Building2 className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <p>No partners added yet</p>
                <p className="text-sm">Add your first partner using the form</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {partners.map((partner) => (
                  <div key={partner.id} className="bg-white p-4 rounded-lg border border-gray-200 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {partner.logoPreview && (
                        <img
                          src={partner.logoPreview}
                          alt={`${partner.companyName} logo`}
                          className="h-12 w-12 object-contain rounded border border-gray-200"
                        />
                      )}
                      <div>
                        <h3 className="font-medium text-gray-900">{partner.companyName}</h3>
                        <a
                          href={partner.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          {partner.website}
                        </a>
                      </div>
                    </div>
                    <button
                      onClick={() => removePartner(partner.id)}
                      className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PartnerUploadForm;
