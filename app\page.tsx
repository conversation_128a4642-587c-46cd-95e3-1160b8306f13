// page.tsx
import React from "react";
import type { Metadata } from "next";

import Navbar from "@/components/header";
import WelcomeSection from "@/components/index/welcomeSection";
import HeroIndex from "@/components/index/heroIndex";
import BlogSection from "@/components/index/BlogSection";
import BrandingSection from "@/components/index/brandingSection";
import PackageSection from "@/components/index/packageSection";
import MinPackageSection from "@/components/index/minPackageSection";
import IconicSection from "@/components/index/iconicSection";
import ReviewsSection from "@/components/index/reviewsSection";
import Enquire from "@/components/index/enquire";
import RentSection from "@/components/index/rentSection";
import PartnersSection from "@/components/index/partnersSection";
import TailorSection from "@/components/index/tailorSection";
import Footer from "@/components/footer";
import StructuredData from "@/components/common/StructuredData";
import {
  generateMetadata,
  generateBreadcrumbSchema,
  defaultSEO,
} from "@/lib/seo";

export const metadata: Metadata = generateMetadata({
  title: "Swift Africa Safaris - Wildlife, Eco-Tourism & Adventure Tours",
  description:
    "Discover the charming of Africa with Swift Africa Safaris. Expert guided safari tours across Rwanda, Tanzania, Uganda & South Africa. Gorilla trekking, Big Five safaris, and enchanting wildlife experiences.",
  url: "/",
  keywords: [
    "African safari tours",
    "Rwanda gorilla trekking",
    "Tanzania safari packages",
    "Uganda wildlife tours",
    "South Africa Big Five safari",
    "East Africa safari operator",
    "gorilla permits Rwanda",
    "Serengeti safari tours",
    "Masai Mara safari",
    "African wildlife photography",
    "safari holidays Africa",
    "luxury safari tours",
    "budget safari packages",
    "family safari tours",
    "honeymoon safari Africa",
    "group safari tours",
    "custom safari itinerary",
    "African adventure travel",
    "wildlife conservation tours",
    "cultural safari experiences",
  ],
  type: "website",
});

export default function Home() {
  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: "Home", url: "/" },
  ]);

  const homepageSchema = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    name: "Swift Africa Safaris - Wildlife, Eco-Tourism & Adventure Tours",
    description:
      "Discover the charming of Africa with Swift Africa Safaris. Expert guided safari tours across Rwanda, Tanzania, Uganda & South Africa. Gorilla trekking, Big Five safaris, and enchanting wildlife experiences.",
    url: defaultSEO.siteUrl,
    mainEntity: {
      "@type": "TourAgency",
      name: defaultSEO.siteName,
      description:
        "Discover the charming of Africa with Swift Africa Safaris. Expert guided safari tours across Rwanda, Tanzania, Uganda & South Africa. Gorilla trekking, Big Five safaris, and enchanting wildlife experiences.",
      serviceType: [
        "Safari Tours",
        "Gorilla Trekking",
        "Wildlife Photography",
        "Cultural Tours",
      ],
      areaServed: ["Rwanda", "Tanzania", "Uganda", "South Africa", "Kenya"],
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Safari Tour Packages",
        itemListElement: [
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "TouristTrip",
              name: "Rwanda Gorilla Trekking Safari",
              description:
                "Experience mountain gorillas in their natural habitat",
            },
          },
          {
            "@type": "Offer",
            itemOffered: {
              "@type": "TouristTrip",
              name: "Tanzania Serengeti Safari",
              description: "Witness the Great Migration and Big Five wildlife",
            },
          },
        ],
      },
    },
  };

  return (
    <div className="">
      <StructuredData data={[breadcrumbSchema, homepageSchema]} />
      <Navbar />
      <main className="bg-[var(--background)]">
        <HeroIndex />
        <WelcomeSection />
        <BlogSection />
        <BrandingSection />
        <PackageSection />
        <TailorSection />
        <MinPackageSection />
        <IconicSection />
        <RentSection />
        <ReviewsSection />
        <PartnersSection />
        <Enquire />
      </main>
      <Footer />
    </div>
  );
}
