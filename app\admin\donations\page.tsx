'use client';

import React, { useState, useEffect } from 'react';
import { DollarSign, Plus, Search, Download, Eye, Edit, Trash2, Calendar, Mail, Phone } from 'lucide-react';

interface Donation {
  id: number;
  donorName: string;
  donorEmail: string;
  donorPhone: string;
  amount: number;
  purpose: string;
  status: 'completed' | 'pending' | 'failed';
  paymentMethod: string;
  date: string;
  receiptUrl?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function DonationsPage() {
  const [donations, setDonations] = useState<Donation[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Mock data - replace with actual API calls
  useEffect(() => {
    const mockDonations: Donation[] = [
      {
        id: 1,
        donorName: 'John Smith',
        donorEmail: '<EMAIL>',
        donorPhone: '******-0123',
        amount: 500,
        purpose: 'Wildlife Conservation',
        status: 'completed',
        paymentMethod: 'Credit Card',
        date: '2024-12-15',
        receiptUrl: '/receipts/receipt-001.pdf'
      },
      {
        id: 2,
        donorName: 'Sarah Johnson',
        donorEmail: '<EMAIL>',
        donorPhone: '******-0456',
        amount: 250,
        purpose: 'Community Development',
        status: 'completed',
        paymentMethod: 'PayPal',
        date: '2024-12-14',
        receiptUrl: '/receipts/receipt-002.pdf'
      },
      {
        id: 3,
        donorName: 'Michael Brown',
        donorEmail: '<EMAIL>',
        donorPhone: '******-0789',
        amount: 1000,
        purpose: 'Education Support',
        status: 'pending',
        paymentMethod: 'Bank Transfer',
        date: '2024-12-13'
      }
    ];
    setDonations(mockDonations);
  }, []);

  const filteredDonations = donations.filter(donation => {
    const matchesSearch = donation.donorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         donation.donorEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         donation.purpose.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || donation.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredDonations.length / itemsPerPage);
  const currentDonations = filteredDonations.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const totalDonations = donations.reduce((sum, donation) => 
    donation.status === 'completed' ? sum + donation.amount : sum, 0
  );

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <DollarSign className="mr-3 text-green-600" />
                Donations Management
              </h1>
              <p className="text-gray-600 mt-1">Track and manage all donations received</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="text-sm text-gray-600">Total Raised</div>
                <div className="text-2xl font-bold text-green-600">${totalDonations.toLocaleString()}</div>
              </div>
              <button className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center">
                <Plus size={16} className="mr-2" />
                Add Donation
              </button>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="p-6 border-b border-gray-300 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search donations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-80 text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
              >
                <option value="all">All Status</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
              </select>
            </div>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <Download size={16} className="mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Donations Table */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Donor</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Amount</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Purpose</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Status</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Date</th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-sm">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentDonations.map((donation) => (
                  <tr key={donation.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div>
                        <div className="font-medium text-gray-900">{donation.donorName}</div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail size={12} className="mr-1" />
                          {donation.donorEmail}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone size={12} className="mr-1" />
                          {donation.donorPhone}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-semibold text-green-600">${donation.amount.toLocaleString()}</div>
                      <div className="text-sm text-gray-500">{donation.paymentMethod}</div>
                    </td>
                    <td className="py-4 px-4">
                      <span className="text-gray-900">{donation.purpose}</span>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getStatusColor(donation.status)}`}>
                        {donation.status}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center text-gray-600">
                        <Calendar size={12} className="mr-1" />
                        {new Date(donation.date).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-2">
                        <button className="p-1 text-blue-600 hover:bg-blue-50 rounded">
                          <Eye size={16} />
                        </button>
                        <button className="p-1 text-green-600 hover:bg-green-50 rounded">
                          <Edit size={16} />
                        </button>
                        {donation.receiptUrl && (
                          <button className="p-1 text-purple-600 hover:bg-purple-50 rounded">
                            <Download size={16} />
                          </button>
                        )}
                        <button className="p-1 text-red-600 hover:bg-red-50 rounded">
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredDonations.length)} of {filteredDonations.length} donations
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Previous
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-3 py-1 border rounded-md ${
                      currentPage === page
                        ? 'bg-[var(--accent)] text-white border-[var(--accent)]'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </main>
  );
}
