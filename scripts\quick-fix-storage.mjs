import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function quickFixStorage() {
  console.log('⚡ Quick Fix for Storage Bucket Issue\n');

  try {
    // Check if bucket exists
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('❌ Cannot access storage:', error.message);
      console.log('\n🔧 Solution: Run this SQL in your Supabase dashboard:');
      console.log(`
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('package-images', 'package-images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;
      `);
      return;
    }

    const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
    
    if (packageImagesBucket) {
      console.log('✅ package-images bucket exists!');
      console.log('🎉 Your storage is working correctly.');
      console.log('📤 You can now upload images in your admin dashboard.');
    } else {
      console.log('❌ package-images bucket not found');
      console.log('\n🔧 Quick Fix: Copy and paste this SQL into your Supabase SQL Editor:');
      console.log(`
-- Quick fix for package-images bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('package-images', 'package-images', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;

-- Enable public read access
CREATE POLICY IF NOT EXISTS "Public read access for package-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'package-images');

-- Enable authenticated uploads
CREATE POLICY IF NOT EXISTS "Authenticated users can upload to package-images"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'package-images' AND auth.role() = 'authenticated');
      `);
      console.log('\n📝 Steps:');
      console.log('   1. Go to your Supabase dashboard');
      console.log('   2. Click SQL Editor');
      console.log('   3. Paste the SQL above');
      console.log('   4. Click Run');
      console.log('   5. Try uploading images again');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

quickFixStorage().catch(console.error); 