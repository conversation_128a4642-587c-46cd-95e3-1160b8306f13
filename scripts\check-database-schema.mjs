#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...\n');
  
  try {
    // Get a sample blog post to see what columns exist
    const { data: samplePost, error } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .limit(1)
      .single();
    
    if (error) {
      console.error('❌ Error fetching sample post:', error);
      return;
    }
    
    if (samplePost) {
      console.log('✅ Available columns in sas_blog_posts:');
      Object.keys(samplePost).forEach((column, index) => {
        console.log(`   ${index + 1}. ${column}`);
      });
    }
    
    console.log('\n🔍 Testing simplified query...\n');
    
    // Test with only basic columns
    const { data: testPost, error: testError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status, published_at, description, hero_image_url, category, view_count')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .eq('status', 'published')
      .is('deleted_at', null)
      .single();
    
    if (testError) {
      console.log(`❌ Test query error: ${testError.message}`);
    } else if (testPost) {
      console.log(`✅ Test query successful: ${testPost.title}`);
      console.log(`   ID: ${testPost.id}`);
      console.log(`   Slug: ${testPost.slug}`);
      console.log(`   Category: ${testPost.category}`);
      console.log(`   View Count: ${testPost.view_count}`);
    }
    
  } catch (error) {
    console.error('❌ Error during schema check:', error);
  }
}

checkDatabaseSchema().catch(console.error);
