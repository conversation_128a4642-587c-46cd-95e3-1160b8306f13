# Blog System Implementation Documentation

## Overview
This document provides a comprehensive overview of the blog system implementation for Swift Africa Safaris, including architecture, components, API endpoints, and integration details.

## Table of Contents
1. [System Architecture](#system-architecture)
2. [Database Schema](#database-schema)
3. [API Endpoints](#api-endpoints)
4. [Frontend Components](#frontend-components)
5. [Admin Interface](#admin-interface)
6. [Real-time Features](#real-time-features)
7. [Security Implementation](#security-implementation)
8. [Performance Optimizations](#performance-optimizations)
9. [Deployment Guide](#deployment-guide)

## System Architecture

### Technology Stack
- **Frontend**: Next.js 14 with App Router, React 18, TypeScript
- **Backend**: Next.js API Routes, Supabase PostgreSQL
- **Database**: Supabase PostgreSQL with Row Level Security
- **Storage**: Supabase Storage for images
- **Real-time**: Supabase Realtime subscriptions
- **Styling**: Tailwind CSS with custom CSS variables
- **Authentication**: Supabase Auth (for admin)

### Project Structure
```
├── app/
│   ├── blog/                    # Public blog pages
│   │   ├── page.tsx            # Blog listing
│   │   └── [slug]/page.tsx     # Individual blog post
│   ├── admin/
│   │   ├── blog/               # Blog management
│   │   └── comments/           # Comment management
│   └── api/
│       ├── blog/               # Public blog API
│       └── admin/              # Admin API routes
├── components/
│   ├── admin/blog/             # Admin blog components
│   ├── readblog/               # Public blog components
│   └── ui/                     # Shared UI components
├── hooks/                      # Custom React hooks
├── lib/                        # Utility libraries
└── docs/                       # Documentation
```

## Database Schema

### Blog Posts Table (`sas_blog_posts`)
```sql
CREATE TABLE sas_blog_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    content JSONB, -- Content blocks
    category TEXT,
    tags TEXT[],
    status TEXT CHECK (status IN ('draft', 'published', 'archived')) DEFAULT 'draft',
    hero_image_url TEXT,
    hero_image_alt TEXT,
    view_count INTEGER DEFAULT 0,
    
    -- SEO fields
    seo_title TEXT,
    seo_description TEXT,
    seo_keywords TEXT[],
    og_title TEXT,
    og_description TEXT,
    og_image_url TEXT,
    canonical_url TEXT,
    robots_index TEXT DEFAULT 'index',
    robots_follow TEXT DEFAULT 'follow',
    schema_data JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    published_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ
);
```

### Comments Table (`sas_blog_comments`)
```sql
CREATE TABLE sas_blog_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    blog_post_id UUID REFERENCES sas_blog_posts(id) ON DELETE CASCADE,
    parent_comment_id UUID REFERENCES sas_blog_comments(id) ON DELETE CASCADE,
    author_name TEXT NOT NULL,
    author_email TEXT NOT NULL,
    content TEXT NOT NULL,
    is_admin_reply BOOLEAN DEFAULT FALSE,
    status TEXT CHECK (status IN ('approved', 'pending', 'rejected')) DEFAULT 'pending',
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Content Blocks Structure
Content blocks are stored as JSONB in the `content` field:
```json
[
  {
    "id": "block-1",
    "block_type": "paragraph",
    "content": { "content": "Text content here" },
    "sort_order": 0
  },
  {
    "id": "block-2",
    "block_type": "image",
    "content": {
      "src": "image-url",
      "alt": "Alt text",
      "caption": "Image caption"
    },
    "sort_order": 1
  }
]
```

## API Endpoints

### Public Blog API

#### GET /api/blog
Fetch published blog posts with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `search` (string): Search term
- `category` (string): Filter by category
- `tag` (string): Filter by tag

**Response:**
```json
{
  "success": true,
  "data": {
    "posts": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalPosts": 50,
      "hasNextPage": true,
      "hasPrevPage": false
    }
  }
}
```

#### GET /api/blog/[slug]
Fetch single blog post by slug.

**Response:**
```json
{
  "success": true,
  "data": {
    "post": {
      "id": "uuid",
      "title": "Blog Title",
      "content": [...], // Content blocks
      "author": "Swift Africa Safaris",
      "created_at": "2024-01-01T00:00:00Z",
      // ... other fields
    }
  }
}
```

#### GET /api/blog/[slug]/comments
Fetch approved comments for a blog post.

#### POST /api/blog/[slug]/comments
Submit a new comment.

**Request Body:**
```json
{
  "author_name": "John Doe",
  "author_email": "<EMAIL>",
  "content": "Comment content",
  "parent_comment_id": "uuid" // Optional for replies
}
```

### Admin Blog API

#### GET /api/admin/blog
Fetch all blog posts (admin view) with status filtering.

#### POST /api/admin/blog
Create new blog post.

#### PUT /api/admin/blog/[slug]
Update existing blog post.

#### DELETE /api/admin/blog/[slug]
Soft delete blog post.

### Admin Comments API

#### GET /api/admin/comments
Fetch all comments with filtering and pagination.

#### POST /api/admin/comments
Create admin reply.

#### DELETE /api/admin/comments/[id]
Delete comment.

## Frontend Components

### Public Components

#### BlogCard (`components/blog/BlogCard.tsx`)
Displays blog post preview in listing pages.

#### BlogContent (`components/readblog/BlogContent.tsx`)
Renders blog post content with content blocks.

#### CommentSection (`components/readblog/comment.jsx`)
Handles comment display and submission with real-time updates.

### Admin Components

#### BlogContentEditor (`components/admin/blog/blogContentEditor.tsx`)
Rich content block editor for creating/editing blog posts.

#### BlogSEO (`components/admin/blog/blogSeo.tsx`)
SEO metadata management interface.

#### BlogImageUpload (`components/admin/blog/blogImageUpload.tsx`)
Image upload component with alt text management.

## Admin Interface

### Blog Management Dashboard
- **Location**: `app/admin/blog/page.tsx`
- **Features**: List, search, filter, bulk actions
- **Permissions**: Admin only

### Blog Creation/Editing
- **Location**: `app/admin/blog/add/page.tsx`, `app/admin/blog/edit/[slug]/page.tsx`
- **Features**: Step-based form, content editor, SEO management
- **Auto-save**: Content saved automatically every 30 seconds

### Comment Management
- **Location**: `app/admin/comments/page.tsx`
- **Features**: View all comments, admin replies, moderation
- **Real-time**: Live updates for new comments

## Real-time Features

### Comment Updates
Uses Supabase Realtime subscriptions to provide live comment updates:

```typescript
// Hook: useRealtimeComments
const subscription = supabase
  .channel(`blog-comments-${postId}`)
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'sas_blog_comments',
    filter: `blog_post_id=eq.${postId}`
  }, handleCommentChange)
  .subscribe();
```

### Admin Notifications
Real-time notifications for new comments using `useAdminNotifications` hook:
- Browser notifications (if permitted)
- In-app notification dropdown
- Unread count badge

## Security Implementation

### Authentication
- Admin routes protected with Supabase Auth
- Row Level Security (RLS) policies on database tables
- JWT token validation on API routes

### Input Validation
- Server-side validation on all API endpoints
- XSS prevention in comment content
- File upload restrictions (type, size)
- Rate limiting on comment submission

### Data Protection
- Sensitive data not exposed in public APIs
- Parameterized database queries
- CSRF protection enabled
- Secure headers configured

## Performance Optimizations

### Database
- Proper indexing on frequently queried columns
- Pagination to limit result sets
- Connection pooling configured
- Query optimization for complex joins

### Frontend
- Image lazy loading and optimization
- Code splitting for admin components
- Static generation for blog listing
- Incremental Static Regeneration (ISR) for blog posts

### Caching
- API response caching with appropriate TTL
- Static asset caching with CDN
- Browser caching headers optimized

## Deployment Guide

### Prerequisites
1. Supabase project with database and storage configured
2. Environment variables set up
3. Domain and SSL certificate configured

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
NEXT_PUBLIC_SITE_URL=https://your-domain.com
```

### Database Setup
1. Run database migrations from `scripts/setup-blog-database.sql`
2. Configure Row Level Security policies
3. Set up storage buckets with proper permissions

### Deployment Steps
1. Build the application: `npm run build`
2. Deploy to hosting platform (Vercel recommended)
3. Configure environment variables
4. Run database migrations
5. Test all functionality in production

### Post-deployment Checklist
- [ ] All pages load correctly
- [ ] Database connections stable
- [ ] Real-time features working
- [ ] Image uploads functional
- [ ] SEO metadata rendering
- [ ] Admin authentication working
- [ ] Comment system operational

## Monitoring and Maintenance

### Performance Monitoring
- Page load times tracking
- Database query performance
- Error rate monitoring
- User engagement metrics

### Regular Maintenance
- Database cleanup of soft-deleted records
- Image storage optimization
- Security updates
- Performance optimization reviews

### Backup Strategy
- Automated daily database backups
- Image storage backups
- Configuration backups
- Disaster recovery procedures

---

For technical support or questions about this implementation, refer to the development team or check the project repository documentation.
