/**
 * Comprehensive Hreflang Utilities for International SEO
 * Handles region detection, content localization, and geographic targeting
 */

import { NextRequest } from 'next/server'
import { 
  HreflangRegion, 
  HreflangConfig, 
  hreflangConfigs, 
  detectUserRegion, 
  formatPriceForRegion,
  getRegionsByArea 
} from './seo'

export interface RegionalContent {
  region: HreflangRegion
  currency: string
  currencySymbol: string
  language: string
  country: string
  timeZone: string
  dateFormat: string
  phoneFormat: string
  addressFormat: string
}

export interface LocalizedPackage {
  title: string
  description: string
  price: string
  currency: string
  features: string[]
  testimonials?: LocalizedTestimonial[]
}

export interface LocalizedTestimonial {
  name: string
  location: string
  text: string
  rating: number
}

/**
 * Enhanced region detection with multiple fallback methods
 */
export function detectRegionFromRequest(request: NextRequest): {
  region: HreflangRegion
  confidence: 'high' | 'medium' | 'low'
  method: string
} {
  const acceptLanguage = request.headers.get('accept-language')
  const cfCountry = request.headers.get('cf-ipcountry') // Cloudflare country header
  const xForwardedFor = request.headers.get('x-forwarded-for')
  const userAgent = request.headers.get('user-agent')

  // Method 1: Cloudflare country header (highest confidence)
  if (cfCountry) {
    const regionByCountry = Object.values(hreflangConfigs).find(config => 
      config.code.toLowerCase().endsWith(`-${cfCountry.toLowerCase()}`)
    )
    if (regionByCountry) {
      return {
        region: regionByCountry.code as HreflangRegion,
        confidence: 'high',
        method: 'cloudflare-country'
      }
    }
  }

  // Method 2: Accept-Language header (medium confidence)
  if (acceptLanguage) {
    const region = detectUserRegion(acceptLanguage)
    if (region !== 'x-default') {
      return {
        region,
        confidence: 'medium',
        method: 'accept-language'
      }
    }
  }

  // Method 3: User-Agent analysis for mobile carriers (low confidence)
  if (userAgent) {
    const mobileCarriers = {
      'Safaricom': 'en-KE', // Kenya
      'MTN': 'en-ZA', // South Africa
      'Vodacom': 'en-ZA', // South Africa
      'Airtel': 'en-NG', // Nigeria
      'Orange': 'en-RW' // Rwanda
    }

    for (const [carrier, region] of Object.entries(mobileCarriers)) {
      if (userAgent.includes(carrier)) {
        return {
          region: region as HreflangRegion,
          confidence: 'low',
          method: 'mobile-carrier'
        }
      }
    }
  }

  // Default fallback
  return {
    region: 'x-default',
    confidence: 'low',
    method: 'default'
  }
}

/**
 * Get regional content configuration
 */
export function getRegionalContent(region: HreflangRegion): RegionalContent {
  const config = hreflangConfigs[region] || hreflangConfigs['x-default']
  
  // Regional timezone mapping
  const timezones: Record<string, string> = {
    'en-US': 'America/New_York',
    'en-GB': 'Europe/London',
    'en-CA': 'America/Toronto',
    'en-AU': 'Australia/Sydney',
    'en-ZA': 'Africa/Johannesburg',
    'en-KE': 'Africa/Nairobi',
    'en-RW': 'Africa/Kigali',
    'en-NG': 'Africa/Lagos',
    'en-IE': 'Europe/Dublin',
    'en-NZ': 'Pacific/Auckland',
    'en-DE': 'Europe/Berlin',
    'en-NL': 'Europe/Amsterdam',
    'en-CH': 'Europe/Zurich'
  }

  // Regional date formats
  const dateFormats: Record<string, string> = {
    'en-US': 'MM/DD/YYYY',
    'en-GB': 'DD/MM/YYYY',
    'en-CA': 'DD/MM/YYYY',
    'en-AU': 'DD/MM/YYYY',
    'en-ZA': 'DD/MM/YYYY',
    'en-KE': 'DD/MM/YYYY',
    'en-RW': 'DD/MM/YYYY',
    'en-NG': 'DD/MM/YYYY',
    'en-IE': 'DD/MM/YYYY',
    'en-NZ': 'DD/MM/YYYY',
    'en-DE': 'DD.MM.YYYY',
    'en-NL': 'DD-MM-YYYY',
    'en-CH': 'DD.MM.YYYY'
  }

  // Regional phone formats
  const phoneFormats: Record<string, string> = {
    'en-US': '+1 (XXX) XXX-XXXX',
    'en-GB': '+44 XXXX XXX XXX',
    'en-CA': '+1 (XXX) XXX-XXXX',
    'en-AU': '+61 X XXXX XXXX',
    'en-ZA': '+27 XX XXX XXXX',
    'en-KE': '+254 XXX XXX XXX',
    'en-RW': '+250 XXX XXX XXX',
    'en-NG': '+234 XXX XXX XXXX',
    'en-IE': '+353 XX XXX XXXX',
    'en-NZ': '+64 X XXX XXXX',
    'en-DE': '+49 XXX XXXXXXX',
    'en-NL': '+31 X XXXX XXXX',
    'en-CH': '+41 XX XXX XX XX'
  }

  return {
    region,
    currency: config.currency,
    currencySymbol: config.currencySymbol,
    language: config.language,
    country: config.country,
    timeZone: timezones[region] || 'UTC',
    dateFormat: dateFormats[region] || 'DD/MM/YYYY',
    phoneFormat: phoneFormats[region] || '+XXX XXX XXX XXX',
    addressFormat: getAddressFormat(region)
  }
}

/**
 * Get address format for region
 */
function getAddressFormat(region: HreflangRegion): string {
  const formats: Record<string, string> = {
    'en-US': 'Street Address\nCity, State ZIP',
    'en-GB': 'Street Address\nCity, Postcode',
    'en-CA': 'Street Address\nCity, Province Postal Code',
    'en-AU': 'Street Address\nSuburb State Postcode',
    'en-ZA': 'Street Address\nCity, Postal Code',
    'en-KE': 'Street Address\nCity, Postal Code',
    'en-RW': 'Street Address\nCity',
    'en-NG': 'Street Address\nCity, State',
    'en-IE': 'Street Address\nCity, Eircode',
    'en-NZ': 'Street Address\nSuburb, City Postcode',
    'en-DE': 'Street Address\nPostal Code City',
    'en-NL': 'Street Address\nPostal Code City',
    'en-CH': 'Street Address\nPostal Code City'
  }
  
  return formats[region] || 'Street Address\nCity, Country'
}

/**
 * Localize package content for specific region
 */
export function localizePackage(
  packageData: any, 
  region: HreflangRegion
): LocalizedPackage {
  const config = hreflangConfigs[region] || hreflangConfigs['x-default']
  
  // Regional content variations
  const regionalVariations: Record<string, Partial<LocalizedPackage>> = {
    'en-US': {
      features: packageData.features?.map((f: string) => 
        f.replace('colour', 'color').replace('organised', 'organized')
      )
    },
    'en-ZA': {
      title: packageData.title + ' - Experience Africa from Home',
      description: packageData.description + ' Perfect for South African adventurers.'
    },
    'en-KE': {
      title: packageData.title + ' - Discover East Africa',
      description: packageData.description + ' Explore the wonders of East Africa.'
    },
    'en-RW': {
      title: packageData.title + ' - Rwanda Safari Experience',
      description: packageData.description + ' Experience Rwanda\'s incredible wildlife.'
    },
    'en-GB': {
      title: packageData.title + ' - British Safari Adventure',
      description: packageData.description + ' Tailored for British travellers.'
    },
    'en-AU': {
      title: packageData.title + ' - Aussie African Adventure',
      description: packageData.description + ' Perfect for Australian adventurers.'
    }
  }

  const variation = regionalVariations[region] || {}
  
  return {
    title: variation.title || packageData.title,
    description: variation.description || packageData.description,
    price: formatPriceForRegion(packageData.price || 0, region),
    currency: config.currency,
    features: variation.features || packageData.features || [],
    testimonials: getRegionalTestimonials(region)
  }
}

/**
 * Get testimonials relevant to specific region
 */
function getRegionalTestimonials(region: HreflangRegion): LocalizedTestimonial[] {
  const testimonials: Record<string, LocalizedTestimonial[]> = {
    'en-US': [
      {
        name: 'Sarah Johnson',
        location: 'New York, USA',
        text: 'An incredible African safari experience! The team made everything seamless.',
        rating: 5
      }
    ],
    'en-GB': [
      {
        name: 'James Wilson',
        location: 'London, UK',
        text: 'Brilliant safari tour! Exceeded all our expectations. Highly recommended.',
        rating: 5
      }
    ],
    'en-ZA': [
      {
        name: 'Thabo Mthembu',
        location: 'Cape Town, South Africa',
        text: 'As a local, I was impressed by their knowledge and professionalism.',
        rating: 5
      }
    ],
    'en-KE': [
      {
        name: 'Grace Wanjiku',
        location: 'Nairobi, Kenya',
        text: 'Fantastic experience exploring East African wildlife. Well organized!',
        rating: 5
      }
    ],
    'en-AU': [
      {
        name: 'Michael Thompson',
        location: 'Sydney, Australia',
        text: 'Amazing African adventure! Worth every dollar. The wildlife was incredible.',
        rating: 5
      }
    ]
  }

  return testimonials[region] || testimonials['x-default'] || []
}

/**
 * Generate regional contact information
 */
export function getRegionalContact(region: HreflangRegion): {
  phone: string
  email: string
  address: string
  workingHours: string
} {
  const regionalContacts: Record<string, any> = {
    'en-US': {
      phone: '+****************',
      email: '<EMAIL>',
      address: 'New York Office\n123 Safari Street\nNew York, NY 10001',
      workingHours: '9:00 AM - 6:00 PM EST'
    },
    'en-GB': {
      phone: '+44 20 7123 4567',
      email: '<EMAIL>',
      address: 'London Office\n123 Safari Lane\nLondon SW1A 1AA',
      workingHours: '9:00 AM - 6:00 PM GMT'
    },
    'en-ZA': {
      phone: '+27 11 123 4567',
      email: '<EMAIL>',
      address: 'Johannesburg Office\n123 Safari Road\nSandton, 2196',
      workingHours: '8:00 AM - 5:00 PM SAST'
    },
    'en-KE': {
      phone: '+254 20 123 4567',
      email: '<EMAIL>',
      address: 'Nairobi Office\n123 Safari Avenue\nNairobi, Kenya',
      workingHours: '8:00 AM - 6:00 PM EAT'
    },
    'x-default': {
      phone: '+250 788 123 456',
      email: '<EMAIL>',
      address: 'Kigali Office\nKigali, Rwanda',
      workingHours: '8:00 AM - 6:00 PM CAT'
    }
  }

  return regionalContacts[region] || regionalContacts['x-default']
}

/**
 * Check if region should show specific content
 */
export function shouldShowRegionalContent(
  region: HreflangRegion, 
  contentType: 'pricing' | 'testimonials' | 'contact' | 'features'
): boolean {
  const config = hreflangConfigs[region]
  if (!config) return false

  // Show regional content for priority markets
  if (config.priority >= 7) return true

  // Show specific content types for certain regions
  const contentRules: Record<string, string[]> = {
    'pricing': ['en-US', 'en-GB', 'en-CA', 'en-AU', 'en-ZA'],
    'testimonials': ['en-US', 'en-GB', 'en-ZA', 'en-KE', 'en-AU'],
    'contact': ['en-US', 'en-GB', 'en-ZA', 'en-KE'],
    'features': ['en-US', 'en-GB', 'en-AU']
  }

  return contentRules[contentType]?.includes(region) || false
}

/**
 * Generate hreflang HTTP headers for middleware
 */
export function generateHreflangHeaders(currentPath: string): Record<string, string> {
  const headers: Record<string, string> = {}
  
  // Get priority regions for headers (to avoid too many headers)
  const priorityRegions = Object.keys(hreflangConfigs).filter(region => 
    hreflangConfigs[region as HreflangRegion]?.priority >= 7
  )

  const links = priorityRegions.map(region => {
    const url = `https://swiftafricasafaris.com${currentPath}`
    return `<${url}>; rel="alternate"; hreflang="${region}"`
  })

  if (links.length > 0) {
    headers['Link'] = links.join(', ')
  }

  return headers
}
