/* eslint-disable react/no-unescaped-entities */
/* eslint-disable @next/next/no-img-element */
'use client'
import { useState, useEffect } from 'react';
import Navbar from '@/components/header';
import { MapPin, Users, Calendar, Heart, ExternalLink } from 'lucide-react';
import Footer from '@/components/footer';
import VolunteeringForm from '@/components/common/volunteeringOpportunities';

const HiveGuardianClient = () => {
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    if (showModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to reset overflow when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showModal]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      {/* Hero Section */}
      <section className="relative bg-[var(--btn)] text-white py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Eco - Hive Guardians
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Empowering Rwandan communities through sustainable beekeeping and agricultural cooperatives
            </p>
            <div className="flex items-center justify-center gap-4 text-sm">
              <span className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Nyamasheke District, Rwanda
              </span>
              <span className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Local Cooperatives
              </span>
              <span className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Since June 2025
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-16">
        {/* Initiative Overview */}
        <section className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <p className="text-[var(--accent)] font-medium text-sm mb-4 text-center-1/3">Overview</p>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Eco-Hive Guardian Initiative
              </h2>
              <p className="text-lg text-gray-700 mb-6">
                According to conservation activist Alex Roessner the Founder of <a href='https://www.rrinitiative.org/projects/rwanda-agricultural-cooperatives' className="underline underline-offset-4 decoration-[var(--btn)] hover:decoration-2 transition-all duration-200">Roessner Restoration Initiative</a>: The Rwanda initiative began not just with an idea, but with an observation that even the most iconic conservation successes are fragile without resilient human systems to support them.
              </p>
              <p className="text-lg text-gray-700 mb-6">
                In <strong>August 2024</strong>, we traveled to the <strong>Volcanoes National Park</strong> region, where we witnessed a powerful but delicate recovery in action. Decades of dedicated conservation have seen mountain gorilla populations rebound, but this success brings its own challenges.
              </p>
              <p className="text-lg text-gray-700 mb-8">
                As gorilla numbers increase, the need for buffer zones, local support, and resilient food systems becomes more pressing. Conservation cannot stop at the park boundary.
              </p>
            </div>
            <div className="relative">
              <img
                src="/images/community/eco-hive-guadians-community-project.webp"
                alt="Supporting sustainable beekeeping practices in Rwanda. This project aims to empower local communities through eco-friendly beekeeping, promoting biodiversity and providing a sustainable source of income."
                className="shadow-lg w-full h-80 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-lg"></div>
            </div>
          </div>
        </section>

        <section className="mb-16 bg-white p-8">
          <p className="text-[var(--accent)] font-medium text-sm mb-4 text-center">Project Story</p>
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            A Story of Resilience and Community
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-12">
            <div className="relative group order-2 lg:order-1">
              <img
                src="/images/community/community-bee-keeping-consevation-innitiative-daniel-ntakirutimana.webp"
                alt="Daniel NTAKIRUTIMANA in the Eco-hive Guardians bee keeping area"
                className=" shadow-xl w-full h-[470px] object-cover transform group-hover:scale-[1.02] transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <p className="text-sm font-medium">Daniel NTAKIRUTIMANA the founder of Swift Africa Safaris</p>
              </div>
            </div>

            <div className="space-y-6 order-1 lg:order-2">
              <div className="bg-gradient-to-r from-[--light-green]/10 to-[var(--btn)]/10 p-6 hover:shadow-lg transition-all duration-300">
                <p className="text-lg text-gray-700 leading-relaxed">
                  Our journey in Rwanda is not just about conservation; it&apos;s about people. It&apos;s about the farmers who live on the edge of protected areas, the communities that depend on these ecosystems, and the visionaries like <strong><a href='https://www.linkedin.com/in/daniel-ntakirutimana-413808301?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app' className="underline underline-offset-4 decoration-[var(--btn)] hover:decoration-2 transition-all duration-200">Daniel Ntakirutimana</a></strong> who are working to create sustainable livelihoods that benefit both nature and people.
                </p>
              </div>

              <div className="bg-gradient-to-r from-[var(--btn)]/10 to-[--light-green]/10 p-6 hover:shadow-lg transition-all duration-300">
                <p className="text-lg text-gray-700 leading-relaxed">
                  This initiative is about more than environmental preservation; it&apos;s about forging a future where communities and ecosystems flourish in unison. It&apos;s a commitment to ensuring that the legacy of conservation is not only about saving landscapes and species but also about uplifting the lives of those who call these places home.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-[--light-green]/5 to-[var(--btn)]/5 p-8">
            <p className="text-lg text-gray-700 leading-relaxed text-center max-w-3xl mx-auto">
              Our approach is holistic, focusing on regenerative agriculture, cooperative development, and community empowerment. By
              supporting local farmers in adopting sustainable practices, we aim to restore degraded lands, improve food security, and reduce the pressure on protected areas. This is not just about conservation; it's about creating resilient communities that can thrive alongside nature.
            </p>
          </div>
        </section>

        {/* team section */}
        <section>
          {/* team section goes here */}

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            {/* Header Section */}
            <div className="text-center mb-8">
              <p className="text-[var(--accent)] font-medium text-sm mb-4">Our Team Member</p>
              <h2 className="text-4xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Eco Hive Guardians
                <br />
                team
              </h2>
              <p className="text-lg text-gray-600 max-w-4xl mx-auto">
                Our team is a diverse group of passionate individuals dedicated to making a positive impact in the world.
                <br />
                From conservationists to engineers, designers, and community organizers.

              </p>
            </div>

            {/* Team Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
              {/* Daniel Ntakirutimana */}
              <div className="text-justify">
                <div className="aspect-square w-full mb-4 overflow-hidden">
                  <img src="/images/community/daniel-ntakirutimana-founder-of-swift-africa-safaris.webp"
                    alt="Daniel NTAKIRUTIMANA the Founder of Swift Africa Safaris" className="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                </div>
                <h3 className="font-semibold text-gray-900 text-xl mb-1">Daniel Ntakirutimana</h3>
                <p className="text-[var(--light-green)] font-medium">Project Manager</p>
              </div>

              {/* Ashwin Santiago */}
              <div className="text-justify">
                <div className="aspect-square w-full mb-4 overflow-hidden">
                  <img src="/images/"
                    alt="Alex Roessner the Founder of Roessner Restoration Initiative" className="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                </div>
                <h3 className="font-semibold text-gray-900 text-xl mb-1">Alex Roessner</h3>
                <p className="text-[var(--light-green)] font-medium">Project Manager</p>
              </div>

             
               <div className="text-justify">
                <div className="aspect-square w-full mb-4 overflow-hidden">
                  <img src="/images/community/ecohivegaurdian/cedrick.webp"
                    alt="Cedrick Hirwa" className="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                </div>
                <h3 className="font-semibold text-gray-900 text-xl mb-1">Cedrick Hirwa</h3>
                <p className="text-[var(--light-green)] font-medium">Administrative</p>
              </div>

              
              {/* <div className="text-justify">
                <div className="aspect-square w-full mb-4 overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                    alt="Owen Garcia" className="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                </div>
                <h3 className="font-semibold text-gray-900 text-xl mb-1">Owen Garcia</h3>
                <p className="text-[var(--light-green)] font-medium">Frontend Developer</p>
              </div>  */}
            </div>
          </div>

        </section>


        {/* Daniel's Story */}
        <section className="mb-16 bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <p className="text-[var(--accent)] font-medium text-sm mb-4">Meet the Founder</p>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Meet Daniel Ntakirutimana
            </h3>
            <p className="text-lg text-gray-600">
              Rwandan conservationist and university student
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <p className="text-lg text-gray-700 mb-6">
                This initiative was born from conversations with <strong><a href='https://www.linkedin.com/in/daniel-ntakirutimana-413808301?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app className="underline underline-offset-4 decoration-[var(--btn)] hover:decoration-2 transition-all duration-200'>Daniel Ntakirutimana</a></strong>, a Rwandan conservationist and university student whose work focuses on improving local agricultural systems.
              </p>
              <p className="text-lg text-gray-700 mb-6">
                Daniel has built a network of cooperatives in the <strong>Nyamasheke District</strong>, where smallholder farmers grow avocados and produce some of the purest honey in the region.
              </p>
              <p className="text-lg text-gray-700">
                His vision is simple but powerful: to align local livelihoods with conservation goals, reducing pressure on protected forests and creating resilient, self-sustaining economies.
              </p>
            </div>
            <div className="text-center">
              <div className="w-48 h-48 mx-auto bg-gradient-to-br from-[--light-green] to-[var(--btn)] rounded-full flex items-center justify-center mb-4">
                <Users className="w-20 h-20 text-white" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-2">
                Cooperative Development
              </h4>
              <p className="text-gray-600">
                Supporting local cooperatives in governance, infrastructure, and market access
              </p>
            </div>
          </div>
        </section>

        {/* Our Work */}
        <section className="mb-16">
          <p className="text-[var(--accent)] font-medium text-sm mb-4 text-center">Our Work</p>
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">
            Our Work in Rwanda
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Heart className="w-8 h-8 text-[var(--light-green)]" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                Regenerative Agriculture Training
              </h4>
              <p className="text-gray-600">
                Equipping local farmers with the skills to restore degraded soils, improve yields, and reduce deforestation.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-16 h-16 mx-auto bg-yellow-100 rounded-full flex items-center justify-center mb-4">
                <Users className="w-8 h-8 text-yellow-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                Cooperative Development
              </h4>
              <p className="text-gray-600">
                Supporting local cooperatives in governance, infrastructure, and market access.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-16 h-16 mx-auto bg-amber-100 rounded-full flex items-center justify-center mb-4">
                <Heart className="w-8 h-8 text-amber-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                Honey Production and Export
              </h4>
              <p className="text-gray-600">
                Enhancing the quality, packaging, and distribution of local honey, creating new income streams.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-lg p-6 text-center">
              <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <MapPin className="w-8 h-8 text-blue-600" />
              </div>
              <h4 className="text-xl font-semibold text-gray-900 mb-3">
                Buffer Zone Reinforcement
              </h4>
              <p className="text-gray-600">
                Establishing agricultural systems that provide viable livelihoods without encroaching on protected areas.
              </p>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="bg-[var(--btn)]  text-white rounded-lg p-8 text-center">
          <h3 className="text-3xl font-bold mb-4">
            Join the Hive Guardian Initiative
          </h3>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            The goal is not just to provide an alternative to destructive land use, but to redefine the relationship between people and place  to build a culture of stewardship that extends far beyond park borders.
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="inline-flex items-center gap-2 bg-white text-[var(--btn)] px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Take Volunteering Opportunities
            <ExternalLink className="w-5 h-5" />
          </button>
        </section>
      </main>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 md:p-6">
          <div className="relative bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10"
            >
              <span className="sr-only">Close</span>
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <VolunteeringForm />
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default HiveGuardianClient;
