'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  BarChart3,
  Package,
  Calendar,
  MessageSquare,
  User,
  LogOut,
  Menu,
  X,
  Home
} from 'lucide-react';
import NotificationCenter from '../../components/partner/NotificationCenter';
import { NotificationProvider } from '../../components/partner/NotificationProvider';

interface NavItem {
  name: string;
  href: string;
  icon: React.ReactNode;
  description: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function PartnerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Don't show navigation on auth pages
  if (pathname?.includes('/auth/')) {
    return <>{children}</>;
  }

  const navigation: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/partner/dashboard',
      icon: <Home className="h-5 w-5" />,
      description: 'Overview and statistics'
    },
    {
      name: 'Packages',
      href: '/partner/packages',
      icon: <Package className="h-5 w-5" />,
      description: 'Browse safari packages'
    },
    {
      name: 'Bookings',
      href: '/partner/bookings',
      icon: <Calendar className="h-5 w-5" />,
      description: 'Manage your bookings'
    },
    {
      name: 'Reports',
      href: '/partner/reports',
      icon: <BarChart3 className="h-5 w-5" />,
      description: 'Commission and analytics'
    },
    {
      name: 'Messages',
      href: '/partner/messages',
      icon: <MessageSquare className="h-5 w-5" />,
      description: 'Communication center'
    },
    {
      name: 'Profile',
      href: '/partner/profile',
      icon: <User className="h-5 w-5" />,
      description: 'Account settings'
    }
  ];

  const handleLogout = () => {
    // Here you would handle logout logic
    router.push('/partner/auth/login');
  };

  const isActive = (href: string) => {
    return pathname === href || (href !== '/partner/dashboard' && pathname?.startsWith(href));
  };

  return (
    <NotificationProvider>
      <div className="min-h-screen bg-gray-50">
        <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Top Navigation Bar */}
      <nav className="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16 gap-6">
            {/* Logo and Mobile Menu Button */}
            <div className="flex items-center gap-3">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
              >
                {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </button>
              <Link href="/partner/dashboard" className="flex items-center gap-3">
                <div className="text-xl font-bold text-blue-600 whitespace-nowrap">Swift Africa Safaris</div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors whitespace-nowrap ${
                    isActive(item.href)
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <span className="ml-2">{item.name}</span>
                </Link>
              ))}
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-3">
              {/* Notifications */}
              <NotificationCenter />

              {/* User Menu */}
              <div className="flex items-center space-x-3">
                <div className="hidden sm:block text-right">
                  <div className="text-sm font-medium text-gray-900 whitespace-nowrap">John Safari Tours</div>
                  <div className="text-xs text-gray-500 whitespace-nowrap">Safari Adventures Ltd</div>
                </div>
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">JS</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Logout"
                >
                  <LogOut className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 bg-white shadow-lg">
            <div className="px-4 py-3 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={() => setIsMobileMenuOpen(false)}
                  className={`flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    isActive(item.href)
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <div className="ml-3">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-500">{item.description}</div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </nav>

        {/* Main Content */}
        <div className="pt-16">
          {children}
        </div>
      </div>
    </NotificationProvider>
  );
}
