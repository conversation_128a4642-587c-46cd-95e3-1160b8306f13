"use client";
import React, { useState, useEffect } from 'react';
import Image from 'next/image';


const HeroSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      id: 1,
      image: "/images/hero/great-migration-serengeti-national-park.webp",
      alt: "Great Migration Serengeti National Park",
      title: "The Rhythm of The Wild",
      description: "As thousands of wildebeest, and zebras across the river filled with crocodiles."
    },
    {
      id: 2,
      image: "/images/hero/mountain-gorillas-trekking-rwanda.webp",
      alt: "a silver Mountain gorilla in the wild",
      title: "The Last Gentle Giant",
      description: "In the heart of the mist laced highlands, a mountain gorilla feasts quietly."
    },
    {
      id: 3,
      image: "/images/hero/kruger-national-park-game-drive.webp",
      alt: "Game drive in Kruger National Park spotting big five",
      title: "Kings in Repose",
      description: "Beneath the golden sun of Kruger, lions rest in regal silence."
    },
    {
      id: 4,
      image: "/images/hero/zanzibar-beach-tanzania-safaris.webp",
      alt: "happy two girls enjoying Zanzibar vibrant beach",
      title: "Zanzibar in Their Smile.",
      description: "On the sun kissed shores of Zanzibar, they share a carefree moments."
    },
    {
      id: 5,
      image: "/images/hero/murchison-falls-uganda-safaris.webp",
      alt: "boat cruising over Murchison Falls in Uganda",
      title: "Chasing The Thunder",
      description: "Cruising the wild pulse of the Murchison river"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 10000);

    return () => clearInterval(timer);
  }, [slides.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };


  return (


      <section className="relative w-full h-[30rem] overflow-hidden bg-black">
        {/* Background Images */}
        <div className="absolute inset-0">
          {slides.map((slide, index) => (
            <div
              key={slide.id}
              className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${index === currentSlide ? 'opacity-100' : 'opacity-0'
                }`}
            >
              <Image
                src={slide.image}
                alt={slide.alt}
                className="w-screen h-full object-cover"
                width={1920}
                height={480}
                priority={index === 0} // Prioritize first slide
                unoptimized
              />
              <div className="absolute inset-0"
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  transition: 'background-color 1s ease-in-out'
                }}
              ></div>
            </div>
          ))}
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 h-full flex items-center justify-center">
          <div className="text-center text-white px-4 max-w-4xl mx-auto">
            <div className="overflow-hidden">
              <h1
                key={`title-${currentSlide}`}
                className="text-5xl md:text-7xl font-bold mb-6 animate-fadeInUp"
                style={{ fontFamily: 'Jost, sans-serif' }}
              >
                {slides[currentSlide].title}
              </h1>
            </div>
            <div className="overflow-hidden">
              <p
                key={`desc-${currentSlide}`}
                className="text-xl md:text-2xl font-light leading-relaxed animate-fadeInUp animation-delay-200"
                style={{ fontFamily: 'Jost, sans-serif' }}
              >
                {slides[currentSlide].description}
              </p>
            </div>
          </div>
        </div>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-4 h-4 rounded-full transition-all duration-300 mx-2 ${index === currentSlide
                  ? 'bg-white scale-125'
                  : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
            />
          ))}
        </div>

        <style jsx>{`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          
          .animate-fadeInUp {
            animation: fadeInUp 0.8s ease-out forwards;
          }
          
          .animation-delay-200 {
            animation-delay: 0.2s;
            opacity: 0;
          }
          
          .transition-all {
            transition-property: all;
          }
        `}</style>
      </section>

  );
};

export default HeroSlider;