import React from 'react';
import { tourPackages } from '../data/packageData';

const TourFilter = ({ selectedCountries, setSelectedCountries, sortBy, setSortBy }) => {
    // Get unique countries
    const countries = [...new Set(tourPackages.map(tour => tour.location))].sort();

    // Get categories
    const categories = [...new Set(tourPackages.map(tour => tour.category))].sort();

    const handleCountryToggle = (country) => {
        setSelectedCountries(prev => 
            prev.includes(country) 
                ? prev.filter(c => c !== country)
                : [...prev, country]
        );
    };

    const clearAllFilters = () => {
        setSelectedCountries([]);
        setSortBy('');
    };

    return (
        <div className="w-full space-y-4 p-4 lg:p-6">
            {/* Categories Section */}
            <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-gray-700 mr-2">Categories:</span>
                {categories.map(category => (
                    <button
                        key={category}
                        onClick={() => setSortBy(sortBy === category ? '' : category)}
                        className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex items-center gap-1.5 ${
                            sortBy === category
                                ? 'bg-black text-white'
                                : 'bg-[var(--secondary-background)] text-gray-700 border border-gray-300 hover:bg-gray-50'
                        }`}
                    >
                        {category}
                    </button>
                ))}
            </div>

            {/* Destinations Section */}
            <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm font-medium text-gray-700 mr-2">Destinations:</span>
                {countries.map(country => (
                    <button
                        key={country}
                        onClick={() => handleCountryToggle(country)}
                        className={`px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 flex items-center gap-1.5 ${
                            selectedCountries.includes(country)
                                ? 'bg-black text-white'
                                : 'bg-[var(--secondary-background)] text-gray-700 border border-gray-300 hover:bg-gray-50'
                        }`}
                    >
                        {country}
                    </button>
                ))}
            </div>

            {/* Compact Filter Summary */}
            {(selectedCountries.length > 0 || sortBy) && (
                <div className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg text-xs">
                    <div className="text-gray-600">
                        {sortBy && <span className="mr-2">Category: {sortBy}</span>}
                        {selectedCountries.length > 0 && (
                            <span>{sortBy ? '| ' : ''}Selected: {selectedCountries.length} {selectedCountries.length === 1 ? 'destination' : 'destinations'}</span>
                        )}
                    </div>
                    <button 
                        onClick={clearAllFilters}
                        className="text-red-500 hover:text-red-700 font-medium"
                    >
                        Clear
                    </button>
                </div>
            )}
        </div>
    );
};

export default TourFilter;