/* eslint-disable @next/next/no-img-element */
'use client';

import React, { useState, useRef } from "react";
import { Home, Save, Upload } from "lucide-react";

interface HeroFormData {
  heroTitle: string;
  heroSubtitle: string;
  heroDescription: string;
  heroImage: string;
  ctaButtonText: string;
  ctaButtonLink: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string;
}

interface FormErrors {
  [key: string]: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6B7280;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #4B5563;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #6B7280 transparent;
  }
`;

export default function HomepageHeroManagement() {
  const [activeTab, setActiveTab] = useState("hero");
  const [formData, setFormData] = useState<HeroFormData>({
    heroTitle: "Discover the Magic of Africa",
    heroSubtitle: "Unforgettable Safari Adventures Await",
    heroDescription: "Experience the breathtaking wildlife, stunning landscapes, and rich culture of East Africa with our expertly guided safari tours.",
    heroImage: "",
    ctaButtonText: "Start Your Adventure",
    ctaButtonLink: "/packages",
    seoTitle: "Swift Africa Safaris - Best Safari Tours in East Africa",
    seoDescription: "Discover amazing safari adventures in Tanzania, Kenya, and Uganda. Expert guides, luxury accommodations, and unforgettable wildlife experiences.",
    seoKeywords: "safari tours, Tanzania safari, Kenya safari, wildlife tours, Africa travel"
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleInputChange = (field: keyof HeroFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real app, you would upload to a server
      const imageUrl = URL.createObjectURL(file);
      setFormData((prev) => ({ ...prev, heroImage: imageUrl }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.heroTitle || formData.heroTitle.length < 5) {
      newErrors.heroTitle = "Please enter a valid hero title (5+ characters)";
    }

    if (!formData.heroSubtitle || formData.heroSubtitle.length < 5) {
      newErrors.heroSubtitle = "Please enter a valid subtitle (5+ characters)";
    }

    if (!formData.heroDescription || formData.heroDescription.length < 20) {
      newErrors.heroDescription = "Please enter a valid description (20+ characters)";
    }

    if (!formData.ctaButtonText) {
      newErrors.ctaButtonText = "Please enter button text";
    }

    if (!formData.ctaButtonLink) {
      newErrors.ctaButtonLink = "Please enter button link";
    }

    if (!formData.seoTitle || formData.seoTitle.length < 10) {
      newErrors.seoTitle = "Please enter a valid SEO title (10+ characters)";
    }

    if (!formData.seoDescription || formData.seoDescription.length < 50) {
      newErrors.seoDescription = "Please enter a valid SEO description (50+ characters)";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (validateForm()) {
      console.log("Form submitted successfully:", formData);
      alert("Homepage hero settings saved successfully!");
    }
  };

  const tabs = [
    { id: "hero", label: "Hero Content" },
    { id: "seo", label: "SEO Settings" },
  ];

  return (
    <main className="ml-16 pt-16 min-h-[calc(100vh-4rem)] mr-2 shadow-sm transition-all duration-300 ease-in-out">
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      <div className="mx-2 rounded-lg border-b border-gray-200 bg-[#d3d3d3] h-[87vh] flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-300">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Home className="mr-3 text-blue-600" />
                Homepage Hero Management
              </h1>
              <p className="text-gray-600 mt-1">Manage your homepage hero section and SEO settings</p>
            </div>
            <button
              type="submit"
              className="bg-[var(--accent)] text-white px-4 py-2 rounded-lg hover:bg-[var(--btn)] transition-colors flex items-center"
            >
              <Save size={16} className="mr-2" />
              Save Changes
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? "border-[var(--accent)] text-[var(--accent)]"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <form onSubmit={handleSubmit} className="p-6">
                {/* Hero Content Tab */}
                {activeTab === "hero" && (
                  <div className="space-y-6">
                    {/* Preview Section */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Hero Section Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-6 bg-gradient-to-r from-blue-50 to-green-50">
                        <div className="text-center space-y-4">
                          {formData.heroImage && (
                            <img
                              src={formData.heroImage}
                              alt="Hero background"
                              className="w-full h-48 object-cover rounded-lg mb-4"
                            />
                          )}
                          <h1 className="text-3xl font-bold text-gray-900">
                            {formData.heroTitle || "Your Hero Title"}
                          </h1>
                          <h2 className="text-xl text-gray-700">
                            {formData.heroSubtitle || "Your Hero Subtitle"}
                          </h2>
                          <p className="text-gray-600 max-w-2xl mx-auto">
                            {formData.heroDescription || "Your hero description will appear here"}
                          </p>
                          <button className="bg-[var(--accent)] text-white px-6 py-3 rounded-lg font-medium">
                            {formData.ctaButtonText || "Call to Action"}
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Hero Title */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Title *
                      </label>
                      <input
                        type="text"
                        value={formData.heroTitle}
                        onChange={(e) => handleInputChange("heroTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.heroTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your main hero title"
                      />
                      {errors.heroTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.heroTitle}</p>
                      )}
                    </div>

                    {/* Hero Subtitle */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Subtitle *
                      </label>
                      <input
                        type="text"
                        value={formData.heroSubtitle}
                        onChange={(e) => handleInputChange("heroSubtitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.heroSubtitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your hero subtitle"
                      />
                      {errors.heroSubtitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.heroSubtitle}</p>
                      )}
                    </div>

                    {/* Hero Description */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Description *
                      </label>
                      <textarea
                        value={formData.heroDescription}
                        onChange={(e) => handleInputChange("heroDescription", e.target.value)}
                        rows={4}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.heroDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter your hero description"
                      />
                      {errors.heroDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.heroDescription}</p>
                      )}
                    </div>

                    {/* CTA Button */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Button Text *
                        </label>
                        <input
                          type="text"
                          value={formData.ctaButtonText}
                          onChange={(e) => handleInputChange("ctaButtonText", e.target.value)}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.ctaButtonText ? "border-red-500" : "border-gray-300"
                          }`}
                          placeholder="e.g., Start Your Adventure"
                        />
                        {errors.ctaButtonText && (
                          <p className="mt-1 text-sm text-red-600">{errors.ctaButtonText}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Button Link *
                        </label>
                        <input
                          type="text"
                          value={formData.ctaButtonLink}
                          onChange={(e) => handleInputChange("ctaButtonLink", e.target.value)}
                          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                            errors.ctaButtonLink ? "border-red-500" : "border-gray-300"
                          }`}
                          placeholder="e.g., /packages or https://..."
                        />
                        {errors.ctaButtonLink && (
                          <p className="mt-1 text-sm text-red-600">{errors.ctaButtonLink}</p>
                        )}
                      </div>
                    </div>

                    {/* Hero Image Upload */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hero Background Image
                      </label>
                      <div
                        onClick={handleFileSelect}
                        className="relative border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-[var(--accent)] transition-colors cursor-pointer bg-gray-50 hover:bg-gray-100"
                      >
                        <div className="text-center">
                          {formData.heroImage ? (
                            <div className="space-y-2">
                              <img
                                src={formData.heroImage}
                                alt="Hero Image Preview"
                                className="mx-auto h-32 w-auto object-cover rounded-lg"
                              />
                              <p className="text-sm text-gray-600">
                                Click to change image
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <Upload className="mx-auto h-12 w-12 text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">
                                  Click to upload hero background image
                                </p>
                                <p className="text-xs text-gray-500">
                                  PNG, JPG, GIF up to 10MB
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* SEO Settings Tab */}
                {activeTab === "seo" && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Title *
                      </label>
                      <input
                        type="text"
                        value={formData.seoTitle}
                        onChange={(e) => handleInputChange("seoTitle", e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoTitle ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO title for homepage"
                      />
                      {errors.seoTitle && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoTitle}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoTitle.length}/70 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Description *
                      </label>
                      <textarea
                        value={formData.seoDescription}
                        onChange={(e) => handleInputChange("seoDescription", e.target.value)}
                        rows={3}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                          errors.seoDescription ? "border-red-500" : "border-gray-300"
                        }`}
                        placeholder="Enter SEO description for homepage"
                      />
                      {errors.seoDescription && (
                        <p className="mt-1 text-sm text-red-600">{errors.seoDescription}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">
                        {formData.seoDescription.length}/160 characters
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        SEO Keywords
                      </label>
                      <input
                        type="text"
                        value={formData.seoKeywords}
                        onChange={(e) => handleInputChange("seoKeywords", e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                        placeholder="Enter keywords separated by commas"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Separate keywords with commas
                      </p>
                    </div>

                    {/* SEO Preview */}
                    <div>
                      <h3 className="text-base font-medium text-[var(--text)] mb-3">
                        Search Engine Preview
                      </h3>
                      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div className="space-y-1">
                          <div className="text-blue-600 text-lg font-medium hover:underline cursor-pointer">
                            {formData.seoTitle || "Your SEO Title"}
                          </div>
                          <div className="text-green-700 text-sm">
                            https://swiftafricasafaris.com
                          </div>
                          <div className="text-gray-600 text-sm">
                            {formData.seoDescription || "Your SEO description will appear here"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
