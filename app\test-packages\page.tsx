'use client';

import React, { useState, useEffect } from 'react';
import TourCard from '@/components/cards/packageCard';

interface Package {
  id: string;
  title: string;
  slug: string;
  image_url: string;
  hero_image_url: string;
  image_alt: string;
  hero_image_alt: string;
  category: string;
  location: string;
  duration: string;
  difficulty: string;
  pricing_solo: number;
  pricing_honeymoon: number;
  pricing_family: number;
  pricing_group: number;
}

export default function TestPackagesPage() {
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await fetch('/api/admin/packages');
        const result = await response.json();
        
        if (result.success) {
          setPackages(result.data);
        } else {
          setError(result.error || 'Failed to fetch packages');
        }
      } catch (err) {
        setError('Failed to fetch packages');
        console.error('Error fetching packages:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPackages();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading packages...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Package Image Test</h1>
          <p className="text-gray-600">Testing package cards with images from database</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {packages.map((pkg) => (
            <div key={pkg.id} className="bg-white rounded-lg shadow-md overflow-hidden">
              {/* Debug Info */}
              <div className="p-3 bg-gray-100 text-xs">
                <p><strong>ID:</strong> {pkg.id}</p>
                <p><strong>Image URL:</strong> {pkg.image_url}</p>
                <p><strong>Hero URL:</strong> {pkg.hero_image_url}</p>
                <p><strong>Is Blob:</strong> {pkg.image_url?.startsWith('blob:') ? 'YES' : 'NO'}</p>
              </div>
              
              {/* Package Card */}
              <TourCard
                tour={{
                  id: pkg.id,
                  title: pkg.title,
                  slug: pkg.slug,
                  image_url: pkg.image_url,
                  hero_image_url: pkg.hero_image_url,
                  image_alt: pkg.image_alt,
                  hero_image_alt: pkg.hero_image_alt,
                  category: pkg.category,
                  location: pkg.location,
                  duration: pkg.duration,
                  difficulty: pkg.difficulty || 'Moderate',
                  pricing: {
                    solo: pkg.pricing_solo,
                    honeymoon: pkg.pricing_honeymoon,
                    family: pkg.pricing_family,
                    group: pkg.pricing_group
                  }
                }}
              />
            </div>
          ))}
        </div>

        {packages.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No packages found</p>
          </div>
        )}
      </div>
    </div>
  );
}
