import nodemailer from 'nodemailer';
import { getNewsletterSettings } from '@/lib/newsletter-settings';

// Create newsletter transporter using dynamic settings
export async function createNewsletterTransporter() {
  const settings = await getNewsletterSettings();
  
  if (!settings) {
    throw new Error('Newsletter SMTP settings not configured');
  }

  return nodemailer.createTransport({
    host: settings.smtp_host,
    port: settings.smtp_port,
    secure: settings.smtp_secure,
    auth: {
      user: settings.smtp_email,
      pass: settings.smtp_password,
    },
  });
}

// Generate newsletter email template
export function generateNewsletterTemplate(
  content: string,
  contentType: 'html' | 'text',
  unsubscribeEmail: string
): { html: string; text: string } {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://swiftafricasafaris.com';
  const unsubscribeUrl = `${baseUrl}/api/newsletter/unsubscribe?email=${encodeURIComponent(unsubscribeEmail)}`;
  
  if (contentType === 'html') {
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Swift Africa Safaris Newsletter</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
          }
          .container {
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #163201, #317100);
            color: white;
            padding: 30px 20px;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
          }
          .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
          }
          .content {
            padding: 30px 20px;
          }
          .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
          }
          .footer a {
            color: #d35400;
            text-decoration: none;
          }
          .footer a:hover {
            text-decoration: underline;
          }
          .unsubscribe {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
          }
          .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #317100;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
          }
          .btn:hover {
            background-color: #2a5f00;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Swift Africa Safaris</h1>
            <p>Discover the Wild Beauty of Africa</p>
          </div>
          
          <div class="content">
            ${content}
          </div>
          
          <div class="footer">
            <p>You received this email because you subscribed to Swift Africa Safaris newsletter.</p>
            <p>
              <a href="${baseUrl}">Visit our website</a> | 
              <a href="${unsubscribeUrl}">Unsubscribe</a>
            </p>
            <div class="unsubscribe">
              <p><strong>Swift Africa Safaris</strong></p>
              <p>Experience authentic African adventures with expert guides and sustainable tourism practices.</p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const textContent = `
SWIFT AFRICA SAFARIS NEWSLETTER
Discover the Wild Beauty of Africa

${content.replace(/<[^>]*>/g, '').replace(/\n\s*\n/g, '\n\n')}

---
You received this email because you subscribed to Swift Africa Safaris newsletter.

Visit our website: ${baseUrl}
Unsubscribe: ${unsubscribeUrl}

Swift Africa Safaris
Experience authentic African adventures with expert guides and sustainable tourism practices.
    `.trim();

    return { html: htmlContent, text: textContent };
  } else {
    // Plain text newsletter
    const textContent = `
SWIFT AFRICA SAFARIS NEWSLETTER
Discover the Wild Beauty of Africa

${content}

---
You received this email because you subscribed to Swift Africa Safaris newsletter.

Visit our website: ${baseUrl}
Unsubscribe: ${unsubscribeUrl}

Swift Africa Safaris
Experience authentic African adventures with expert guides and sustainable tourism practices.
    `.trim();

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Swift Africa Safaris Newsletter</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
          }
          .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #317100;
          }
          .header h1 {
            color: #163201;
            margin: 0;
            font-size: 28px;
          }
          .header p {
            color: #666;
            margin: 10px 0 0 0;
          }
          .content {
            white-space: pre-line;
            margin-bottom: 30px;
          }
          .footer {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
          }
          .footer a {
            color: #d35400;
            text-decoration: none;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Swift Africa Safaris</h1>
            <p>Discover the Wild Beauty of Africa</p>
          </div>
          
          <div class="content">${content}</div>
          
          <div class="footer">
            <p>You received this email because you subscribed to Swift Africa Safaris newsletter.</p>
            <p>
              <a href="${baseUrl}">Visit our website</a> | 
              <a href="${unsubscribeUrl}">Unsubscribe</a>
            </p>
            <p><strong>Swift Africa Safaris</strong><br>
            Experience authentic African adventures with expert guides and sustainable tourism practices.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    return { html: htmlContent, text: textContent };
  }
}

// Send newsletter to a single subscriber
export async function sendNewsletterToSubscriber(
  subscriberEmail: string,
  subject: string,
  content: string,
  contentType: 'html' | 'text' = 'html'
): Promise<boolean> {
  try {
    const transporter = await createNewsletterTransporter();
    const settings = await getNewsletterSettings();
    
    if (!settings) {
      throw new Error('Newsletter settings not found');
    }

    const { html, text } = generateNewsletterTemplate(content, contentType, subscriberEmail);

    const mailOptions = {
      from: `Swift Africa Safaris <${settings.smtp_email}>`,
      to: subscriberEmail,
      subject: subject,
      html: html,
      text: text,
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error(`Failed to send newsletter to ${subscriberEmail}:`, error);
    return false;
  }
}

// Send test newsletter
export async function sendTestNewsletter(
  testEmail: string,
  subject: string = 'Test Newsletter - Swift Africa Safaris',
  content: string = '<h2>Test Newsletter</h2><p>This is a test email to verify your newsletter configuration is working correctly.</p><p>If you received this email, your SMTP settings are properly configured!</p>'
): Promise<boolean> {
  try {
    return await sendNewsletterToSubscriber(testEmail, subject, content, 'html');
  } catch (error) {
    console.error('Failed to send test newsletter:', error);
    return false;
  }
}

// Send admin notification for new subscription
export async function sendSubscriptionNotification(subscriberEmail: string): Promise<boolean> {
  try {
    const settings = await getNewsletterSettings();
    if (!settings) return false;

    const transporter = await createNewsletterTransporter();
    const adminEmail = '<EMAIL>';

    const mailOptions = {
      from: `Swift Africa Safaris <${settings.smtp_email}>`,
      to: adminEmail,
      subject: 'New Newsletter Subscription',
      html: `
        <h2>New Newsletter Subscription</h2>
        <p>A new user has subscribed to the Swift Africa Safaris newsletter:</p>
        <p><strong>Email:</strong> ${subscriberEmail}</p>
        <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
        <p>You can manage newsletter subscribers in the admin dashboard.</p>
      `,
      text: `
New Newsletter Subscription

A new user has subscribed to the Swift Africa Safaris newsletter:
Email: ${subscriberEmail}
Date: ${new Date().toLocaleString()}

You can manage newsletter subscribers in the admin dashboard.
      `
    };

    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error('Failed to send subscription notification:', error);
    return false;
  }
}
