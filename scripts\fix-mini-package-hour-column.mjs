import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addHourNumberColumn() {
  console.log('🔧 Manual fix: Adding hour_number column to sas_mini_package_itinerary...\n');

  console.log('❌ Cannot execute DDL statements through Supabase client API');
  console.log('📋 MANUAL STEPS REQUIRED:');
  console.log('');
  console.log('1. Open Supabase Dashboard > SQL Editor');
  console.log('2. Copy and paste this SQL command:');
  console.log('');
  console.log('   ALTER TABLE public.sas_mini_package_itinerary ADD COLUMN IF NOT EXISTS hour_number INTEGER;');
  console.log('   UPDATE public.sas_mini_package_itinerary SET hour_number = day_number WHERE hour_number IS NULL;');
  console.log('   ALTER TABLE public.sas_mini_package_itinerary ALTER COLUMN hour_number SET NOT NULL;');
  console.log('');
  console.log('3. Execute the SQL');
  console.log('4. Run this script again to test');
  console.log('');

  // Test if the column already exists
  try {
    const { error } = await supabase
      .from('sas_mini_package_itinerary')
      .select('hour_number')
      .limit(1);

    if (!error) {
      console.log('✅ hour_number column already exists!');
      return true;
    } else if (error.message.includes('does not exist')) {
      console.log('⚠️  hour_number column does not exist - please run the SQL above');
      return false;
    } else {
      console.error('❌ Error checking column:', error.message);
      return false;
    }
  } catch (err) {
    console.error('❌ Error:', err.message);
    return false;
  }
}

async function testFixedTable() {
  console.log('\n🔍 Testing the fixed table...\n');
  
  try {
    // Test the query that was failing before
    const { data, error } = await supabase
      .from('sas_mini_package_itinerary')
      .select('id, hour_number, title, description, sort_order')
      .limit(3);

    if (error) {
      console.error('❌ Test query failed:', error.message);
      return false;
    }

    console.log('✅ Test query successful!');
    console.log('📊 Sample records:');
    data.forEach(record => {
      console.log(`   Hour ${record.hour_number}: ${record.title}`);
    });

    return true;
  } catch (err) {
    console.error('❌ Test error:', err.message);
    return false;
  }
}

async function testMiniPackagePage() {
  console.log('\n🔍 Testing mini package page query...\n');
  
  try {
    // This is the exact query that was failing in database-utils.ts
    const { data, error } = await supabase
      .from('sas_mini_packages')
      .select(`
        *,
        sas_mini_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_mini_package_itinerary (
          id,
          hour_number,
          title,
          description,
          sort_order
        ),
        sas_mini_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', 'the-last-login')
      .eq('status', 'published')
      .single();

    if (error) {
      console.error('❌ Mini package query failed:', error.message);
      return false;
    }

    if (data) {
      console.log('✅ Mini package query successful!');
      console.log(`📋 Found: ${data.title}`);
      console.log(`⏰ Duration: ${data.duration}`);
      console.log(`📅 Itinerary items: ${data.sas_mini_package_itinerary?.length || 0}`);
      
      if (data.sas_mini_package_itinerary && data.sas_mini_package_itinerary.length > 0) {
        console.log('\n📅 Itinerary preview:');
        data.sas_mini_package_itinerary.slice(0, 3).forEach(item => {
          console.log(`   Hour ${item.hour_number}: ${item.title}`);
        });
      }
      
      console.log('\n🎉 The /mini-package/the-last-login page should now work!');
      return true;
    } else {
      console.log('❌ No mini package found with slug "the-last-login"');
      return false;
    }
  } catch (err) {
    console.error('❌ Test error:', err.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Fixing mini package hour_number column issue...\n');
  
  const success = await addHourNumberColumn();
  
  if (success) {
    await testFixedTable();
    await testMiniPackagePage();
    
    console.log('\n' + '='.repeat(60));
    console.log('✅ ISSUE FIXED!');
    console.log('🔧 Added hour_number column to sas_mini_package_itinerary');
    console.log('📋 Copied existing day_number values to hour_number');
    console.log('🔗 The mini package page should now work: /mini-package/the-last-login');
    console.log('\n💡 Note: The day_number column still exists for backward compatibility');
    console.log('   You can remove it later if needed using the full migration script');
  } else {
    console.log('\n❌ Fix failed. Please check the errors above.');
    process.exit(1);
  }
}

main();
