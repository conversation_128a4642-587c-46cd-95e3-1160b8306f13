import React from 'react';

/**
 * Base Skeleton Component with Facebook-style shimmer animation
 * Provides the foundation for all skeleton loading components
 */
const SkeletonBase = ({ 
  className = '', 
  width = 'w-full', 
  height = 'h-4', 
  rounded = 'rounded',
  children,
  ...props 
}) => {
  return (
    <div 
      className={`
        ${width} ${height} ${rounded} ${className}
        bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200
        bg-[length:200%_100%]
        animate-shimmer
        relative overflow-hidden
      `}
      {...props}
    >
      {children}
    </div>
  );
};

export default SkeletonBase;
