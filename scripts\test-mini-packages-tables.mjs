import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testMiniPackageTables() {
  console.log('🔍 Testing mini package tables...\n');
  
  const tables = [
    'sas_mini_packages',
    'sas_mini_package_content_blocks', 
    'sas_mini_package_itinerary',
    'sas_mini_package_images'
  ];
  
  let allTablesExist = true;
  
  for (const tableName of tables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table ${tableName}: ${error.message}`);
        allTablesExist = false;
      } else {
        console.log(`✅ Table ${tableName}: EXISTS`);
      }
    } catch (err) {
      console.log(`❌ Table ${tableName}: ${err.message}`);
      allTablesExist = false;
    }
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (allTablesExist) {
    console.log('✅ All mini package tables exist!');
    console.log('🔗 You can now test: /mini-package/the-last-login');
  } else {
    console.log('❌ Some mini package tables are missing.');
    console.log('\n📋 To fix this issue:');
    console.log('1. Open Supabase Dashboard > SQL Editor');
    console.log('2. Copy and paste the contents of: scripts/setup-mini-packages-database.sql');
    console.log('3. Run the SQL script');
    console.log('4. Run this test script again to verify');
  }
}

async function testSpecificMiniPackage() {
  console.log('\n🔍 Testing specific mini package: the-last-login...\n');
  
  try {
    const { data, error } = await supabase
      .from('sas_mini_packages')
      .select(`
        *,
        sas_mini_package_content_blocks (
          id,
          block_type,
          content,
          content_data,
          image_url,
          image_alt,
          image_caption,
          sort_order
        ),
        sas_mini_package_itinerary (
          id,
          hour_number,
          title,
          description,
          sort_order
        ),
        sas_mini_package_images (
          id,
          image_url,
          image_alt,
          caption,
          sort_order,
          is_featured
        )
      `)
      .eq('slug', 'the-last-login')
      .eq('status', 'published')
      .single();

    if (error) {
      console.log(`❌ Error fetching mini package: ${error.message}`);
      
      if (error.message.includes('hour_number')) {
        console.log('\n🔧 SOLUTION: The sas_mini_package_itinerary table is missing the hour_number column.');
        console.log('   This is exactly the issue mentioned in the original problem!');
        console.log('   Run the setup script to fix this.');
      }
    } else if (data) {
      console.log('✅ Mini package found:', data.title);
      console.log(`📍 Location: ${data.location}`);
      console.log(`⏰ Duration: ${data.duration}`);
      console.log(`📋 Itinerary items: ${data.sas_mini_package_itinerary?.length || 0}`);
      
      if (data.sas_mini_package_itinerary && data.sas_mini_package_itinerary.length > 0) {
        console.log('\n📅 Itinerary preview:');
        data.sas_mini_package_itinerary.slice(0, 3).forEach(item => {
          console.log(`   Hour ${item.hour_number}: ${item.title}`);
        });
      }
    } else {
      console.log('❌ Mini package "the-last-login" not found');
    }
  } catch (err) {
    console.log(`❌ Error: ${err.message}`);
  }
}

async function main() {
  await testMiniPackageTables();
  await testSpecificMiniPackage();
}

main();
