import type { Metadata } from 'next';
import Navbar from '@/components/header'
import HeroComponent from '@/components/heroes'
import { heroData } from '@/components/data/heroesdata'
import Footer from '@/components/footer'
import BlogWelcomeSection from '@/components/blog/blogWelcomeSection'
import BlogSection from '@/components/blog/blogSection'
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - Safari Stories & Travel Tips',
  description: 'Explore inspiring safari stories, expert travel tips, and destination guides from across Rwanda, Uganda, Tanzania, and South Africa.',
  url: '/blog',
  keywords: [
    'African safari blog',
    'safari travel tips',
    'wildlife photography blog',
    'gorilla trekking stories',
    'African travel guide',
    'safari planning tips',
    'East Africa travel blog',
    'Rwanda travel blog',
    'Tanzania safari blog',
    'Uganda wildlife blog',
    'safari photography tips',
    'African wildlife stories',
    'safari adventure blog',
    'travel inspiration Africa',
    'safari destination guides'
  ],
  type: 'website'
});

export default function Blog() {
  const defaultProps = {
    title: heroData.blog.title,
    subtitle: heroData.blog.subtitle,
    backgroundImage: heroData.blog.backgroundImage
  }

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Blog', url: '/blog' }
  ]);

  const blogPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Swift Africa Safaris - Safari Stories & Travel Tips',
    description: 'Explore inspiring safari stories, expert travel tips, and destination guides from across Rwanda, Uganda, Tanzania, and South Africa.',
    url: `${defaultSEO.siteUrl}/blog`,
    keywords: [
      'African safari blog',
      'safari travel tips',
      'wildlife photography blog',
      'gorilla trekking stories',
      'African travel guide',
      'safari planning tips',
      'East Africa travel blog',
      'Rwanda travel blog',
      'Tanzania safari blog',
      'Uganda wildlife blog',
      'safari photography tips',
      'African wildlife stories',
      'safari adventure blog',
      'travel inspiration Africa',
      'safari destination guides'
    ],
    publisher: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      description: 'Explore inspiring safari stories, expert travel tips, and destination guides from across Rwanda, Uganda, Tanzania, and South Africa.',
      logo: {
        '@type': 'ImageObject',
        url: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-logo.png`
      }
    },
    inLanguage: 'en-US',
    about: [
      'African Safari Tours',
      'Wildlife Photography',
      'Gorilla Trekking',
      'Travel Tips',
      'Conservation'
    ]
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, blogPageSchema]} />
      <Navbar />
      {/* Hero Section */}
      <HeroComponent {...defaultProps} />
      <BlogWelcomeSection />
      <BlogSection />
      <Footer />
    </div>
  )
}