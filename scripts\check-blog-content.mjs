#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function checkBlogContent() {
  console.log('🔍 Checking blog content blocks...\n');
  
  try {
    // Get a specific blog post
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status')
      .eq('slug', 'mountain-gorilla-trekking-in-rwanda-and-uganda')
      .single();
    
    if (postError || !post) {
      console.error('❌ Error fetching blog post:', postError);
      return;
    }
    
    console.log(`✅ Found blog post: ${post.title}`);
    console.log(`   ID: ${post.id}`);
    console.log(`   Slug: ${post.slug}`);
    console.log(`   Status: ${post.status}\n`);
    
    // Get content blocks for this post
    const { data: contentBlocks, error: blocksError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true });
    
    if (blocksError) {
      console.error('❌ Error fetching content blocks:', blocksError);
      return;
    }
    
    if (!contentBlocks || contentBlocks.length === 0) {
      console.log('❌ No content blocks found for this blog post');
      
      // Check if there are any content blocks in the database at all
      const { data: allBlocks, error: allBlocksError } = await supabase
        .from('sas_blog_content_blocks')
        .select('blog_post_id, block_type')
        .limit(10);
      
      if (allBlocksError) {
        console.error('❌ Error checking all content blocks:', allBlocksError);
      } else if (allBlocks && allBlocks.length > 0) {
        console.log('\n📋 Sample content blocks from other posts:');
        allBlocks.forEach((block, index) => {
          console.log(`   ${index + 1}. Post ID: ${block.blog_post_id}, Type: ${block.block_type}`);
        });
      } else {
        console.log('❌ No content blocks found in the entire database');
      }
      
      return;
    }
    
    console.log(`✅ Found ${contentBlocks.length} content blocks:\n`);
    
    contentBlocks.forEach((block, index) => {
      console.log(`📝 Block ${index + 1}:`);
      console.log(`   ID: ${block.id}`);
      console.log(`   Type: ${block.block_type}`);
      console.log(`   Sort Order: ${block.sort_order}`);
      console.log(`   Content Preview: ${JSON.stringify(block.content).substring(0, 100)}...`);
      console.log(`   Created: ${block.created_at}`);
      console.log('');
    });
    
    // Test the fetch function used by the app
    console.log('🧪 Testing fetchBlogContentBlocksSafe function...\n');
    
    const testFetch = async (blogPostId) => {
      try {
        const { data, error } = await supabase
          .from('sas_blog_content_blocks')
          .select('*')
          .eq('blog_post_id', blogPostId)
          .order('sort_order', { ascending: true });
        
        if (error) {
          console.error('❌ Test fetch error:', error);
          return null;
        }
        
        console.log(`✅ Test fetch successful: ${data?.length || 0} blocks`);
        return data;
      } catch (err) {
        console.error('❌ Test fetch exception:', err);
        return null;
      }
    };
    
    await testFetch(post.id);
    
  } catch (error) {
    console.error('❌ Error during content check:', error);
  }
}

checkBlogContent().catch(console.error);
