import { Metadata } from 'next'

export interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'organization'
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  price?: number
  currency?: string
  availability?: string
  brand?: string
  category?: string
}

// Enhanced Hreflang configuration for international SEO
export interface HreflangConfig {
  code: string
  language: string
  country: string
  currency: string
  currencySymbol: string
  region: 'africa' | 'europe' | 'north-america' | 'oceania' | 'south-america'
  priority: number // 1-10, higher = more important market
  isDefault?: boolean
}

export const hreflangConfigs: Record<string, HreflangConfig> = {
  'x-default': {
    code: 'x-default',
    language: 'English',
    country: 'Global',
    currency: 'USD',
    currencySymbol: '$',
    region: 'africa',
    priority: 10,
    isDefault: true
  },
  'en-US': {
    code: 'en-US',
    language: 'English',
    country: 'United States',
    currency: 'USD',
    currencySymbol: '$',
    region: 'north-america',
    priority: 9
  },
  'en-GB': {
    code: 'en-GB',
    language: 'English',
    country: 'United Kingdom',
    currency: 'GBP',
    currencySymbol: '£',
    region: 'europe',
    priority: 9
  },
  'en-CA': {
    code: 'en-CA',
    language: 'English',
    country: 'Canada',
    currency: 'CAD',
    currencySymbol: 'C$',
    region: 'north-america',
    priority: 8
  },
  'en-AU': {
    code: 'en-AU',
    language: 'English',
    country: 'Australia',
    currency: 'AUD',
    currencySymbol: 'A$',
    region: 'oceania',
    priority: 8
  },
  'en-ZA': {
    code: 'en-ZA',
    language: 'English',
    country: 'South Africa',
    currency: 'ZAR',
    currencySymbol: 'R',
    region: 'africa',
    priority: 9
  },
  'en-KE': {
    code: 'en-KE',
    language: 'English',
    country: 'Kenya',
    currency: 'KES',
    currencySymbol: 'KSh',
    region: 'africa',
    priority: 8
  },
  'en-RW': {
    code: 'en-RW',
    language: 'English',
    country: 'Rwanda',
    currency: 'RWF',
    currencySymbol: 'FRw',
    region: 'africa',
    priority: 7
  },
  'en-NG': {
    code: 'en-NG',
    language: 'English',
    country: 'Nigeria',
    currency: 'NGN',
    currencySymbol: '₦',
    region: 'africa',
    priority: 7
  },
  'en-IE': {
    code: 'en-IE',
    language: 'English',
    country: 'Ireland',
    currency: 'EUR',
    currencySymbol: '€',
    region: 'europe',
    priority: 7
  },
  'en-NZ': {
    code: 'en-NZ',
    language: 'English',
    country: 'New Zealand',
    currency: 'NZD',
    currencySymbol: 'NZ$',
    region: 'oceania',
    priority: 6
  },
  'en-DE': {
    code: 'en-DE',
    language: 'English',
    country: 'Germany',
    currency: 'EUR',
    currencySymbol: '€',
    region: 'europe',
    priority: 6
  },
  'en-NL': {
    code: 'en-NL',
    language: 'English',
    country: 'Netherlands',
    currency: 'EUR',
    currencySymbol: '€',
    region: 'europe',
    priority: 5
  },
  'en-CH': {
    code: 'en-CH',
    language: 'English',
    country: 'Switzerland',
    currency: 'CHF',
    currencySymbol: 'CHF',
    region: 'europe',
    priority: 5
  }
}

export const hreflangRegions = Object.keys(hreflangConfigs) as const
export type HreflangRegion = typeof hreflangRegions[number]

export const defaultSEO = {
  siteName: 'Swift Africa Safaris',
  siteUrl: 'https://swiftafricasafaris.com',
  defaultTitle: 'Swift Africa Safaris - Premier African Safari Tours & Adventures',
  defaultDescription: 'Experience unforgettable African safari adventures with Swift Africa Safaris. Expert-guided tours across Rwanda, Tanzania, Uganda, and South Africa. Book your dream safari today!',
  defaultImage: '/images/common/swift-africa-safaris-best-tour-operator-icon.png',
  twitterHandle: '@SwiftAfricaSafaris',
  facebookPage: 'http://web.facebook.com/profile.php?id=61550743088246',
  instagramHandle: '@swiftafricasafaris',
  linkedinPage: 'https://linkedin.com/company/swift-africa-safaris'
}

/**
 * Enhanced hreflang generation for international SEO
 * @param currentPath - The current page path (e.g., '/about', '/package/safari-tour')
 * @param options - Additional options for hreflang generation
 * @returns Array of hreflang link objects for Next.js metadata
 */
export function generateHreflangTags(currentPath: string = '/', options: {
  excludeRegions?: HreflangRegion[]
  priorityRegionsOnly?: boolean
  includeSubdomains?: boolean
} = {}) {
  const { excludeRegions = [], priorityRegionsOnly = false, includeSubdomains = false } = options

  // Ensure path starts with /
  const normalizedPath = currentPath.startsWith('/') ? currentPath : `/${currentPath}`

  let regionsToInclude = hreflangRegions.filter(region => !excludeRegions.includes(region))

  // If priority regions only, filter by priority >= 7
  if (priorityRegionsOnly) {
    regionsToInclude = regionsToInclude.filter(region =>
      hreflangConfigs[region]?.priority >= 7
    )
  }

  return regionsToInclude.map(region => {
    const config = hreflangConfigs[region]
    let baseUrl = defaultSEO.siteUrl

    // Use subdomains for major markets if enabled
    if (includeSubdomains && config && !config.isDefault) {
      const countryCode = region.split('-')[1]?.toLowerCase()
      if (countryCode && ['us', 'gb', 'ca', 'au', 'za'].includes(countryCode)) {
        baseUrl = `https://${countryCode}.swiftafricasafaris.com`
      }
    }

    return {
      rel: 'alternate' as const,
      hrefLang: region,
      href: `${baseUrl}${normalizedPath}`,
      title: config ? `${config.country} - ${config.language}` : region
    }
  })
}

/**
 * Get hreflang configuration for a specific region
 */
export function getHreflangConfig(region: HreflangRegion): HreflangConfig | undefined {
  return hreflangConfigs[region]
}

/**
 * Get regions by geographic area
 */
export function getRegionsByArea(area: 'africa' | 'europe' | 'north-america' | 'oceania' | 'south-america'): HreflangRegion[] {
  return hreflangRegions.filter(region =>
    hreflangConfigs[region]?.region === area
  )
}

/**
 * Get priority regions (priority >= 7)
 */
export function getPriorityRegions(): HreflangRegion[] {
  return hreflangRegions.filter(region =>
    hreflangConfigs[region]?.priority >= 7
  )
}

/**
 * Detect user's preferred region from headers
 */
export function detectUserRegion(acceptLanguage?: string, countryCode?: string): HreflangRegion {
  // Default fallback
  let detectedRegion: HreflangRegion = 'x-default'

  // Try to match by country code first (most accurate)
  if (countryCode) {
    const regionByCountry = hreflangRegions.find(region =>
      region.toLowerCase().endsWith(`-${countryCode.toLowerCase()}`)
    )
    if (regionByCountry) {
      return regionByCountry
    }
  }

  // Try to match by Accept-Language header
  if (acceptLanguage) {
    const languages = acceptLanguage.split(',').map(lang =>
      lang.split(';')[0].trim().toLowerCase()
    )

    for (const lang of languages) {
      // Look for exact match (e.g., "en-us")
      const exactMatch = hreflangRegions.find(region =>
        region.toLowerCase() === lang
      )
      if (exactMatch) {
        return exactMatch
      }

      // Look for language match (e.g., "en" matches "en-us")
      if (lang.startsWith('en')) {
        // Prefer major English-speaking countries
        const preferredOrder = ['en-US', 'en-GB', 'en-CA', 'en-AU', 'en-ZA']
        for (const preferred of preferredOrder) {
          if (hreflangRegions.includes(preferred as HreflangRegion)) {
            return preferred as HreflangRegion
          }
        }
      }
    }
  }

  return detectedRegion
}

/**
 * Format price for specific region
 */
export function formatPriceForRegion(price: number, region: HreflangRegion): string {
  const config = hreflangConfigs[region]
  if (!config) return `$${price}`

  // Simple currency conversion (in production, use real exchange rates)
  const exchangeRates: Record<string, number> = {
    'USD': 1,
    'GBP': 0.79,
    'EUR': 0.85,
    'CAD': 1.25,
    'AUD': 1.35,
    'ZAR': 18.5,
    'KES': 110,
    'RWF': 1000,
    'NGN': 410,
    'NZD': 1.45,
    'CHF': 0.92
  }

  const convertedPrice = price * (exchangeRates[config.currency] || 1)

  // Format with proper currency symbol and locale
  try {
    const locale = region === 'x-default' ? 'en-US' : region
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: config.currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(convertedPrice)
  } catch {
    // Fallback formatting
    return `${config.currencySymbol}${Math.round(convertedPrice).toLocaleString()}`
  }
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    image = defaultSEO.defaultImage,
    url,
    type = 'website',
    author,
    publishedTime,
    modifiedTime,
    section,
    tags = []
  } = config

  const fullTitle = title.includes(defaultSEO.siteName) 
    ? title 
    : `${title} | ${defaultSEO.siteName}`
  
  const fullUrl = url ? `${defaultSEO.siteUrl}${url}` : defaultSEO.siteUrl
  const fullImage = image.startsWith('http') ? image : `${defaultSEO.siteUrl}${image}`

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: author ? [{ name: author }] : [{ name: defaultSEO.siteName }],
    creator: defaultSEO.siteName,
    publisher: defaultSEO.siteName,
    alternates: {
      canonical: fullUrl,
      languages: generateHreflangTags(url || '/', { priorityRegionsOnly: true }).reduce((acc, tag) => {
        acc[tag.hrefLang] = tag.href
        return acc
      }, {} as Record<string, string>)
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: type as 'website' | 'article',
      locale: 'en_US',
      url: fullUrl,
      title: fullTitle,
      description,
      siteName: defaultSEO.siteName,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullImage],
      creator: defaultSEO.twitterHandle,
      site: defaultSEO.twitterHandle,
    },
    alternates: {
      canonical: fullUrl,
      languages: generateHreflangTags(url || '/').reduce((acc, tag) => {
        acc[tag.hrefLang] = tag.href
        return acc
      }, {} as Record<string, string>)
    },
  }

  // Add article-specific metadata
  if (type === 'article' && publishedTime) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      section,
      tags,
      authors: author ? [author] : undefined,
    }
  }

  return metadata
}

export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'TravelAgency',
    name: defaultSEO.siteName,
    description: defaultSEO.defaultDescription,
    url: defaultSEO.siteUrl,
    logo: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`,
    image: `${defaultSEO.siteUrl}${defaultSEO.defaultImage}`,
    telephone: '+250 788 123 456',
    email: '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Kigali, Rwanda',
      addressLocality: 'Kigali',
      addressCountry: 'RW'
    },
    sameAs: [
      defaultSEO.facebookPage,
      defaultSEO.linkedinPage,
      `https://instagram.com/${defaultSEO.instagramHandle.replace('@', '')}`,
      `https://twitter.com/${defaultSEO.twitterHandle.replace('@', '')}`
    ],
    serviceArea: {
      '@type': 'Place',
      name: 'East Africa',
      description: 'Rwanda, Tanzania, Uganda, Kenya, South Africa'
    },
    priceRange: '$$-$$$',
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '150',
      bestRating: '5',
      worstRating: '1'
    }
  }
}

export function generateWebsiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: defaultSEO.siteName,
    description: defaultSEO.defaultDescription,
    url: defaultSEO.siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${defaultSEO.siteUrl}/search?q={search_term_string}`
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`
      }
    }
  }
}

export function generateBreadcrumbSchema(items: Array<{ name: string; url: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${defaultSEO.siteUrl}${item.url}`
    }))
  }
}

// Product Schema for Tour Packages
export function generateProductSchema(config: {
  name: string
  description: string
  image: string
  price: number
  currency: string
  availability: string
  category: string
  url: string
  brand?: string
  sku?: string
  reviews?: Array<{
    rating: number
    reviewBody: string
    author: string
    datePublished: string
  }>
}) {
  const {
    name,
    description,
    image,
    price,
    currency,
    availability,
    category,
    url,
    brand = defaultSEO.siteName,
    sku,
    reviews = []
  } = config

  const aggregateRating = reviews.length > 0 ? {
    '@type': 'AggregateRating',
    ratingValue: (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1),
    reviewCount: reviews.length,
    bestRating: 5,
    worstRating: 1
  } : undefined

  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name,
    description,
    image: image.startsWith('http') ? image : `${defaultSEO.siteUrl}${image}`,
    brand: {
      '@type': 'Brand',
      name: brand
    },
    category,
    sku: sku || `tour-${url.split('/').pop()}`,
    offers: {
      '@type': 'Offer',
      price,
      priceCurrency: currency,
      availability: `https://schema.org/${availability}`,
      url: `${defaultSEO.siteUrl}${url}`,
      seller: {
        '@type': 'Organization',
        name: defaultSEO.siteName
      }
    },
    ...(aggregateRating && { aggregateRating }),
    ...(reviews.length > 0 && {
      review: reviews.map(review => ({
        '@type': 'Review',
        reviewRating: {
          '@type': 'Rating',
          ratingValue: review.rating,
          bestRating: 5,
          worstRating: 1
        },
        reviewBody: review.reviewBody,
        author: {
          '@type': 'Person',
          name: review.author
        },
        datePublished: review.datePublished
      }))
    })
  }
}

// Local Business Schema
export function generateLocalBusinessSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'TravelAgency',
    name: defaultSEO.siteName,
    description: defaultSEO.defaultDescription,
    url: defaultSEO.siteUrl,
    logo: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`,
    image: `${defaultSEO.siteUrl}${defaultSEO.defaultImage}`,
    telephone: '+250 788 123 456',
    email: '<EMAIL>',
    address: {
      '@type': 'PostalAddress',
      streetAddress: 'Kigali, Rwanda',
      addressLocality: 'Kigali',
      addressRegion: 'Kigali Province',
      postalCode: '00000',
      addressCountry: 'RW'
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: -1.9441,
      longitude: 30.0619
    },
    openingHours: [
      'Mo-Fr 08:00-18:00',
      'Sa 09:00-17:00'
    ],
    priceRange: '$$-$$$',
    servesCuisine: 'African Safari Tours',
    areaServed: [
      {
        '@type': 'Country',
        name: 'Rwanda'
      },
      {
        '@type': 'Country',
        name: 'Uganda'
      },
      {
        '@type': 'Country',
        name: 'Tanzania'
      },
      {
        '@type': 'Country',
        name: 'South Africa'
      }
    ],
    sameAs: [
      defaultSEO.facebookPage,
      defaultSEO.linkedinPage,
      `https://instagram.com/${defaultSEO.instagramHandle.replace('@', '')}`,
      `https://twitter.com/${defaultSEO.twitterHandle.replace('@', '')}`
    ]
  }
}

// Review Schema
export function generateReviewSchema(config: {
  itemName: string
  itemType: string
  rating: number
  reviewBody: string
  author: string
  datePublished: string
  url: string
}) {
  const { itemName, itemType, rating, reviewBody, author, datePublished, url } = config

  return {
    '@context': 'https://schema.org',
    '@type': 'Review',
    itemReviewed: {
      '@type': itemType,
      name: itemName
    },
    reviewRating: {
      '@type': 'Rating',
      ratingValue: rating,
      bestRating: 5,
      worstRating: 1
    },
    reviewBody,
    author: {
      '@type': 'Person',
      name: author
    },
    datePublished,
    url: `${defaultSEO.siteUrl}${url}`
  }
}

export function generateArticleSchema(config: {
  title: string
  description: string
  author: string
  publishedTime: string
  modifiedTime?: string
  image: string
  url: string
  tags?: string[]
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: config.title,
    description: config.description,
    image: config.image.startsWith('http') ? config.image : `${defaultSEO.siteUrl}${config.image}`,
    author: {
      '@type': 'Person',
      name: config.author
    },
    publisher: {
      '@type': 'Organization',
      name: defaultSEO.siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${defaultSEO.siteUrl}/images/common/swift-africa-safaris-best-tour-operator-icon.png`
      }
    },
    datePublished: config.publishedTime,
    dateModified: config.modifiedTime || config.publishedTime,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${defaultSEO.siteUrl}${config.url}`
    },
    keywords: config.tags?.join(', ')
  }
}

export function generateTourSchema(config: {
  name: string
  description: string
  image: string
  price: number
  currency: string
  duration: string
  location: string
  url: string
  rating?: number
  reviewCount?: number
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'TouristTrip',
    name: config.name,
    description: config.description,
    image: config.image.startsWith('http') ? config.image : `${defaultSEO.siteUrl}${config.image}`,
    touristType: 'Safari Enthusiast',
    itinerary: {
      '@type': 'ItemList',
      name: `${config.name} Itinerary`,
      description: config.description
    },
    offers: {
      '@type': 'Offer',
      price: config.price,
      priceCurrency: config.currency,
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'TravelAgency',
        name: defaultSEO.siteName
      },
      url: `${defaultSEO.siteUrl}${config.url}`
    },
    provider: {
      '@type': 'TravelAgency',
      name: defaultSEO.siteName,
      url: defaultSEO.siteUrl
    },
    location: {
      '@type': 'Place',
      name: config.location,
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'East Africa'
      }
    },
    duration: config.duration,
    aggregateRating: config.rating ? {
      '@type': 'AggregateRating',
      ratingValue: config.rating,
      reviewCount: config.reviewCount || 1,
      bestRating: 5,
      worstRating: 1
    } : undefined
  }
}
