import type { Metadata } from 'next';
import Navbar from '@/components/header';
import HeroComponent from '@/components/heroes';
import { heroData } from '@/components/data/heroesdata';
import Footer from '@/components/footer';
import WelcomeSection from '@/components/contact/welcomeSection';
import StructuredData from '@/components/common/StructuredData';
import { generateMetadata, generateBreadcrumbSchema, defaultSEO } from '@/lib/seo';

export const metadata: Metadata = generateMetadata({
  title: 'Swift Africa Safaris - Plan Your African Safari with Experts',
  description: 'Start your journey with us. Contact Swift Africa Safaris for expert planning, custom tours, and personal assistance across Africa',
  url: '/contact',
  keywords: [
    'contact Swift Africa Safaris',
    'safari consultation',
    'African safari planning',
    'custom safari itinerary',
    'safari booking inquiry',
    'gorilla trekking booking',
    'Tanzania safari contact',
    'Rwanda safari inquiry',
    'Uganda safari planning',
    'safari travel agent',
    'African tour operator contact',
    'safari quote request',
    'wildlife tour consultation'
  ],
  type: 'website'
});

const Contact = () => {
  const defaultProps = {
    title: heroData.contact.title,
    subtitle: heroData.contact.subtitle,
    backgroundImage: heroData.contact.backgroundImage
  };

  const breadcrumbSchema = generateBreadcrumbSchema([
    { name: 'Home', url: '/' },
    { name: 'Contact Us', url: '/contact' }
  ]);

  const contactPageSchema = {
    '@context': 'https://schema.org',
    '@type': 'ContactPage',
    name: 'Swift Africa Safaris - Plan Your African Safari with Experts',
    description: 'Start your journey with us. Contact Swift Africa Safaris for expert planning, custom tours, and personal assistance across Africa',
    url: `${defaultSEO.siteUrl}/contact`,
    keywords: [
      'contact Swift Africa Safaris',
      'safari consultation',
      'African safari planning',
      'custom safari itinerary',
      'safari booking inquiry',
      'gorilla trekking booking',
      'Tanzania safari contact',
      'Rwanda safari inquiry',
      'Uganda safari planning',
      'safari travel agent',
      'African tour operator contact',
      'safari quote request',
      'wildlife tour consultation'
    ],
    mainEntity: {
      '@type': 'TravelAgency',
      name: defaultSEO.siteName,
      telephone: '+250 788 123 456',
      email: '<EMAIL>',
      description: 'Start your journey with us. Contact Swift Africa Safaris for expert planning, custom tours, and personal assistance across Africa',
      address: {
        '@type': 'PostalAddress',
        streetAddress: 'Kigali, Rwanda',
        addressLocality: 'Kigali',
        addressCountry: 'RW'
      },
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+250 788 123 456',
        contactType: 'customer service',
        availableLanguage: ['English', 'French', 'Kinyarwanda'],
        hoursAvailable: {
          '@type': 'OpeningHoursSpecification',
          dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
          opens: '08:00',
          closes: '18:00'
        }
      }
    }
  };

  return (
    <div className="bg-[#efe9e0]">
      <StructuredData data={[breadcrumbSchema, contactPageSchema]} />
      <Navbar />
      <main>
        <HeroComponent {...defaultProps} />
        <WelcomeSection />
      </main>
      <Footer />
    </div>
  );
};

export default Contact;