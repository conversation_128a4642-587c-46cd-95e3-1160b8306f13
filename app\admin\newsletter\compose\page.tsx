'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Send, Eye, Save, Mail, Users } from 'lucide-react';

export default function ComposeNewsletterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    subject: '',
    content: '',
    contentType: 'html'
  });
  const [loading, setLoading] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [subscriberCount, setSubscriberCount] = useState(0);

  // Fetch subscriber count
  useEffect(() => {
    const fetchSubscriberCount = async () => {
      try {
        const response = await fetch('/api/admin/newsletter/subscribers?status=subscribed&limit=1');
        const data = await response.json();
        if (data.success) {
          setSubscriberCount(data.pagination.totalSubscribers);
        }
      } catch (error) {
        console.error('Error fetching subscriber count:', error);
      }
    };

    fetchSubscriberCount();
  }, []);

  // Handle form submission
  const handleSendNewsletter = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.subject.trim() || !formData.content.trim()) {
      alert('Please fill in both subject and content');
      return;
    }

    if (!confirm(`Are you sure you want to send this newsletter to ${subscriberCount} subscribers?`)) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/newsletter/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();
      if (data.success) {
        alert(`Newsletter sent successfully to ${data.data.successfulSends} subscribers!`);
        setFormData({ subject: '', content: '', contentType: 'html' });
        router.push('/admin/newsletter');
      } else {
        alert(data.error || 'Failed to send newsletter');
      }
    } catch (error) {
      console.error('Error sending newsletter:', error);
      alert('Failed to send newsletter');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Generate preview content
  const getPreviewContent = () => {
    if (formData.contentType === 'html') {
      return formData.content;
    } else {
      return formData.content.replace(/\n/g, '<br>');
    }
  };

  return (
    <main className="ml-16 pt-16 min-h-screen" style={{ backgroundColor: 'var(--background)' }}>
      <div className="p-6">
        {/* Header */}
        <div className="mb-8">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <button
                  onClick={() => router.back()}
                  className="mr-4 p-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <ArrowLeft className="w-6 h-6" />
                </button>
                <div className="flex items-center">
                  <div 
                    className="p-3 rounded-xl text-white mr-4"
                    style={{ backgroundColor: 'var(--accent)' }}
                  >
                    <Mail className="w-8 h-8" />
                  </div>
                  <div>
                    <h1 
                      className="text-4xl font-bold"
                      style={{ color: 'var(--text)' }}
                    >
                      Compose Newsletter
                    </h1>
                    <p className="text-gray-600 mt-2 text-lg">
                      Create and send newsletter to {subscriberCount} subscribers
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="w-4 h-4" />
                  <span>{subscriberCount} subscribers</span>
                </div>
                <button
                  onClick={() => setPreviewMode(!previewMode)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
                >
                  <Eye className="w-4 h-4" />
                  {previewMode ? 'Edit' : 'Preview'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Form */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <form onSubmit={handleSendNewsletter} className="p-8">
            {!previewMode ? (
              <>
                {/* Subject */}
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Subject Line
                  </label>
                  <input
                    type="text"
                    value={formData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Enter newsletter subject..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                    required
                  />
                </div>

                {/* Content Type */}
                <div className="mb-6">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Content Type
                  </label>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="html"
                        checked={formData.contentType === 'html'}
                        onChange={(e) => handleInputChange('contentType', e.target.value)}
                        className="mr-2"
                      />
                      HTML
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="text"
                        checked={formData.contentType === 'text'}
                        onChange={(e) => handleInputChange('contentType', e.target.value)}
                        className="mr-2"
                      />
                      Plain Text
                    </label>
                  </div>
                </div>

                {/* Content */}
                <div className="mb-8">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Newsletter Content
                  </label>
                  <textarea
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    placeholder={formData.contentType === 'html' 
                      ? "Enter HTML content for your newsletter..." 
                      : "Enter plain text content for your newsletter..."
                    }
                    rows={20}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
                    required
                  />
                  {formData.contentType === 'html' && (
                    <p className="mt-2 text-sm text-gray-600">
                      You can use HTML tags for formatting. Unsubscribe link will be automatically added.
                    </p>
                  )}
                </div>
              </>
            ) : (
              /* Preview Mode */
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Subject Preview</h3>
                  <div className="p-4 bg-gray-50 rounded-lg border">
                    <p className="text-lg font-medium">{formData.subject || 'No subject'}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Content Preview</h3>
                  <div className="p-6 bg-gray-50 rounded-lg border min-h-96">
                    {formData.content ? (
                      <div 
                        dangerouslySetInnerHTML={{ 
                          __html: getPreviewContent() 
                        }} 
                      />
                    ) : (
                      <p className="text-gray-500 italic">No content to preview</p>
                    )}
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <p className="text-sm text-yellow-800">
                    <strong>Note:</strong> Unsubscribe link and footer will be automatically added to the actual newsletter.
                  </p>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => router.push('/admin/newsletter')}
                className="px-6 py-3 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>

              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => setPreviewMode(!previewMode)}
                  className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center gap-2"
                >
                  <Eye className="w-4 h-4" />
                  {previewMode ? 'Edit' : 'Preview'}
                </button>
                
                <button
                  type="submit"
                  disabled={loading || !formData.subject.trim() || !formData.content.trim()}
                  className="px-6 py-3 text-white rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  style={{ backgroundColor: 'var(--light-green)' }}
                >
                  <Send className="w-4 h-4" />
                  {loading ? 'Sending...' : `Send to ${subscriberCount} Subscribers`}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
}
