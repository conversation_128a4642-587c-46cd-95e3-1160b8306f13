import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nPlease check your .env.local file and ensure these variables are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createStorageBuckets() {
  console.log('🚀 Creating Supabase storage buckets...\n');

  const buckets = [
    {
      name: 'package-images',
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    },
    {
      name: 'blog-images',
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    },
    {
      name: 'user-avatars',
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      fileSizeLimit: 2097152 // 2MB
    },
    {
      name: 'destination-images',
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
      fileSizeLimit: 5242880 // 5MB
    }
  ];

  for (const bucket of buckets) {
    try {
      console.log(`📦 Creating bucket: ${bucket.name}`);
      
      // Check if bucket already exists
      const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();
      
      if (listError) {
        console.error(`❌ Error listing buckets: ${listError.message}`);
        continue;
      }

      const bucketExists = existingBuckets.some(b => b.name === bucket.name);
      
      if (bucketExists) {
        console.log(`✅ Bucket '${bucket.name}' already exists`);
        continue;
      }

      // Create the bucket
      const { error } = await supabase.storage.createBucket(bucket.name, {
        public: bucket.public,
        allowedMimeTypes: bucket.allowedMimeTypes,
        fileSizeLimit: bucket.fileSizeLimit
      });

      if (error) {
        console.error(`❌ Error creating bucket '${bucket.name}': ${error.message}`);
      } else {
        console.log(`✅ Successfully created bucket '${bucket.name}'`);
      }

    } catch (error) {
      console.error(`❌ Unexpected error creating bucket '${bucket.name}': ${error.message}`);
    }
  }

  console.log('\n🔒 Setting up storage policies...\n');

  // Set up RLS policies for package-images bucket
  try {
    console.log('📋 Setting up policies for package-images bucket...');
    
    // Policy to allow public read access
    const { error: readPolicyError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE POLICY "Public read access for package-images" ON storage.objects
        FOR SELECT USING (bucket_id = 'package-images');
      `
    });

    if (readPolicyError) {
      console.log(`ℹ️  Read policy may already exist: ${readPolicyError.message}`);
    } else {
      console.log('✅ Created public read policy for package-images');
    }

    // Policy to allow authenticated users to upload
    const { error: insertPolicyError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE POLICY "Authenticated users can upload to package-images" ON storage.objects
        FOR INSERT WITH CHECK (bucket_id = 'package-images' AND auth.role() = 'authenticated');
      `
    });

    if (insertPolicyError) {
      console.log(`ℹ️  Insert policy may already exist: ${insertPolicyError.message}`);
    } else {
      console.log('✅ Created upload policy for package-images');
    }

    // Policy to allow authenticated users to update their uploads
    const { error: updatePolicyError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE POLICY "Users can update their uploads in package-images" ON storage.objects
        FOR UPDATE USING (bucket_id = 'package-images' AND auth.uid()::text = (storage.foldername(name))[1]);
      `
    });

    if (updatePolicyError) {
      console.log(`ℹ️  Update policy may already exist: ${updatePolicyError.message}`);
    } else {
      console.log('✅ Created update policy for package-images');
    }

    // Policy to allow authenticated users to delete their uploads
    const { error: deletePolicyError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE POLICY "Users can delete their uploads in package-images" ON storage.objects
        FOR DELETE USING (bucket_id = 'package-images' AND auth.uid()::text = (storage.foldername(name))[1]);
      `
    });

    if (deletePolicyError) {
      console.log(`ℹ️  Delete policy may already exist: ${deletePolicyError.message}`);
    } else {
      console.log('✅ Created delete policy for package-images');
    }

  } catch (error) {
    console.error(`❌ Error setting up storage policies: ${error.message}`);
  }

  console.log('\n🎉 Storage bucket setup completed!');
  console.log('\n📝 Next steps:');
  console.log('   1. Verify buckets were created in your Supabase dashboard');
  console.log('   2. Test image uploads in your admin dashboard');
  console.log('   3. Check that images are publicly accessible');
}

createStorageBuckets().catch(console.error); 