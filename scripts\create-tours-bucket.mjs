import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createToursBucket() {
  console.log('🎯 CREATING TOURS BUCKET AND UPDATING CONFIGURATIONS\n');

  try {
    // Check if tours bucket already exists
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log('❌ Cannot access storage:', error.message);
    } else {
      const toursBucket = buckets?.find(b => b.name === 'tours');
      
      if (toursBucket) {
        console.log('✅ tours bucket already exists!');
        console.log(`   Public: ${toursBucket.public}`);
        console.log(`   File size limit: ${toursBucket.file_size_limit} bytes`);
      } else {
        console.log('❌ tours bucket not found');
        console.log('\n🔧 FOLLOW THESE STEPS TO CREATE THE TOURS BUCKET:\n');
        
        console.log('📋 STEP-BY-STEP INSTRUCTIONS:');
        console.log('');
        console.log('1️⃣  Open your Supabase Dashboard');
        console.log('   • Go to: https://supabase.com/dashboard');
        console.log('   • Sign in to your account');
        console.log('   • Select your project');
        console.log('');
        console.log('2️⃣  Navigate to Storage');
        console.log('   • Click "Storage" in the left sidebar');
        console.log('');
        console.log('3️⃣  Create New Bucket');
        console.log('   • Click "Create a new bucket" button');
        console.log('');
        console.log('4️⃣  Fill in the Bucket Details');
        console.log('   • Name: tours');
        console.log('   • Public bucket: ✅ CHECK THIS BOX (very important!)');
        console.log('   • File size limit: ******** (10MB)');
        console.log('   • Allowed MIME types: image/jpeg, image/png, image/webp, image/gif');
        console.log('');
        console.log('5️⃣  Create the Bucket');
        console.log('   • Click "Create bucket" or "Save"');
        console.log('');
        console.log('6️⃣  Verify Creation');
        console.log('   • You should see "tours" in your bucket list');
        console.log('   • The bucket should show as "Public"');
        console.log('');
      }
    }

    // Create SQL file for manual creation
    const sqlContent = `-- Create tours bucket (COMPLETELY PUBLIC)
-- Copy and paste this into your Supabase SQL Editor

-- Create the tours bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tours',
  'tours',
  true,
  ********, -- 10MB file size limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- Verify the bucket was created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'tours';`;

    fs.writeFileSync('CREATE_TOURS_BUCKET.sql', sqlContent);
    console.log('📄 SQL file created: CREATE_TOURS_BUCKET.sql');

    console.log('\n🔧 UPDATING ALL NECESSARY FILES...\n');

    // Update the admin tours page
    console.log('📝 Updating admin tours page...');
    
    // Create updated tours page content
    const updatedToursPage = `// Updated tours page with tours bucket
// This will be applied to app/admin/tours/page.tsx

// In the handleImageUpload function, change:
// supabase.storage.from('package-images') to:
// supabase.storage.from('tours')

// In the handleGalleryUpload function, change:
// supabase.storage.from('package-images') to:
// supabase.storage.from('tours')

// All bucket references should be changed from 'package-images' to 'tours'`;

    fs.writeFileSync('TOURS_BUCKET_UPDATES.md', updatedToursPage);
    console.log('📄 Update instructions created: TOURS_BUCKET_UPDATES.md');

    console.log('\n🎯 NEXT STEPS:');
    console.log('1. Create the tours bucket using the dashboard or SQL');
    console.log('2. Update all bucket references from "package-images" to "tours"');
    console.log('3. Test image uploads in your admin dashboard');
    console.log('4. Verify public access works');

    console.log('\n🔍 After creating the bucket, run:');
    console.log('   node scripts/test-tours-bucket.mjs');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createToursBucket().catch(console.error); 