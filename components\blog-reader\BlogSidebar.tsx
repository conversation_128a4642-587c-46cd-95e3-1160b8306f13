'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
interface BlogPost {
  id: string
  title: string
  slug: string
  description: string
  hero_image_url: string
  hero_image_alt: string
  category: string
  published_at: string
  tags: string[]
}

interface BlogSidebarProps {
  currentSlug: string
  currentCategory: string
}

const BlogSidebar: React.FC<BlogSidebarProps> = ({ currentSlug, currentCategory }) => {
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const fetchRelatedPosts = async () => {
    try {
      setLoading(true)
      // Fetch related posts from the same category
      const response = await fetch(`/api/blog?category=${currentCategory}&limit=4&exclude=${currentSlug}`)
      const data = await response.json()

      if (data.success) {
        setRelatedPosts(data.data.posts || [])
      }
    } catch (error) {
      console.error('Error fetching related posts:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (currentCategory && currentSlug) {
      fetchRelatedPosts()
    }
  }, [currentSlug, currentCategory])





  if (loading) {
    return (
      <aside className="space-y-8">
        {/* Loading skeleton for You May Also Like */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="h-6 bg-gray-200 rounded mb-4 animate-pulse"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-3">
                <div className="w-20 h-20 bg-gray-200 rounded-lg animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mt-2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="bg-gradient-to-br from-accent to-accent rounded-xl p-6 text-white" style={{ background: `linear-gradient(to bottom right, var(--accent), var(--accent))` }}>
          <h3 className="text-xl font-bold mb-3">Stay Updated</h3>
          <p className="text-white/80 text-sm mb-4">
            Get the latest travel insights and safari adventures delivered to your inbox.
          </p>

          <form className="space-y-3">
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/30"
            />
            <button
              type="submit"
              className="w-full bg-white text-accent font-semibold py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
              style={{ color: 'var(--accent)' }}
            >
              Subscribe
            </button>
          </form>
        </div>

        {/* Categories */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            Categories
          </h3>

          <div className="space-y-2">
            {['Wildlife', 'Culture', 'Adventure', 'Conservation', 'Travel Tips'].map((category) => (
              <Link
                key={category}
                href={`/blog?category=${category.toLowerCase()}`}
                className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-accent rounded-lg transition-colors"
              >
                {category}
              </Link>
            ))}
          </div>
        </div>
      </aside>
    )
  }

  return (
    <aside className="space-y-8">
      {/* You May Also Like */}
      {relatedPosts.length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
            You May Also Like
          </h3>

          <div className="space-y-4">
            {relatedPosts.map((post) => (
              <Link
                key={post.id}
                href={`/blog/${post.slug}`}
                className="group block"
              >
                <article className="flex gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="relative w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden">
                    <Image
                      src={post.hero_image_url}
                      alt={post.hero_image_alt || post.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="80px"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <h4 className="font-semibold text-gray-900 text-sm leading-tight mb-1 group-hover:text-accent transition-colors line-clamp-2">
                      {post.title}
                    </h4>
                    <div className="flex items-center gap-2 text-xs text-gray-500 mb-2">
                      <time>{formatDate(post.published_at)}</time>
                    </div>
                    <div>
                      <span className="inline-block bg-accent/10 text-accent px-2 py-1 rounded-full text-xs font-medium">
                        {post.category}
                      </span>
                    </div>
                  </div>
                </article>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Newsletter Signup */}
      <div className="bg-gradient-to-br from-accent to-accent rounded-xl p-6 text-white" style={{ background: `linear-gradient(to bottom right, var(--accent), var(--accent))` }}>
        <h3 className="text-xl font-bold mb-3">Stay Updated</h3>
        <p className="text-white/80 text-sm mb-4">
          Get the latest travel insights and safari adventures delivered to your inbox.
        </p>

        <form className="space-y-3">
          <input
            type="email"
            placeholder="Enter your email"
            className="w-full px-4 py-2 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white/30"
          />
          <button
            type="submit"
            className="w-full bg-white text-accent font-semibold py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
            style={{ color: 'var(--accent)' }}
          >
            Subscribe
          </button>
        </form>
      </div>

      {/* Categories */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          Categories
        </h3>
        
        <div className="space-y-2">
          {['Wildlife', 'Culture', 'Adventure', 'Conservation', 'Travel Tips'].map((category) => (
            <Link
              key={category}
              href={`/blog?category=${category.toLowerCase()}`}
              className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-accent rounded-lg transition-colors"
            >
              {category}
            </Link>
          ))}
        </div>
      </div>
    </aside>
  )
}

export default BlogSidebar
