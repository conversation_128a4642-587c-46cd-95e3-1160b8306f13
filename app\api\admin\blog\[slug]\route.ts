import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';
import { validateRequestSize, validateBlogContentSize, getContentSizeReductionTips } from '@/lib/request-utils';

// GET - Fetch single blog post for admin (including drafts)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Fetch blog post (admin can see all statuses)
    const { data: post, error: postError } = await supabaseAdmin
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count,
        seo_title,
        seo_description,
        seo_keywords,
        og_title,
        og_description,
        og_image_url,
        canonical_url,
        robots_index,
        robots_follow,
        schema_data,
        content
      `)
      .eq('slug', slug)
      .is('deleted_at', null)
      .single();

    if (postError || !post) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Fetch content blocks from the separate table
    const { data: contentBlocks, error: contentError } = await supabaseAdmin
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true });

    if (contentError) {
      console.error('Content blocks error:', contentError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch blog content' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        ...post,
        content: contentBlocks || []
      }
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update blog post (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Validate request size
    const sizeValidation = validateRequestSize(request);
    if (!sizeValidation.isValid) {
      const tips = getContentSizeReductionTips();
      return NextResponse.json(
        {
          success: false,
          error: sizeValidation.error,
          tips: tips
        },
        { status: 413 }
      );
    }

    const body = await request.json();

    // Additional validation on the parsed content
    const contentValidation = validateBlogContentSize(body);
    if (!contentValidation.isValid) {
      const tips = getContentSizeReductionTips();
      return NextResponse.json(
        {
          success: false,
          error: contentValidation.error,
          tips: tips
        },
        { status: 413 }
      );
    }

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get existing post
    const { data: existingPost, error: fetchError } = await supabaseAdmin
      .from('sas_blog_posts')
      .select('id, status')
      .eq('slug', slug)
      .is('deleted_at', null)
      .single();

    if (fetchError || !existingPost) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      title: body.title,
      description: body.description,
      hero_image_url: body.hero_image_url,
      hero_image_alt: body.hero_image_alt,
      category: body.category,
      tags: body.tags || [],
      status: body.status || existingPost.status,
      seo_title: body.seo_title || null,
      seo_description: body.seo_description || null,
      seo_keywords: body.seo_keywords || [],
      og_title: body.og_title || null,
      og_description: body.og_description || null,
      og_image_url: body.og_image_url || null,
      canonical_url: body.canonical_url || null,
      robots_index: body.robots_index || 'index',
      robots_follow: body.robots_follow || 'follow',
      schema_data: body.schema_data || null
    };

    // Handle slug update if provided and different
    if (body.slug && body.slug !== slug) {
      // Check if new slug already exists
      const { data: slugExists } = await supabaseAdmin
        .from('sas_blog_posts')
        .select('id')
        .eq('slug', body.slug)
        .neq('id', existingPost.id)
        .is('deleted_at', null)
        .single();

      if (slugExists) {
        return NextResponse.json(
          { success: false, error: 'Slug already exists' },
          { status: 400 }
        );
      }

      updateData.slug = body.slug;
    }

    // Set published_at if status is changing to published
    if (body.status === 'published' && existingPost.status !== 'published') {
      updateData.published_at = new Date().toISOString();
    } else if (body.status !== 'published') {
      updateData.published_at = null;
    }

    // Update blog post
    const { data: updatedPost, error: updateError } = await supabaseAdmin
      .from('sas_blog_posts')
      .update(updateData)
      .eq('id', existingPost.id)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return NextResponse.json(
        { success: false, error: 'Failed to update blog post' },
        { status: 500 }
      );
    }

    // Update content blocks if provided
    if (body.content && Array.isArray(body.content)) {
      // Delete existing content blocks
      const { error: deleteError } = await supabaseAdmin
        .from('sas_blog_content_blocks')
        .delete()
        .eq('blog_post_id', existingPost.id);

      if (deleteError) {
        console.error('Delete content blocks error:', deleteError);
      }

      // Insert new content blocks
      if (body.content.length > 0) {
        // Map editor types to database types
        const typeMapping: { [key: string]: string } = {
          'heading2': 'h2',
          'heading3': 'h3',
          'heading4': 'h4',
          'heading5': 'h5',
          'heading6': 'h6',
          'bulleted-list': 'listing',
          'numbered-list': 'listing',
          'paragraph': 'paragraph',
          'image': 'image',
          'video': 'video',
          'quote': 'quote',
          'divider': 'divider'
        };

        const contentBlocks = body.content.map((block: any, index: number) => {
          const blockType = block.type || block.block_type;
          const mappedType = typeMapping[blockType] || blockType;

          return {
            blog_post_id: existingPost.id,
            block_type: mappedType,
            content: block.content,
            sort_order: block.sort_order || index
          };
        });

        const { error: contentError } = await supabaseAdmin
          .from('sas_blog_content_blocks')
          .insert(contentBlocks);

        if (contentError) {
          console.error('Content blocks error:', contentError);
          console.error('Content blocks data:', contentBlocks);
          return NextResponse.json(
            { success: false, error: `Failed to insert content blocks: ${contentError.message}` },
            { status: 500 }
          );
        }
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedPost,
      message: 'Blog post updated successfully'
    });

  } catch (error) {
    console.error('API error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('PayloadTooLargeError') || error.message.includes('request entity too large')) {
        const tips = getContentSizeReductionTips();
        return NextResponse.json(
          {
            success: false,
            error: 'Blog content is too large. Please reduce the content size or split into multiple posts.',
            tips: tips
          },
          { status: 413 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete blog post (admin only) - Soft delete
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Validate and extract slug parameter
    const resolvedParams = await params;
    const { slug } = resolvedParams;

    // Validate slug parameter
    if (!slug || typeof slug !== 'string' || slug.trim() === '') {
      return NextResponse.json(
        { success: false, error: 'Invalid slug parameter' },
        { status: 400 }
      );
    }

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { success: false, error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // First, check if the blog post exists and is not already deleted
    const { data: existingPost, error: fetchError } = await supabaseAdmin
      .from('sas_blog_posts')
      .select('id, title, slug, status, deleted_at')
      .eq('slug', slug)
      .single();

    if (fetchError) {
      return NextResponse.json(
        { success: false, error: `Blog post not found: ${fetchError.message}` },
        { status: 404 }
      );
    }

    if (!existingPost) {
      return NextResponse.json(
        { success: false, error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Check if already deleted
    if (existingPost.deleted_at) {
      return NextResponse.json(
        { success: false, error: 'Blog post is already deleted' },
        { status: 400 }
      );
    }

    // Perform soft delete
    const deleteTimestamp = new Date().toISOString();

    const { data: updateResult, error: deleteError } = await supabaseAdmin
      .from('sas_blog_posts')
      .update({
        deleted_at: deleteTimestamp,
        updated_at: deleteTimestamp
      })
      .eq('id', existingPost.id)
      .select('id, slug, deleted_at');

    if (deleteError) {
      return NextResponse.json(
        { success: false, error: `Failed to delete blog post: ${deleteError.message}` },
        { status: 500 }
      );
    }

    if (!updateResult || updateResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Failed to delete blog post - no rows affected' },
        { status: 500 }
      );
    }

    // Create success response
    const response = NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully',
      data: {
        id: existingPost.id,
        slug: existingPost.slug,
        deleted_at: deleteTimestamp
      }
    });

    // Add cache-busting headers
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('Unexpected error in DELETE API:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
