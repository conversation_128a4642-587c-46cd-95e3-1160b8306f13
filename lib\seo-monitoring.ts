/**
 * SEO Monitoring and Reporting System
 * Tracks SEO performance, crawl errors, and optimization opportunities
 */

import { supabase } from './supabase'

export interface SEOMetric {
  url: string
  metric_type: 'crawl' | 'performance' | 'indexing' | 'error'
  metric_name: string
  metric_value: number | string
  user_agent?: string
  timestamp: string
  additional_data?: Record<string, any>
}

export interface CrawlEvent {
  url: string
  user_agent: string
  status_code: number
  response_time: number
  bot_type: 'search_engine' | 'social' | 'other'
  timestamp: string
}

class SEOMonitor {
  private static instance: SEOMonitor
  private isEnabled = true

  static getInstance(): SEOMonitor {
    if (!SEOMonitor.instance) {
      SEOMonitor.instance = new SEOMonitor()
    }
    return SEOMonitor.instance
  }

  // Track bot crawls and indexing events
  async trackCrawl(event: CrawlEvent): Promise<void> {
    if (!this.isEnabled) return

    try {
      const { error } = await supabase
        .from('seo_crawl_events')
        .insert({
          url: event.url,
          user_agent: event.user_agent,
          status_code: event.status_code,
          response_time: event.response_time,
          bot_type: event.bot_type,
          timestamp: event.timestamp
        })

      if (error) {
        console.error('Failed to track crawl event:', error)
      }
    } catch (error) {
      console.error('SEO monitoring error:', error)
    }
  }

  // Track SEO-related metrics
  async trackMetric(metric: SEOMetric): Promise<void> {
    if (!this.isEnabled) return

    try {
      const { error } = await supabase
        .from('seo_metrics')
        .insert({
          url: metric.url,
          metric_type: metric.metric_type,
          metric_name: metric.metric_name,
          metric_value: metric.metric_value,
          user_agent: metric.user_agent,
          timestamp: metric.timestamp,
          additional_data: metric.additional_data
        })

      if (error) {
        console.error('Failed to track SEO metric:', error)
      }
    } catch (error) {
      console.error('SEO monitoring error:', error)
    }
  }

  // Detect and classify bot types
  detectBotType(userAgent: string): 'search_engine' | 'social' | 'other' {
    const searchEngines = /googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot/i
    const socialBots = /facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram/i

    if (searchEngines.test(userAgent)) {
      return 'search_engine'
    } else if (socialBots.test(userAgent)) {
      return 'social'
    }
    return 'other'
  }

  // Track 404 errors for SEO
  async track404Error(url: string, referrer?: string, userAgent?: string): Promise<void> {
    await this.trackMetric({
      url,
      metric_type: 'error',
      metric_name: '404_error',
      metric_value: 1,
      user_agent: userAgent,
      timestamp: new Date().toISOString(),
      additional_data: { referrer }
    })
  }

  // Track redirect chains
  async trackRedirect(fromUrl: string, toUrl: string, statusCode: number): Promise<void> {
    await this.trackMetric({
      url: fromUrl,
      metric_type: 'indexing',
      metric_name: 'redirect',
      metric_value: statusCode,
      timestamp: new Date().toISOString(),
      additional_data: { redirect_to: toUrl }
    })
  }

  // Track page load performance for SEO
  async trackPagePerformance(url: string, metrics: {
    lcp?: number
    fid?: number
    cls?: number
    ttfb?: number
  }): Promise<void> {
    for (const [metricName, value] of Object.entries(metrics)) {
      if (value !== undefined) {
        await this.trackMetric({
          url,
          metric_type: 'performance',
          metric_name: metricName,
          metric_value: value,
          timestamp: new Date().toISOString()
        })
      }
    }
  }

  // Get SEO performance summary
  async getSEOSummary(days: number = 7): Promise<{
    crawl_events: number
    unique_pages_crawled: number
    avg_response_time: number
    error_rate: number
    top_crawled_pages: Array<{ url: string; count: number }>
    bot_distribution: Record<string, number>
  }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString()

      // Get crawl events
      const { data: crawlEvents } = await supabase
        .from('seo_crawl_events')
        .select('*')
        .gte('timestamp', startDate)

      if (!crawlEvents) return this.getEmptySummary()

      // Calculate metrics
      const totalCrawls = crawlEvents.length
      const uniquePages = new Set(crawlEvents.map(e => e.url)).size
      const avgResponseTime = crawlEvents.reduce((sum, e) => sum + e.response_time, 0) / totalCrawls
      const errorRate = crawlEvents.filter(e => e.status_code >= 400).length / totalCrawls

      // Top crawled pages
      const pageCount = crawlEvents.reduce((acc, event) => {
        acc[event.url] = (acc[event.url] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      const topPages = Object.entries(pageCount)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([url, count]) => ({ url, count }))

      // Bot distribution
      const botDistribution = crawlEvents.reduce((acc, event) => {
        acc[event.bot_type] = (acc[event.bot_type] || 0) + 1
        return acc
      }, {} as Record<string, number>)

      return {
        crawl_events: totalCrawls,
        unique_pages_crawled: uniquePages,
        avg_response_time: Math.round(avgResponseTime),
        error_rate: Math.round(errorRate * 100) / 100,
        top_crawled_pages: topPages,
        bot_distribution: botDistribution
      }

    } catch (error) {
      console.error('Failed to get SEO summary:', error)
      return this.getEmptySummary()
    }
  }

  private getEmptySummary() {
    return {
      crawl_events: 0,
      unique_pages_crawled: 0,
      avg_response_time: 0,
      error_rate: 0,
      top_crawled_pages: [],
      bot_distribution: {}
    }
  }

  // Disable monitoring (useful for development)
  disable(): void {
    this.isEnabled = false
  }

  // Enable monitoring
  enable(): void {
    this.isEnabled = true
  }
}

// Export singleton instance
export const seoMonitor = SEOMonitor.getInstance()

// Utility functions for middleware integration
export function trackMiddlewareCrawl(
  url: string,
  userAgent: string,
  statusCode: number,
  responseTime: number
): void {
  const botType = seoMonitor.detectBotType(userAgent)
  
  seoMonitor.trackCrawl({
    url,
    user_agent: userAgent,
    status_code: statusCode,
    response_time: responseTime,
    bot_type: botType,
    timestamp: new Date().toISOString()
  }).catch(error => {
    console.error('Failed to track middleware crawl:', error)
  })
}

export function isSearchEngineBot(userAgent: string): boolean {
  return /googlebot|bingbot|slurp|duckduckbot|baiduspider|yandexbot/i.test(userAgent)
}

export function isSocialBot(userAgent: string): boolean {
  return /facebookexternalhit|twitterbot|linkedinbot|whatsapp|telegram/i.test(userAgent)
}

// Database schema for SEO monitoring tables
export const SEO_MONITORING_SCHEMA = `
-- SEO Crawl Events Table
CREATE TABLE IF NOT EXISTS seo_crawl_events (
  id SERIAL PRIMARY KEY,
  url TEXT NOT NULL,
  user_agent TEXT NOT NULL,
  status_code INTEGER NOT NULL,
  response_time INTEGER NOT NULL,
  bot_type TEXT NOT NULL CHECK (bot_type IN ('search_engine', 'social', 'other')),
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- SEO Metrics Table
CREATE TABLE IF NOT EXISTS seo_metrics (
  id SERIAL PRIMARY KEY,
  url TEXT NOT NULL,
  metric_type TEXT NOT NULL CHECK (metric_type IN ('crawl', 'performance', 'indexing', 'error')),
  metric_name TEXT NOT NULL,
  metric_value TEXT NOT NULL,
  user_agent TEXT,
  timestamp TIMESTAMPTZ NOT NULL,
  additional_data JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_seo_crawl_events_timestamp ON seo_crawl_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_seo_crawl_events_url ON seo_crawl_events(url);
CREATE INDEX IF NOT EXISTS idx_seo_crawl_events_bot_type ON seo_crawl_events(bot_type);
CREATE INDEX IF NOT EXISTS idx_seo_metrics_timestamp ON seo_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_seo_metrics_url ON seo_metrics(url);
CREATE INDEX IF NOT EXISTS idx_seo_metrics_type ON seo_metrics(metric_type);
`
