import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugToursBucket() {
  console.log('🔍 DEBUGGING TOURS BUCKET ISSUE\n');

  try {
    // Test 1: Check environment variables
    console.log('🔧 Environment Check:');
    console.log(`   Supabase URL: ${supabaseUrl ? '✅ Set' : '❌ Missing'}`);
    console.log(`   Supabase Key: ${supabaseAnonKey ? '✅ Set' : '❌ Missing'}`);
    console.log('');

    // Test 2: Test basic connection
    console.log('🌐 Testing Supabase connection...');
    try {
      const { data, error } = await supabase.from('packages').select('count').limit(1);
      if (error) {
        console.log(`❌ Database connection failed: ${error.message}`);
      } else {
        console.log('✅ Database connection successful');
      }
    } catch (err) {
      console.log(`❌ Connection error: ${err.message}`);
    }
    console.log('');

    // Test 3: List all buckets with detailed info
    console.log('📦 Listing all storage buckets...');
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError);
      console.log('🔧 This might be a permissions issue');
      console.log('   Try checking your Supabase project settings');
    } else {
      console.log(`✅ Found ${buckets?.length || 0} buckets:`);
      
      if (!buckets || buckets.length === 0) {
        console.log('   No buckets found');
      } else {
        buckets.forEach((bucket, index) => {
          console.log(`   ${index + 1}. ${bucket.name} (ID: ${bucket.id})`);
          console.log(`      Public: ${bucket.public}`);
          console.log(`      File size limit: ${bucket.file_size_limit} bytes`);
          console.log(`      Created: ${bucket.created_at}`);
          console.log('');
        });
      }

      // Test 4: Check specifically for tours bucket
      console.log('🎯 Checking for tours bucket specifically...');
      const toursBucket = buckets?.find(b => b.name === 'tours');
      const toursById = buckets?.find(b => b.id === 'tours');
      
      if (toursBucket) {
        console.log('✅ Found tours bucket by name!');
        console.log(`   ID: ${toursBucket.id}`);
        console.log(`   Public: ${toursBucket.public}`);
        console.log(`   File size limit: ${toursBucket.file_size_limit} bytes`);
      } else {
        console.log('❌ No bucket found with name: tours');
      }

      if (toursById) {
        console.log('✅ Found tours bucket by ID!');
        console.log(`   Name: ${toursById.name}`);
        console.log(`   Public: ${toursById.public}`);
      } else {
        console.log('❌ No bucket found with ID: tours');
      }
    }

    // Test 5: Try direct bucket access
    console.log('\n🧪 Testing direct bucket access...');
    try {
      const { data: files, error: accessError } = await supabase.storage
        .from('tours')
        .list();
      
      if (accessError) {
        console.log(`❌ Cannot access tours bucket: ${accessError.message}`);
        
        // Try alternative bucket names
        console.log('\n🔍 Trying alternative bucket names...');
        const alternativeNames = ['tour-images', 'tour', 'tours-images', 'package-images'];
        
        for (const name of alternativeNames) {
          try {
            const { data: altFiles, error: altError } = await supabase.storage
              .from(name)
              .list();
            
            if (!altError) {
              console.log(`✅ Found alternative bucket: ${name}`);
              console.log(`   Files: ${altFiles?.length || 0}`);
            }
          } catch (err) {
            // Ignore errors for alternative names
          }
        }
      } else {
        console.log(`✅ Can access tours bucket! Files: ${files?.length || 0}`);
      }
    } catch (err) {
      console.log(`❌ Direct access error: ${err.message}`);
    }

    // Test 6: Check bucket permissions
    console.log('\n🔒 Testing bucket permissions...');
    try {
      const { data: bucketInfo, error: infoError } = await supabase.storage
        .getBucket('tours');
      
      if (infoError) {
        console.log(`❌ Cannot get bucket info: ${infoError.message}`);
      } else {
        console.log('✅ Can get bucket info');
        console.log(`   Bucket info:`, bucketInfo);
      }
    } catch (err) {
      console.log(`❌ Bucket info error: ${err.message}`);
    }

    console.log('\n📋 DIAGNOSIS SUMMARY:');
    console.log('1. Check if the bucket name is exactly "tours"');
    console.log('2. Verify the bucket is public');
    console.log('3. Check your Supabase project permissions');
    console.log('4. Try refreshing your browser/app');
    console.log('5. Check if there are any RLS policies blocking access');

    console.log('\n🔧 POSSIBLE SOLUTIONS:');
    console.log('1. Recreate the bucket with exact name "tours"');
    console.log('2. Check bucket permissions in Supabase dashboard');
    console.log('3. Verify your environment variables');
    console.log('4. Try using a different bucket name');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

debugToursBucket().catch(console.error); 