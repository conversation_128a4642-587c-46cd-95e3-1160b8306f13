import { config } from 'dotenv'
import { createClient } from '@supabase/supabase-js'

// Load environment variables
config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

// Simulate the new blog service functions
async function fetchBlogPostBySlug(slug) {
  try {
    console.log(`🔍 Fetching blog post: ${slug}`)
    
    // Fetch the main blog post
    const { data: post, error: postError } = await supabase
      .from('sas_blog_posts')
      .select(`
        id,
        title,
        slug,
        description,
        hero_image_url,
        hero_image_alt,
        category,
        tags,
        status,
        published_at,
        created_at,
        updated_at,
        view_count,
        seo_title,
        seo_description,
        seo_keywords,
        og_title,
        og_description,
        og_image_url,
        canonical_url,
        robots_index,
        robots_follow,
        schema_data
      `)
      .eq('slug', slug)
      .eq('status', 'published')
      .is('deleted_at', null)
      .single()

    if (postError || !post) {
      console.log(`❌ Blog post not found: ${slug}`)
      console.log('Error:', postError?.message)
      return null
    }

    console.log(`✅ Blog post found: ${post.title}`)

    // Fetch content blocks
    const { data: contentBlocks, error: blocksError } = await supabase
      .from('sas_blog_content_blocks')
      .select('*')
      .eq('blog_post_id', post.id)
      .order('sort_order', { ascending: true })

    if (blocksError) {
      console.error(`❌ Error fetching content blocks: ${blocksError.message}`)
      // Continue without content blocks rather than failing completely
    }

    // Attach content blocks to the post
    const blogPost = {
      ...post,
      content_blocks: contentBlocks || []
    }

    console.log(`✅ Blog post loaded with ${contentBlocks?.length || 0} content blocks`)
    return blogPost

  } catch (error) {
    console.error(`❌ Error in fetchBlogPostBySlug: ${error}`)
    return null
  }
}

async function generateBlogStaticParams() {
  try {
    console.log('🔍 Generating static params for blog posts')
    
    const { data: posts, error } = await supabase
      .from('sas_blog_posts')
      .select('slug')
      .eq('status', 'published')
      .is('deleted_at', null)
      .order('created_at', { ascending: false })

    if (error) {
      console.error(`❌ Error generating static params: ${error.message}`)
      return []
    }

    const params = posts?.map(post => ({ slug: post.slug })) || []
    console.log(`✅ Generated ${params.length} static params`)
    
    return params

  } catch (error) {
    console.error(`❌ Error in generateBlogStaticParams: ${error}`)
    return []
  }
}

async function testNewBlogService() {
  console.log('🧪 TESTING NEW BLOG SERVICE')
  console.log('=' .repeat(40))

  try {
    // Test 1: Generate static params
    console.log('\n📋 1. TESTING generateBlogStaticParams():')
    const staticParams = await generateBlogStaticParams()
    
    if (staticParams.length > 0) {
      console.log('   Generated slugs:')
      staticParams.forEach((param, index) => {
        console.log(`   ${index + 1}. ${param.slug}`)
      })
      
      // Check if volcanoes-national-park is included
      const volcanoesIncluded = staticParams.some(p => p.slug === 'volcanoes-national-park')
      console.log(`   ✅ "volcanoes-national-park" included: ${volcanoesIncluded ? 'YES' : 'NO'}`)
    }

    // Test 2: Fetch specific problematic post
    console.log('\n🎯 2. TESTING fetchBlogPostBySlug("volcanoes-national-park"):')
    const volcanoesPost = await fetchBlogPostBySlug('volcanoes-national-park')
    
    if (volcanoesPost) {
      console.log('   ✅ Post successfully fetched!')
      console.log(`   Title: "${volcanoesPost.title}"`)
      console.log(`   Content blocks: ${volcanoesPost.content_blocks?.length || 0}`)
      console.log(`   Hero image: ${volcanoesPost.hero_image_url ? 'YES' : 'NO'}`)
      console.log(`   SEO title: ${volcanoesPost.seo_title || 'Not set'}`)
    } else {
      console.log('   ❌ Failed to fetch post')
    }

    // Test 3: Test a few other posts
    console.log('\n📝 3. TESTING OTHER POSTS:')
    const testSlugs = ['mountain-gorilla-trekking-in-rwanda-and-uganda', 'maasai-culture-and-connection']
    
    for (const slug of testSlugs) {
      console.log(`\n   Testing: ${slug}`)
      const post = await fetchBlogPostBySlug(slug)
      if (post) {
        console.log(`   ✅ "${post.title}" - ${post.content_blocks?.length || 0} blocks`)
      } else {
        console.log(`   ❌ Failed to fetch`)
      }
    }

    // Test 4: Summary
    console.log('\n📊 4. SUMMARY:')
    console.log(`   - Static params generated: ${staticParams.length}`)
    console.log(`   - "volcanoes-national-park" accessible: ${volcanoesPost ? 'YES' : 'NO'}`)
    console.log(`   - New blog service working: ${volcanoesPost ? 'YES' : 'NO'}`)

    if (volcanoesPost) {
      console.log('\n✅ NEW BLOG SERVICE IS WORKING CORRECTLY!')
      console.log('   The issue is likely that Vercel hasn\'t deployed the new code yet.')
      console.log('   Wait 5-10 minutes for deployment to complete.')
    } else {
      console.log('\n❌ NEW BLOG SERVICE HAS ISSUES!')
      console.log('   Need to investigate further.')
    }

  } catch (error) {
    console.error('❌ Fatal error during testing:', error)
  }
}

// Run the test
testNewBlogService()
