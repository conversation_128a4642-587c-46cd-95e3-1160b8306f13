import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { verifyAdminAuth } from '@/lib/admin-auth';

// POST - Bulk delete blog posts (admin only) - Soft delete with cascading cleanup
export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication (in development, this is bypassed)
    const auth = await verifyAdminAuth(request);

    if (!auth.isAuthenticated) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (!auth.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin privileges required' },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { blogPostIds } = body;

    // Validate input
    if (!blogPostIds || !Array.isArray(blogPostIds) || blogPostIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Blog post IDs array is required and cannot be empty' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid UUIDs
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const invalidIds = blogPostIds.filter(id => !uuidRegex.test(id));
    
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid blog post IDs provided',
          invalidIds 
        },
        { status: 400 }
      );
    }

    // Validate environment variables
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      return NextResponse.json(
        { success: false, error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Create service role client for admin operations (bypasses RLS)
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Track results for detailed response
    const results = {
      successful: [] as Array<{
        id: string;
        title: string;
        slug: string;
        deleted_at: string;
      }>,
      failed: [] as Array<{
        id: string;
        title?: string;
        error: string;
      }>,
      imagesDeleted: [] as string[],
      imageDeleteErrors: [] as Array<{
        fileName: string;
        error: string;
      }>
    };

    const deleteTimestamp = new Date().toISOString();

    // Process each blog post
    for (const blogPostId of blogPostIds) {
      try {
        // Start a transaction-like process for each blog post
        
        // 1. Check if blog post exists and is not already deleted
        const { data: existingPost, error: fetchError } = await supabaseAdmin
          .from('sas_blog_posts')
          .select('id, title, slug, hero_image_url, deleted_at')
          .eq('id', blogPostId)
          .single();

        if (fetchError || !existingPost) {
          results.failed.push({
            id: blogPostId,
            error: 'Blog post not found'
          });
          continue;
        }

        if (existingPost.deleted_at) {
          results.failed.push({
            id: blogPostId,
            title: existingPost.title,
            error: 'Blog post is already deleted'
          });
          continue;
        }

        // 2. Get all images from content blocks for storage cleanup
        const { data: contentBlocks, error: contentError } = await supabaseAdmin
          .from('sas_blog_content_blocks')
          .select('content')
          .eq('blog_post_id', blogPostId)
          .eq('block_type', 'image');

        const imagesToDelete = [];
        
        // Extract hero image
        if (existingPost.hero_image_url) {
          const heroImageFileName = extractFileNameFromUrl(existingPost.hero_image_url);
          if (heroImageFileName) {
            imagesToDelete.push(heroImageFileName);
          }
        }

        // Extract content block images
        if (contentBlocks && !contentError) {
          contentBlocks.forEach(block => {
            if (block.content && typeof block.content === 'object' && block.content.src) {
              const fileName = extractFileNameFromUrl(block.content.src);
              if (fileName) {
                imagesToDelete.push(fileName);
              }
            }
          });
        }

        // 3. Soft delete comments associated with this blog post
        const { error: commentsDeleteError } = await supabaseAdmin
          .from('sas_blog_comments')
          .update({ 
            deleted_at: deleteTimestamp,
            updated_at: deleteTimestamp 
          })
          .eq('blog_post_id', blogPostId)
          .is('deleted_at', null);

        if (commentsDeleteError) {
          console.error(`Error soft deleting comments for post ${blogPostId}:`, commentsDeleteError);
          // Continue with blog post deletion even if comments fail
        }

        // 4. Soft delete the blog post (content blocks will be handled by CASCADE on hard delete if needed)
        const { error: deleteError } = await supabaseAdmin
          .from('sas_blog_posts')
          .update({
            deleted_at: deleteTimestamp,
            updated_at: deleteTimestamp
          })
          .eq('id', blogPostId);

        if (deleteError) {
          results.failed.push({
            id: blogPostId,
            title: existingPost.title,
            error: `Failed to delete blog post: ${deleteError.message}`
          });
          continue;
        }

        // 5. Delete associated images from storage (non-blocking)
        for (const fileName of imagesToDelete) {
          try {
            // Check if image is used by other non-deleted posts before deleting
            const isImageUsedElsewhere = await checkImageUsage(supabaseAdmin, fileName, blogPostId);
            
            if (!isImageUsedElsewhere) {
              const { error: storageError } = await supabaseAdmin.storage
                .from('sas-blog-images')
                .remove([fileName]);

              if (storageError) {
                results.imageDeleteErrors.push({
                  fileName,
                  error: storageError.message
                });
              } else {
                results.imagesDeleted.push(fileName);
              }
            }
          } catch (imageError) {
            results.imageDeleteErrors.push({
              fileName,
              error: imageError instanceof Error ? imageError.message : 'Unknown error'
            });
          }
        }

        // Success
        results.successful.push({
          id: blogPostId,
          title: existingPost.title,
          slug: existingPost.slug,
          deleted_at: deleteTimestamp
        });

      } catch (error) {
        results.failed.push({
          id: blogPostId,
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        });
      }
    }

    // Prepare response
    const response = {
      success: true,
      message: `Bulk deletion completed. ${results.successful.length} posts deleted, ${results.failed.length} failed.`,
      data: {
        summary: {
          totalRequested: blogPostIds.length,
          successful: results.successful.length,
          failed: results.failed.length,
          imagesDeleted: results.imagesDeleted.length,
          imageDeleteErrors: results.imageDeleteErrors.length
        },
        details: results
      }
    };

    // Set appropriate status code
    const statusCode = results.failed.length === 0 ? 200 : 207; // 207 = Multi-Status

    const nextResponse = NextResponse.json(response, { status: statusCode });

    // Add cache-busting headers
    nextResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    nextResponse.headers.set('Pragma', 'no-cache');
    nextResponse.headers.set('Expires', '0');

    return nextResponse;

  } catch (error) {
    console.error('Unexpected error in bulk delete API:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper function to extract filename from Supabase storage URL
function extractFileNameFromUrl(url: string): string | null {
  try {
    if (!url || typeof url !== 'string') return null;
    
    // Handle Supabase storage URLs
    if (url.includes('/storage/v1/object/public/')) {
      const parts = url.split('/');
      return parts[parts.length - 1];
    }
    
    // Handle other URL formats
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    return pathname.split('/').pop() || null;
  } catch {
    return null;
  }
}

// Helper function to check if an image is used by other non-deleted posts
async function checkImageUsage(supabaseAdmin: any, fileName: string, excludePostId: string): Promise<boolean> {
  try {
    // Check hero images
    const { data: heroImagePosts } = await supabaseAdmin
      .from('sas_blog_posts')
      .select('id')
      .ilike('hero_image_url', `%${fileName}%`)
      .neq('id', excludePostId)
      .is('deleted_at', null);

    if (heroImagePosts && heroImagePosts.length > 0) {
      return true;
    }

    // Check content block images
    const { data: contentImagePosts } = await supabaseAdmin
      .from('sas_blog_content_blocks')
      .select('blog_post_id')
      .eq('block_type', 'image')
      .ilike('content->src', `%${fileName}%`);

    if (contentImagePosts && contentImagePosts.length > 0) {
      // Check if any of these posts are not deleted and not the current post
      for (const block of contentImagePosts) {
        const { data: post } = await supabaseAdmin
          .from('sas_blog_posts')
          .select('id')
          .eq('id', block.blog_post_id)
          .neq('id', excludePostId)
          .is('deleted_at', null)
          .single();

        if (post) {
          return true;
        }
      }
    }

    return false;
  } catch {
    // If we can't determine usage, err on the side of caution and don't delete
    return true;
  }
}
