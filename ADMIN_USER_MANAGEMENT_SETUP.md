# Admin User Management System Setup Guide

This guide will help you set up the complete admin user management system for Swift Africa Safaris.

## 🚀 Quick Start

### 1. Database Setup

First, run the SQL script to create the necessary database schema:

1. Go to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `scripts/setup-admin-database.sql`
4. Run the script

This will create:
- `users` table with proper RLS policies
- `avatars` storage bucket for profile pictures
- Triggers and functions for user management

### 2. Environment Variables

Make sure you have these environment variables set in your `.env.local`:

```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Create Admin User

Run the setup script to create your first admin user:

```bash
node scripts/setup-admin-user.mjs
```

This script will:
- Check if the database schema is set up correctly
- Create the avatars storage bucket if it doesn't exist
- Create an admin user with the email and password you provide
- Test the system to ensure everything works

## 📋 Features

### User Management
- ✅ Create new users with different roles
- ✅ Edit user profiles and permissions
- ✅ Suspend/activate users
- ✅ Delete users (except admins)
- ✅ Search and filter users
- ✅ Pagination for large user lists

### User Roles
- **Administrator**: Full system access and user management
- **Content Creator**: Manage blog posts, packages, and destinations
- **Tour Specialist**: Manage bookings, customers, and travel operations
- **IT Support**: System maintenance and technical support
- **Travel Agent**: Manage bookings and customer interactions

### Authentication
- ✅ Secure login with email/password
- ✅ Role-based access control
- ✅ Session management
- ✅ Password visibility toggle
- ✅ Remember me functionality

### Profile Management
- ✅ Upload profile pictures
- ✅ Update personal information
- ✅ Change passwords
- ✅ Department assignment

## 🎨 Beautiful UI Features

### Design Elements
- Modern gradient backgrounds
- Smooth animations and transitions
- Responsive design for all devices
- Beautiful icons from Lucide React
- Professional color scheme
- Clean typography

### User Experience
- Loading states and spinners
- Success/error notifications
- Form validation with helpful messages
- Intuitive navigation
- Search and filtering capabilities
- Real-time updates

## 🔧 Technical Implementation

### Database Schema

```sql
CREATE TABLE users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT,
    full_name TEXT,
    role TEXT DEFAULT 'travel-agent',
    status TEXT DEFAULT 'active',
    department TEXT DEFAULT 'General',
    phone TEXT,
    company TEXT,
    avatar_url TEXT,
    permissions TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS)
- Users can only view/edit their own profiles
- Admins can view/edit all users
- Proper permission checks for all operations

### Storage
- Profile pictures stored in Supabase Storage
- Public access for avatar images
- Automatic file naming and organization

## 🚀 Usage

### Accessing the Admin Panel

1. Navigate to `/admin/login`
2. Enter your admin credentials
3. You'll be redirected to `/admin/user-management`

### Creating New Users

1. Click "Add User" button
2. Fill in the user information:
   - Name and email
   - Password (minimum 8 characters)
   - Role and department
   - Contact information
   - Profile picture (optional)
3. Click "Create User"

### Managing Users

- **View**: Click the eye icon to view user details
- **Edit**: Click the edit icon to modify user information
- **Suspend/Activate**: Toggle user status
- **Delete**: Remove users (admins cannot be deleted)

### Search and Filter

- Use the search bar to find users by name, email, or department
- Filter by role or status using the dropdown menus
- Results are paginated for better performance

## 🔒 Security Features

### Authentication
- Secure password hashing via Supabase Auth
- Session management with automatic expiration
- CSRF protection
- Rate limiting on login attempts

### Authorization
- Role-based access control
- Row-level security policies
- Permission-based feature access
- Admin-only operations protected

### Data Protection
- Encrypted data transmission
- Secure file uploads
- Input validation and sanitization
- SQL injection prevention

## 🐛 Troubleshooting

### Common Issues

**"Users table not found"**
- Run the SQL setup script in Supabase
- Check that the script executed successfully

**"Storage bucket not found"**
- The setup script will create the avatars bucket
- If it fails, manually create it in Supabase Storage

**"Permission denied"**
- Check that your user has admin role
- Verify RLS policies are set up correctly

**"Login not working"**
- Verify environment variables are set correctly
- Check that the user exists and has admin role
- Ensure the user status is 'active'

### Debug Mode

To enable debug logging, add this to your environment:

```bash
NEXT_PUBLIC_DEBUG=true
```

## 📱 Mobile Responsiveness

The admin panel is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🎯 Best Practices

### User Management
- Regularly review user permissions
- Use descriptive role names
- Keep user information up to date
- Monitor login activity

### Security
- Use strong passwords
- Regularly rotate admin credentials
- Monitor for suspicious activity
- Keep the system updated

### Performance
- Use pagination for large user lists
- Optimize images before upload
- Monitor database performance
- Cache frequently accessed data

## 🔄 Updates and Maintenance

### Regular Tasks
- Review and update user roles
- Clean up inactive users
- Monitor storage usage
- Update security policies

### Backup
- Regular database backups via Supabase
- Export user data periodically
- Keep configuration backups

## 📞 Support

If you encounter any issues:

1. Check the troubleshooting section above
2. Review the console logs for errors
3. Verify your Supabase configuration
4. Contact the development team

## 🎉 Success!

Once you've completed the setup, you'll have a fully functional, beautiful, and secure admin user management system for Swift Africa Safaris!

The system includes:
- ✅ Complete user management
- ✅ Beautiful, modern UI
- ✅ Secure authentication
- ✅ Role-based access control
- ✅ Profile management
- ✅ Mobile responsiveness
- ✅ Comprehensive error handling

Enjoy managing your users with style and security! 🚀 