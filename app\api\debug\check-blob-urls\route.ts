import { NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET() {
  try {
    // Fetch all packages with image URLs
    const { data: packages, error } = await supabase
      .from('sas_packages')
      .select('id, title, slug, image_url, hero_image_url, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase error:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch packages' },
        { status: 500 }
      );
    }

    if (!packages || packages.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No packages found',
        packages: [],
        summary: { total: 0, withBlobUrls: 0, withValidUrls: 0 }
      });
    }

    // Analyze URLs
    const packagesWithAnalysis = packages.map(pkg => {
      const hasBlobImageUrl = pkg.image_url && pkg.image_url.startsWith('blob:');
      const hasBlobHeroUrl = pkg.hero_image_url && pkg.hero_image_url.startsWith('blob:');
      
      return {
        ...pkg,
        hasBlobImageUrl,
        hasBlobHeroUrl,
        hasBlobUrls: hasBlobImageUrl || hasBlobHeroUrl
      };
    });

    const summary = {
      total: packages.length,
      withBlobUrls: packagesWithAnalysis.filter(pkg => pkg.hasBlobUrls).length,
      withValidUrls: packagesWithAnalysis.filter(pkg => !pkg.hasBlobUrls).length
    };

    return NextResponse.json({
      success: true,
      packages: packagesWithAnalysis,
      summary
    });

  } catch (error) {
    console.error('Error checking blob URLs:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
