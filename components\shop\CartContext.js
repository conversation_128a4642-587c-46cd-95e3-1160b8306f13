import React, { createContext, useContext, useState } from 'react';

const CartContext = createContext();

export const CartProvider = ({ children }) => {
    const [cartItems, setCartItems] = useState([]);
    const [isCartOpen, setIsCartOpen] = useState(false);

    const addToCart = (product) => {
        setCartItems(currentItems => {
            // Check if product exists by both id AND name to ensure uniqueness
            const existingItem = currentItems.find(
                item => item.id === product.id && item.name === product.name
            );
            
            if (existingItem) {
                return currentItems.map(item =>
                    item.id === product.id && item.name === product.name
                        ? { ...item, quantity: item.quantity + 1 }
                        : item
                );
            }
            // Add as new item if not found
            return [...currentItems, { ...product, quantity: 1 }];
        });
        setIsCartOpen(true); // Open cart when adding items
    };

    const updateQuantity = (id, change) => {
        setCartItems(currentItems =>
            currentItems.map(item => {
                if (item.id === id) {
                    const newQuantity = Math.max(0, item.quantity + change);
                    return newQuantity === 0 ? null : { ...item, quantity: newQuantity };
                }
                return item;
            }).filter(Boolean)
        );
    };

    const removeFromCart = (id) => {
        setCartItems(currentItems => currentItems.filter(item => item.id !== id));
    };

    return (
        <CartContext.Provider value={{
            cartItems,
            isCartOpen,
            setIsCartOpen,
            addToCart,
            updateQuantity,
            removeFromCart
        }}>
            {children}
        </CartContext.Provider>
    );
};

export const useCart = () => {
    const context = useContext(CartContext);
    if (!context) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
};

