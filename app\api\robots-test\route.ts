import { NextRequest, NextResponse } from 'next/server'
import { validateRobotsTxt, testRobotsForUserAgent } from '@/lib/robots-validation'
import { defaultSEO } from '@/lib/seo'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const testType = searchParams.get('test') || 'validate'
    const userAgent = searchParams.get('userAgent') || 'Googlebot'
    const baseUrl = searchParams.get('baseUrl') || defaultSEO.siteUrl

    switch (testType) {
      case 'validate':
        return await handleValidation(baseUrl)
      
      case 'user-agent':
        return await handleUserAgentTest(userAgent, baseUrl)
      
      case 'crawlability':
        return await handleCrawlabilityTest(baseUrl)
      
      case 'security':
        return await handleSecurityTest(baseUrl)
      
      case 'comprehensive':
        return await handleComprehensiveTest(baseUrl)
      
      default:
        return NextResponse.json({ error: 'Invalid test type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Robots test API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

async function handleValidation(baseUrl: string) {
  const validationResult = await validateRobotsTxt(baseUrl)
  
  return NextResponse.json({
    test_type: 'validation',
    timestamp: new Date().toISOString(),
    result: validationResult
  })
}

async function handleUserAgentTest(userAgent: string, baseUrl: string) {
  const testPaths = [
    '/',
    '/about',
    '/contact',
    '/package/gorilla-trekking',
    '/blog/safari-tips',
    '/admin/dashboard',
    '/api/packages',
    '/private/data',
    '/_next/static/css/app.css',
    '/sitemap.xml',
    '/robots.txt'
  ]

  const testResult = await testRobotsForUserAgent(userAgent, testPaths, baseUrl)
  
  return NextResponse.json({
    test_type: 'user-agent',
    timestamp: new Date().toISOString(),
    result: testResult
  })
}

async function handleCrawlabilityTest(baseUrl: string) {
  const crawlabilityTests = []

  // Test important content paths
  const importantPaths = [
    { path: '/', description: 'Homepage' },
    { path: '/package/', description: 'Package listings' },
    { path: '/blog/', description: 'Blog section' },
    { path: '/about', description: 'About page' },
    { path: '/contact', description: 'Contact page' },
    { path: '/faqs', description: 'FAQ page' },
    { path: '/journey', description: 'Journey page' },
    { path: '/community', description: 'Community page' }
  ]

  const userAgents = ['Googlebot', 'Bingbot', '*']

  for (const userAgent of userAgents) {
    const testResult = await testRobotsForUserAgent(
      userAgent, 
      importantPaths.map(p => p.path), 
      baseUrl
    )

    crawlabilityTests.push({
      user_agent: userAgent,
      results: testResult.results.map((result, index) => ({
        ...result,
        description: importantPaths[index]?.description || 'Unknown'
      }))
    })
  }

  // Calculate crawlability score
  const totalTests = crawlabilityTests.reduce((sum, test) => sum + test.results.length, 0)
  const allowedTests = crawlabilityTests.reduce((sum, test) => 
    sum + test.results.filter(r => r.allowed).length, 0
  )
  const crawlabilityScore = Math.round((allowedTests / totalTests) * 100)

  return NextResponse.json({
    test_type: 'crawlability',
    timestamp: new Date().toISOString(),
    result: {
      score: crawlabilityScore,
      total_tests: totalTests,
      allowed_paths: allowedTests,
      blocked_paths: totalTests - allowedTests,
      details: crawlabilityTests
    }
  })
}

async function handleSecurityTest(baseUrl: string) {
  const securityTests = []

  // Test sensitive paths that should be blocked
  const sensitivePaths = [
    { path: '/admin/', description: 'Admin dashboard' },
    { path: '/admin/users', description: 'User management' },
    { path: '/partner/', description: 'Partner portal' },
    { path: '/api/admin/', description: 'Admin API' },
    { path: '/api/auth/', description: 'Authentication API' },
    { path: '/login', description: 'Login page' },
    { path: '/private/', description: 'Private content' },
    { path: '/debug/', description: 'Debug information' },
    { path: '/_next/', description: 'Next.js internals' },
    { path: '/api/upload', description: 'File upload API' },
    { path: '/test-packages', description: 'Test content' }
  ]

  const userAgents = ['*', 'Googlebot', 'Bingbot', 'AhrefsBot']

  for (const userAgent of userAgents) {
    const testResult = await testRobotsForUserAgent(
      userAgent, 
      sensitivePaths.map(p => p.path), 
      baseUrl
    )

    securityTests.push({
      user_agent: userAgent,
      results: testResult.results.map((result, index) => ({
        ...result,
        description: sensitivePaths[index]?.description || 'Unknown',
        security_status: result.allowed ? 'vulnerable' : 'protected'
      }))
    })
  }

  // Calculate security score (higher is better when sensitive paths are blocked)
  const totalTests = securityTests.reduce((sum, test) => sum + test.results.length, 0)
  const protectedTests = securityTests.reduce((sum, test) => 
    sum + test.results.filter(r => !r.allowed).length, 0
  )
  const securityScore = Math.round((protectedTests / totalTests) * 100)

  return NextResponse.json({
    test_type: 'security',
    timestamp: new Date().toISOString(),
    result: {
      score: securityScore,
      total_tests: totalTests,
      protected_paths: protectedTests,
      vulnerable_paths: totalTests - protectedTests,
      details: securityTests
    }
  })
}

async function handleComprehensiveTest(baseUrl: string) {
  // Run all tests
  const [validation, crawlability, security] = await Promise.all([
    handleValidation(baseUrl),
    handleCrawlabilityTest(baseUrl),
    handleSecurityTest(baseUrl)
  ])

  const validationData = await validation.json()
  const crawlabilityData = await crawlability.json()
  const securityData = await security.json()

  // Calculate overall score
  const overallScore = Math.round(
    (validationData.result.score + 
     crawlabilityData.result.score + 
     securityData.result.score) / 3
  )

  // Generate comprehensive recommendations
  const recommendations = [
    ...validationData.result.recommendations,
    ...(crawlabilityData.result.score < 90 ? ['Improve crawlability by ensuring important content is accessible'] : []),
    ...(securityData.result.score < 90 ? ['Enhance security by blocking more sensitive paths'] : [])
  ]

  return NextResponse.json({
    test_type: 'comprehensive',
    timestamp: new Date().toISOString(),
    result: {
      overall_score: overallScore,
      validation: validationData.result,
      crawlability: crawlabilityData.result,
      security: securityData.result,
      recommendations: [...new Set(recommendations)], // Remove duplicates
      summary: {
        total_issues: validationData.result.issues.length,
        critical_issues: validationData.result.issues.filter((i: any) => i.type === 'error').length,
        crawlability_score: crawlabilityData.result.score,
        security_score: securityData.result.score,
        validation_score: validationData.result.score
      }
    }
  })
}

// POST endpoint for testing custom robots.txt content
export async function POST(request: NextRequest) {
  try {
    const { content, testPaths, userAgents } = await request.json()

    if (!content) {
      return NextResponse.json({ error: 'Robots.txt content is required' }, { status: 400 })
    }

    const defaultTestPaths = [
      '/',
      '/package/',
      '/blog/',
      '/admin/',
      '/api/',
      '/private/'
    ]

    const defaultUserAgents = ['*', 'Googlebot', 'Bingbot']

    const pathsToTest = testPaths || defaultTestPaths
    const agentsToTest = userAgents || defaultUserAgents

    const testResults = []

    for (const userAgent of agentsToTest) {
      // Parse robots rules from content
      const rules = parseRobotsContent(content, userAgent)
      
      const results = pathsToTest.map((path: string) => ({
        path,
        allowed: isPathAllowedByRules(path, rules),
        rule: rules.find((r: any) => path.startsWith(r.pattern))?.directive || 'default'
      }))

      testResults.push({
        user_agent: userAgent,
        results
      })
    }

    return NextResponse.json({
      test_type: 'custom_content',
      timestamp: new Date().toISOString(),
      result: {
        test_results: testResults,
        content_length: content.length,
        lines_count: content.split('\n').length
      }
    })

  } catch (error) {
    console.error('Custom robots test error:', error)
    return NextResponse.json(
      { error: 'Failed to test custom content', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// Helper functions for custom content testing
function parseRobotsContent(content: string, userAgent: string): Array<{
  directive: string
  pattern: string
}> {
  const lines = content.split('\n').map(line => line.trim())
  const rules = []
  let inRelevantSection = false

  for (const line of lines) {
    if (line.toLowerCase().startsWith('user-agent:')) {
      const agent = line.substring(11).trim()
      inRelevantSection = agent === '*' || agent.toLowerCase() === userAgent.toLowerCase()
    } else if (inRelevantSection && (line.toLowerCase().startsWith('allow:') || line.toLowerCase().startsWith('disallow:'))) {
      const directive = line.toLowerCase().startsWith('allow:') ? 'allow' : 'disallow'
      const pattern = line.substring(directive === 'allow' ? 6 : 9).trim()
      rules.push({ directive, pattern })
    }
  }

  return rules
}

function isPathAllowedByRules(path: string, rules: Array<{ directive: string; pattern: string }>): boolean {
  let allowed = true // Default is to allow
  
  for (const rule of rules) {
    if (path.startsWith(rule.pattern) || rule.pattern === '/') {
      allowed = rule.directive === 'allow'
    }
  }
  
  return allowed
}
