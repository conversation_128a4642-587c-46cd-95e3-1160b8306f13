/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-unused-vars */

import React, { useState, useRef, useEffect } from "react";

interface BlogFormData {
  title: string;
  description: string;
  imageUrl: string;
  imageAlt: string;
  category: string;
  tags: string[];
  status: 'published' | 'draft' | 'archived';
  content: any[];
  publishedAt: string;
  slug: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  ogTitle: string;
  ogDescription: string;
  ogImageUrl: string;
  canonicalUrl: string;
  robotsIndex: string;
  robotsFollow: string;
}

interface BlogSEOProps {
  formData: BlogFormData;
  onFormDataChange: (data: Partial<BlogFormData>) => void;
}

interface FormErrors {
  [key: string]: string;
}

const BlogSEO: React.FC<BlogSEOProps> = ({ formData, onFormDataChange }) => {
  const [activeTab, setActiveTab] = useState("basic");
  const [errors, setErrors] = useState<FormErrors>({});
  const keywordInputRef = useRef<HTMLInputElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Auto-generate SEO fields from basic info
  useEffect(() => {
    if (formData.title && !formData.seoTitle) {
      onFormDataChange({ seoTitle: formData.title });
    }
    if (formData.description && !formData.seoDescription) {
      onFormDataChange({ seoDescription: formData.description });
    }
    if (formData.title && !formData.ogTitle) {
      onFormDataChange({ ogTitle: formData.title });
    }
    if (formData.description && !formData.ogDescription) {
      onFormDataChange({ ogDescription: formData.description });
    }
    if (formData.imageUrl && !formData.ogImageUrl) {
      onFormDataChange({ ogImageUrl: formData.imageUrl });
    }
  }, [formData.title, formData.description, formData.imageUrl]);

  const handleInputChange = (field: keyof BlogFormData, value: string | string[]) => {
    onFormDataChange({ [field]: value });

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }

    // Auto-format URL slug
    if (field === "slug") {
      const slug = (value as string)
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
      onFormDataChange({ [field]: slug });
    }

    // Auto-sync Featured Image to Open Graph Image URL
    if (field === "imageUrl") {
      onFormDataChange({ ogImageUrl: value as string });
    }
  };

  const addKeyword = (keyword: string) => {
    keyword = keyword.trim();
    if (keyword && !formData.seoKeywords.includes(keyword)) {
      const newKeywords = [...formData.seoKeywords, keyword];
      onFormDataChange({ seoKeywords: newKeywords });
    }
  };

  const removeKeyword = (keyword: string) => {
    const newKeywords = formData.seoKeywords.filter(k => k !== keyword);
    onFormDataChange({ seoKeywords: newKeywords });
  };

  const handleKeywordInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      const target = e.target as HTMLInputElement;
      const value = target.value
        .split(",")
        .map((k: string) => k.trim())
        .filter((k: string) => k);
      value.forEach(addKeyword);
      target.value = "";
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Create a temporary URL for preview
      const imageUrl = URL.createObjectURL(file);
      handleInputChange("ogImageUrl", imageUrl);

      // In a real application, you would upload the file to your server here
      // and then use the returned URL instead of the blob URL
      console.log("File selected:", file);
    }
  };

  const tabs = [
    { id: "basic", label: "Basic SEO" },
    { id: "social", label: "Social Media" },
    { id: "advanced", label: "Advanced" },
  ];

  return (
    <div className="w-full mx-auto p-4 bg-[var(--background)] min-h-screen">
      <div className="bg-[var(--white)] rounded-lg shadow-sm border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h1 className="text-lg font-semibold text-[var(--text)]">
            SEO Management
          </h1>
        </div>

        <div className="p-4">
          {/* Tabs */}
          <div className="flex border-b border-gray-200 mb-4">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 font-medium text-sm border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? "border-[var(--accent)] text-[var(--accent)]"
                    : "border-transparent text-gray-500 hover:text-[var(--text)]"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          <div>
            {/* Basic SEO Tab */}
            {activeTab === "basic" && (
              <div className="space-y-4">
                {/* Search Engine Preview */}
                <div>
                  <h3 className="text-base font-medium text-[var(--text)] mb-3">
                    Search Engine Preview
                  </h3>
                  <div className="border border-gray-200 rounded-lg p-3 bg-[var(--hero)]">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {formData.ogImageUrl || formData.imageUrl ? (
                          <img
                            src={formData.ogImageUrl || formData.imageUrl}
                            alt="Preview"
                            className="w-16 h-16 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <span className="text-gray-400 text-xs">Image</span>
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-[var(--accent)] text-base font-medium truncate">
                          {formData.seoTitle || formData.title || "SEO Title - Your Site Name"}
                        </div>
                        <div className="text-[var(--light-green)] text-sm mb-1">
                          {`www.swiftafricasafaris.com/blog/${formData.slug || "url-slug"}`}
                        </div>
                        <div className="text-gray-600 text-sm leading-tight line-clamp-2">
                          {formData.seoDescription || formData.description ||
                            "This is a sample meta description that will appear in search engine results. It should be compelling and contain relevant keywords."}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="space-y-4">
                  {/* SEO Title */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      SEO Title
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The title that appears in search engine results
                      (Recommended: 50-60 characters)
                    </p>
                    <input
                      type="text"
                      value={formData.seoTitle}
                      onChange={(e) =>
                        handleInputChange("seoTitle", e.target.value)
                      }
                      maxLength={70}
                      placeholder="Enter SEO title"
                      className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                        errors.seoTitle ? "border-red-300" : "border-gray-300"
                      }`}
                    />
                    <div className="flex justify-between items-center mt-1">
                      {errors.seoTitle && (
                        <p className="text-red-500 text-xs">
                          {errors.seoTitle}
                        </p>
                      )}
                      <p className="text-xs text-gray-500 ml-auto">
                        {(formData.seoTitle || '').length}/70
                      </p>
                    </div>
                  </div>

                  {/* SEO Description */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      SEO Description
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      A brief summary that appears in search results
                      (Recommended: 140-160 characters)
                    </p>
                    <textarea
                      value={formData.seoDescription}
                      onChange={(e) =>
                        handleInputChange("seoDescription", e.target.value)
                      }
                      maxLength={160}
                      rows={3}
                      placeholder="Enter meta description"
                      className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                        errors.seoDescription
                          ? "border-red-300"
                          : "border-gray-300"
                      }`}
                    />
                    <div className="flex justify-between items-center mt-1">
                      {errors.seoDescription && (
                        <p className="text-red-500 text-xs">
                          {errors.seoDescription}
                        </p>
                      )}
                      <p className="text-xs text-gray-500 ml-auto">
                        {formData.seoDescription.length}/160
                      </p>
                    </div>
                  </div>

                  {/* SEO Keywords */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      SEO Keywords
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      Enter keywords separated by commas or press Enter after
                      each keyword
                    </p>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formData.seoKeywords.map((keyword: string) => (
                        <span
                          key={keyword}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[var(--card-bg)] text-[var(--text)]"
                        >
                          {keyword}
                          <button
                            type="button"
                            onClick={() => removeKeyword(keyword)}
                            className="ml-1 text-[var(--accent)] hover:text-[var(--btn)]"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                    <input
                      ref={keywordInputRef}
                      type="text"
                      onKeyDown={handleKeywordInput}
                      onBlur={(e) => {
                        if (e.target.value) {
                          const values = e.target.value
                            .split(",")
                            .map((k: string) => k.trim())
                            .filter((k: string) => k);
                          values.forEach(addKeyword);
                          e.target.value = "";
                        }
                      }}
                      placeholder="Enter keywords and press Enter"
                      className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                        errors.keywords ? "border-red-300" : "border-gray-300"
                      }`}
                    />
                    {errors.keywords && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.keywords}
                      </p>
                    )}
                  </div>

                  {/* Featured Image */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Featured Image
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The main image used for this content (automatically used
                      for search results and social sharing)
                    </p>

                    {/* Image Upload Area */}
                    <div className="space-y-3">
                      {/* Clickable Upload Area */}
                      <div
                        onClick={handleFileSelect}
                        className="relative border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-[var(--accent)] transition-colors cursor-pointer bg-[var(--hero)] hover:bg-[var(--card-bg)]"
                      >
                        <div className="text-center">
                          {formData.seoImage ? (
                            <div className="space-y-2">
                              <img
                                src={formData.ogImageUrl || formData.imageUrl}
                                alt="Featured Image Preview"
                                className="mx-auto h-24 w-24 object-cover rounded-lg"
                              />
                              <p className="text-sm text-gray-600">
                                Click to change image
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-2">
                              <svg
                                className="mx-auto h-12 w-12 text-gray-400"
                                stroke="currentColor"
                                fill="none"
                                viewBox="0 0 48 48"
                              >
                                <path
                                  d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                  strokeWidth={2}
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />
                              </svg>
                              <div className="text-sm text-gray-600">
                                <span className="font-medium text-[var(--accent)] hover:text-[var(--btn)]">
                                  Click to upload
                                </span>{" "}
                                or drag and drop
                              </div>
                              <p className="text-xs text-gray-500">
                                PNG, JPG, GIF up to 10MB
                              </p>
                            </div>
                          )}
                        </div>

                        {/* Hidden file input */}
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                      </div>

                      {/* URL Input Alternative */}
                      <div className="flex items-center space-x-3">
                        <div className="flex-1 h-px bg-gray-300"></div>
                        <span className="text-xs text-gray-500 bg-white px-2">
                          or enter URL
                        </span>
                        <div className="flex-1 h-px bg-gray-300"></div>
                      </div>

                      <input
                        type="text"
                        value={formData.ogImageUrl}
                        onChange={(e) =>
                          handleInputChange("ogImageUrl", e.target.value)
                        }
                        placeholder="https://example.com/image.jpg"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                      />
                    </div>
                  </div>

                  {/* URL Slug */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      URL Slug
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The last part of the URL (e.g., &quot;product-name&quot; in
                      example.com/product-name)
                    </p>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) =>
                        handleInputChange("slug", e.target.value)
                      }
                      placeholder="Enter URL slug"
                      className={`w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)] ${
                        errors.slug ? "border-red-300" : "border-gray-300"
                      }`}
                    />
                    {errors.slug && (
                      <p className="text-red-500 text-xs mt-1">
                        {errors.slug}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Social Media Tab */}
            {activeTab === "social" && (
              <div className="space-y-4">
                {/* Social Media Preview */}
                <div>
                  <h3 className="text-base font-medium text-[var(--text)] mb-3">
                    Social Media Preview
                  </h3>
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <div className="flex">
                      <div className="w-32 h-32 bg-[var(--hero)] flex items-center justify-center">
                        {formData.ogImageUrl ? (
                          <img
                            src={formData.ogImageUrl}
                            alt="OG Preview"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-gray-400 text-sm">
                            Image Preview
                          </span>
                        )}
                      </div>
                      <div className="flex-1 p-3">
                        <div className="font-semibold text-[var(--text)] mb-2 line-clamp-2 text-sm">
                          {formData.ogTitle || "Open Graph Title"}
                        </div>
                        <div className="text-gray-600 text-xs line-clamp-3">
                          {formData.ogDescription ||
                            "This is a sample Open Graph description that will appear when sharing on social media. It should be compelling and informative."}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="space-y-4">
                  {/* OG Title */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Open Graph Title
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The title that appears when shared on social media
                    </p>
                    <input
                      type="text"
                      value={formData.ogTitle}
                      onChange={(e) =>
                        handleInputChange("ogTitle", e.target.value)
                      }
                      placeholder="Enter Open Graph title"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {formData.ogTitle.length}/90
                    </p>
                  </div>

                  {/* OG Description */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Open Graph Description
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The description that appears when shared on social media
                    </p>
                    <textarea
                      value={formData.ogDescription}
                      onChange={(e) =>
                        handleInputChange("ogDescription", e.target.value)
                      }
                      rows={3}
                      placeholder="Enter Open Graph description"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                    <p className="text-xs text-gray-500 mt-1 text-right">
                      {formData.ogDescription.length}/200
                    </p>
                  </div>

                  {/* OG Image */}
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Open Graph Image URL
                    </label>
                    <p className="text-xs text-gray-500 mb-2">
                      The image that appears when shared on social media
                      (automatically synced from Featured Image, Recommended:
                      1200×630 pixels)
                    </p>
                    <input
                      type="text"
                      value={formData.ogImageUrl}
                      onChange={(e) =>
                        handleInputChange("ogImageUrl", e.target.value)
                      }
                      placeholder="Enter image URL or upload"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Advanced Tab */}
            {activeTab === "advanced" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-[var(--text)] mb-1">
                    Canonical URL
                  </label>
                  <p className="text-xs text-gray-500 mb-2">
                    The preferred URL for this content if duplicate versions
                    exist
                  </p>
                  <input
                    type="text"
                    value={formData.canonicalUrl}
                    onChange={(e) =>
                      handleInputChange("canonicalUrl", e.target.value)
                    }
                    placeholder="Enter canonical URL"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Search Engine Indexing
                    </label>
                    <select
                      value={formData.robotsIndex}
                      onChange={(e) =>
                        handleInputChange("robotsIndex", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    >
                      <option value="index">
                        Index (allow search engines to index)
                      </option>
                      <option value="noindex">
                        No Index (prevent indexing)
                      </option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-[var(--text)] mb-1">
                      Link Following
                    </label>
                    <select
                      value={formData.robotsFollow}
                      onChange={(e) =>
                        handleInputChange("robotsFollow", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[var(--accent)]"
                    >
                      <option value="follow">
                        Follow (allow following links)
                      </option>
                      <option value="nofollow">
                        No Follow (prevent following links)
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogSEO;
