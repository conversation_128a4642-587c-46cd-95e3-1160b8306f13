import React from 'react';
import { SearchX } from 'lucide-react';

const NoResults = ({ searchTerm, suggestions, onSuggestionClick }) => {
  return (
    <div className="flex flex-col items-center justify-center py-8 px-4">
      <SearchX className="w-16 h-16 text-gray-300 mb-4" />
      <h3 className="text-lg font-medium text-gray-900 mb-2">
        No results found for &quot;{searchTerm}&quot;
      </h3>
      
      {suggestions?.length > 0 && (
        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600 mb-3">Try these instead:</p>
          <div className="flex flex-wrap gap-2 justify-center">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => onSuggestionClick(suggestion)}
                className="px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-sm text-gray-700"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default NoResults;
