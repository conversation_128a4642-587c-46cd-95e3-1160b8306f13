"use client";
import React, { useState } from 'react';
import { SkeletonGrid, SkeletonCard, SkeletonText, SkeletonImage } from '../skeleton';
import LazyImage from '../common/LazyImage';

/**
 * Loading Test Component
 * Demonstrates all skeleton loading components and lazy loading functionality
 */
const LoadingTest = () => {
  const [showSkeletons, setShowSkeletons] = useState(true);
  const [testVariant, setTestVariant] = useState('blog');

  const variants = ['blog', 'package', 'product', 'mini-package'];

  const toggleSkeletons = () => {
    setShowSkeletons(!showSkeletons);
  };

  return (
    <div className="min-h-screen bg-[var(--primary-background)] p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Loading System Test</h1>
          <p className="text-lg text-gray-600 mb-6">
            Test the comprehensive loading system with Facebook-style skeleton preloaders and lazy loading
          </p>
          
          {/* Controls */}
          <div className="flex flex-wrap justify-center gap-4 mb-6">
            <button
              onClick={toggleSkeletons}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {showSkeletons ? 'Hide Skeletons' : 'Show Skeletons'}
            </button>
            
            <select
              value={testVariant}
              onChange={(e) => setTestVariant(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg"
            >
              {variants.map(variant => (
                <option key={variant} value={variant}>
                  {variant.charAt(0).toUpperCase() + variant.slice(1)} Cards
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Skeleton Grid Test */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-4">Skeleton Grid - {testVariant}</h2>
          {showSkeletons ? (
            <SkeletonGrid 
              variant={testVariant}
              count={6}
              cardProps={{ 
                showPrice: testVariant === 'package' || testVariant === 'mini-package',
                showButton: true 
              }}
            />
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="bg-white p-6 rounded-lg shadow-md">
                  <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">Loaded Content {index + 1}</h3>
                    <p className="text-gray-600">This represents actual loaded content</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>

        {/* Individual Skeleton Components */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-4">Individual Skeleton Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            
            {/* Skeleton Card */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Skeleton Card</h3>
              <SkeletonCard variant="blog" showButton={true} />
            </div>

            {/* Skeleton Text */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Skeleton Text</h3>
              <SkeletonText variant="title" className="mb-3" />
              <SkeletonText variant="paragraph" lines={3} />
            </div>

            {/* Skeleton Image */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Skeleton Image</h3>
              <SkeletonImage variant="card" showIcon={true} />
            </div>

            {/* Lazy Image */}
            <div className="bg-white p-4 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-3">Lazy Image</h3>
              <LazyImage
                src="/images/hero/great-migration-serengeti-national-park.webp"
                alt="Test lazy loading image"
                width={300}
                height={200}
                className="w-full h-48 object-cover rounded"
                skeletonVariant="card"
              />
            </div>
          </div>
        </section>

        {/* Performance Info */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-4">Performance Features</h2>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">✨ Implemented Features</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• Facebook-style shimmer animations</li>
                  <li>• Lazy loading with intersection observer</li>
                  <li>• Responsive skeleton grids</li>
                  <li>• Smooth transitions from skeleton to content</li>
                  <li>• Performance monitoring and optimization</li>
                  <li>• Core Web Vitals improvements</li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">🚀 Performance Benefits</h3>
                <ul className="space-y-2 text-gray-700">
                  <li>• Reduced perceived loading time</li>
                  <li>• Better user experience during loading</li>
                  <li>• Optimized image loading</li>
                  <li>• Improved Core Web Vitals scores</li>
                  <li>• Consistent loading patterns</li>
                  <li>• Mobile-optimized performance</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default LoadingTest;
