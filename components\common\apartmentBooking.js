/* eslint-disable react/no-unescaped-entities */

import React, { useState } from 'react';
import { User, Mail, Phone, Home, CheckCircle, ArrowLeft } from 'lucide-react';

export default function ApartmentBookingForm({ onClose }) {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    whatsapp: '',
    properties: ''
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.whatsapp.trim()) {
      newErrors.whatsapp = 'WhatsApp number is required';
    }

    if (!formData.properties.trim()) {
      newErrors.properties = 'Please tell us about your preferred apartment properties';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        const response = await fetch('/api/bookings/apartment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fullName: formData.fullName,
            email: formData.email,
            whatsapp: formData.whatsapp,
            properties: formData.properties
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to submit booking');
        }

        const result = await response.json();
        if (result.success) {
          setIsSubmitted(true);
          console.log('Form submitted successfully:', result.data);

          // Close modal after 3 seconds
          setTimeout(() => {
            onClose && onClose();
          }, 3000);
        } else {
          throw new Error(result.error || 'Failed to submit booking');
        }
      } catch (error) {
        console.error('Error submitting booking:', error);
        setErrors({ submit: 'Failed to submit booking. Please try again.' });
      }
    }
  };

  const resetForm = () => {
    setFormData({
      fullName: '',
      email: '',
      whatsapp: '',
      properties: ''
    });
    setIsSubmitted(false);
    setErrors({});
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="bg-[var(--white)] rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="mb-6">
            <CheckCircle className="mx-auto h-16 w-16 text-[var(--light-green)] mb-4" />
            <h2 className="text-2xl font-bold text-[var(--text)] mb-2">Thank You!</h2>
            <p className="text-gray-600 leading-relaxed">
              We appreciate your interest in booking an apartment with us.
              Our team will contact you within 24 hours to discuss your requirements
              and help you find the perfect apartment.
            </p>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <p className="text-sm text-blue-800">
              <strong>What's next?</strong><br />
              We'll review your preferences and prepare suitable options for you.
            </p>
          </div>

          <button
            onClick={resetForm}
            className="inline-flex items-center gap-2 bg-[var(--btn)] text-[var(--white)] px-6 py-3 rounded-lg hover:bg-[var(--light-green)] transition-colors font-medium"
          >
            <ArrowLeft className="h-4 w-4" />
            Submit Another Request
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      
      <div className="bg-[var(--white)] backdrop-blur-sm rounded-xl shadow-xl p-3 px-6 max-w-lg w-full">
        <div className="text-center mb-1">
          <Home className="mx-auto h-5 w-5 text-[var(--accent)] mb-1" />
          <h1 className="text-xl font-bold text-[var(--text)]">Book Your Apartment</h1>
          <p className="text-sm text-gray-600">Find your perfect home with us</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-2 text-left">
          {/* Full Name */}
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${errors.fullName ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter your full name"
              />
            </div>
            {errors.fullName && <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>}
          </div>

          {/* Email */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter your email address"
              />
            </div>
            {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
          </div>

          {/* WhatsApp Number */}
          <div>
            <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-700 mb-2">
              WhatsApp Number
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="tel"
                id="whatsapp"
                name="whatsapp"
                value={formData.whatsapp}
                onChange={handleChange}
                className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${errors.whatsapp ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter your WhatsApp number"
              />
            </div>
            {errors.whatsapp && <p className="text-red-500 text-sm mt-1">{errors.whatsapp}</p>}
          </div>

          {/* Apartment Properties */}
          <div>
            <label htmlFor="properties" className="block text-sm font-medium text-gray-700 mb-2">
              Tell us about your ideal apartment
            </label>
            <textarea
              id="properties"
              name="properties"
              value={formData.properties}
              onChange={handleChange}
              rows={2}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none ${errors.properties ? 'border-red-500' : 'border-gray-300'
                }`}
              placeholder="Describe your preferences: number of bedrooms, location, budget, amenities, etc."
            />
            {errors.properties && <p className="text-red-500 text-sm mt-1">{errors.properties}</p>}
          </div>

          {/* Submit Button */}
          <div className="flex justify-center">
            <button
              type="submit"
              className="w-2/4 bg-[var(--btn)] text-[var(--white)] py-2 px-6 rounded-lg hover:bg-[var(--light-green)] focus:ring-2 focus:ring-[var(--btn-background)] focus:ring-offset-2 transition-colors font-medium"
            >
              Submit Request
            </button>
          </div>
        </form>

        <div className="mt-1 text-center">
          <p className="text-xs text-gray-500">
            We'll contact you within 24 hours
          </p>
        </div>
      </div>
    </div>
  );
}