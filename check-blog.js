const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkBlogPosts() {
  console.log('Checking blog posts in database...\n');
  
  try {
    // Check all blog posts
    const { data: allPosts, error: allError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status, published_at, deleted_at')
      .order('created_at', { ascending: false });
    
    if (allError) {
      console.error('Error fetching all posts:', allError);
      return;
    }
    
    console.log(`Total blog posts found: ${allPosts.length}`);
    allPosts.forEach(post => {
      console.log(`- ${post.title}`);
      console.log(`  Slug: ${post.slug}`);
      console.log(`  Status: ${post.status}`);
      console.log(`  Published: ${post.published_at}`);
      console.log(`  Deleted: ${post.deleted_at}`);
      console.log('');
    });
    
    // Check published posts specifically
    const { data: publishedPosts, error: publishedError } = await supabase
      .from('sas_blog_posts')
      .select('id, title, slug, status')
      .eq('status', 'published')
      .is('deleted_at', null);
    
    if (publishedError) {
      console.error('Error fetching published posts:', publishedError);
      return;
    }
    
    console.log(`\nPublished blog posts: ${publishedPosts.length}`);
    publishedPosts.forEach(post => {
      console.log(`- ${post.title} (${post.slug})`);
    });
    
    // Check specific post
    const targetSlug = 'mountain-gorilla-trekking-in-rwanda-and-uganda';
    const { data: specificPost, error: specificError } = await supabase
      .from('sas_blog_posts')
      .select('*')
      .eq('slug', targetSlug)
      .single();
    
    if (specificError) {
      console.error(`\nError fetching post with slug '${targetSlug}':`, specificError);
    } else {
      console.log(`\nFound specific post: ${specificPost.title}`);
      console.log(`Status: ${specificPost.status}`);
      console.log(`Published: ${specificPost.published_at}`);
      console.log(`Deleted: ${specificPost.deleted_at}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkBlogPosts();
