export const searchProducts = (products, searchTerm) => {
  if (!searchTerm) return products;
  
  const terms = searchTerm.toLowerCase().split(' ').filter(term => term.length > 0);
  
  return products
    .map(product => {
      let score = 0;
      const titleLower = product.title.toLowerCase();
      const descLower = product.description.toLowerCase();
      
      terms.forEach(term => {
        // Title matches (weighted higher)
        if (titleLower.includes(term)) {
          score += 10;
          // Bonus for exact matches
          if (titleLower === term) score += 5;
          // Bonus for start of word matches
          if (titleLower.startsWith(term)) score += 3;
        }
        
        // Description matches
        if (descLower.includes(term)) {
          score += 5;
          // Bonus for exact phrase matches
          if (descLower.includes(term + ' ')) score += 2;
        }
      });
      
      return { ...product, score };
    })
    .filter(product => product.score > 0)
    .sort((a, b) => b.score - a.score);
};

export const getSearchSuggestions = (products, searchTerm) => {
  if (!searchTerm || searchTerm.length < 2) return [];
  
  const matches = searchProducts(products, searchTerm);
  return matches.slice(0, 5).map(p => p.title);
};
