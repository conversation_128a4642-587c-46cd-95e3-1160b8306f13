'use client'

import React, { useState, useEffect } from 'react'

interface TestResult {
  name: string
  status: 'pass' | 'fail' | 'warning'
  details?: any
  error?: string
}

interface TestSuite {
  total_tests: number
  passed: number
  failed: number
  tests: TestResult[]
}

interface ComprehensiveTestResults {
  summary: {
    total_tests: number
    passed: number
    failed: number
    score: number
    health_status: string
    execution_time_ms: number
  }
  test_suites: {
    metadata: TestSuite
    hreflang: TestSuite
    structured_data: TestSuite
    sitemap: TestSuite
    performance: TestSuite
    indexing: TestSuite
    validation: TestSuite
  }
  recommendations: string[]
  timestamp: string
}

export default function SEOTestingDashboard() {
  const [testResults, setTestResults] = useState<ComprehensiveTestResults | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedSuite, setSelectedSuite] = useState<string | null>(null)
  const [autoRefresh, setAutoRefresh] = useState(false)

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(runComprehensiveTests, 300000) // 5 minutes
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  const runComprehensiveTests = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch('/api/seo-test?test=all')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      setTestResults(data.results)
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const runSpecificTest = async (testType: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await fetch(`/api/seo-test?test=${testType}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const data = await response.json()
      
      // Update specific test suite in results
      if (testResults) {
        setTestResults({
          ...testResults,
          test_suites: {
            ...testResults.test_suites,
            [testType]: data.results
          }
        })
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'text-green-600 bg-green-100'
      case 'fail': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600'
      case 'good': return 'text-blue-600'
      case 'fair': return 'text-yellow-600'
      case 'poor': return 'text-orange-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const formatExecutionTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">SEO Testing & Validation</h1>
          <p className="text-gray-600">Comprehensive testing of all SEO implementations</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600">Auto-refresh (5min)</span>
          </label>
          
          <button
            onClick={runComprehensiveTests}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm flex items-center"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Running Tests...
              </>
            ) : (
              'Run All Tests'
            )}
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Test Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
              <button 
                onClick={() => setError(null)}
                className="mt-3 bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded text-sm"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Results Summary */}
      {testResults && (
        <>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className={`text-3xl font-bold ${getHealthColor(testResults.summary.health_status)}`}>
                  {testResults.summary.score}%
                </div>
                <div className="text-sm text-gray-600">Overall Score</div>
                <div className={`text-xs font-medium ${getHealthColor(testResults.summary.health_status)}`}>
                  {testResults.summary.health_status.toUpperCase()}
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{testResults.summary.total_tests}</div>
                <div className="text-sm text-gray-600">Total Tests</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{testResults.summary.passed}</div>
                <div className="text-sm text-gray-600">Passed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{testResults.summary.failed}</div>
                <div className="text-sm text-gray-600">Failed</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatExecutionTime(testResults.summary.execution_time_ms)}
                </div>
                <div className="text-sm text-gray-600">Execution Time</div>
              </div>
            </div>
          </div>

          {/* Test Suites Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Object.entries(testResults.test_suites).map(([suiteName, suite]) => (
              <div key={suiteName} className="bg-white rounded-lg shadow p-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-semibold text-gray-900 capitalize">
                    {suiteName.replace('_', ' ')}
                  </h3>
                  <button
                    onClick={() => runSpecificTest(suiteName)}
                    disabled={loading}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Retest
                  </button>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Total:</span>
                    <span className="font-medium">{suite.total_tests}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Passed:</span>
                    <span className="font-medium text-green-600">{suite.passed}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Failed:</span>
                    <span className="font-medium text-red-600">{suite.failed}</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${(suite.passed / suite.total_tests) * 100}%` }}
                    ></div>
                  </div>
                </div>
                
                <button
                  onClick={() => setSelectedSuite(selectedSuite === suiteName ? null : suiteName)}
                  className="mt-3 text-sm text-blue-600 hover:text-blue-800"
                >
                  {selectedSuite === suiteName ? 'Hide Details' : 'View Details'}
                </button>
              </div>
            ))}
          </div>

          {/* Detailed Test Results */}
          {selectedSuite && testResults.test_suites[selectedSuite as keyof typeof testResults.test_suites] && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 capitalize">
                {selectedSuite.replace('_', ' ')} Test Details
              </h3>
              
              <div className="space-y-3">
                {testResults.test_suites[selectedSuite as keyof typeof testResults.test_suites].tests.map((test, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{test.name}</h4>
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(test.status)}`}>
                        {test.status.toUpperCase()}
                      </span>
                    </div>
                    
                    {test.error && (
                      <div className="text-sm text-red-600 bg-red-50 p-2 rounded mt-2">
                        <strong>Error:</strong> {test.error}
                      </div>
                    )}
                    
                    {test.details && (
                      <div className="text-sm text-gray-600 mt-2">
                        <strong>Details:</strong>
                        <pre className="mt-1 bg-gray-50 p-2 rounded text-xs overflow-x-auto">
                          {JSON.stringify(test.details, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recommendations */}
          {testResults.recommendations.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">SEO Recommendations</h3>
              <ul className="space-y-2">
                {testResults.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                    <span className="text-blue-800">{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Test Timestamp */}
          <div className="text-center text-sm text-gray-500">
            Last tested: {new Date(testResults.timestamp).toLocaleString()}
          </div>
        </>
      )}

      {/* Initial State */}
      {!testResults && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test SEO Implementation</h3>
          <p className="text-gray-600 mb-4">
            Run comprehensive tests to validate all SEO features including metadata, hreflang, structured data, sitemaps, and performance monitoring.
          </p>
          <button
            onClick={runComprehensiveTests}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium"
          >
            Start SEO Testing
          </button>
        </div>
      )}
    </div>
  )
}
