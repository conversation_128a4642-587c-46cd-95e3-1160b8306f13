-- =====================================================
-- QUICK STORAGE FIX - Run this to resolve upload issues
-- =====================================================

-- Create the main package images bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
  ('sas-package-images', 'sas-package-images', true, 10485760,
   ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO NOTHING;

-- Create policies (ignore errors if they already exist)
DO $$
BEGIN
  -- Try to create SELECT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Anyone can view package images" ON storage.objects FOR SELECT USING (bucket_id = ''sas-package-images'')';
  EXCEPTION WHEN duplicate_object THEN
    -- Policy already exists, ignore
  END;

  -- Try to create INSERT policy
  BEGIN
    EXECUTE 'CREATE POLICY "Authenticated users can upload package images" ON storage.objects FOR INSERT WITH CHECK (bucket_id = ''sas-package-images'' AND auth.role() = ''authenticated'')';
  EXCEPTION WHEN duplicate_object THEN
    -- Policy already exists, ignore
  END;

  -- Try to create UPDATE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Authenticated users can update package images" ON storage.objects FOR UPDATE USING (bucket_id = ''sas-package-images'' AND auth.role() = ''authenticated'')';
  EXCEPTION WHEN duplicate_object THEN
    -- Policy already exists, ignore
  END;

  -- Try to create DELETE policy
  BEGIN
    EXECUTE 'CREATE POLICY "Authenticated users can delete package images" ON storage.objects FOR DELETE USING (bucket_id = ''sas-package-images'' AND auth.role() = ''authenticated'')';
  EXCEPTION WHEN duplicate_object THEN
    -- Policy already exists, ignore
  END;
END $$;
