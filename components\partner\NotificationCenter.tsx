'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  Bell, 
  X, 
  CheckCheck, 
  Calendar, 
  DollarSign, 
  Package, 
  MessageSquare, 
  AlertCircle,
  Info,
  Clock,
  Settings
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'booking' | 'payment' | 'message' | 'system' | 'package';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  metadata?: {
    bookingId?: string;
    amount?: number;
    packageName?: string;
  };
}

interface NotificationCenterProps {
  className?: string;
}

const styles = `
  /* Webkit (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #D1D5DB;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #9CA3AF;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: #D1D5DB transparent;
  }
`;

export default function NotificationCenter({ className = '' }: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Mock notifications - replace with actual API calls
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'booking',
        title: 'New Booking Confirmed',
        message: 'Booking SA-B2B-001 for Serengeti Safari has been confirmed',
        timestamp: '2024-12-20T10:30:00Z',
        read: false,
        priority: 'high',
        actionUrl: '/partner/bookings/SA-B2B-001',
        metadata: {
          bookingId: 'SA-B2B-001',
          packageName: 'Serengeti 5-Day Safari'
        }
      },
      {
        id: '2',
        type: 'payment',
        title: 'Commission Payment Processed',
        message: 'Your commission payment of $1,080 has been processed',
        timestamp: '2024-12-20T09:15:00Z',
        read: false,
        priority: 'medium',
        actionUrl: '/partner/reports',
        metadata: {
          amount: 1080
        }
      },
      {
        id: '3',
        type: 'message',
        title: 'New Message from Admin',
        message: 'Sarah Johnson replied to your booking modification request',
        timestamp: '2024-12-19T16:45:00Z',
        read: true,
        priority: 'medium',
        actionUrl: '/partner/messages',
        metadata: {
          bookingId: 'SA-B2B-001'
        }
      },
      {
        id: '4',
        type: 'package',
        title: 'New Package Available',
        message: 'Zanzibar Beach Extension package is now available for booking',
        timestamp: '2024-12-19T14:20:00Z',
        read: true,
        priority: 'low',
        actionUrl: '/partner/packages',
        metadata: {
          packageName: 'Zanzibar Beach Extension'
        }
      },
      {
        id: '5',
        type: 'system',
        title: 'System Maintenance Notice',
        message: 'Scheduled maintenance on Dec 25th from 2-4 AM UTC',
        timestamp: '2024-12-18T11:30:00Z',
        read: false,
        priority: 'medium',
        actionUrl: '/partner/profile'
      },
      {
        id: '6',
        type: 'booking',
        title: 'Booking Modification Request',
        message: 'Customer requested date change for booking SA-B2B-002',
        timestamp: '2024-12-18T08:15:00Z',
        read: true,
        priority: 'high',
        actionUrl: '/partner/bookings/SA-B2B-002',
        metadata: {
          bookingId: 'SA-B2B-002'
        }
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const unreadCount = notifications.filter(n => !n.read).length;
  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.read)
    : notifications;

  const getNotificationIcon = (type: string, priority: string) => {
    const iconClass = priority === 'high' ? 'text-red-600' : priority === 'medium' ? 'text-yellow-600' : 'text-blue-600';
    
    switch (type) {
      case 'booking': return <Calendar className={`h-4 w-4 ${iconClass}`} />;
      case 'payment': return <DollarSign className={`h-4 w-4 ${iconClass}`} />;
      case 'message': return <MessageSquare className={`h-4 w-4 ${iconClass}`} />;
      case 'package': return <Package className={`h-4 w-4 ${iconClass}`} />;
      case 'system': return <AlertCircle className={`h-4 w-4 ${iconClass}`} />;
      default: return <Info className={`h-4 w-4 ${iconClass}`} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-blue-500';
      default: return 'border-l-gray-500';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else if (diffInHours < 48) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(n => ({ ...n, read: true }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(n => n.id !== notificationId)
    );
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <style dangerouslySetInnerHTML={{ __html: styles }} />
      
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Notifications</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 hover:bg-gray-100 rounded transition-colors"
              >
                <X className="h-4 w-4 text-gray-500" />
              </button>
            </div>
            
            {/* Filter Tabs */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setFilter('all')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filter === 'all' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                All ({notifications.length})
              </button>
              <button
                onClick={() => setFilter('unread')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filter === 'unread' 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                Unread ({unreadCount})
              </button>
            </div>
          </div>

          {/* Actions */}
          {notifications.length > 0 && (
            <div className="px-4 py-2 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <button
                  onClick={markAllAsRead}
                  disabled={unreadCount === 0}
                  className="text-sm text-blue-600 hover:text-blue-500 disabled:text-gray-400 disabled:cursor-not-allowed flex items-center"
                >
                  <CheckCheck className="h-3 w-3 mr-1" />
                  Mark all as read
                </button>
                <button className="text-sm text-gray-600 hover:text-gray-500 flex items-center">
                  <Settings className="h-3 w-3 mr-1" />
                  Settings
                </button>
              </div>
            </div>
          )}

          {/* Notifications List */}
          <div className="max-h-96 overflow-y-auto">
            {filteredNotifications.length === 0 ? (
              <div className="p-8 text-center">
                <Bell className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 font-medium">
                  {filter === 'unread' ? 'No unread notifications' : 'No notifications'}
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  {filter === 'unread' 
                    ? 'All caught up! Check back later for updates.' 
                    : 'You\'ll see notifications here when they arrive.'
                  }
                </p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {filteredNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors border-l-4 ${getPriorityColor(notification.priority)} ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type, notification.priority)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className={`text-sm font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center mt-2 space-x-2">
                              <div className="flex items-center text-xs text-gray-500">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatTimestamp(notification.timestamp)}
                              </div>
                              {notification.metadata?.amount && (
                                <span className="text-xs text-green-600 font-medium">
                                  ${notification.metadata.amount.toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-1 ml-2">
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteNotification(notification.id);
                              }}
                              className="p-1 hover:bg-gray-200 rounded transition-colors opacity-0 group-hover:opacity-100"
                            >
                              <X className="h-3 w-3 text-gray-400" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <button
                onClick={() => {
                  window.location.href = '/partner/notifications';
                  setIsOpen(false);
                }}
                className="w-full text-center text-sm text-blue-600 hover:text-blue-500 font-medium"
              >
                View all notifications
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
