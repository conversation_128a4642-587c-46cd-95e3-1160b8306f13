import FaqQuestion from './FaqQuestion';

const FaqCategory = ({ category, questions, openItems, toggleItem, categoryIndex }) => {
  return (
    <div className="pb-8 border-b border-gray-200">
      <div className="flex justify-left items-left mb-6">
        <h2 className="text-xl font-semibold text-gray-800 pb-2 border-b-2 border-[var(--accent)]">
          {category}
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 auto-rows-auto">
        {questions.map((item, questionIndex) => (
          <FaqQuestion
            key={questionIndex}
            question={item.question}
            answer={item.answer}
            isOpen={openItems[`${categoryIndex}-${questionIndex}`]}
            onToggle={() => toggleItem(categoryIndex, questionIndex)}
          />
        ))}
      </div>
    </div>
  );
};

export default FaqCategory;
