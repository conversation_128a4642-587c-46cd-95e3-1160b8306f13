import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - NEXT_PUBLIC_SUPABASE_ANON_KEY');
  console.error('\nPlease check your .env.local file and ensure these variables are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyStorageBucket() {
  console.log('🔍 Checking if package-images bucket exists...\n');

  try {
    // List all buckets
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('❌ Error listing buckets:', error.message);
      return;
    }

    const packageImagesBucket = buckets.find(bucket => bucket.name === 'package-images');
    
    if (packageImagesBucket) {
      console.log('✅ package-images bucket exists!');
      console.log('📋 Bucket details:');
      console.log(`   - Name: ${packageImagesBucket.name}`);
      console.log(`   - Public: ${packageImagesBucket.public}`);
      console.log(`   - File size limit: ${packageImagesBucket.file_size_limit} bytes`);
      console.log(`   - Allowed MIME types: ${packageImagesBucket.allowed_mime_types?.join(', ') || 'All'}`);
      console.log(`   - Created: ${packageImagesBucket.created_at}`);
      
      // Test if we can list files in the bucket
      const { data: files, error: listError } = await supabase.storage
        .from('package-images')
        .list();
      
      if (listError) {
        console.log('⚠️  Warning: Cannot list files in bucket (this might be normal if bucket is empty)');
      } else {
        console.log(`📁 Files in bucket: ${files?.length || 0}`);
      }
      
    } else {
      console.log('❌ package-images bucket does not exist');
      console.log('\n📝 To create the bucket, run the SQL script:');
      console.log('   scripts/create-package-images-bucket.sql');
      console.log('\nOr follow these steps:');
      console.log('   1. Go to your Supabase dashboard');
      console.log('   2. Open the SQL Editor');
      console.log('   3. Copy and paste the contents of create-package-images-bucket.sql');
      console.log('   4. Run the SQL');
    }

    console.log('\n📦 All available buckets:');
    buckets.forEach(bucket => {
      console.log(`   - ${bucket.name} (public: ${bucket.public})`);
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

verifyStorageBucket().catch(console.error); 