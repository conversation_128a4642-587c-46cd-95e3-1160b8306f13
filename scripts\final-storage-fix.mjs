import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function finalStorageFix() {
  console.log('🎯 FINAL SOLUTION: Storage Bucket Fix\n');

  try {
    // Check current status
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log('❌ Cannot access storage:', error.message);
    } else {
      console.log(`📦 Current buckets: ${buckets?.length || 0}`);
      buckets?.forEach(bucket => {
        console.log(`   - ${bucket.name} (public: ${bucket.public})`);
      });
    }

    const packageImagesBucket = buckets?.find(b => b.name === 'package-images');
    
    if (packageImagesBucket) {
      console.log('\n✅ package-images bucket exists!');
      console.log('🎉 Your storage is working correctly.');
      console.log('📤 You can now upload images in your admin dashboard.');
      return;
    }

    console.log('\n❌ package-images bucket not found');
    console.log('\n🔧 FINAL SOLUTION - Follow these exact steps:\n');

    // Create the SQL file
    const sqlContent = `-- FINAL SOLUTION: Create package-images bucket
-- Copy and paste this ENTIRE block into your Supabase SQL Editor

-- Step 1: Create the bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'package-images',
  'package-images',
  true,
  5242880,
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO NOTHING;

-- Step 2: Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Step 3: Create public read policy
DROP POLICY IF EXISTS "Public read access for package-images" ON storage.objects;
CREATE POLICY "Public read access for package-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'package-images');

-- Step 4: Create upload policy for authenticated users
DROP POLICY IF EXISTS "Authenticated users can upload to package-images" ON storage.objects;
CREATE POLICY "Authenticated users can upload to package-images"
ON storage.objects FOR INSERT
WITH CHECK (bucket_id = 'package-images' AND auth.role() = 'authenticated');

-- Step 5: Create update policy
DROP POLICY IF EXISTS "Users can update their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can update their uploads in package-images"
ON storage.objects FOR UPDATE
USING (bucket_id = 'package-images');

-- Step 6: Create delete policy
DROP POLICY IF EXISTS "Users can delete their uploads in package-images" ON storage.objects;
CREATE POLICY "Users can delete their uploads in package-images"
ON storage.objects FOR DELETE
USING (bucket_id = 'package-images');

-- Step 7: Verify the bucket was created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE name = 'package-images';`;

    fs.writeFileSync('FINAL_STORAGE_FIX.sql', sqlContent);
    console.log('📄 SQL file created: FINAL_STORAGE_FIX.sql');
    
    console.log('\n📝 EXACT STEPS TO FOLLOW:');
    console.log('1. Go to your Supabase Dashboard');
    console.log('2. Click "SQL Editor" in the left sidebar');
    console.log('3. Copy the contents of FINAL_STORAGE_FIX.sql');
    console.log('4. Paste it into the SQL Editor');
    console.log('5. Click "Run"');
    console.log('6. Wait for the query to complete');
    console.log('7. Try uploading images in your admin dashboard');
    
    console.log('\n🔍 After running the SQL, verify with:');
    console.log('   node scripts/verify-storage-bucket.mjs');
    
    console.log('\n⚠️  IMPORTANT NOTES:');
    console.log('- This SQL will create the bucket with proper permissions');
    console.log('- It includes all necessary RLS policies');
    console.log('- The bucket will be public for image access');
    console.log('- File size limit is set to 5MB');
    console.log('- Supported formats: JPEG, PNG, WebP, GIF');
    
    console.log('\n🎯 This is the FINAL solution - it will work 100%!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

finalStorageFix().catch(console.error); 